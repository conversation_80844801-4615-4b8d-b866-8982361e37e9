name: PXM AI Translation
on:
  workflow_dispatch:
  pull_request:
    types: [closed]
    branches:
      - main

jobs:
  translate_pxm:
    # Only run if PR was merged in and not by the bot
    if: github.event.pull_request.merged == true && !endsWith(github.event.pull_request.user.login, '[bot]')
    runs-on: ubuntu-latest
    name: 'translate pxm frontend'
    steps:
      - name: Checkout code # Fetch all history for all branches and tags
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Get base commit # Grab the starting commit of the PR
        id: get_base
        run: |
          BASE_SHA=$(git merge-base ${{ github.event.pull_request.base.sha }} ${{ github.event.pull_request.head.sha }})
          echo "base_sha=$BASE_SHA" >> $GITHUB_OUTPUT
       
      - name: Get list of changed files
        id: changed_files
        run: |
          # Fetch the latest commit on the base branch (main) to ensure we have the latest state
          git fetch origin main

          # Compare base commit SHA with the PR head to get the list of changed files
          git diff --name-only ${{ steps.get_base.outputs.base_sha }} ${{ github.event.pull_request.head.sha }} > changed_files.txt
          
          # Debugging: Output the contents of the changed files
          cat changed_files.txt
      
      - name: Check if changes are in apps/pxm
        id: check_changes
        run: |
          if grep -q '^apps/pxm/' changed_files.txt; then
            echo "changes_in_pxm=true" >> $GITHUB_ENV
          else
            echo "changes_in_pxm=false" >> $GITHUB_ENV
          fi

      # Autotranslate any language files that were changed
      - name: Autotranslate PXM
        if: env.changes_in_pxm == 'true'
        uses: patterninc/translate_frontend_action@v2.6
        with:
          BRAIN_API_KEY: ${{ secrets.BRAIN_PROD_API_KEY }}
          GHA_RUNNER_TOKEN: ${{ secrets.GHA_RUNNER_TOKEN }}
          TRANSLATION_DIR_PATH: 'apps/pxm/src/modules/static-shared/src/translations/resources/' # path to the translations directory
          LANGUAGES: 'zh:chinese, fr:french' # languages to translate to (en.content.json will be always be the source)
          BASE_BRANCH: main
          TRANSLATION_BRANCH: pxm_translation # name of translation branch where PRs come from
          BASE_COMMIT: ${{ steps.get_base.outputs.base_sha }}
          SLACK_WEBHOOK: ${{ secrets.PXM_TRANSLATION_WEBOOK_URL }} # Get this from Slack Workflows, in the app
          # In the future, would probably be good to regenerate this webhook and move it into secrets
