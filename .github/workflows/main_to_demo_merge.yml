name: Merge demo workflow

on:
  push:
    branches: [main]

jobs:
  sync-demo-branch:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      - name: Merge main -> demo
        uses: devmasx/merge-branch@master
        with:
          type: now
          from_branch: main
          message: "Merge from main to demo"
          target_branch: demo
          github_token: ${{ secrets.GHA_RUNNER_TOKEN }}

      - name: Slack Notification
        uses: rtCamp/action-slack-notify@v2
        if: failure()
        env:
          SLACK_CHANNEL: ${{ secrets.SLACK_CHANNEL }}
          SLACK_COLOR: '#f27873'
          SLACK_MESSAGE: Oh no! Merge from main to demo branch for *pattern-exp/predict-demo has failed*.
          SLACK_TITLE: Build Message
          SLACK_USERNAME: Github Action
          SLACK_WEBHOOK: ${{ secrets.SLACK_WEBHOOK_URL }}
          SLACK_FOOTER: Github Action