name: Storybook CD - Predict 
on:
  workflow_dispatch:
  push:
    branches:
      - 'main'
    paths:
      - 'apps/predict/**'
  pull_request:
    branches:
      - 'main'
    paths:
      - 'apps/predict/**'
concurrency: predict_storybook_deployment

jobs:
  build:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Read .pnpmversion file
        id: read_pnpm_version
        run: echo "PNPM_VERSION=$(cat .pnpmversion)" >> $GITHUB_ENV

      - name: Install pnpm
        uses: pnpm/action-setup@v4
        with:
          version: ${{ env.PNPM_VERSION }}
          run_install: false

      - name: Setup Node
        uses: actions/setup-node@v4
        with:
          node-version-file: .nvmrc
          cache: 'pnpm'

      - name: Get pnpm store directory
        run: |
          echo "STORE_PATH=$(pnpm store path --silent)" >> $GITHUB_ENV

      - name: Cache pnpm store
        uses: actions/cache@v4
        with:
          path: ${{ env.STORE_PATH }}
          key: ${{ runner.os }}-pnpm-store-${{ hashFiles('**/pnpm-lock.yaml') }}
          restore-keys: |
            ${{ runner.os }}-pnpm-store-

      - name: Install dependencies
        run: pnpm install

      - name: Install predict-specific dependencies
        working-directory: apps/predict
        run: pnpm install

      - name: Build Storybook
        run: npx nx run predict:"build-storybook"

      - name: Archive Stage storybook Build Execution Result
        if: success()
        uses: actions/upload-artifact@v4
        with:
          name: build-artifact-storybook
          path: apps/predict/storybook-static
  stage_deployment:
    if: github.ref == 'refs/heads/main' && github.event_name == 'push'
    runs-on: ubuntu-latest
    needs: build
    env:
      AWS_ACCESS_KEY_ID: ${{ secrets.AWS_DEV_ACCESS_KEY }}
      AWS_SECRET_ACCESS_KEY: ${{ secrets.AWS_DEV_SECRET_KEY }}
      AWS_REGION: 'us-west-2'
      STORYBOOK_BUCKET_NAME: library-stage.pattern.com
      SB_SITE_CF_ID: EZVJRH04PCNSV
    steps:
      - name: Download Build Artifact
        uses: actions/download-artifact@v4
        with:
          name: build-artifact-storybook
          path: storybook-static

      - name: Deploy storybook app
        env:
          FOLDER: storybook-static
        run: |
          aws s3 sync ./$FOLDER s3://$STORYBOOK_BUCKET_NAME/predict --delete

      - name: Invalidate cache
        run: aws cloudfront create-invalidation --distribution-id $SB_SITE_CF_ID --paths "/predict/*"

  production_deployment:
    if: github.ref == 'refs/heads/main' && github.event_name == 'push'
    runs-on: ubuntu-latest
    needs: stage_deployment
    env:
      AWS_ACCESS_KEY_ID: ${{ secrets.AWS_PROD_ACCESS_KEY }}
      AWS_SECRET_ACCESS_KEY: ${{ secrets.AWS_PROD_SECRET_KEY }}
      AWS_REGION: 'us-west-2'
      STORYBOOK_BUCKET_NAME: library.pattern.com
      SB_SITE_CF_ID: E1DGAT6V4NU0N9
    steps:
      - name: Download Build Artifact
        uses: actions/download-artifact@v4
        with:
          name: build-artifact-storybook
          path: storybook-static

      - name: Deploy storybook app
        env:
          FOLDER: storybook-static
        run: aws s3 sync ./$FOLDER s3://$STORYBOOK_BUCKET_NAME/predict --delete

      - name: Invalidate cache
        run: aws cloudfront create-invalidation --distribution-id $SB_SITE_CF_ID --paths "/predict/*"
