name: Deploy - PXM App
on:
  push:
    branches:
      - main
    paths:
      - 'apps/pxm/**'
      - '*.*'
  workflow_dispatch:
jobs:
  ### ============================= STAGE-build-upload-docker-image ============================= ###
  stage-build:
    uses: ampmedia/amp-reusable-workflow/.github/workflows/docker-build-and-push.yml@v1.3.2
    with:
      ecr-repo-name: stage-amp-ws
      build-args: |
        NODE_ENV=production
      dockerfile-path: apps/pxm/Dockerfile
    secrets:
      AWS_ACCESS_KEY_FROM_GITHUB_SECRETS: ${{ secrets.AWS_AMPLIFIDEV_ACCESS_KEY }}
      AWS_SECRET_KEY_FROM_GITHUB_SECRETS: ${{ secrets.AWS_AMPLIFIDEV_SECRET_KEY }}
      SHEETJS_TOKEN: ${{ secrets.SHEETJS_TOKEN }}

  stage-deployment:
    needs: [stage-build]
    uses: ampmedia/amp-reusable-workflow/.github/workflows/ecs-deployment.yml@v1.3.2
    with:
      environment: staging
      service-name: stage-amp-ws-api
      image-uri: ${{ needs.stage-build.outputs.image-uri }}
      is-green-blue-deployment: false
    secrets:
      AWS_ACCESS_KEY_FROM_GITHUB_SECRETS: ${{ secrets.AWS_AMPLIFIDEV_ACCESS_KEY }}
      AWS_SECRET_KEY_FROM_GITHUB_SECRETS: ${{ secrets.AWS_AMPLIFIDEV_SECRET_KEY }}

  prod-build:
    needs: [stage-build]
    uses: ampmedia/amp-reusable-workflow/.github/workflows/docker-build-and-push.yml@v1.3.2
    with:
      ecr-repo-name: prod-amp-ws
      build-args: |
        NODE_ENV=production
      dockerfile-path: apps/pxm/Dockerfile
    secrets:
      AWS_ACCESS_KEY_FROM_GITHUB_SECRETS: ${{ secrets.AWS_AMPLIFIPROD_ACCESS_KEY }}
      AWS_SECRET_KEY_FROM_GITHUB_SECRETS: ${{ secrets.AWS_AMPLIFIPROD_SECRET_KEY }}
      SHEETJS_TOKEN: ${{ secrets.SHEETJS_TOKEN }}

  prod-deployment:
    needs: [prod-build]
    uses: ampmedia/amp-reusable-workflow/.github/workflows/ecs-deployment.yml@v1.3.2
    with:
      environment: production
      service-name: prod-amp-ws-api
      image-uri: ${{ needs.prod-build.outputs.image-uri }}
      is-green-blue-deployment: false
    secrets:
      AWS_ACCESS_KEY_FROM_GITHUB_SECRETS: ${{ secrets.AWS_AMPLIFIPROD_ACCESS_KEY }}
      AWS_SECRET_KEY_FROM_GITHUB_SECRETS: ${{ secrets.AWS_AMPLIFIPROD_SECRET_KEY }}
