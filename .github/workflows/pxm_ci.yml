name: CI -PXM App

on:
  pull_request:
    branches:
      - main
    paths:
      - 'apps/pxm/**'
      - '*.*'
      - '.copilot/**'
      - '.github/workflows/pxm_ci.yml'
      - '.github/workflows/pxm_app_deployment.yml'
      - '.github/workflows/pxm_storybook_deployment.yml'
      - '.github/workflows/ai_translation_pxm.yml'
  workflow_dispatch:

jobs:
  continuous-integration-checks:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 2

      - name: Read .pnpmversion file
        id: read_pnpm_version
        run: echo "PNPM_VERSION=$(cat .pnpmversion)" >> $GITHUB_ENV

      - uses: pnpm/action-setup@v4
        name: Install pnpm
        with:
          version: ${{ env.PNPM_VERSION }}
          run_install: false

      # Cache node_modules
      - uses: actions/setup-node@v4
        with:
          node-version-file: .nvmrc
          cache: 'pnpm'

      - name: Get pnpm store directory
        shell: bash
        run: |
          echo "STORE_PATH=$(pnpm store path --silent)" >> $GITHUB_ENV

      - uses: actions/cache@v4
        name: Setup pnpm cache
        with:
          path: ${{ env.STORE_PATH }}
          key: ${{ runner.os }}-pnpm-store-${{ hashFiles('**/pnpm-lock.yaml') }}
          restore-keys: |
            ${{ runner.os }}-pnpm-store-

      - name: Install dependencies
        run: pnpm install

      - name: create .npmrc file
        working-directory: apps/pxm
        run: pnpm setup:npmrc
        env:
          SHEETJS_TOKEN: ${{ secrets.SHEETJS_TOKEN }}

      - name: Install app-specific dependencies
        working-directory: apps/pxm
        run: pnpm install

      - name: Lint Check
        run: npx nx run pxm:lint --max-warnings=0

      - name: TypeCheck
        run: npx nx run pxm:type-check

      - uses: nrwl/nx-set-shas@v4

      - name: Prettier Check
        run: npx nx run pxm:prettier-check

      - name: Test
        run: npx nx run pxm:test

      # Commenting these out as they are not needed for the initial setup. We will enable it if needed after RnD
      # - run: npx nx-cloud record -- nx format:check
      # - run: npx nx affected -t lint test build
