name: Deploy - <PERSON><PERSON> App
on:
  push:
    branches: [main]
    paths:
      - 'apps/shelf/**'
      - '*.*'
  pull_request:
    branches: [main]
    paths:
      - 'apps/shelf/**'
      - '*.*'
      - '.copilot/**'
      - '.github/workflows/shelf_app_deployment.yml'
      - '.github/workflows/shelf_storybook_deployment.yml'
      - '.github/workflows/ai_translation.yml'
  workflow_dispatch:

jobs:
  # This job performs commit checks and builds the code that has been committed. testing the CI checks
  continuous-integration-checks:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@v4
        with:
          fetch-depth: 2

      - name: Read .pnpmversion file
        id: read_pnpm_version
        run: echo "PNPM_VERSION=$(cat .pnpmversion)" >> $GITHUB_ENV

      - uses: pnpm/action-setup@v4
        name: Install pnpm
        with:
          version: ${{ env.PNPM_VERSION }}
          run_install: false

      - name: Setup Node
        uses: actions/setup-node@v3
        with:
          node-version-file: .nvmrc
          cache: 'pnpm'

      - name: Get pnpm store directory
        shell: bash
        run: |
          echo "STORE_PATH=$(pnpm store path --silent)" >> $GITHUB_ENV

      - name: Cache pnpm store
        uses: actions/cache@v4
        with:
          path: ${{ env.STORE_PATH }}
          key: ${{ runner.os }}-pnpm-store-${{ hashFiles('**/pnpm-lock.yaml') }}
          restore-keys: |
            ${{ runner.os }}-pnpm-store-

      - name: Install dependencies
        run: |
          pnpm install

      - name: Install shelf-specific dependencies
        working-directory: apps/shelf
        run: |
          pnpm install
          
      - name: Lint Check
        run: npx nx run shelf:lint

      - name: PrettierCheck
        run: npx nx run shelf:prettier-check

      # Create build for staging
      - name: Build shelf app for staging
        env:
          IMPORT_MAP_SUFFIX: '-staging'
        run: npx nx run shelf:"rsbuild:build"

      - name: Archive Build Execution Result
        if: success()
        uses: actions/upload-artifact@v4
        with:
          name: build-artifact
          path: dist/apps/shelf

      - name: Slack Notification
        uses: rtCamp/action-slack-notify@v2
        if: failure()
        env:
          SLACK_CHANNEL: ${{ secrets.SLACK_CHANNEL }}
          SLACK_COLOR: '#f27873'
          SLACK_MESSAGE: Oh no! The build for *pattern-exp/shelf has failed*.
          SLACK_TITLE: Build Message
          SLACK_USERNAME: Github Action
          SLACK_WEBHOOK: ${{ secrets.SLACK_WEBHOOK_URL }}
          SLACK_FOOTER: Github Action

  # Publish modifications that have been committed to the staging environment.
  stage-deployment:
    if: github.ref == 'refs/heads/main' && github.event_name == 'push'
    runs-on: ubuntu-latest
    name: stage-deployment
    needs: [continuous-integration-checks]
    environment: staging
    env:
      AWS_ACCESS_KEY_ID: ${{ secrets.AWS_DEV_ACCESS_KEY }}
      AWS_SECRET_ACCESS_KEY: ${{ secrets.AWS_DEV_SECRET_KEY }}
      AWS_DEFAULT_REGION: us-west-2
      AWS_DEFAULT_OUTPUT: json
      AMPLIFY_APP_ID: d1yoitiwrk86tz
      BRANCH_NAME: main

    steps:
      - name: Deploy
        id: start-job
        run: |
          if [ -z "$COMMIT_ID" ]
          then
            JOB_ID=$(aws amplify start-job --app-id $AMPLIFY_APP_ID --branch-name $BRANCH_NAME --job-type RELEASE | jq -r '.jobSummary.jobId')
          else
            JOB_ID=$(aws amplify start-job --app-id $AMPLIFY_APP_ID --branch-name $BRANCH_NAME --job-type RELEASE --commit-id $COMMIT_ID | jq -r '.jobSummary.jobId')
            echo "build started for commit: $COMMIT_ID"
          fi
          echo "Release started"
          echo "Job ID is $JOB_ID"

          while [[ "$(aws amplify get-job --app-id $AMPLIFY_APP_ID --branch-name $BRANCH_NAME --job-id $JOB_ID | jq -r '.job.summary.status')" =~ ^(PENDING|RUNNING)$ ]]; do sleep 1; done
          JOB_STATUS="$(aws amplify get-job --app-id $AMPLIFY_APP_ID --branch-name $BRANCH_NAME --job-id $JOB_ID | jq -r '.job.summary.status')"
          LOG_URL="$(aws amplify get-job --app-id $AMPLIFY_APP_ID --branch-name $BRANCH_NAME --job-id $JOB_ID | jq -r '.job.steps[] | select(.stepName == "BUILD") | .logUrl')"
          echo "Job finished"
          echo "Job status is $JOB_STATUS"
          echo "Build Log URL is $LOG_URL"
          echo ::set-output name=status::$JOB_STATUS

      # Check if previous job is failed or not
      - name: Check on failures
        if: steps.start-job.outputs.status == 'FAILED'
        run: exit 1

      # Check for the redirection rule file changes
      - name: Checkout
        uses: actions/checkout@v4
        with:
          fetch-depth: 2

      - name: Get changed files
        id: changed-files
        uses: tj-actions/changed-files@v46.0.1
        with:
          files: |
            apps/shelf/amplify_redirection_rules.json

      - name: List all changed files
        if: steps.changed-files.outputs.any_changed == 'true'
        run: |
          for file in ${{ steps.changed-files.outputs.all_changed_files }}; do
            echo "$file was changed"
          done

      # Deploy changed amplify_redirection_rules file into the amplify stage
      - name: Execute shelf-tf GHA workflow to sync custom rules with amplify
        uses: convictional/trigger-workflow-and-wait@v1.3.0
        if: steps.changed-files.outputs.any_changed == 'true'
        with:
          owner: patterninc
          repo: shelf-ui-tf
          github_token: ${{ secrets.GHA_RUNNER_TOKEN }}
          workflow_file_name: terraform-ci-cd-stage.yml
          ref: main

      - name: Slack Notification
        uses: rtCamp/action-slack-notify@v2
        if: failure()
        env:
          SLACK_CHANNEL: ${{ secrets.SLACK_CHANNEL }}
          SLACK_COLOR: '#f27873'
          SLACK_MESSAGE: Oh no! The deployment for *staging pattern-exp/shelf has failed*.
          SLACK_TITLE: Build Message
          SLACK_USERNAME: Github Action
          SLACK_WEBHOOK: ${{ secrets.SLACK_WEBHOOK_URL }}
          SLACK_FOOTER: Github Action

# Publish modifications that have been committed to the production environment.
  prod-deployment:
    if: github.ref == 'refs/heads/main' && github.event_name == 'push'
    runs-on: ubuntu-latest
    name: prod-deployment
    needs: [stage-deployment]
    environment: production
    env:
      AWS_ACCESS_KEY_ID: ${{ secrets.AWS_PROD_ACCESS_KEY }}
      AWS_SECRET_ACCESS_KEY: ${{ secrets.AWS_PROD_SECRET_KEY }}
      AWS_DEFAULT_REGION: us-west-2
      AWS_DEFAULT_OUTPUT: json
      AMPLIFY_APP_ID: d1rm5a12icv5o9
      BRANCH_NAME: main

    steps:
      - name: Deploy
        id: start-job
        run: |
          if [ -z "$COMMIT_ID" ]
          then
            JOB_ID=$(aws amplify start-job --app-id $AMPLIFY_APP_ID --branch-name $BRANCH_NAME --job-type RELEASE | jq -r '.jobSummary.jobId')
          else
            JOB_ID=$(aws amplify start-job --app-id $AMPLIFY_APP_ID --branch-name $BRANCH_NAME --job-type RELEASE --commit-id $COMMIT_ID | jq -r '.jobSummary.jobId')
            echo "build started for commit: $COMMIT_ID"
          fi
          echo "Release started"
          echo "Job ID is $JOB_ID"

          while [[ "$(aws amplify get-job --app-id $AMPLIFY_APP_ID --branch-name $BRANCH_NAME --job-id $JOB_ID | jq -r '.job.summary.status')" =~ ^(PENDING|RUNNING)$ ]]; do sleep 1; done
          JOB_STATUS="$(aws amplify get-job --app-id $AMPLIFY_APP_ID --branch-name $BRANCH_NAME --job-id $JOB_ID | jq -r '.job.summary.status')"
          LOG_URL="$(aws amplify get-job --app-id $AMPLIFY_APP_ID --branch-name $BRANCH_NAME --job-id $JOB_ID | jq -r '.job.steps[] | select(.stepName == "BUILD") | .logUrl')"
          echo "Job finished"
          echo "Job status is $JOB_STATUS"
          echo "Build Log URL is $LOG_URL"
          echo ::set-output name=status::$JOB_STATUS

      # Check if previous job is failed or not
      - name: Check on failures
        if: steps.start-job.outputs.status == 'FAILED'
        run: exit 1

      # Check for the redirection rule file changes
      - name: Checkout
        uses: actions/checkout@v4
        with:
          fetch-depth: 2

      - name: Get changed files
        id: changed-files
        uses: tj-actions/changed-files@v46.0.1
        with:
          files: |
            apps/shelf/amplify_redirection_rules.json

      - name: List all changed files
        if: steps.changed-files.outputs.any_changed == 'true'
        run: |
          for file in ${{ steps.changed-files.outputs.all_changed_files }}; do
            echo "$file was changed"
          done

      # Deploy changed amplify_redirection_rules file into the amplify stage
      - name: Execute shelf-tf GHA workflow to sync custom rules with amplify
        uses: convictional/trigger-workflow-and-wait@v1.3.0
        if: steps.changed-files.outputs.any_changed == 'true'
        with:
          owner: patterninc
          repo: shelf-ui-tf
          github_token: ${{ secrets.GHA_RUNNER_TOKEN }}
          workflow_file_name: terraform-ci-cd-prod.yml
          ref: main

      - name: Slack Notification
        uses: rtCamp/action-slack-notify@v2
        if: failure()
        env:
          SLACK_CHANNEL: ${{ secrets.SLACK_CHANNEL }}
          SLACK_COLOR: '#f27873'
          SLACK_MESSAGE: Oh no! The deployment for *production pattern-exp/shelf has failed*.
          SLACK_TITLE: Build Message
          SLACK_USERNAME: Github Action
          SLACK_WEBHOOK: ${{ secrets.SLACK_WEBHOOK_URL }}
          SLACK_FOOTER: Github Action

      - name: Slack Notification
        uses: rtCamp/action-slack-notify@v2
        if: success()
        env:
          SLACK_CHANNEL: ${{ secrets.SLACK_CHANNEL }}
          SLACK_COLOR: '#46deba'
          SLACK_MESSAGE: Awesome! The deployment for *pattern-exp/shelf was successful*.
          SLACK_TITLE: Build Message
          SLACK_USERNAME: Github Action
          SLACK_WEBHOOK: ${{ secrets.SLACK_WEBHOOK_URL }}
          SLACK_FOOTER: Github Action