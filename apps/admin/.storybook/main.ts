import type { StorybookConfig } from 'storybook-react-rsbuild'
import { pluginSass } from '@rsbuild/plugin-sass'
import { pluginSvgr } from '@rsbuild/plugin-svgr'
import { pluginReact } from '@rsbuild/plugin-react'
const config: StorybookConfig = {
  stories: ['../src/**/*.mdx', '../src/**/*.stories.@(js|jsx|mjs|ts|tsx)'],
  addons: [
    '@storybook/addon-designs',
    '@storybook/addon-links',
    '@storybook/preset-scss',
    '@chromatic-com/storybook',
    '@storybook/addon-docs',
  ],
  managerHead: (head) => `
    ${head}
    <link
      rel="shortcut icon"
      href="/favicon.ico"
      sizes="192x192"
    />
  `,

  framework: {
    name: 'storybook-react-rsbuild',
    options: {},
  },
  rsbuildFinal: (config) => {
    config.plugins = [pluginSass(), pluginSvgr(), pluginReact()]
    if (config.resolve) {
      config.resolve.extensions = [
        '.tsx',
        '.ts',
        '.jsx',
        '.js',
        '.json',
        '.mdx',
      ]
    }
    return config
  },
  typescript: {
    reactDocgen: 'react-docgen-typescript',
    check: true,
  },
}

export default config
