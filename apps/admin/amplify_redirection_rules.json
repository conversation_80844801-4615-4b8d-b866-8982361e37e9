[{"source": "/user-settings/<*>", "status": "200", "target": "https://api.pattern.com/user-settings/<*>", "condition": null}, {"source": "/staging-user-settings/<*>", "status": "200", "target": "https://stage-api.pattern.com/user-settings/<*>", "condition": null}, {"source": "/demo-user-settings/<*>", "status": "200", "target": "https://stage-api.pattern.com/user-settings/<*>", "condition": null}, {"source": "/adminczar/<*>", "status": "200", "target": "https://adminczar.usepredict.com/<*>", "condition": null}, {"source": "/staging-adminczar/<*>", "status": "200", "target": "https://stage-adminczar.usepredict.com/<*>", "condition": null}, {"source": "/toggle/<*>", "status": "200", "target": "https://toggle-api.usepredict.com/<*>", "condition": null}, {"source": "/staging-toggle/<*>", "status": "200", "target": "https://stage-toggle-api.usepredict.com/<*>", "condition": null}, {"source": "/manifest.json", "status": "200", "target": "/manifest.json", "condition": null}, {"source": "/images/<*>", "status": "200", "target": "/images/<*>", "condition": null}, {"source": "/<*>.csv", "status": "200", "target": "/<*>.csv", "condition": null}, {"source": "/robots.txt", "status": "200", "target": "/robots.txt", "condition": null}, {"source": "</^[^.]+$|\\.(?!(css|gif|ico|jpg|js|png|txt|svg|woff|ttf|map|json)$)([^.]+$)/>", "status": "200", "target": "/index.html", "condition": null}, {"source": "/<*>", "status": "404-200", "target": "/index.html", "condition": null}]