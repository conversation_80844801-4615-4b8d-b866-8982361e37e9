{"$schema": "../../node_modules/nx/schemas/project-schema.json", "name": "admin", "projectType": "application", "sourceRoot": "apps/admin/src", "tags": ["scope:ui", "type:lib"], "targets": {"serve-static": {"executor": "@nx/web:file-server", "dependsOn": ["build"], "options": {"buildTarget": "admin:build", "spa": true}}, "get-types": {"command": "pnpm update @patterninc/react-ui"}, "update-s3-test": {"executor": "nx:run-commands", "options": {"command": "node apps/toggle/update-s3.mjs"}}, "prettier": {"executor": "nx:run-commands", "options": {"command": "prettier --write 'apps/admin/src/**/*' --ignore-path apps/admin/.prettierignore || true"}}, "prettier-check": {"executor": "nx:run-commands", "options": {"command": "prettier --check 'apps/admin/src/**/*'"}}, "chromatic": {"executor": "nx:run-commands", "options": {"commands": ["nx run admin:build-storybook", ". .env && pnpm dlx chromatic --project-token=$ADMIN_CHROMATIC_TOKEN --only-changed --storybook-build-dir=apps/admin/storybook-static"], "parallel": false}}}}