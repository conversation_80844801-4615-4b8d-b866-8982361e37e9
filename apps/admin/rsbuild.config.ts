import path from 'path'

import { defineConfig } from '@rsbuild/core'
import { pluginReact } from '@rsbuild/plugin-react'
import { pluginSass } from '@rsbuild/plugin-sass'
import { pluginSvgr } from '@rsbuild/plugin-svgr'
import { pluginAssetsRetry } from '@rsbuild/plugin-assets-retry'
import { pluginTypeCheck } from '@rsbuild/plugin-type-check'
const shouldKeepBrowserClosed = process.env.OPEN_BROWSER === 'false'

export default defineConfig({
  plugins: [
    pluginReact(),
    pluginSvgr({
      svgrOptions: {
        exportType: 'default',
      },
    }),
    pluginSass(),
    pluginAssetsRetry(),
    pluginTypeCheck(),
  ],

  resolve: {
    extensions: ['.tsx', '.ts', '.js', '.jsx', '.json'],
  },

  source: {
    define: {
      'process.env': JSON.stringify(process.env),
    },
  },

  output: {
    distPath: { root: path.resolve(__dirname, '../../dist/apps/admin') },
    cleanDistPath: true,
  },

  server: {
    // https: true, // uncomment this line if you want to use overrides for stage/prod
    port: 9090,
    host: 'admin.localhost',
    historyApiFallback: true,
    open: !shouldKeepBrowserClosed,
    compress: true,
    headers: { 'Access-Control-Allow-Origin': '*' },
    proxy: [
      // PRODUCTION
      {
        context: ['/user-settings'],
        target: 'https://api.pattern.com/user-settings',
        pathRewrite: { '^/user-settings': '' },
        changeOrigin: true,
        secure: false,
      },
      {
        context: ['/toggle'],
        target: 'https://toggle-api.usepredict.com',
        pathRewrite: { '^/toggle': '' },
        changeOrigin: true,
        secure: false,
      },
      {
        context: ['/adminczar'],
        target: 'https://adminczar.pattern.com',
        pathRewrite: { '^/adminczar': '' },
        changeOrigin: true,
        secure: false,
      },
      // STAGING
      {
        context: ['/staging-user-settings'],
        target: 'https://stage-api.pattern.com/user-settings',
        pathRewrite: { '^/staging-user-settings': '' },
        changeOrigin: true,
        secure: false,
      },
      {
        context: ['/staging-toggle'],
        target: 'https://stage-toggle-api.usepredict.com',
        pathRewrite: { '^/staging-toggle': '' },
        changeOrigin: true,
        secure: false,
      },
      {
        context: ['/staging-adminczar'],
        target: 'https://stage-adminczar.usepredict.com',
        pathRewrite: { '^/staging-adminczar': '' },
        changeOrigin: true,
        secure: false,
      },
    ],
  },
  tools: {
    htmlPlugin: {
      title: 'Admin',
    },
  },
})
