import {
  APP_LOGOS,
  DevTools,
  getEnvironmentName,
  PatternToastContainer,
  ToggleProvider,
} from '@patterninc/react-ui'
import React, { useState } from 'react'
import { BrowserRouter, Outlet, Route, Routes } from 'react-router-dom'

import ScreenLoader from './common/components/ScreenLoader/ScreenLoader'
import './common/scss/main.scss'
import { useUser } from './context/user-context'
import SignUpRoutes from './modules/SignUp/SignUpRoutes'

export const nonAutheticateRoute = [
  '/signup',
  '/signup/completed',
  '/signup/invitation',
  '/signup/change-password',
]

const AuthenticatedApp = React.lazy(
  () => import(/* webpackChunkName: "authenticatedApp" */ './AuthenticatedApp'),
)
const UnauthenticatedApp = React.lazy(
  () =>
    import(/* webpackChunkName: "unauthenticatedApp" */ './UnauthenticatedApp'),
)

const ADMIN_DISTRIBUTION_KEY = 'c10ec850-c39b-47e2-a52a-fdb012fe388a'

export function getCurrentEnv(): 'development' | 'production' | 'staging' {
  const environmentName = getEnvironmentName()
  if (environmentName === 'demo' || environmentName === 'stage') {
    return 'staging'
  }
  return environmentName
}

export default function App(): React.JSX.Element {
  const user = useUser(),
    [, setRerender] = useState({}),
    [areTogglesLoaded, setAreTogglesLoaded] = useState(false),
    loader = <ScreenLoader logo={APP_LOGOS.ADMIN.logo} />

  return (
    <BrowserRouter
      future={{
        v7_relativeSplatPath: true,
        v7_startTransition: true,
      }}
    >
      <ToggleProvider
        distributionKey={ADMIN_DISTRIBUTION_KEY}
        environment={getCurrentEnv()}
        finishedLoadingCallback={setAreTogglesLoaded}
      >
        {!areTogglesLoaded ? (
          loader
        ) : (
          <React.Suspense fallback={loader}>
            {nonAutheticateRoute.includes(window.location.pathname) ? (
              <Routes>
                <Route path='/' element={<Outlet />}>
                  <Route path='signup/*' element={<SignUpRoutes />}></Route>
                </Route>
              </Routes>
            ) : user ? (
              <AuthenticatedApp />
            ) : (
              <UnauthenticatedApp />
            )}
          </React.Suspense>
        )}
        <DevTools backendNames={['adminczar']} rerenderCallout={setRerender} />
        <PatternToastContainer />
      </ToggleProvider>
    </BrowserRouter>
  )
}
