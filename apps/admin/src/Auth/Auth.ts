import auth0, {
  type Auth0Result,
  type Auth0UserProfile,
  type AuthOptions,
} from 'auth0-js'

import { nonAutheticateRoute } from '../App'
import { AUTH_CONFIG } from './auth0-variables'

type GenericFunction = () => void
type User = Auth0UserProfile

interface Auth0Response extends Auth0Result {
  idTokenPayload?: {
    /** actual expiration time (in seconds) */
    exp: number
  }
}

const ACCESS_TOKEN_KEY = 'access_token'
const ID_TOKEN_KEY = 'id_token'
const EXPIRES_AT_KEY = 'expires_at'
export const USER_KEY = 'user'
const REDIRECT_URL_KEY = 'redirect_url'
export const ORGANIZATION_KEY = 'organization'
export const ADMIN_APP_KEY = 'admin_app'
export const ORGANIZATIONS_KEY = 'organizations'
export const APP_KEY = 'app'
export default class Auth {
  accessToken = localStorage.getItem(ACCESS_TOKEN_KEY)
  idToken = localStorage.getItem(ID_TOKEN_KEY)
  expiresAt = localStorage.getItem(EXPIRES_AT_KEY)
  user: User | null = null
  organization = localStorage.getItem(ORGANIZATION_KEY)
  adminApp = localStorage.getItem(ADMIN_APP_KEY)
  organizations = localStorage.getItem(ORGANIZATIONS_KEY)

  auth0 = new auth0.WebAuth({
    domain: AUTH_CONFIG.domain,
    clientID: AUTH_CONFIG.clientId,
    redirectUri: `${window.location.protocol}//${window.location.host}${AUTH_CONFIG.callbackUrl}`,
    responseType: 'token id_token',
    scope: 'openid profile email',
  } as AuthOptions)

  constructor() {
    this.login = this.login.bind(this)
    this.logout = this.logout.bind(this)
    this.handleAuthentication = this.handleAuthentication.bind(this)
    this.isAuthenticated = this.isAuthenticated.bind(this)
    this.getAccessToken = this.getAccessToken.bind(this)
    this.getIdToken = this.getIdToken.bind(this)
    this.getUser = this.getUser.bind(this)
    this.user = null
  }

  login(): void {
    const pathname = nonAutheticateRoute.includes(window.location.pathname)
        ? '/'
        : window.location.pathname,
      search = window.location.search
    localStorage.setItem(REDIRECT_URL_KEY, `${pathname}${search}`)
    this.auth0.authorize()
  }

  handleAuthentication(callback: GenericFunction): void {
    this.auth0.parseHash((err, authResult) => {
      if (authResult && authResult.accessToken && authResult.idToken) {
        this.setSession(authResult)
        // get the user now
        this.setupUser(callback)
      } else if (err) {
        this.auth0.authorize()
      }
    })
  }

  getAccessToken(): string {
    return (this.accessToken || localStorage.getItem(ACCESS_TOKEN_KEY)) ?? ''
  }

  getIdToken(): string | null {
    return this.idToken || localStorage.getItem(ID_TOKEN_KEY)
  }

  setSession(authResult: Auth0Response): void {
    this.expiresAt = ((authResult?.idTokenPayload?.exp ?? 0) * 1000).toString()
    this.accessToken = authResult.accessToken ?? ''
    this.idToken = authResult.idToken ?? ''

    localStorage.setItem(ACCESS_TOKEN_KEY, this.accessToken ?? '')
    localStorage.setItem(ID_TOKEN_KEY, this.idToken ?? '')
    localStorage.setItem(EXPIRES_AT_KEY, this.expiresAt)
  }

  setupUser(callback: GenericFunction): void {
    this.auth0.client.userInfo(this.getAccessToken(), (err, user) => {
      if (err) {
        console.log(err)
      } else {
        localStorage.setItem(USER_KEY, JSON.stringify(user))
        this.user = user
        callback?.()
      }
    })
  }

  getUser(): User | null {
    const localUser = JSON.parse(localStorage.getItem(USER_KEY) ?? '')
    if (!this.user && localUser) this.user = localUser
    return this.user
  }

  logout(): void {
    // Remove tokens and expiry time
    this.accessToken = null
    this.idToken = null
    this.expiresAt = '0'
    localStorage.removeItem(ACCESS_TOKEN_KEY)
    localStorage.removeItem(ID_TOKEN_KEY)
    localStorage.removeItem(EXPIRES_AT_KEY)
    localStorage.removeItem(ORGANIZATION_KEY)
    localStorage.removeItem(ADMIN_APP_KEY)
    localStorage.removeItem(ORGANIZATIONS_KEY)
    localStorage.clear()
    this.user = null
    this.auth0.logout({
      returnTo: `${window.location.protocol}//${window.location.host}/`,
      clientID: AUTH_CONFIG.clientId,
    })
  }

  authenticationValid(): boolean {
    // Check whether the current time is past the
    // access token's expiry time
    return (
      new Date().getTime() < Number(localStorage.getItem(EXPIRES_AT_KEY)) &&
      !!localStorage.getItem(USER_KEY)
    )
  }

  isAuthenticated(): User | boolean | null {
    return this.authenticationValid() ? this.getUser() : false
  }
}

export { REDIRECT_URL_KEY }
