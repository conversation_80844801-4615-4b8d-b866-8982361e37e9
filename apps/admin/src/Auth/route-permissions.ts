export const routePermissions: Record<string, string[]> = {}

export const ADMIN_PRIVILEGES = {
  read_user: 'admin:read_users',
  write_user: 'admin:write_users',
  create_users: 'admin:create_users',
  manage_users: 'admin:manage_users',
  read_orgunit: 'admin:read_org_units',
  write_orgunit: 'admin:write_org_units',
  read_roles: 'admin:read_roles',
  write_roles: 'admin:write_roles',
  read_privileges: 'admin:read_privileges',
  write_privileges: 'admin:write_privileges',
  edit_privilege_code: 'admin:edit_privilege_code',
  read_history: 'admin:read_history',
  read_global_users: 'admin:read_global_users',
  write_global_users: 'admin:write_global_users',
  read_orgs: 'admin:read_orgs',
  write_orgs: 'admin:write_orgs',
  read_app_settings: 'admin:read_app_settings',
  write_app_settings: 'admin:write_app_settings',
  view_usage_metrics: 'admin:view_usage_metrics',
}

export const ROUTE_PRIVILEGES = {
  users: [
    ADMIN_PRIVILEGES.read_user,
    ADMIN_PRIVILEGES.write_user,
    ADMIN_PRIVILEGES.create_users,
    ADMIN_PRIVILEGES.manage_users,
  ],
  create_users: [ADMIN_PRIVILEGES.create_users],
  manage_users: [ADMIN_PRIVILEGES.manage_users],
  org_units: [ADMIN_PRIVILEGES.read_orgunit, ADMIN_PRIVILEGES.write_orgunit],
  roles: [ADMIN_PRIVILEGES.read_roles, ADMIN_PRIVILEGES.write_roles],
  privileges: [
    ADMIN_PRIVILEGES.read_privileges,
    ADMIN_PRIVILEGES.write_privileges,
  ],
  history: [ADMIN_PRIVILEGES.read_history],
  privileges_edit_code: [ADMIN_PRIVILEGES.edit_privilege_code],
  global_users: [
    ADMIN_PRIVILEGES.read_global_users,
    ADMIN_PRIVILEGES.write_global_users,
  ],
  orgs: [ADMIN_PRIVILEGES.read_orgs, ADMIN_PRIVILEGES.write_orgs],
  apps: [
    ADMIN_PRIVILEGES.read_app_settings,
    ADMIN_PRIVILEGES.write_app_settings,
  ],
  dashboard: [ADMIN_PRIVILEGES.view_usage_metrics],
}

export const hasRoutePermissions = (
  route: keyof typeof ROUTE_PRIVILEGES | 'no-access',
  privileges: string[],
): boolean => {
  if (route === 'no-access') {
    return true // Allow access to 'no-access' page without any privilege check
  }
  const requiredPrivileges = ROUTE_PRIVILEGES[route]
  return requiredPrivileges.some((privilege) => privileges.includes(privilege))
}

export const getRedirectRoute = (privileges: string[]): string => {
  for (const route in ROUTE_PRIVILEGES) {
    if (
      hasRoutePermissions(route as keyof typeof ROUTE_PRIVILEGES, privileges)
    ) {
      return `/${route}`
    }
  }
  return '/no_access'
}

export const WRITE_PRIVILEGES = {
  users: [ADMIN_PRIVILEGES.write_user],
  create_users: [ADMIN_PRIVILEGES.create_users],
  manage_users: [ADMIN_PRIVILEGES.manage_users],
  org_units: [ADMIN_PRIVILEGES.write_orgunit],
  roles: [ADMIN_PRIVILEGES.write_roles],
  privileges: [ADMIN_PRIVILEGES.write_privileges],
  privileges_edit_code: [ADMIN_PRIVILEGES.edit_privilege_code],
  history: [],
  global_users: [ADMIN_PRIVILEGES.write_global_users],
  orgs: [ADMIN_PRIVILEGES.write_orgs],
  apps: [ADMIN_PRIVILEGES.write_app_settings],
  dashboard: [],
}

export const hasWritePermission = (
  route: keyof typeof ROUTE_PRIVILEGES,
  userPrivileges: string[],
): boolean => {
  const requiredWritePrivileges = WRITE_PRIVILEGES[route]
  return requiredWritePrivileges.some((writePrivilege) =>
    userPrivileges.includes(writePrivilege),
  )
}
