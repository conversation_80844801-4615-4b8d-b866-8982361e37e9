import React from 'react'
import { useRef } from 'react'

import { useAuth } from './context/auth-context'
import { ThemeProvider } from './Context'
import Main from './common/components/Main'
import ScrollToTop from './common/ScrollToTop'
import { initDataDogRUM } from './common/services/Datadog'

function AuthenticatedApp(): React.JSX.Element {
  const isDDInitialized = useRef(false)
  const { isAuthenticated } = useAuth()
  if (!isDDInitialized.current && isAuthenticated()) {
    initDataDogRUM()
    isDDInitialized.current = true
  }

  return (
    <ThemeProvider>
      <ScrollToTop>
        <Main />
      </ScrollToTop>
    </ThemeProvider>
  )
}

export default AuthenticatedApp
