import {
  addNewBreadcrumb,
  breadcrumbNavigation,
  type BreadcrumbType,
  getApiUrlPrefix,
  standardSortParams,
} from '@patterninc/react-ui'
import React, {
  createContext,
  useCallback,
  useEffect,
  useMemo,
  useState,
} from 'react'
import { useQuery } from '@tanstack/react-query'
import { groupBy } from 'lodash'
import { useSearchParams } from 'react-router-dom'

import {
  ADMIN_APP_KEY,
  APP_KEY,
  ORGANIZATION_KEY,
  ORGANIZATIONS_KEY,
} from './Auth/Auth'
import SecureAxios from './common/services/SecureAxios'
import { tryLocalStorageParse } from './common/services/HelperService'
import { useAuth } from './context/auth-context'

type ThemeContextType = {
  breadcrumbs: Array<BreadcrumbType>
  updateBreadcrumbs: (breadcrumb: BreadcrumbType) => void
  breadcrumbCallout: (breadcrumb: BreadcrumbType) => void
  organization: OrgObjectType
  updateOrganization: (organizationToUpdate: OrgObjectType) => void
  adminApp: AppWithMetadataType
  updateAdminApp: (adminAppToUpdate: AppWithMetadataType) => void
  organizations: Array<OrgObjectType>
  orgUnits: Array<ObjectType>
  updateApp: (appToUpdate: AppWithMetadataType) => void
  apps: Array<AppWithMetadataType>
  app: AppWithMetadataType
  appBasedOrganization: OrgObjectType
  appBasedOrganizations: Array<OrgObjectType>
  privileges: Array<string>
  arePrivilegesLoaded: boolean
}

export type ObjectType = {
  id: string
  name: string
  code?: string
}

type OrgObjectType = {
  id: string
  name: string
  code: string
}

type AppWithMetadataType = {
  id: string
  name: string
  metadata?: AppMetadata
}

type AppMetadata = {
  require_org_unit_selection: boolean
  hide_access_to_all_org_units: boolean
  hide_select_all_org_units: boolean
}

type AppOrgs = {
  app: { id: string; name: string }
  id: number
  org: OrgObjectType
  privileges: string[]
}

type UserInfo = {
  status: string
  apps_orgs: AppOrgs[]
}

export const ThemeContext = createContext<ThemeContextType>({
  breadcrumbs: [
    {
      name: '',
      link: '',
    },
  ],
  updateBreadcrumbs: () => null,
  breadcrumbCallout: () => null,
  organization: { id: '', name: '', code: '' },
  updateOrganization: () => null,
  adminApp: { id: '', name: '' },
  updateAdminApp: () => null,
  organizations: [{ id: '', name: '', code: '' }],
  orgUnits: [{ id: '', name: '' }],
  updateApp: () => null,
  apps: [{ id: '', name: '' }],
  app: { id: '', name: '' },
  appBasedOrganization: { id: '', name: '', code: '' },
  appBasedOrganizations: [{ id: '', name: '', code: '' }],
  privileges: [],
  arePrivilegesLoaded: false,
})
const { Provider } = ThemeContext

export const ThemeConsumer = ThemeContext.Consumer

type ThemeProviderProps = {
  children: React.JSX.Element
}

const defaultOrg = tryLocalStorageParse('organization')
const defaultApp = tryLocalStorageParse('app')
const parsedBreadcrumbs = tryLocalStorageParse('breadcrumbs')
const breadcrumbsData: BreadcrumbType[] = Array.isArray(parsedBreadcrumbs)
  ? parsedBreadcrumbs
  : []

const defaultValue: OrgObjectType = defaultOrg
  ? {
      id: defaultOrg?.id as string,
      name: defaultOrg?.name as string,
      code: defaultOrg?.code as string,
    }
  : { id: '', name: '', code: '' }

const defaultAppValue: ObjectType = defaultApp
  ? { id: defaultApp?.id as string, name: defaultApp?.name as string }
  : { id: '', name: '' }

const ThemeProvider = ({ children }: ThemeProviderProps): React.JSX.Element => {
  const [organization, setOrganization] = useState<OrgObjectType>(defaultValue),
    [organizations, setOrganizations] = useState<OrgObjectType[]>([]),
    [adminApp, setAdminApp] = useState<ObjectType>(defaultValue),
    [apps, setApps] = useState<ObjectType[]>([]),
    [appBasedOrganization, setAppBasedOrganization] =
      useState<OrgObjectType>(defaultValue),
    [appBasedOrganizations, setAppBasedOrganizations] = useState<
      OrgObjectType[]
    >([]),
    [orgUnits, setOrgUnits] = useState<ObjectType[]>([]),
    [breadcrumbs, setBreadcrumbs] = useState<BreadcrumbType[]>(breadcrumbsData),
    [app, setApp] = useState<ObjectType>(defaultAppValue),
    [privileges, setPrivileges] = useState<string[]>([]),
    [arePrivilegesLoaded, setArePrivilegesLoaded] = useState(false),
    { logout } = useAuth()

  const updateBreadcrumbs = useCallback(
    (breadcrumb: BreadcrumbType, replace?: boolean) => {
      setBreadcrumbs((prevState) => {
        const breadcrumbsToUpdate = replace
          ? [...prevState].slice(0, -1)
          : [...prevState]
        const newBreadcrumb = addNewBreadcrumb({
          breadcrumb,
          breadcrumbs: breadcrumbsToUpdate,
        })

        localStorage.setItem('breadcrumbs', JSON.stringify(newBreadcrumb))
        return newBreadcrumb
      })
    },
    [],
  )

  const breadcrumbCallout = useCallback((breadcrumb: BreadcrumbType) => {
    setBreadcrumbs((prevState) => {
      const newBreadcrumbs = breadcrumbNavigation({
        breadcrumb,
        breadcrumbs: prevState,
      })
      localStorage.setItem('breadcrumbs', JSON.stringify(newBreadcrumbs))
      return newBreadcrumbs
    })
  }, [])

  const [searchParams] = useSearchParams()
  const [isOrgAndAppSet, setIsOrgAndAppSet] = useState('loading')
  useEffect(() => {
    const org = searchParams.get('org')
    const app = searchParams.get('app')
    if (org && app) {
      localStorage.setItem('organization_name', org)
      localStorage.setItem('app', app)
      setIsOrgAndAppSet('success')
    } else {
      setIsOrgAndAppSet('notFound')
    }
  }, [searchParams])

  const updatePrivileges = useCallback(
    (appId: string, organizationId: string) => {
      const userInfo: UserInfo | null = tryLocalStorageParse(
        'current_user',
      ) as UserInfo

      let privileges: string[] = []
      if (userInfo && appId && organizationId) {
        const selectedApp = userInfo.apps_orgs.find(
          (app_org) =>
            appId === app_org.app.id && organizationId === app_org.org.id,
        )
        privileges = selectedApp?.privileges || []
      } else {
        privileges = []
      }
      setPrivileges(privileges)
    },
    [],
  )

  const checkOrgAndAppId = () => {
    const userInfo: UserInfo | null = tryLocalStorageParse(
      'current_user',
    ) as UserInfo
    if (!userInfo) return true
    if (userInfo && app?.id && organization?.id) {
      const appOrg = userInfo.apps_orgs.find(
        (app_org) =>
          app.id === app_org.app.id && organization.id === app_org.org.id,
      )
      return !!appOrg
    }
    return false
  }

  const updateOrganization = useCallback(
    (organizationToUpdate: OrgObjectType) => {
      setOrganization(organizationToUpdate)
      updatePrivileges(app?.id, organizationToUpdate?.id)
      localStorage.setItem(
        ORGANIZATION_KEY,
        JSON.stringify(organizationToUpdate),
      )
      localStorage.setItem('organization_name', organizationToUpdate?.name)
    },
    [app.id, updatePrivileges],
  )

  const updateAdminApp = useCallback((adminAppToUpdate: ObjectType) => {
    setAdminApp(adminAppToUpdate)
    localStorage.setItem(ADMIN_APP_KEY, JSON.stringify(adminAppToUpdate))
  }, [])

  const updateApp = useCallback(
    (appToUpdate: ObjectType) => {
      setApp(appToUpdate)
      updatePrivileges(appToUpdate?.id, organization?.id)
      setArePrivilegesLoaded(false)
      localStorage.setItem(APP_KEY, JSON.stringify(appToUpdate))
    },
    [organization.id, updatePrivileges],
  )
  //// APP API ////
  const appApiUrl = `${getApiUrlPrefix('adminczar')}/api/v1/apps`,
    { isLoading: appIsLoading, status: appStatus } = useQuery({
      queryKey: [appApiUrl],
      queryFn: async ({ signal }) => {
        const response = await SecureAxios.get(appApiUrl, {
          signal,
        })
        const appList = response.data.data.map((app: AppWithMetadataType) => ({
          id: app.id,
          name: app.name,
          metadata: app?.metadata,
        }))

        if (appList.length === 0) {
          logout()
        } else {
          setApps(appList)
          let localApp: string | null | { id: string; name: string } =
            localStorage.getItem('app')
          if (isOrgAndAppSet !== 'success' && localApp) {
            try {
              localApp = JSON.parse(localApp)
              // eslint-disable-next-line @typescript-eslint/no-unused-vars
            } catch (error) {
              /* Do nothing */
            }
          }
          const localAssignedAppName =
            typeof localApp === 'object'
              ? localApp?.name?.toLowerCase()
              : localApp?.toLowerCase()
          if (!localApp) {
            updateApp(appList?.[0])
          } else {
            const appToSet = appList?.find(
              (app: AppWithMetadataType) =>
                app.name.toLowerCase() === localAssignedAppName,
            )
            if (appToSet) {
              updateApp(appToSet)
            } else {
              updateApp(appList?.[0])
            }
          }
        }
        return apps
      },

      refetchOnWindowFocus: false,
      retry: false,
      gcTime: 1000 * 60 * 60 * 8,
      enabled: isOrgAndAppSet !== 'loading',
    })

  //// CURRENT_USER_INFO API /////

  const apiUrl = `${getApiUrlPrefix(
      'adminczar',
    )}/api/v1/users/current_user_info`,
    { isLoading } = useQuery({
      queryKey: [apiUrl],
      queryFn: async ({ signal }) => {
        const response = await SecureAxios.get(apiUrl, {
          signal,
        })
        updateOrgAndApp(response)
        localStorage.setItem('current_user', JSON.stringify(response?.data))
        return response
      },

      refetchOnWindowFocus: false,
      retry: false,
      gcTime: 1000 * 60 * 60 * 8,
      enabled:
        isOrgAndAppSet !== 'loading' && appStatus === 'success' && !!app.id,
    })

  //// ORGS API ////
  const orgApi = `${appApiUrl}/${app.id}/orgs`,
    { status: orgApiStatus } = useQuery({
      queryKey: [orgApi, app.id],
      queryFn: async ({ signal }) => {
        const response = await SecureAxios.get(orgApi, {
          signal,
        })
        const apps = response.data.data.map((org: ObjectType) => ({
          id: org.id,
          name: org.name,
          code: org.code,
        }))
        setAppBasedOrganizations(apps)
        setAppBasedOrganization(apps?.[0])
        updateOrganizationWithOrgParam(apps)
        return response
      },
      refetchOnWindowFocus: false,
      retry: false,
      gcTime: 1000 * 60 * 60 * 8,
      enabled: !!app?.id && appStatus === 'success',
    })

  //// ORG UNIT API ////
  const orgUnitApiUrl = `${getApiUrlPrefix('adminczar')}/api/v1/apps/${
      app?.id ?? adminApp.id
    }/orgs/${organization.id}/org_units`,
    { data: orgUnitsResponse, status: orgUnitApiStatus } = useQuery({
      queryKey: ['all-org-units', orgUnitApiUrl, organization.id],
      queryFn: async ({ signal }) => {
        return await SecureAxios.get(orgUnitApiUrl, {
          signal,
          params: {
            sort: standardSortParams({
              prop: 'name',
              flip: true,
            }),
          },
        })
      },
      refetchOnWindowFocus: false,
      retry: false,
      gcTime: 1000 * 60 * 60 * 8,
      enabled: orgApiStatus === 'success' && checkOrgAndAppId,
    })

  const orgUnitsData = useMemo(
    () =>
      orgUnitApiStatus === 'success'
        ? orgUnitsResponse.data.data.map(
            (orgUnit: { id: string; name: string }) => ({
              id: orgUnit.id,
              name: orgUnit.name,
            }),
          )
        : [],
    [orgUnitApiStatus, orgUnitsResponse?.data?.data],
  )

  useEffect(() => {
    if (orgUnitsData) setOrgUnits(orgUnitsData)
  }, [orgUnitsData])

  const updateOrgAndApp = useCallback(
    (response: { data: UserInfo }) => {
      const userInfo: UserInfo = response?.data
      const isAppActive: boolean = userInfo?.status === 'active'
      if (isAppActive) {
        const localOrg = localStorage.getItem('organization_name')
        let defaultApp
        if (localOrg && app?.id) {
          defaultApp = userInfo.apps_orgs.find(
            (apps) =>
              apps.org.name.toLowerCase() === localOrg.toLowerCase() &&
              apps.app.id === app?.id,
          )
        } else if (localOrg) {
          defaultApp = userInfo.apps_orgs.find(
            (apps) => apps.org.name.toLowerCase() === localOrg.toLowerCase(),
          )
        } else if (app?.id) {
          defaultApp = userInfo.apps_orgs.find((apps) => apps.app.id === app.id)
        }

        // Fallback to an app with privileges if defaultApp is not found or lacks privileges
        if (!defaultApp || defaultApp?.privileges?.length === 0) {
          defaultApp = userInfo.apps_orgs.find(
            (app) => app.privileges.length > 0,
          )
        }
        if (defaultApp) {
          updateApp(defaultApp.app)
          setPrivileges(defaultApp.privileges || [])
        }
        setCurrentOrganization(defaultApp as AppOrgs)
        setCurrentAdminApp(defaultApp as AppOrgs)
        setCurrentAdminOrganizations(userInfo, defaultApp as AppOrgs)
      }
    },
    [app.id, updateApp],
  )

  const setCurrentAdminOrganizations = (
    userInfo: UserInfo,
    defaultApp: AppOrgs,
  ) => {
    const groupByOrganizations = groupBy(userInfo.apps_orgs, 'app.name')
    const associatedOrganizations =
      groupByOrganizations[defaultApp.app.name] || []
    const organizations = associatedOrganizations.map(
      (org) => org.org as OrgObjectType,
    )

    setOrganizations(organizations)
    localStorage.setItem(ORGANIZATIONS_KEY, JSON.stringify(organizations))
  }

  const setCurrentOrganization = (appInfo: AppOrgs) => {
    // Make sure org object has all needed properties including code
    const org: OrgObjectType = {
      id: appInfo.org.id,
      name: appInfo.org.name,
      code: appInfo.org.code,
    }

    localStorage.setItem(ORGANIZATION_KEY, JSON.stringify(org))
    localStorage.setItem('organization_name', appInfo.org?.name)
    setOrganization(org)
  }

  const setCurrentAdminApp = (appInfo: AppOrgs) => {
    localStorage.setItem(ADMIN_APP_KEY, JSON.stringify(appInfo.app))
    setAdminApp(appInfo.app)
  }

  const updateOrganizationWithOrgParam = (apps: OrgObjectType[]) => {
    const orgName = localStorage.getItem('organization_name')
    const organizationToUpdate = apps.find(
      (org) => org.name.toLowerCase() === orgName?.toLowerCase(),
    )
    if (organizationToUpdate) updateOrganization(organizationToUpdate)
    else updateOrganization(apps?.[0])
  }

  useEffect(() => {
    if (!isLoading && privileges.length > 0) {
      setArePrivilegesLoaded(true)
    }
  }, [isLoading, privileges.length])

  return (
    <>
      {appIsLoading
        ? !isLoading && !appIsLoading
        : !isLoading && (
            <Provider
              value={{
                breadcrumbs,
                updateBreadcrumbs,
                breadcrumbCallout,
                organization,
                updateOrganization,
                adminApp,
                updateAdminApp,
                organizations,
                orgUnits,
                updateApp,
                apps,
                app,
                appBasedOrganization,
                appBasedOrganizations,
                privileges,
                arePrivilegesLoaded,
              }}
            >
              {children}
            </Provider>
          )}
    </>
  )
}

export { ThemeProvider }
