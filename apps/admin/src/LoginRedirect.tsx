import React, { useEffect } from 'react'
import { useNavigate } from 'react-router-dom'

import { REDIRECT_URL_KEY } from './Auth/Auth'

const LoginRedirect = (): React.JSX.Element => {
  const redirectUrl = localStorage.getItem(REDIRECT_URL_KEY),
    navigate = useNavigate()

  useEffect(() => {
    navigate(redirectUrl ?? '/')
    localStorage.removeItem(REDIRECT_URL_KEY)
  }, [navigate, redirectUrl])

  return <div />
}

export default LoginRedirect
