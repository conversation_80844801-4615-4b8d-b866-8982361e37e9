import { Alert, APP_LOGOS, Button } from '@patterninc/react-ui'
import React, { useEffect, useState } from 'react'

import ScreenLoader from './common/components/ScreenLoader/ScreenLoader'
import { useAuth } from './context/auth-context'

const REDIRECT_URL_KEY = 'redirect_url'

const UnauthenticatedApp: React.FC = () => {
  const {
    login,
    logout,
    handleAuthentication,
    setUser,
    isAuthenticated,
    getUser,
  } = useAuth()

  const pathname = window.location.pathname
  const hash = window.location.hash
  const [showErrorPage, setShowErrorPage] = useState<boolean>(false)
  const pageUrl = new URL(window.location.href.replace(/#/g, '?'))
  const error = pageUrl.searchParams.get('error')
  const error_description = pageUrl.searchParams.get('error_description')

  useEffect(() => {
    if (
      pathname === '/authenticate' &&
      /Unauthorized|access_denied/.test(hash)
    ) {
      setShowErrorPage(true)
    } else {
      setShowErrorPage(false)
      if (
        pathname === '/authenticate' &&
        /access_token|id_token|error/.test(hash)
      ) {
        if (isAuthenticated()) {
          const redirectUrl = localStorage.getItem(REDIRECT_URL_KEY) || ''
          window.location.replace(`${window.location.origin}${redirectUrl}`)
        } else {
          handleAuthentication(() => setUser(getUser()))
        }
      } else {
        if (isAuthenticated()) {
          setUser(getUser())
        } else {
          login()
        }
      }
    }
  }, [
    pathname,
    hash,
    isAuthenticated,
    handleAuthentication,
    setUser,
    getUser,
    login,
  ])

  return (
    <div className='unauthenticated-screen'>
      {showErrorPage ? (
        <ScreenLoader logo={APP_LOGOS.ADMIN.logo}>
          <Alert type='error' text={`Error Type : ${error}`} />
          <Alert
            type='info'
            text={`Error Description : ${error_description}`}
          />
          <Button styleType='primary-blue' onClick={() => logout()}>
            Login
          </Button>
        </ScreenLoader>
      ) : (
        <ScreenLoader logo={APP_LOGOS.ADMIN.logo} />
      )}
    </div>
  )
}

export default UnauthenticatedApp
