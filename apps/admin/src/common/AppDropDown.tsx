import React, { useContext, useEffect, useState } from 'react'
import { DrawerSelect } from '@patterninc/react-ui'

import { ThemeContext } from '../Context'
import { c } from './services/TranslationService'

const AppDropDown = (): React.JSX.Element => {
  const { updateApp, apps, app: currentApp } = useContext(ThemeContext)
  const [selectedApp, setSelectedApp] = useState<{
    id: string
    name: string
  }>(currentApp)
  const onOrgChange = (app: { id: string; name: string }) => {
    setSelectedApp(app)
    updateApp(app)
  }

  useEffect(() => {
    if (currentApp) {
      setSelectedApp(currentApp)
    }
  }, [currentApp])

  return (
    <DrawerSelect
      header={c('applications')}
      options={apps.map((app) => ({
        ...app,
        label: app.name,
      }))}
      setActive={(value) => {
        onOrgChange(value)
      }}
      active={{
        ...selectedApp,
        label: selectedApp.name,
      }}
      placeholder={apps?.[0]?.name}
      disabled={apps?.length === 1}
    />
  )
}

export default AppDropDown
