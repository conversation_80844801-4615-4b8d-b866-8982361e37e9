import React, { useContext, useEffect, useMemo, useState } from 'react'
import { useLocation } from 'react-router-dom'
import { Select, useIsMobileView } from '@patterninc/react-ui'

import { ThemeContext } from '../Context'
import styles from './orgDropdown.module.scss'
import { type FilterAction } from '../modules/Users/<USER>'

type UserProps = {
  userDispatch?: React.Dispatch<FilterAction>
}

const OrganizationDropDownNew = ({
  userDispatch,
}: UserProps): React.JSX.Element => {
  const {
    appBasedOrganizations,
    organization: currentOrganization,
    updateOrganization,
  } = useContext(ThemeContext)

  const [selectedOrganization, setSelectedOrganization] = useState<{
    id: string
    name: string
    code: string
  }>(currentOrganization)

  const location = useLocation(),
    isRegionsPage = useMemo(
      () => location.pathname.includes('regions'),
      [location],
    ),
    isMobileView = useIsMobileView()

  const onOrgChange = (organization: {
    id: string
    name: string
    code: string
  }) => {
    setSelectedOrganization(organization)
    updateOrganization(organization)
    if (userDispatch) userDispatch({ type: 'RESET_FILTER' })
  }

  useEffect(() => {
    if (currentOrganization) {
      setSelectedOrganization(currentOrganization)
    }
  }, [currentOrganization])

  return (
    <div
      className={`${isMobileView && 'pat-mb-4'} ${isRegionsPage ? styles.regionsOrgDropdownNew : styles.orgDropdownNew}`}
    >
      <div className={styles.orgDropdown}>
        <Select
          options={appBasedOrganizations}
          optionKeyName={'name'}
          labelKeyName={'name'}
          selectedItem={selectedOrganization}
          onChange={(value) => {
            if (value) {
              onOrgChange(value)
            }
          }}
        />
      </div>
    </div>
  )
}

export default OrganizationDropDownNew
