import React, { useContext, useEffect, useMemo } from 'react'
import { Select } from '@patterninc/react-ui'

import styles from './orgDropdown.module.scss'
import { SecondaryAppContext } from '../modules/contexts/SecondaryAppContext'
import { ThemeContext } from '../Context'

const SecondaryAppDropDown = () => {
  const { appsList, secondaryApp, updateSecondaryApp } =
    useContext(SecondaryAppContext)
  const { app: currentApp } = useContext(ThemeContext)
  const appChange = (app: { id: string; name: string }) => {
    updateSecondaryApp(app)
  }

  const isCurrentAppAdmin = useMemo(
    () => currentApp.name === 'Admin',
    [currentApp],
  )

  useEffect(() => {
    if (!isCurrentAppAdmin) {
      updateSecondaryApp(currentApp)
    }
  }, [isCurrentAppAdmin, updateSecondaryApp, currentApp])

  return isCurrentAppAdmin ? (
    <div className={styles.orgDropdownNew}>
      <div className={styles.orgDropdown}>
        <Select
          selectedItem={secondaryApp}
          options={appsList}
          optionKeyName={'name'}
          labelKeyName={'name'}
          onChange={(value) => {
            if (value) {
              appChange(value)
            }
          }}
        />
      </div>
    </div>
  ) : null
}

export default SecondaryAppDropDown
