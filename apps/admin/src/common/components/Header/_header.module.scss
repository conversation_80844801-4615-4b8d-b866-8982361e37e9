@use '@patterninc/react-ui/dist/variables' as variables;

.headerContainer {
  position: sticky;
  top: 0;
  background: var(--white);
  z-index: 99;

  .header {
    .header-left-section {
      display: flex;
      justify-content: space-between;
      align-items: center;
      button {
        margin-left: 16px;
      }
      @media only screen and (max-width: variables.$breakpoint-sm) {
        width: 100%;
      }
    }
    .header-right-section {
      display: grid;
      grid-gap: 32px;
      grid-template-columns: repeat(2, auto);
      align-items: center;
      position: relative;
      @media only screen and (max-width: variables.$breakpoint-sm) {
        grid-gap: unset;
      }
    }
  }
}
