import React, { useEffect, useRef } from 'react'
import { useSearchParams } from 'react-router-dom'

import AppRoutes from '../../modules/AppRoutes'
import Header from './Header/Header'
import SidebarMenu from './Sidebar/Sidebar'

const Main = (): React.JSX.Element => {
  const appWindowRef = useRef(null),
    [searchParams] = useSearchParams()

  useEffect(() => {
    const org = searchParams.get('org')
    if (org) {
      localStorage.setItem('organization_name', org)
    }
  }, [searchParams])

  return (
    <div className='App relative'>
      <SidebarMenu />
      <div
        className='app-content-layout'
        id='app-content-layout'
        ref={appWindowRef}
      >
        <Header />
        <div className='App-content'>
          <div className='page-container admin-page'>
            <AppRoutes />
          </div>
        </div>
      </div>
    </div>
  )
}

export default Main
