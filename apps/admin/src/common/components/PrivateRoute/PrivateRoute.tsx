import React, { useContext } from 'react'
import { Navigate, useLocation } from 'react-router-dom'
import { Ellipsis } from '@patterninc/react-ui'

import { c } from '../../services/TranslationService'
import {
  getRedirectRoute,
  hasRoutePermissions,
  type ROUTE_PRIVILEGES,
} from '../../../Auth/route-permissions'
import { ThemeContext } from '../../../Context'

interface PrivateRouteProps {
  /** Component to render */
  children: React.JSX.Element
  /** Auth roles to check */
  authRoles?: string[]
  /** Route to redirect to if auth checks fail */
  redirectTo?: string
}

const PrivateRoute = ({ children }: PrivateRouteProps): React.JSX.Element => {
  const { privileges } = useContext(ThemeContext),
    { pathname } = useLocation()

  const route = pathname.split('/')[1] as keyof typeof ROUTE_PRIVILEGES
  const hasAuthorize = hasRoutePermissions(route, privileges)

  const redirectRoute = privileges.length
    ? getRedirectRoute(privileges)
    : '/no-access'

  if (privileges.length === 0) {
    return (
      <span>
        {c('fetching')}
        <Ellipsis />
      </span>
    )
  }

  return hasAuthorize ? children : <Navigate to={redirectRoute} />
}

export default PrivateRoute
