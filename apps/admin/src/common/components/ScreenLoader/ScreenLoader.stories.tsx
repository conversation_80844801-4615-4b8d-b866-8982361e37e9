import { type Meta, type StoryObj } from '@storybook/react'
import { APP_LOGOS } from '@patterninc/react-ui'

import ScreenLoader from './ScreenLoader'
const meta = {
  title: 'Components/ScreenLoader',
  component: ScreenLoader,
  parameters: {
    layout: 'centered',
  },
  tags: ['autodocs'],
} as Meta<typeof ScreenLoader>

export default meta
type Story = StoryObj<typeof meta>
export const Basic: Story = {
  args: {
    logo: APP_LOGOS.ADMIN.logo,
  },
}
