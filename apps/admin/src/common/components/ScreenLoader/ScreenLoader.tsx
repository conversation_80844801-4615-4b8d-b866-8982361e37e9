import React from 'react'

import styles from './_screen-loader.module.scss'

type ScreenLoaderProps = {
  logo: string
  children?: React.ReactNode
}

const ScreenLoader = ({
  logo,
  children,
}: ScreenLoaderProps): React.JSX.Element => {
  return (
    <div className={styles.container}>
      <img src={logo} className={styles.logo} alt='logo' />
      {children}
    </div>
  )
}

export default ScreenLoader
