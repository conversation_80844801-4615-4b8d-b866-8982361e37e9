import React, { useContext } from 'react'
import {
  APP_LOGOS,
  envColorMap,
  envName,
  type IconStringList,
  LeftNav,
  Tag,
  useIsMobileView,
  useToggle,
} from '@patterninc/react-ui'
import { Link, useNavigate } from 'react-router-dom'
import { type LeftNavLinkObj } from '@patterninc/react-ui'

import { ThemeContext } from '../../../Context'
import { useUser } from '../../../context/user-context'
import { useAuth } from '../../../context/auth-context'
import { getCurrentEnv } from '../../../App'
import { hasRoutePermissions } from '../../../Auth/route-permissions'
import { c } from '../../services/TranslationService'

const SidebarMenu = (): React.JSX.Element => {
  const { breadcrumbs, privileges } = useContext(ThemeContext),
    navigate = useNavigate(),
    user = useUser(),
    { logout } = useAuth(),
    screenIsMobile = useIsMobileView(),
    environment = getCurrentEnv(),
    isDashboardEnabled = useToggle('dashboard_enabled')

  const sidebarContent: LeftNavLinkObj[] = [
    ...(hasRoutePermissions('users', privileges)
      ? [
          {
            name: c('users'),
            link: '/users',
            icon: 'femaleUser' as IconStringList,
          },
        ]
      : []),
    ...(hasRoutePermissions('org_units', privileges)
      ? [
          {
            name: c('orgUnits'),
            link: '/org_units',
            icon: 'globe1' as IconStringList,
          },
        ]
      : []),

    ...(hasRoutePermissions('history', privileges)
      ? [
          {
            name: c('eventHistory'),
            link: '/history',
            icon: 'feed' as IconStringList,
          },
        ]
      : []),

    ...(hasRoutePermissions('roles', privileges)
      ? [
          {
            name: c('roles'),
            link: '/roles',
            icon: 'toggle' as IconStringList,
          },
        ]
      : []),
    ...(hasRoutePermissions('privileges', privileges)
      ? [
          {
            name: c('privileges'),
            link: '/privileges',
            icon: 'key' as IconStringList,
          },
        ]
      : []),
    ...(hasRoutePermissions('global_users', privileges)
      ? [
          {
            name: c('globalUser'),
            link: '/global_users',
            icon: 'friends' as IconStringList,
          },
        ]
      : []),
    ...(hasRoutePermissions('orgs', privileges)
      ? [
          {
            name: c('orgs'),
            link: '/orgs',
            icon: 'directions' as IconStringList,
          },
        ]
      : []),
    ...(hasRoutePermissions('apps', privileges)
      ? [
          {
            name: c('apps'),
            link: '/apps',
            icon: 'layers' as IconStringList,
          },
        ]
      : []),
    ...(isDashboardEnabled && hasRoutePermissions('dashboard', privileges)
      ? [
          {
            name: 'Dashboard',
            link: '/dashboard',
            icon: 'gridIcon' as IconStringList,
          },
        ]
      : []),
  ]

  return (
    <LeftNav
      leftNavLinks={sidebarContent}
      breadcrumbs={breadcrumbs}
      logo={{
        url: APP_LOGOS.ADMIN.logo,
        abbreviatedUrl: APP_LOGOS.ADMIN.abbr,
        isolatedUrl: APP_LOGOS.ADMIN.isolated,
      }}
      routerComponent={Link}
      routerProp='to'
      mobileProps={{
        mobileHeaderChildren:
          screenIsMobile && environment !== 'production' ? (
            <div className='flex justify-content-end'>
              {/* The 2 different color options for Admin should be red (Staging) and yellow (Development) */}
              <Tag color={envColorMap[environment] as 'red' | 'yellow'}>
                {envName[environment]}
              </Tag>
            </div>
          ) : null,
      }}
      navigate={() => navigate('/')}
      userPermissions={[]} // TODO: Add in the user permissions when user roles are defined
      accountPopoverProps={{
        name: user.nickname ?? user.name,
        options: [
          {
            icon: 'logout',
            label: 'Logout',
            callout: logout,
          },
        ],
      }}
    />
  )
}

export default SidebarMenu
