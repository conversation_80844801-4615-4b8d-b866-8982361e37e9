@use '../base/mixins' as *;

.page-container {
  padding-top: 20px;
}

.actions {
  button,
  a {
    display: flex;
    align-items: center;
    gap: 16px;
    white-space: nowrap;
    line-height: 22px;
    padding: 4px 0;
    font-size: var(--font-size-12);
    color: var(--dark-purple);
    cursor: pointer;
    @include transition;
    background: none;
    border: 0;
    font-weight: var(--font-weight-regular);
    text-transform: none;
    height: auto;

    &:hover {
      color: var(--purple);

      svg path {
        fill: var(--purple);
      }
    }
  }
}

.admin-page {
  margin-bottom: 60px;
}

.left-download-section {
  .info {
    margin-top: 4px;
  }
}

.main-box-data .stat-figures {
  min-width: 250px;
  max-width: 250px;
}

.regions-page .main-box-data .stat-figures {
  min-width: 5%;
  max-width: 5%;
}

@media only screen and (max-width: 767px) {
  .main-box-data .stat-figures {
    min-width: 170px;
    max-width: 170px;
  }
}
