import { datadogRum } from '@datadog/browser-rum'
import { getEnvironmentName } from '@patterninc/react-ui'

const environment = {
  production: 'admin',
  stage: 'admin-stage',
}
const environmentName = getEnvironmentName()

export const initDataDogRUM = () => {
  try {
    datadogRum.init({
      applicationId: '08c6bd03-b9c9-4f3c-868a-cbcddb91ef40',
      clientToken: 'pubfefde367af931cf9cc6208ba2e7296de',
      site: 'datadoghq.com',
      service: environment[environmentName as keyof typeof environment],
      env: environmentName,
      sessionSampleRate: 100,
      sessionReplaySampleRate: environmentName === 'production' ? 100 : 20, // 100% recording only in production, 20% for other envs is enough
      trackUserInteractions: true,
      trackResources: true,
      trackLongTasks: true,
      defaultPrivacyLevel: 'mask-user-input',
      allowedTracingUrls: [
        {
          match: 'admin.pattern.com',
          propagatorTypes: ['tracecontext', 'datadog', 'b3', 'b3multi'],
        },
        {
          match: 'admin-stage.pattern.com',
          propagatorTypes: ['tracecontext', 'datadog', 'b3', 'b3multi'],
        },
        {
          match: (url: string) =>
            url.startsWith('https://admin.pattern.com') ||
            url.startsWith('https://admin-stage.pattern.com'),
          propagatorTypes: ['tracecontext', 'datadog', 'b3', 'b3multi'],
        },
      ],

      beforeSend: (event, context) => {
        if (!['stage', 'production'].includes(getEnvironmentName())) {
          return false
        }
        if ('error' in context && context.error) {
          const errorObj = context.error as unknown
          if (typeof errorObj === 'object' && errorObj !== null) {
            const message =
              'message' in errorObj ? String(errorObj.message) : 'Unknown error'
            const stack = 'stack' in errorObj ? String(errorObj.stack) : ''
            const errorMessage = `Error: ${message}\nStack: ${stack}`
            if (
              'error' in event &&
              event.error &&
              typeof event.error === 'object'
            ) {
              ;(event.error as { message: string }).message = errorMessage
            }
          }
        }
        return true
      },
    })
  } catch (error) {
    console.error('Error initializing Datadog RUM', error)
  }

  try {
    const userItem = localStorage.getItem('user')
    if (userItem) {
      const fullUser = JSON.parse(userItem)
      if (fullUser) {
        const user = {
          email: fullUser.email,
          id: fullUser.id,
        }
        datadogRum.setUser(user)
      }
    }
  } catch {
    // don't do anything - if it has an error then don't worry about it at this point
  }
  datadogRum.startSessionReplayRecording()
}
