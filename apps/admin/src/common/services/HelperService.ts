const authKey = 'https://pattern.com/user_authorization'
const GLOBAL_ADMIN_PRIVILEGE = 'admin_access_all'
const ADMIN_APP_NAME = 'Admin'

export const haveRoles = (
  user: {
    [authKey]: {
      roles: string[]
    }
  },
  rolesRequired: string[] = [],
): boolean => {
  if (rolesRequired.length === 0) return true
  if (!user?.[authKey].roles) return false
  return rolesRequired.some((role) => user[authKey].roles.includes(role))
}

export const tryLocalStorageParse = (
  key: string,
): Record<string, unknown> | null => {
  const item = localStorage.getItem(key)
  if (!item) {
    return null
  }
  try {
    return JSON.parse(item)
  } catch {
    return null
  }
}

export const keyTextConversion = (key: string): string => {
  return key
    .replace(/\s+/g, '_')
    .toLowerCase()
    .replace(/[^a-z0-9_]/g, '_')
}

export const shouldIncludeApp = (app: { code: string }) => {
  const currentUser = tryLocalStorageParse('current_user')

  type AppOrg = {
    app?: { name: string }
    privileges?: string[]
    org_units?: string[]
    access_to_all_org_units?: boolean
  }

  const adminApp = (currentUser?.apps_orgs as AppOrg[] | undefined)?.find(
    (appOrg) => appOrg?.app?.name === ADMIN_APP_NAME,
  )

  const hasAdminAccessAll = adminApp?.privileges?.includes(
    GLOBAL_ADMIN_PRIVILEGE,
  )
  const isOrgUnitIncluded = !!adminApp?.org_units?.includes(app?.code)
  const hasAccessToAllOrgUnits = !!adminApp?.access_to_all_org_units

  const isDisabled =
    !hasAdminAccessAll && !isOrgUnitIncluded && !hasAccessToAllOrgUnits

  return isDisabled
}
