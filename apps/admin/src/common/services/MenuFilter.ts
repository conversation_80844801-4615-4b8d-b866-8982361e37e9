import { type IconStringList } from '@patterninc/react-ui'

import { useAuth } from '../../context/auth-context'
import { haveRoles } from './HelperService'

export type SidebarContent = {
  breadcrumbs: Record<string, unknown>[]
  icon: IconStringList
  link: string
  page: string
  permissions: string[]
}

const MenuFilter = (list: SidebarContent[]): Array<SidebarContent> => {
  const { user } = useAuth()
  const filterSidebar = (list: SidebarContent[]) => {
    return list.filter((l) => {
      if (l.permissions) {
        if (haveRoles(user, l.permissions)) {
          return l
        }
      } else {
        return l
      }
      return false
    })
  }

  return filterSidebar(list)
}

export default MenuFilter
