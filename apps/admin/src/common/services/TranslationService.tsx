import {
  newTranslationInstance,
  tr,
  useTranslation,
} from '@patterninc/react-ui'

import { RESOURCES } from '../../translations'

export const LOCAL_STORAGE_LANGUAGE_SETTING_KEY = 'language_setting'

/** i18n instance for the whole Admin app */
export const i18nAdminInstance = newTranslationInstance(RESOURCES)

export const initAdminI18nInstance = () => {
  i18nAdminInstance.init()
  changeLanguageTo(getLocalStorageLanguageTag())
}

export const changeLanguageTo = (languageTag: string) => {
  const isDebugMode = localStorage.getItem('debug-translations') === 'true'
  i18nAdminInstance.changeLanguage(isDebugMode ? 'cimode' : languageTag)
}

export const NS = {
  common: 'common',
  eventHistory: 'eventHistory',
  privileges: 'privileges',
  regions: 'regions',
  roles: 'roles',
  signup: 'signup',
  users: 'users',
  globalUsers: 'globalUsers',
  orgs: 'orgs',
  apps: 'apps',
  dashboard: 'dashboard',
} as const

export type TranslationNamespace = keyof typeof NS
export type TranslationKey = `${TranslationNamespace}:${string}`

/**
 * Translation helper function. Alternative to the useTranslate hook in places where hooks aren't easily available (outside of components)
 * @param key - namespace:key of the translation (in the format 'namespace:key')
 * @param values - (optional) values to be interpolated into the translated string
 * @returns translated string
 **/
export const t = (
  namespaceAndKey: TranslationKey,
  values?: Record<string, unknown>,
) => tr(namespaceAndKey, i18nAdminInstance, values)

/**
 * Translation helper function, shorthand for using the "common" namespace `t('common:key')`
 * @param key - key of the translation
 * @param values - (optional) values to be interpolated into the translated string
 * @returns translated string
 **/
export const c = (key: string, values?: Record<string, unknown>) =>
  t(`common:${key}`, values)

export const getLocalStorageLanguageTag = () => {
  return localStorage.getItem(LOCAL_STORAGE_LANGUAGE_SETTING_KEY) ?? 'en'
}

export const useTranslate = (
  namespace: TranslationNamespace,
): ReturnType<typeof useTranslation> => useTranslation(namespace)
