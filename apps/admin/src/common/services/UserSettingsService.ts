import { getApiUrlPrefix } from '@patterninc/react-ui'
import { jwtDecode, type JwtPayload } from 'jwt-decode'

export const getUserSettingEndpoint = (
  /** To have a unique key. Eg: users:details:table */
  key: string,
): { endpoint: string; decoded: JwtPayload } => {
  const token = localStorage.getItem('id_token'),
    decoded = jwtDecode<JwtPayload>(token ?? '')
  return {
    endpoint: `${getApiUrlPrefix('user-settings')}/v1/${
      decoded?.sub
    }/admin/${key}`,
    decoded,
  }
}
