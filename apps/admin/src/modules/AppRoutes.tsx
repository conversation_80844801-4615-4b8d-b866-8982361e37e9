import React from 'react'
import { Navigate, Outlet, Route, Routes } from 'react-router-dom'

import LoginRedirect from '../LoginRedirect'
import UsersRoutes from './Users/<USER>'
import RegionRoutes from './Regions/RegionRoutes'
import RolesRoutes from './Roles/RolesRoutes'
import PrivilegesRoutes from './Privileges/PrivilegesRoutes'
import EventHistoryRoutes from './EventHistory/EventHistoryRoutes'
import GlobalUserRoutes from './GlobalUsers/GlobalUsersRoutes'
import OrgsRoutes from './Orgs/OrgsRoutes'
import AdminAppsRoutes from './Apps/AdminAppsRoutes'
import DashboardRoutes from './Dashboard/DashboardRoutes'

const AppRoutes = (): React.JSX.Element => {
  return (
    <Routes>
      <Route path='no-access' element={<div>Access Denied</div>} />
      <Route path='/' element={<Outlet />}>
        <Route index element={<Navigate to='/users' replace />} />
        <Route path='users/*' element={<UsersRoutes />} />
        <Route path='org_units/*' element={<RegionRoutes />} />
        <Route path='roles/*' element={<RolesRoutes />} />
        <Route path='privileges/*' element={<PrivilegesRoutes />} />
        <Route path='history' element={<EventHistoryRoutes />} />
        <Route path='global_users/*' element={<GlobalUserRoutes />} />
        <Route path='dashboard/*' element={<DashboardRoutes />} />
        <Route path='orgs/*' element={<OrgsRoutes />} />
        <Route path='apps/*' element={<AdminAppsRoutes />} />
      </Route>
      <Route path='authenticate' element={<LoginRedirect />} />
      {/* Redirect to the home page when a route does not exist. */}
      <Route path='*' element={<Navigate to='/users' replace />} />
    </Routes>
  )
}

export default AppRoutes
