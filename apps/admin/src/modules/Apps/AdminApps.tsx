import { PageHeader } from '@patterninc/react-ui'
import { useContext, useEffect, useState } from 'react'

import SecondaryAppDropDown from '../../common/SecondaryAppDropDown'
import { ThemeContext } from '../../Context'
import { useTranslate } from '../../common/services/TranslationService'
import Features from './Table/Features'
import AdminAppsTab from './AdminAppsTab'

type AdminAppsProps = {
  isInactiveTab?: boolean
}

const AdminApps = ({ isInactiveTab }: AdminAppsProps) => {
  const { updateBreadcrumbs } = useContext(ThemeContext),
    { t } = useTranslate('apps'),
    [count, setCount] = useState(0),
    [searchText, setSearchText] = useState('')
  useEffect(() => {
    updateBreadcrumbs({
      name: t('apps'),
      link: '/apps',
      changeType: 'rootLevel',
    })
  }, [t, updateBreadcrumbs])

  const searchInputHandler = (query: string) => {
    setSearchText(query)
  }

  return (
    <>
      <PageHeader
        header={{
          name: t('features'),
          value: count,
        }}
        search={{
          placeholder: t('search'),
          value: searchText,
          onChange: searchInputHandler,
          debounce: 500,
        }}
        rightSectionChildren={<SecondaryAppDropDown />}
      />
      <AdminAppsTab />
      <Features
        setCount={setCount}
        searchText={searchText}
        isInactiveTab={isInactiveTab}
      />
    </>
  )
}
export default AdminApps
