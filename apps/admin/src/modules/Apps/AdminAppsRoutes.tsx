import React from 'react'
import { Navigate, Route, Routes } from 'react-router-dom'

import PrivateRoute from '../../common/components/PrivateRoute/PrivateRoute'
import AdminApps from './AdminApps'
import {
  SecondaryAppContext,
  SecondaryAppProvider,
} from '../contexts/SecondaryAppContext'

const AdminAppsRoutes = (): React.JSX.Element => {
  const renderAdminAppsRoute = () => (
    <Routes>
      <Route
        path='features/active'
        element={
          <PrivateRoute>
            <AdminApps />
          </PrivateRoute>
        }
      />
      <Route
        path='features/inactive'
        element={
          <PrivateRoute>
            <AdminApps isInactiveTab />
          </PrivateRoute>
        }
      />
      <Route index element={<Navigate to='features/active' replace />} />
    </Routes>
  )

  return (
    <SecondaryAppProvider>
      <SecondaryAppContext.Consumer>
        {() => renderAdminAppsRoute()}
      </SecondaryAppContext.Consumer>
    </SecondaryAppProvider>
  )
}

export default AdminAppsRoutes
