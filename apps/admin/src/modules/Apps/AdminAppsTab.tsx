import React from 'react'
import { RouterTabs } from '@patterninc/react-ui'
import { NavLink, useLocation, useNavigate } from 'react-router-dom'

import { useTranslate } from '../../common/services/TranslationService'

const AdminAppsTab = (): React.JSX.Element => {
  const navigate = useNavigate()
  const pathname = useLocation().pathname
  const { t } = useTranslate('apps')

  const navigateMobileTabs = (link: string) => {
    navigate(link)
  }

  return (
    <RouterTabs
      mobileConfig={[
        { label: t('active'), link: '/apps/features/active' },
        { label: t('inactive'), link: '/apps/features/inactive' },
      ]}
      navigate={navigateMobileTabs}
      currentPath={pathname}
    >
      <NavLink to='/apps/features/active'>{t('active')}</NavLink>
      <NavLink to='/apps/features/inactive'>{t('inactive')}</NavLink>
    </RouterTabs>
  )
}

export default AdminAppsTab
