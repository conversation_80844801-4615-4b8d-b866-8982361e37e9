// Base type for entities with name, code, and description
export type ConfigurableItem = {
  id: string
  name: string
  code: string
  status: string
  description: string
  created_at?: string
  created_by_name?: string
  deactivated_at?: string
  last_deactivated_by?: string
  can_delete?: boolean
}

export type ConfigurableItemFormState = Omit<ConfigurableItem, 'id'> & {
  id?: string
}

export type ConfigurableItemFormParams = {
  action?: string
  setAction: (action: string) => void
  formState: ConfigurableItemFormState
  setFormState: (key: keyof ConfigurableItemFormState, value: string) => void
  formSubmit: () => void
  savingForm?: boolean
}

// Feature-specific types
export type Feature = ConfigurableItem
export type FeatureFormState = ConfigurableItemFormState
export type FeatureFormParams = ConfigurableItemFormParams & {
  selectedFeature?: Feature
}

export type HeaderProps = {
  setCount: (count: number) => void
  searchText: string
  isInactiveTab?: boolean
}
export type ParamTypes = {
  params: {
    feature?: {
      name?: string
      code?: string
      status?: string
      description?: string
      app_id: string
    }
  }
  apiUrl: string
  method: string
  actiontype?: string
}

export const initialFormState: ConfigurableItemFormState = {
  name: '',
  code: '',
  status: '',
  description: '',
}
