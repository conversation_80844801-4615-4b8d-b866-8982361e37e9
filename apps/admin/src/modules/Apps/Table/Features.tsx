import {
  ButtonGroup,
  type ConfigItemType,
  getApiUrlPrefix,
  <PERSON><PERSON><PERSON>,
  PageFooter,
  type SortColumnProps,
  standardSortParams,
  StandardTable,
  Tag,
  toast,
  TrimText,
} from '@patterninc/react-ui'
import { useContext, useEffect, useMemo, useState } from 'react'
import { useInfiniteQuery, useMutation } from '@tanstack/react-query'
import moment from 'moment'

import FeaturesForm from './FeaturesForm'
import {
  type Feature,
  type FeatureFormState,
  type HeaderProps,
  initialFormState,
  type ParamTypes,
} from '../AppTypes'
import { useTranslate } from '../../../common/services/TranslationService'
import SecureAxios from '../../../common/services/SecureAxios'
import { SecondaryAppContext } from '../../../modules/contexts/SecondaryAppContext'
import { hasWritePermission } from '../../../Auth/route-permissions'
import { ThemeContext } from '../../../Context'

const Features = ({
  setCount,
  searchText,
  isInactiveTab,
}: HeaderProps): React.JSX.Element => {
  const { t } = useTranslate('apps'),
    { t: c } = useTranslate('common'),
    { privileges } = useContext(ThemeContext),
    [formState, setFormState] = useState<FeatureFormState>(initialFormState),
    { secondaryApp } = useContext(SecondaryAppContext),
    updateForm = (key: keyof FeatureFormState, value: string) => {
      setFormState((prevState) => ({
        ...prevState,
        [key]: value,
      }))
    },
    [action, setAction] = useState<string>(''),
    [reload, setReload] = useState(false),
    [sortBy, setSort] = useState({
      prop: isInactiveTab ? 'deactivated_at' : 'created_at',
      flip: false,
    }),
    setSortBy: SortColumnProps['sorter'] = (sortObj) => {
      setSort({
        prop: sortObj.activeColumn,
        flip: sortObj.direction,
      })
    }
  const canWriteAppSettings = useMemo(
    () => hasWritePermission('apps', privileges),
    [privileges],
  )

  useEffect(() => {
    setSort((prevState) => ({
      ...prevState,
      prop: isInactiveTab ? 'deactivated_at' : 'created_at',
      flip: false,
    }))
  }, [isInactiveTab])

  const [isPrevTabInactive, setIsPrevTabInactive] = useState(isInactiveTab)

  useEffect(() => {
    setIsPrevTabInactive(isInactiveTab)
  }, [isInactiveTab])

  const isSortSetForTab = useMemo(() => {
    const isTabChanged =
      isPrevTabInactive !== isInactiveTab &&
      isInactiveTab &&
      sortBy.prop === 'deactivated_at'
    const isTabUnchanged = isPrevTabInactive === isInactiveTab

    return isTabChanged || isTabUnchanged
  }, [isPrevTabInactive, isInactiveTab, sortBy.prop])
  // Orgs Table API
  const apiUrl = `${getApiUrlPrefix('adminczar')}/api/v1/apps/${secondaryApp.id}/features`,
    {
      data: featuresPaginatedData,
      status,
      fetchNextPage,
      hasNextPage,
      isLoading,
    } = useInfiniteQuery({
      queryKey: [apiUrl, sortBy, searchText, reload, isInactiveTab],
      queryFn: async ({ pageParam = 1, signal }) => {
        const data = await SecureAxios.get(apiUrl, {
          params: {
            page: pageParam ?? 1,
            per_page: 20,
            sort: standardSortParams(sortBy),
            ...(searchText ? { search_for: searchText } : {}),
            filter: {
              status: {
                in: isInactiveTab ? 'inactive' : 'active',
              },
            },
          },
          signal,
        })
        setCount(data?.data?.pagination?.count)
        setReload(false)
        return data
      },
      enabled: isSortSetForTab,
      initialPageParam: 1,
      gcTime: 1000 * 60 * 60 * 8,
      getNextPageParam: (previousResponse) => {
        return previousResponse?.data?.pagination?.last_page
          ? undefined
          : previousResponse?.data?.pagination?.next_page
      },
    })

  const FeaturesData = useMemo(
    () =>
      featuresPaginatedData
        ? featuresPaginatedData?.pages?.flatMap((page) => {
            return page?.data?.data
          })
        : [],
    [featuresPaginatedData],
  )
  const FeaturesConfig = [
    {
      name: 'name',
      label: c('name'),
      cell: {
        children: (data: Feature) => (
          <span className={`flex align-items-center`}>{data?.name}</span>
        ),
      },
      mainColumn: true,
    },
    {
      name: 'code',
      label: c('code'),
      cell: {
        children: (data: Feature) => (
          <span className={`flex align-items-center `}>{data?.code}</span>
        ),
      },
    },
    {
      name: 'description',
      label: c('description'),
      cell: {
        children: (data: Feature) => (
          <span className={`flex align-items-center `}>
            {data?.description}
          </span>
        ),
      },
    },
    ...(isInactiveTab
      ? [
          {
            name: 'deactivated_at',
            label: c('deactivated'),
            cell: {
              children: (data: Feature) => (
                <div>
                  <span
                    className={`flex align-items-center ${
                      sortBy.prop === 'created_at' ? 'fw-semi-bold' : ''
                    }`}
                  >
                    {data.deactivated_at ? (
                      moment(data.deactivated_at).utc().format('MMMM DD, YYYY')
                    ) : (
                      <Mdash />
                    )}
                  </span>
                  {data?.last_deactivated_by && (
                    <TrimText
                      customClass='fs-10 fc-purple'
                      limit={24}
                      text={data.last_deactivated_by}
                    />
                  )}
                </div>
              ),
            },
          },
        ]
      : [
          {
            name: 'status',
            label: c('status'),
            cell: {
              children: (data: Feature) => (
                <span>
                  <Tag color='green'>{data?.status}</Tag>
                </span>
              ),
            },
          },
        ]),
    {
      name: 'created_at',
      label: c('created'),
      cell: {
        children: (data: Feature) => (
          <div>
            <span
              className={`flex align-items-center ${
                sortBy.prop === 'created_at' ? 'fw-semi-bold' : ''
              }`}
            >
              {moment(data.created_at).utc().format('MMMM DD, YYYY')}
            </span>
            {data?.created_by_name && (
              <TrimText
                customClass='fs-10 fc-purple'
                limit={24}
                text={data.created_by_name}
              />
            )}
          </div>
        ),
      },
    },
    ...(canWriteAppSettings
      ? [
          {
            name: '',
            label: '',
            noSort: true,
            isButton: true,
            cell: {
              children: (data: Feature) => {
                return isInactiveTab ? (
                  <ButtonGroup
                    buttons={[
                      {
                        actions: [
                          {
                            text: t('editFeature'),
                            icon: 'pencil',
                            callout: () => {
                              setFormState(data)
                              setAction('edit')
                            },
                          },
                          {
                            text: t('deleteFeature'),
                            icon: 'trash',
                            disabled: {
                              value: !data.can_delete,
                              tooltip: {
                                tooltipContent: t(
                                  'cannotDeleteFeatureWithActiveAssociations',
                                ),
                              },
                            },
                            destructive: true,
                            confirmation: {
                              type: 'red',
                              header: t('deleteFeature'),
                              body: (
                                <span>{t('thisFeatureWillBeDeleted')}</span>
                              ),
                              confirmCallout: () => {
                                deleteMutation.mutate(data.id)
                              },
                            },
                          },
                        ],
                      },
                      {
                        children: t('reactivateFeature'),
                        onClick: () => {
                          toggleFeatureStatus(data.id)
                        },
                      },
                    ]}
                  />
                ) : (
                  <ButtonGroup
                    buttons={[
                      {
                        icon: 'archive',
                        as: 'confirmation',
                        destructive: true,
                        confirmation: {
                          type: 'red',
                          header: c('areYouSure'),
                          body: t('featureDeactivationBody'),
                          confirmCallout: () => {
                            toggleFeatureStatus(data.id)
                          },
                        },
                        tooltip: {
                          tooltipContent: t('deactivateFeature'),
                        },
                      },
                      {
                        children: t('editFeature'),
                        onClick: () => {
                          setFormState(data)
                          setAction('edit')
                        },
                      },
                    ]}
                  />
                )
              },
            },
          },
        ]
      : []),
  ]

  const toggleFeatureStatus = (featureId: string) => {
    mutation.mutate({
      params: {
        feature: {
          status: isInactiveTab ? 'active' : 'inactive',
          app_id: secondaryApp.id,
        },
      },
      apiUrl: `${getApiUrlPrefix('adminczar')}/api/v1/apps/${secondaryApp.id}/features/${featureId}`,
      method: 'PUT',
      actiontype: 'toggle',
    })
  }

  const deleteMutation = useMutation({
    mutationFn: async (featureId: string) => {
      return await SecureAxios.delete(
        `${getApiUrlPrefix('adminczar')}/api/v1/apps/${secondaryApp.id}/features/${featureId}`,
      )
    },
    onSuccess: () => {
      toast({
        message: t('featureDeletedSuccessfully'),
        type: 'success',
      })
      setReload(true)
    },
    onError: () => {
      toast({
        message: c('somethingWentWrongPleaseTryAgain'),
        type: 'error',
        config: {
          closeOnClick: true,
        },
      })
    },
  })
  const createParams: ParamTypes = {
    params: {
      feature: {
        name: formState.name,
        code: formState.code,
        description: formState.description,
        app_id: secondaryApp.id,
      },
    },
    apiUrl: `${getApiUrlPrefix('adminczar')}/api/v1/apps/${secondaryApp.id}/features`,
    method: 'POST',
  }

  const updateParams: ParamTypes = {
    params: {
      feature: {
        name: formState.name,
        code: formState.code,
        description: formState.description,
        app_id: secondaryApp.id,
      },
    },
    apiUrl: `${getApiUrlPrefix('adminczar')}/api/v1/apps/${secondaryApp.id}/features/${formState.id}`,
    method: 'PUT',
  }

  const formSubmit = () => {
    if (action === 'create') {
      mutation.mutate(createParams)
    } else if (action === 'edit') {
      mutation.mutate(updateParams)
    }
  }

  const mutation = useMutation({
    mutationFn: (featureParams: ParamTypes) => {
      const { method, apiUrl, params } = featureParams
      return SecureAxios[method === 'POST' ? 'post' : 'put'](apiUrl, {
        ...params,
      })
    },
    onSuccess: (_data, variables) => {
      toast({
        message:
          action === 'create'
            ? t('featureCreatedSuccessfully')
            : variables.actiontype === 'toggle'
              ? isInactiveTab
                ? t('featureReactivatedSuccessfully')
                : t('featureDeactivatedSuccessfully')
              : t('featureUpdatedSuccessfully'),
        type: 'success',
      })
      setFormState(initialFormState)
      setAction('')
      setReload(true)
    },
    onError: (error: {
      data: {
        message: string
      }
    }) => {
      const errorText = error?.data?.message
      toast({
        message: `${errorText ? errorText : c('somethingWentWrongPleaseTryAgain')}`,
        type: 'error',
        config: {
          closeOnClick: true,
        },
      })
    },
  })

  const savingForm = mutation.isPending
  return (
    <>
      <StandardTable
        data={FeaturesData}
        config={
          FeaturesConfig as ConfigItemType<Feature, Record<string, unknown>>[]
        }
        dataKey='id'
        hasData={FeaturesData.length > 0}
        tableId={'features-table'}
        customHeight={'auto'}
        sort={setSortBy}
        sortBy={sortBy}
        noDataFields={{
          primaryText: t('No features found'),
        }}
        successStatus={status === 'success'}
        loading={isLoading}
        hasMore={!!hasNextPage}
        getData={fetchNextPage}
      />
      {canWriteAppSettings && (
        <PageFooter
          rightSection={[
            {
              as: 'button',
              onClick: () => {
                setAction('create')
              },
              disabled: false,
              children: t('createNewFeature'),
              styleType: 'primary-green',
              type: 'button',
            },
          ]}
        />
      )}
      <FeaturesForm
        action={action}
        setAction={setAction}
        setFormState={updateForm}
        formState={formState}
        formSubmit={formSubmit}
        savingForm={savingForm}
      />
    </>
  )
}
export default Features
