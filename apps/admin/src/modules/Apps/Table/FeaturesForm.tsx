import { FormFooter, SideDrawer, TextInput } from '@patterninc/react-ui'
import { useEffect, useMemo, useState } from 'react'

import { type FeatureFormParams } from '../AppTypes'
import { useTranslate } from '../../../common/services/TranslationService'
import { keyTextConversion } from '../../../common/services/HelperService'

const FeaturesForm = ({
  action,
  setAction,
  formState,
  setFormState,
  formSubmit,
  savingForm,
}: FeatureFormParams): React.JSX.Element => {
  const { t } = useTranslate('apps'),
    { t: c } = useTranslate('common'),
    [disabled, setDisabled] = useState<boolean>(false)

  useEffect(() => {
    setDisabled(!(formState.name && formState.code && formState.description))
  }, [formState])

  const formFields = useMemo(
    () => ['id', 'name', 'code', 'description'] as const,
    [],
  )

  const resetForm = () => {
    setAction('')
    formFields.forEach((field) => setFormState(field, ''))
  }

  const renderEditForm = () => {
    return (
      <div className='flex flex-direction-column pat-gap-4'>
        <TextInput
          labelText={c('name')}
          value={formState.name}
          callout={(_, value) => {
            const name = value.toString().trim()
            setFormState('name', name)
            action === 'create' && setFormState('code', keyTextConversion(name))
          }}
          type='text'
          stateName='name'
          placeholder={t('typeAUniqueFeatureName')}
          required
        />
        <TextInput
          labelText={c('code')}
          value={formState.code}
          callout={(_, value) => {
            setFormState('code', keyTextConversion(value.toString().trim()))
          }}
          type='text'
          stateName='code'
          required
          disabled={action === 'edit'}
        />
        <TextInput
          labelText={c('description')}
          value={formState.description}
          callout={(_, value) => {
            setFormState('description', value.toString().trim())
          }}
          type='text'
          stateName='description'
          required
        />
      </div>
    )
  }

  return (
    <>
      <SideDrawer
        isOpen={!!action}
        headerContent={
          action === 'create' ? t('createFeature') : t('editFeature')
        }
        closeCallout={() => {
          resetForm()
        }}
        footerContent={
          <FormFooter
            cancelButtonProps={{
              onClick: () => {
                resetForm()
              },
            }}
            saveButtonProps={{
              disabled: disabled || savingForm,
              onClick: () => {
                formSubmit()
              },
            }}
          />
        }
      >
        {renderEditForm()}
      </SideDrawer>
    </>
  )
}
export default FeaturesForm
