import React, { useContext, useEffect } from 'react'
import { getApiUrlPrefix, HeaderMetricGroup } from '@patterninc/react-ui'
import { useQuery } from '@tanstack/react-query'

import { ThemeContext } from '../../Context'
import type { DashboardStats, MetricConfig } from './DashboardTypes'
import { useTranslate } from '../../common/services/TranslationService'
import SecureAxios from '../../common/services/SecureAxios'

const Dashboard = (): React.JSX.Element => {
  const { updateBreadcrumbs } = useContext(ThemeContext)

  const { t } = useTranslate('dashboard')

  useEffect(() => {
    updateBreadcrumbs({
      name: t('dashboardTitle'),
      link: '/dashboard',
      changeType: 'rootLevel',
    })
  }, [updateBreadcrumbs, t])

  // Dashboard Stats API Query
  const apiUrl = `${getApiUrlPrefix('adminczar')}/api/v1/dashboard/summary`
  const { data: dashboardData, isLoading } = useQuery<DashboardStats>({
    queryKey: [apiUrl],
    queryFn: async ({ signal }) => {
      const response = await SecureAxios.get(apiUrl, { signal })
      return response.data
    },
    gcTime: 1000 * 60 * 5, // cache for 5 minutes
    retry: 1, // Only retry once on failure
  })

  const metricsConfig: MetricConfig[] = [
    {
      key: 'active_users_count',
      title: t('totalUsersTitle'),
      tooltip: t('totalUsersTooltip'),
    },
    {
      key: 'active_apps_count',
      title: t('applicationsTitle'),
      tooltip: t('applicationsTooltip'),
    },
    {
      key: 'active_orgs_count',
      title: t('organizationsTitle'),
      tooltip: t('organizationsTooltip'),
    },
  ]

  const mappedData = metricsConfig.map((metric) => ({
    beta: false,
    decimalScale: 0,
    formatType: 'number' as const,
    reverse: false,
    showChangeBelowMainMetric: false,
    showLessThanZeroPercentageChange: true,
    title: metric.title,
    tooltip: metric.tooltip,
    truncateValues: false,
    value:
      dashboardData?.data?.[metric.key as keyof typeof dashboardData.data] ?? 0,
  }))

  return (
    <div>
      <div className='pat-mt-3'>
        <HeaderMetricGroup loading={isLoading} data={mappedData} />
      </div>
    </div>
  )
}

export default Dashboard
