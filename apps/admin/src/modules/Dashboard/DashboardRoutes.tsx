import React from 'react'
import { Route, Routes } from 'react-router-dom'

import PrivateRoute from '../../common/components/PrivateRoute/PrivateRoute'
import Dashboard from './Dashboard'

const DashboardRoutes = (): React.JSX.Element => {
  return (
    <Routes>
      <Route
        index
        element={
          <PrivateRoute>
            <Dashboard />
          </PrivateRoute>
        }
      />
    </Routes>
  )
}

export default DashboardRoutes
