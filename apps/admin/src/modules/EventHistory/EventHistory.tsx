import React, {
  useCallback,
  useContext,
  useEffect,
  useMemo,
  useState,
} from 'react'
import {
  type ConfigItemType,
  type Filter,
  getApiUrlPrefix,
  PageHeader,
  SideDrawer,
  type SortColumnProps,
  standardSortParams,
  StandardTable,
  useIsMobileView,
} from '@patterninc/react-ui'
import { useInfiniteQuery } from '@tanstack/react-query'
import moment from 'moment'

import { ThemeContext } from '../../Context'
import OrganizationDropDownNew from '../../common/OrganizationDropDownNew'
import { GenerateTableConfig } from './history-table-config'
import HistoryTable from './HistoryTable'
import SecureAxios from '../../common/services/SecureAxios'
import { tryLocalStorageParse } from '../../common/services/HelperService'
import { c, useTranslate } from '../../common/services/TranslationService'

export type FilterStateType = {
  event?: { id?: string; name?: string }
  date?: {
    start_date: moment.Moment | null | string
    end_date: moment.Moment | null | string
  }
  entityType?: { id?: string; name?: string }
  appId?: string
}

const EventHistory = (): React.JSX.Element => {
  const { t } = useTranslate('eventHistory')

  const { updateBreadcrumbs, organization, app } = useContext(ThemeContext),
    isMobileView = useIsMobileView()

  useEffect(() => {
    updateBreadcrumbs({
      name: c('eventHistory'),
      link: '/history',
      changeType: 'rootLevel',
    })
  }, [updateBreadcrumbs])

  const [sortBy, setSort] = useState({
      prop: 'created_at',
      flip: false,
    }),
    [searchText, setSearchText] = useState<string>('')
  const setSortBy: SortColumnProps['sorter'] = (sortObj) => {
    setSort({
      prop: sortObj.activeColumn,
      flip: sortObj.direction,
    })
  }

  const searchInputHandler = (query: string) => {
    setSearchText(query)
  }

  const defaultFilter = useMemo(
    () => ({
      event: { id: 'all', name: c('all') },
      date: {
        start_date: null,
        end_date: null,
      },
      entityType: { id: 'all', name: c('all') },
    }),
    [],
  )

  const initialFilterState = useCallback((): FilterStateType => {
    const storedState = tryLocalStorageParse('history_filter')
    if (storedState?.appId === app.id) {
      return {
        ...defaultFilter,
        ...storedState,
      }
    }
    return defaultFilter
  }, [app.id, defaultFilter])

  const isCurrentAppAdmin = useMemo(() => app.name === 'Admin', [app])

  const [isDrawerOpen, setIsDrawerOpen] = useState(false),
    [history, setHistory] = useState({}),
    [itemType, setItemType] = useState(''),
    [reload, setReload] = useState(false),
    [filterState, setFilterState] =
      useState<FilterStateType>(initialFilterState),
    [filterStateCopy, setFilterStateCopy] =
      useState<FilterStateType>(initialFilterState),
    [filtersCount, setFiltersCount] = useState(0)

  const filterParams = useMemo(() => {
    const hasValidDateRange =
      filterStateCopy?.date?.start_date &&
      filterStateCopy?.date?.end_date &&
      moment(filterStateCopy?.date?.start_date).isValid() &&
      moment(filterStateCopy?.date?.end_date).isValid()

    return {
      ...(filterStateCopy?.event?.name !== 'All'
        ? { event: filterStateCopy?.event?.name?.toLocaleLowerCase() }
        : {}),

      ...(hasValidDateRange
        ? {
            start_date: moment(filterStateCopy?.date?.start_date).format(
              'MMMM DD, YYYY',
            ),
            end_date: moment(filterStateCopy?.date?.end_date).format(
              'MMMM DD, YYYY',
            ),
          }
        : {}),

      ...(filterStateCopy?.entityType?.name !== 'All'
        ? { entity_type: filterStateCopy?.entityType?.name }
        : {}),
    }
  }, [
    filterStateCopy?.event?.name,
    filterStateCopy?.date?.start_date,
    filterStateCopy?.date?.end_date,
    filterStateCopy?.entityType?.name,
  ])

  // Event History Table API
  const apiUrl = `${getApiUrlPrefix('adminczar')}/api/v1/apps/${app.id}/orgs/${organization.id}/history`,
    {
      data: historyPaginatedData,
      status,
      fetchNextPage,
      hasNextPage,
      isLoading,
    } = useInfiniteQuery({
      queryKey: [
        apiUrl,
        sortBy,
        reload,
        organization.id,
        searchText,
        filterParams,
      ],
      queryFn: async ({ pageParam = 1, signal }) => {
        const data = await SecureAxios.get(apiUrl, {
          params: {
            page: pageParam ?? 1,
            per_page: 20,
            sort: standardSortParams(sortBy),
            ...(searchText ? { search_for: searchText } : {}),
            ...filterParams,
          },
          signal,
        })
        setReload(false)
        return data
      },
      initialPageParam: 1,
      gcTime: 1000 * 60 * 60 * 8,
      getNextPageParam: (previousResponse) => {
        return previousResponse?.data?.pagination?.last_page
          ? undefined
          : previousResponse?.data?.pagination?.next_page
      },
    })

  const historyData = useMemo(
    () =>
      historyPaginatedData
        ? historyPaginatedData?.pages?.flatMap((page) => {
            return page?.data?.data
          })
        : [],
    [historyPaginatedData],
  )

  const appliedFilters = useMemo((): React.ComponentProps<
    typeof Filter
  >['filterStates'] => {
    return {
      event: {
        type: 'select',
        defaultValue: filterState?.event,
        options: [
          {
            id: 0,
            name: c('all'),
          },
          {
            id: 1,
            name: t('create'),
          },
          {
            id: 2,
            name: t('destroy'),
          },
          {
            id: 3,
            name: t('activate'),
          },
          {
            id: 4,
            name: t('deactivate'),
          },
          {
            id: 5,
            name: t('update'),
          },
        ],
        stateName: 'event',
        optionKeyName: 'name',
        labelText: t('event'),
      },
      date: {
        type: 'dates',
        labelText: t('datesRange'),
        defaultValue: {
          start_date: moment(filterState?.date?.start_date).isValid()
            ? filterState?.date?.start_date
            : null,
          end_date: filterState?.date?.end_date,
        },
        stateName: 'date',
        hideCustomDateSearch: true,
      },
      entityType: {
        type: 'select',
        defaultValue: filterState?.entityType,
        options: [
          {
            id: 0,
            name: 'All',
          },
          {
            id: 1,
            name: 'AdminAppsOrgsUser',
          },
          {
            id: 2,
            name: 'AdminAppsOrgsUsersOrgUnit',
          },
          {
            id: 3,
            name: 'AdminAppsOrgsUsersRole',
          },
          {
            id: 4,
            name: 'OrgUnit',
          },
          ...(isCurrentAppAdmin
            ? [
                {
                  id: 5,
                  name: 'Privilege',
                },
                {
                  id: 6,
                  name: 'Role',
                },
                {
                  id: 7,
                  name: 'PrivilegesRole',
                },
                {
                  id: 8,
                  name: 'Org',
                },
                {
                  id: 9,
                  name: 'AdminAppsOrg',
                },

                {
                  id: 10,
                  name: 'Feature',
                },
                {
                  id: 11,
                  name: 'AppsOrgsFeature',
                },
              ]
            : []),
        ],
        stateName: 'entityType',
        optionKeyName: 'name',
        labelText: 'Entity Type',
      },
    }
  }, [
    filterState?.event,
    filterState?.date?.start_date,
    filterState?.date?.end_date,
    filterState?.entityType,
    t,
    isCurrentAppAdmin,
  ])

  const applyFilterCallout = () => {
    setFilterState((prevState) => {
      const newState = {
        ...prevState,
        ...filterState,
        appId: app.id,
      }
      localStorage.setItem('history_filter', JSON.stringify(newState))
      setFilterStateCopy(newState)
      return newState
    })
  }

  const applyResetCallout = useCallback(() => {
    setFilterState(defaultFilter)
    localStorage.setItem('history_filter', JSON.stringify(defaultFilter))
    setFilterStateCopy(defaultFilter)
  }, [defaultFilter])

  const cancelCallout = () => {
    setFilterState(initialFilterState)
    setFilterStateCopy(initialFilterState)
  }

  const difference = useCallback((filterState: FilterStateType) => {
    let count = 0

    if (filterState.event) {
      if (
        filterState.event.name !== undefined &&
        filterState.event.name !== c('all')
      )
        count++
    }

    if (filterState.date) {
      if (
        filterState.date.start_date !== undefined &&
        filterState.date.start_date !== null &&
        filterState.date.start_date !== '' &&
        moment(filterState.date.start_date).isValid()
      )
        count++
    }
    if (filterState.entityType) {
      if (
        filterState.entityType.name !== undefined &&
        filterState.entityType.name !== c('all')
      )
        count++
    }

    return count
  }, [])

  useEffect(() => {
    const count = difference(filterState)
    setFiltersCount(count)
  }, [difference, filterState])

  useEffect(() => {
    setFilterState(initialFilterState())
    setFilterStateCopy(initialFilterState())
    localStorage.setItem('history_filter', JSON.stringify(initialFilterState()))
  }, [app, initialFilterState])

  const onChangeFilter = (...params: unknown[]) => {
    const key = params[0] as string
    const value = params[1]

    setFilterState({
      ...filterState,
      [key]: value,
    })
  }

  const totalhistory = useMemo(
    () => historyPaginatedData?.pages[0]?.data?.pagination?.count,

    [historyPaginatedData],
  )
  const hasData = !!(status === 'success' && historyData?.length)

  return (
    <>
      <PageHeader
        header={{
          name: t('events'),
          value: totalhistory,
        }}
        search={{
          placeholder: t('searchEvents'),
          value: searchText,
          onChange: searchInputHandler,
        }}
        tooltip={{
          tooltipContent: (
            <div className='flex flex-direction-column pat-gap-4'>
              <span className='fs-18'>{t('eventHistoriesList')}</span>
              <div>
                <span>{t('theTableBelowContainrsAllEventHistories')}</span>
              </div>
            </div>
          ),
        }}
        pageFilterProps={{
          filterStates: appliedFilters,
          filterCallout: applyFilterCallout,
          resetCallout: applyResetCallout,
          onChangeCallout: onChangeFilter,
          cancelCallout: cancelCallout,
          appliedFilters: filtersCount,
        }}
        {...(isMobileView
          ? { bottomSectionChildren: <OrganizationDropDownNew /> }
          : { rightSectionChildren: <OrganizationDropDownNew /> })}
      />

      <StandardTable
        data={historyData ?? []}
        config={
          GenerateTableConfig({
            sortBy,
            setIsDrawerOpen,
            setHistory,
            setItemType,
          }) as ConfigItemType<unknown, Record<string, unknown>>[]
        }
        stickyTableConfig={{ left: 1, right: 1 }}
        hasData={hasData}
        getData={fetchNextPage}
        loading={isLoading}
        hasMore={!!hasNextPage}
        sort={setSortBy}
        sortBy={sortBy}
        successStatus={status === 'success'}
        customHeight={'auto'}
        dataKey={'id'}
        tableId={'history_table'}
        noDataFields={{
          primaryText: t('noEventsFound'),
          secondaryText: t('weCouldNotFindAnyEventHistories'),
        }}
      />
      <SideDrawer
        size='md'
        headerContent={c('history')}
        isOpen={isDrawerOpen}
        closeCallout={() => setIsDrawerOpen(false)}
      >
        <div>
          <div>{itemType}</div>
          <HistoryTable history={history} />
        </div>
      </SideDrawer>
    </>
  )
}

export default EventHistory
