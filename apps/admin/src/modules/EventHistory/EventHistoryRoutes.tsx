import React from 'react'
import { Route, Routes } from 'react-router-dom'

import PrivateRoute from '../../common/components/PrivateRoute/PrivateRoute'
import EventHistory from './EventHistory'

const EventHistoryRoutes = (): React.JSX.Element => {
  const renderEventHistoryRoutes = () => (
    <Routes>
      <Route
        index
        element={
          <PrivateRoute>
            <EventHistory />
          </PrivateRoute>
        }
      />
    </Routes>
  )

  return renderEventHistoryRoutes()
}

export default EventHistoryRoutes
