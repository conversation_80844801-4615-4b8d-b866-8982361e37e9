import React, { useState } from 'react'
import {
  type SortColumnProps,
  StandardTable,
  TrimText,
} from '@patterninc/react-ui'

import { useTranslate } from '../../common/services/TranslationService'
import styles from './_eventHistory.module.scss'

type ObjectChanges = {
  [key: string]: (string | null)[]
}

type HistoryChanges = {
  key: string
  old: string | null | object
  new: string | null | object
}

type HistoryProps = {
  history: ObjectChanges
}
interface DisplayMetadataObjectProps {
  data: object
  limit?: number
}

const DisplayMetadataObject = React.memo(
  ({ data, limit = 3 }: DisplayMetadataObjectProps): React.JSX.Element => {
    const { t } = useTranslate('eventHistory')
    const [expanded, setExpanded] = useState(false)
    if (!data || Object.keys(data).length === 0) {
      return <span>-</span>
    }
    const jsonStr = JSON.stringify(data, null, 2)
    const lines = jsonStr.split('\n')
    const displayStr =
      expanded || lines.length <= limit
        ? jsonStr
        : lines.slice(0, limit).join('\n') + `\n... ${t('more')}`

    return (
      <div
        onMouseEnter={() => setExpanded(true)}
        onMouseLeave={() => setExpanded(false)}
        className={styles.orgMetadataStyle}
      >
        {displayStr}
      </div>
    )
  },
)

const HistoryTable = ({ history }: HistoryProps): React.JSX.Element => {
  const { t } = useTranslate('eventHistory')

  const getFormattedData = () => {
    const data = Object.entries(history)
    return data.map(([key, [oldValue, newValue]]) => ({
      key,
      old: oldValue,
      new: newValue,
    }))
  }

  const [sortBy, setSort] = useState({
    prop: 'field',
    flip: false,
  })
  const setSortBy: SortColumnProps['sorter'] = (sortObj) => {
    setSort({
      prop: sortObj.activeColumn,
      flip: sortObj.direction,
    })
  }

  const historyConfig = [
    {
      name: 'field',
      label: t('field'),
      noSort: true,
      mainColumn: true,
      cell: {
        children: (event: HistoryChanges) => <span>{event.key}</span>,
      },
    },
    {
      name: 'old_value',
      label: t('oldValue'),
      noSort: true,
      cell: {
        children: (event: HistoryChanges) => (
          <span>
            {event.old !== null &&
            (typeof event.old === 'string' || typeof event.old === 'number') ? (
              <TrimText
                customClass='fs-12 fc-black'
                limit={15}
                text={event.old?.toString() || '-'}
              />
            ) : (
              <DisplayMetadataObject data={event.old as object} />
            )}
          </span>
        ),
      },
    },
    {
      name: 'new_value',
      label: t('newValue'),
      noSort: true,
      cell: {
        children: (event: HistoryChanges) => (
          <span>
            {event.new !== null &&
            (typeof event.new === 'string' || typeof event.new === 'number') ? (
              <TrimText
                customClass='fs-12 fc-black'
                limit={15}
                text={event.new?.toString() || '-'}
              />
            ) : (
              <DisplayMetadataObject data={event.new as object} />
            )}
          </span>
        ),
      },
    },
  ]

  return (
    <StandardTable
      data={getFormattedData() ?? []}
      config={historyConfig}
      dataKey='key'
      customWidth={600}
      getData={() => {
        // no need to fetch data
      }}
      hasData
      noDataFields={{
        primaryText: t('noEventsFound'),
        secondaryText: t('weCouldNotFindAnyEventHistories'),
      }}
      successStatus
      tableId='history_table'
      hasMore={false}
      loading={false}
      sort={setSortBy}
      sortBy={sortBy}
    />
  )
}

export default HistoryTable
