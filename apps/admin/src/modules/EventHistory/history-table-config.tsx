import React from 'react'
import { Button, type ConfigItemType } from '@patterninc/react-ui'
import moment from 'moment'

import { c, t } from '../../common/services/TranslationService'

export type DataItem = {
  id: string
  item_id: string
  item_type: string
  event: string
  event_type?: string
  whodunnit: string | null
  object_changes: object
  created_at: string
  metadata?: {
    app_id: string
    org_id: string
    acted_user?: {
      id: string
      name: string
      email: string
    }
  }
  acted_user_name: string | null
  acted_user_email: string | null
  acting_entity_name?: string | null
  acting_entity_id?: string | null
  acting_user_name?: string | null
  acting_user_email?: string | null
  description: string | null
}

type TableConfigProps = {
  sortBy: { prop: string; flip: boolean }
  setIsDrawerOpen: (status: boolean) => void
  setHistory: (history: object) => void
  setItemType: (item: string) => void
}

export const GenerateTableConfig = ({
  sortBy,
  setIsDrawerOpen,
  setHistory,
  setItemType,
}: TableConfigProps): ConfigItemType<DataItem, Record<string, unknown>>[] => {
  return [
    {
      name: 'description',
      label: t('eventHistory:eventDescription'),
      cell: {
        children: (event: DataItem) => (
          <div className='max-content-width'>{event.description}</div>
        ),
      },
      mainColumn: true,
    },
    {
      name: 'event_type',
      label: t('eventHistory:event'),
      cell: {
        children: (event: DataItem) => (
          <div className={sortBy.prop === 'event' ? 'fw-semi-bold' : ''}>
            <span className='fw-semi-bold'>{event.event_type}</span>
          </div>
        ),
      },
    },
    {
      name: 'acting_entity_id',
      label: t('eventHistory:actingEntity'),
      cell: {
        children: (event: DataItem) => (
          <div>
            <div className={'pat-mb-0.5'}>{event.acting_entity_name}</div>
            <div className='fs-10 fc-purple'>{event.acting_entity_id}</div>
          </div>
        ),
      },
    },
    {
      name: 'created_at',
      label: t('eventHistory:eventTime'),
      cell: {
        children: (event: DataItem) => (
          <div>
            <div
              className={`pat-mb-0.5 ${
                sortBy.prop === 'created_at' ? 'fw-semi-bold' : ''
              }`}
            >
              {moment(event.created_at).utc().format('MMMM DD, YYYY')}
            </div>
            <div className='fs-10 fc-purple'>
              {moment(event.created_at).utc().format('HH:mm:ss [UTC]')}
            </div>
          </div>
        ),
      },
    },
    {
      name: 'details',
      label: t('eventHistory:details'),
      noSort: true,
      isButton: true,
      cell: {
        children: (event: DataItem) => {
          return (
            <Button
              as='button'
              onClick={() => {
                setHistory(event.object_changes)
                setIsDrawerOpen(true)
                setItemType(event.item_type)
              }}
            >
              {c('view')}
            </Button>
          )
        },
      },
    },
  ]
}
