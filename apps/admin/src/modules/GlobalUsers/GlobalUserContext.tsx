'use client'

import { createContext, type Dispatch, useReducer } from 'react'

import { tryLocalStorageParse } from '../../common/services/HelperService'

export type FilterAction =
  | { type: 'UPDATE_FILTER'; payload: { filters: filterType } }
  | { type: 'RESET_FILTER' }

export type filterType = {
  apps_selection: []
  orgs_selection: []
}

export const defaultFilter: filterType = {
  apps_selection: [],
  orgs_selection: [],
}

const initialState = (): filterType => {
  const storedState = tryLocalStorageParse('global_user_filter')
  if (storedState) {
    return {
      ...defaultFilter,
      ...storedState,
    }
  }
  return defaultFilter
}

const GlobalUserContext = createContext<{
  state: filterType
  dispatch: Dispatch<FilterAction>
}>({
  state: defaultFilter,
  dispatch: () => null,
})

const reducer = (state: filterType, action: FilterAction): filterType => {
  switch (action.type) {
    case 'UPDATE_FILTER':
      localStorage.setItem(
        'global_user_filter',
        JSON.stringify({
          ...state,
          ...action.payload.filters,
        }),
      )
      return {
        ...state,
        ...action.payload.filters,
      }
    case 'RESET_FILTER':
      localStorage.setItem('global_user_filter', JSON.stringify(defaultFilter))
      return { ...defaultFilter }
    default:
      return { ...state }
  }
}

const GlobalUserContextProvider = ({
  children,
}: {
  children: React.ReactNode
}) => {
  const [state, dispatch] = useReducer(reducer, initialState())
  return (
    <GlobalUserContext.Provider value={{ state, dispatch }}>
      {children}
    </GlobalUserContext.Provider>
  )
}

export { GlobalUserContext, GlobalUserContextProvider, reducer }
