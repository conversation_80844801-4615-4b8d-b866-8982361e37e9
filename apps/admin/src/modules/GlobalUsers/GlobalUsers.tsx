import React, {
  useCallback,
  useContext,
  useEffect,
  useMemo,
  useState,
} from 'react'
import {
  type Filter,
  getApiUrlPrefix,
  PageHeader,
  type SortColumnProps,
  TagInput,
} from '@patterninc/react-ui'
import { useQuery } from '@tanstack/react-query'

import SecureAxios from '../../common/services/SecureAxios'
import { ThemeContext } from '../../Context'
import { c, useTranslate } from '../../common/services/TranslationService'
import GlobalUsersTabs from './GlobalUsersTabs'
import GlobalUsersTable from './GlobalUsersTable'
import {
  defaultFilter,
  type filterType,
  GlobalUserContext,
} from './GlobalUserContext'
import styles from '../Users/<USER>'

const GlobalUsers = ({
  isInactiveTab,
}: {
  isInactiveTab?: boolean
}): React.JSX.Element => {
  const { updateBreadcrumbs } = useContext(ThemeContext)
  const [sortBy, setSort] = useState({
      prop: 'privilege_count',
      flip: false,
    }),
    { state: globalUserState, dispatch: globalUserDispatch } =
      useContext(GlobalUserContext),
    [totalCount, setTotalCount] = useState<number>(0),
    [searchText, setSearchText] = useState<string>(''),
    [bulkSearchUsers, setBulkSearchUsers] = useState<string[]>([]),
    [filterStateCopy, setFilterStateCopy] = useState(globalUserState),
    [filtersCount, setFiltersCount] = useState(0),
    { t } = useTranslate('globalUsers')

  useEffect(() => {
    updateBreadcrumbs({
      name: c('globalUserManagement'),
      link: '/global_users',
      changeType: 'rootLevel',
    })
  }, [updateBreadcrumbs])

  const setSortBy: SortColumnProps['sorter'] = (sortObj) => {
    setSort({
      prop: sortObj.activeColumn,
      flip: sortObj.direction,
    })
  }

  useEffect(() => {
    setSearchText(bulkSearchUsers.join(','))
  }, [bulkSearchUsers])

  const updateSelect = (...params: unknown[]) => {
    const stateAttr = params[0] as string
    const value = params[1]
    setFilterStateCopy({
      ...filterStateCopy,
      [stateAttr]: value,
    })
  }

  const resetCallout = () => {
    setFilterStateCopy(defaultFilter)
    updateFilters(defaultFilter)
  }

  const cancelCallout = () => {
    setFilterStateCopy(globalUserState)
  }

  const applyFilterCallout = () => {
    updateFilters({ ...filterStateCopy })
  }

  const formatResponseData = <T extends { name: string; id: string }>(
    data: T[],
  ): { name: string; id: string }[] =>
    data.map(({ name, id }) => ({ name, id }))

  const appsApiUrl = `${getApiUrlPrefix('adminczar')}/api/v1/apps`
  const { data: apps } = useQuery({
    queryKey: ['apps', appsApiUrl],
    queryFn: async ({ signal }) => {
      const response = await SecureAxios.get(appsApiUrl, {
        signal,
      })
      return formatResponseData(response.data.data)
    },
  })

  const orgsApiUrl = `${getApiUrlPrefix('adminczar')}/api/v1/orgs`
  const { data: orgs } = useQuery({
    queryKey: ['orgs', orgsApiUrl],
    queryFn: async ({ signal }) => {
      const response = await SecureAxios.get(orgsApiUrl, {
        signal,
      })
      return formatResponseData(response.data)
    },
  })

  const difference = useCallback((filterState: filterType) => {
    let count = 0
    if (filterState?.apps_selection?.length !== 0) {
      count += 1
    }
    if (filterState?.orgs_selection?.length !== 0) {
      count += 1
    }
    return count
  }, [])

  useEffect(() => {
    setFilterStateCopy(globalUserState)
    const count = difference(globalUserState)
    setFiltersCount(count)
  }, [difference, globalUserState])

  const appliedFilters = useMemo((): React.ComponentProps<
    typeof Filter
  >['filterStates'] => {
    return {
      apps: {
        type: 'multi-select',
        options: apps || [],
        stateName: 'apps_selection',
        labelKey: 'name',
        formLabelProps: {
          label: t('apps'),
        },
        selectPlaceholder: `-- ${t('selectApps')} --`,
        searchBarProps: {
          placeholder: t('searchApps'),
          value: '',
        },
        selectedOptions: filterStateCopy.apps_selection.map((app) => ({
          name: app,
        })),
      },
      orgs: {
        type: 'multi-select',
        options: orgs || [],
        stateName: 'orgs_selection',
        labelKey: 'name',
        formLabelProps: {
          label: t('organizations'),
        },
        selectPlaceholder: `-- ${t('selectOrganizations')} --`,
        searchBarProps: {
          placeholder: t('searchOrganizations'),
          value: '',
        },
        selectedOptions: filterStateCopy.orgs_selection.map((org) => ({
          name: org,
        })),
      },
    }
  }, [
    apps,
    filterStateCopy.apps_selection,
    filterStateCopy.orgs_selection,
    orgs,
    t,
  ])

  const filterParams = useMemo(() => {
    const appIds =
      globalUserState.apps_selection.length > 0
        ? globalUserState.apps_selection
            .map((app) => apps?.find((a) => a.name === app)?.id)
            .filter((id): id is string => Boolean(id))
        : []

    const orgIds =
      globalUserState.orgs_selection.length > 0
        ? globalUserState.orgs_selection
            .map((org) => orgs?.find((o) => o.name === org)?.id)
            .filter((id): id is string => Boolean(id))
        : []

    return {
      app_ids: appIds,
      org_ids: orgIds,
    }
  }, [
    apps,
    globalUserState.apps_selection,
    globalUserState.orgs_selection,
    orgs,
  ])

  const updateFilters = useCallback(
    (filter: filterType) => {
      globalUserDispatch({
        type: 'UPDATE_FILTER',
        payload: {
          filters: filter,
        },
      })
    },
    [globalUserDispatch],
  )

  return (
    <>
      <PageHeader
        header={{
          name: `${c('total')} ${isInactiveTab ? c('deactivated') : c('active')} ${c('users')}`,
          value: totalCount,
        }}
        leftSectionChildren={
          <div className={styles.setDimension}>
            <TagInput
              autoFocus
              placeholder={
                bulkSearchUsers.length === 0 ? t('searchNameEmail') : ''
              }
              tags={bulkSearchUsers}
              setTags={setBulkSearchUsers}
            />
          </div>
        }
        pageFilterProps={{
          filterStates: appliedFilters,
          filterCallout: applyFilterCallout,
          resetCallout: resetCallout,
          onChangeCallout: updateSelect,
          cancelCallout: cancelCallout,
          appliedFilters: filtersCount,
        }}
      />
      <GlobalUsersTabs />
      <GlobalUsersTable
        searchText={searchText}
        sortBy={sortBy}
        setSortBy={setSortBy}
        isInactiveTab={isInactiveTab}
        setTotalCount={setTotalCount}
        filterParams={filterParams}
      />
    </>
  )
}

export default GlobalUsers
