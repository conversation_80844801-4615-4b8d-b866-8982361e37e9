import React from 'react'
import { Navigate, Route, Routes } from 'react-router-dom'

import PrivateRoute from '../../common/components/PrivateRoute/PrivateRoute'
import GlobalUsers from './GlobalUsers'
import {
  GlobalUserContext,
  GlobalUserContextProvider,
} from './GlobalUserContext'
import UserDetails from './UserDetails/UserDetails'

const GlobalUsersRoutes = (): React.JSX.Element => {
  const renderGlobalUsersRoute = () => (
    <Routes>
      <Route
        path=':id/active'
        element={
          <PrivateRoute>
            <UserDetails />
          </PrivateRoute>
        }
      />
      <Route
        path=':id/inactive'
        element={
          <PrivateRoute>
            <UserDetails isInactiveTab />
          </PrivateRoute>
        }
      />
      <Route
        path='active'
        element={
          <PrivateRoute>
            <GlobalUsers />
          </PrivateRoute>
        }
      />
      <Route
        path='inactive'
        element={
          <PrivateRoute>
            <GlobalUsers isInactiveTab />
          </PrivateRoute>
        }
      />
      <Route index element={<Navigate to='active' replace />} />
    </Routes>
  )

  return (
    <GlobalUserContextProvider>
      <GlobalUserContext.Consumer>
        {() => renderGlobalUsersRoute()}
      </GlobalUserContext.Consumer>
    </GlobalUserContextProvider>
  )
}

export default GlobalUsersRoutes
