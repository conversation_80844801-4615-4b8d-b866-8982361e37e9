import { useContext, useMemo, useState } from 'react'
import {
  ButtonGroup,
  capitalize,
  getApiUrlPrefix,
  PageFooter,
  standardSortParams,
  StandardTable,
  toast,
  useToggle,
} from '@patterninc/react-ui'
import {
  useInfiniteQuery,
  useMutation,
  useQueryClient,
} from '@tanstack/react-query'
import { useNavigate } from 'react-router-dom'

import { c, useTranslate } from '../../common/services/TranslationService'
import { type GlobalUserType, type ParamsType } from './GlobalUsersTypes'
import SecureAxios from '../../common/services/SecureAxios'
import { hasWritePermission } from '../../Auth/route-permissions'
import { ThemeContext } from '../../Context'
import { tryLocalStorageParse } from '../../common/services/HelperService'

type TableStateType = {
  checkedBoxes: GlobalUserType[]
  selectAll: boolean
  isResetCheckboxes: boolean
}

type GlobalUsersTableProps = {
  searchText: string
  sortBy: { prop: string; flip: boolean }
  setSortBy: (sortObject: { activeColumn: string; direction: boolean }) => void
  isInactiveTab: boolean | undefined
  setTotalCount: (count: number) => void
  filterParams: {
    app_ids: string[]
    org_ids: string[]
  }
}

const GlobalUsersTable = ({
  sortBy,
  setSortBy,
  searchText,
  isInactiveTab,
  setTotalCount,
  filterParams,
}: GlobalUsersTableProps) => {
  const { privileges } = useContext(ThemeContext),
    navigate = useNavigate(),
    currentUser = tryLocalStorageParse('current_user')

  const [tableState, setTableState] = useState<TableStateType>({
    checkedBoxes: [],
    selectAll: false,
    isResetCheckboxes: false,
  })
  const { checkedBoxes, isResetCheckboxes } = tableState
  const queryClient = useQueryClient()
  const { t } = useTranslate('globalUsers')
  const [reload, setReload] = useState(false),
    isBulkUpdateConfirmationEnabled = useToggle('bulk_update_confirmation')

  const apiUrl = `${getApiUrlPrefix('adminczar')}/api/v1/global_users`
  const {
    data: globalUsersData,
    status,
    isLoading,
    hasNextPage,
    fetchNextPage,
  } = useInfiniteQuery({
    queryKey: [apiUrl, searchText, sortBy, isInactiveTab, filterParams, reload],
    queryFn: async ({ pageParam = 1, signal }) => {
      const data = await SecureAxios.get(apiUrl, {
        params: {
          page: pageParam ?? 1,
          per_page: 20,
          sort: standardSortParams(sortBy),
          status: isInactiveTab ? 'inactive' : 'active',
          ...(searchText ? { bulk_search: searchText } : {}),
          ...filterParams,
        },
        signal,
      })
      const totalCount = data?.data?.pagination?.count
      setTotalCount(totalCount)
      setReload(false)
      return data
    },
    initialPageParam: 1,
    getNextPageParam: (previousResponse) => {
      return previousResponse?.data?.pagination?.last_page
        ? undefined
        : previousResponse?.data?.pagination?.next_page
    },
  })

  const globalAdmins = useMemo(
    () =>
      globalUsersData
        ? globalUsersData?.pages?.flatMap((page) => {
            return page?.data?.data
          })
        : [],
    [globalUsersData],
  )

  const hasData = !!(status === 'success' && globalAdmins?.length)
  const isActionDisabled = (user: GlobalUserType) => {
    return user?.email === currentUser?.email
  }
  const mutation = useMutation({
    mutationFn: (globalUsersParams: ParamsType) => {
      const { apiUrl, params } = globalUsersParams
      return SecureAxios['put'](apiUrl, {
        ...params,
      })
    },
    onSuccess: () => {
      const successMessage = isInactiveTab
        ? t('userActivated')
        : t('userDeactivated')
      toast({
        message: successMessage,
        type: 'success',
      })
      setReload(true)
      queryClient.invalidateQueries({ queryKey: [apiUrl] })
    },
    onError: (error: { data: { message: string } }) => {
      const errorText = error?.data?.message
      toast({
        message: errorText || c('somethingWentWrongPleaseTryAgain'),
        type: 'error',
      })
    },
  })

  const deleteUserMutation = useMutation({
    mutationFn: async (apiUrl: string) => {
      return await SecureAxios.delete(apiUrl)
    },
    onSuccess: () => {
      toast({
        type: 'success',
        message: t('userDeleted'),
      })
      setReload(true)
    },
    onError: () => {
      toast({
        type: 'error',
        message: c('somethingWentWrongPleaseTryAgain'),
      })
    },
  })

  const toggleUserStatusFromAllOrgs = (userId: string) => {
    mutation.mutate({
      method: 'PUT',
      apiUrl: `${getApiUrlPrefix('adminczar')}/api/v1/global_users/${userId}`,
      params: {
        status: isInactiveTab ? 'active' : 'inactive',
      },
    })
  }

  const deleteUserFromAllOrgs = (userId: string) => {
    const apiUrl = `${getApiUrlPrefix('adminczar')}/api/v1/global_users/${userId}`
    deleteUserMutation.mutate(apiUrl)
  }

  const tableConfig = [
    {
      name: 'name',
      label: t('name'),
      cell: {
        children: (data: GlobalUserType) => (
          <span
            className={`flex align-items-center ${
              sortBy.prop === 'username' ? 'fw-semi-bold' : ''
            }`}
          >
            {data?.name}
          </span>
        ),
      },
      mainColumn: true,
    },
    {
      name: 'email',
      label: t('email'),
      cell: {
        children: (data: GlobalUserType) => (
          <span
            className={`flex align-items-center ${
              sortBy.prop === 'email' ? 'fw-semi-bold' : ''
            }`}
          >
            {data?.email}
          </span>
        ),
      },
    },
    {
      name: 'privilege_count',
      label: c('privileges'),
      cell: {
        children: (data: GlobalUserType) => (
          <span
            className={`flex align-items-center ${
              sortBy.prop === 'privilege_count' ? 'fw-semi-bold' : ''
            }`}
          >
            {data?.privilege_count}
          </span>
        ),
      },
      tooltip: {
        content: <span>{t('privilegesCountTooltip')}</span>,
      },
    },
    {
      name: 'orgs_count',
      label: c('orgs'),
      cell: {
        children: (data: GlobalUserType) => (
          <span
            className={`flex align-items-center ${
              sortBy.prop === 'orgs_count' ? 'fw-semi-bold' : ''
            }`}
          >
            {data?.orgs_count}
          </span>
        ),
      },
      tooltip: {
        content: <span>{t('orgsCountTooltip')}</span>,
      },
    },
    {
      name: 'apps_access',
      label: t('apps'),
      cell: {
        children: (data: GlobalUserType) => {
          const appsAccess = data?.apps_access
          return (
            <span
              className={`flex align-items-center ${
                sortBy.prop === 'app_access' ? 'fw-semi-bold' : ''
              }`}
            >
              {appsAccess
                ? appsAccess.map((app: string) => capitalize(app)).join(', ')
                : ''}
            </span>
          )
        },
      },
      tooltip: {
        content: <span>{t('appsAccessTooltip')}</span>,
      },
      noSort: true,
    },
    {
      name: '',
      label: '',
      cell: {
        children: (data: GlobalUserType) =>
          isInactiveTab ? (
            <ButtonGroup
              buttons={[
                {
                  actions: [
                    {
                      text: t('reactivateUser'),
                      icon: 'follow',
                      confirmation: {
                        type: 'red',
                        header: c('areYouSure'),
                        body: t('activateUserBody'),
                        confirmCallout: () =>
                          toggleUserStatusFromAllOrgs(data.id),
                      },
                    },
                    {
                      text: t('deleteUser'),
                      icon: 'trash',
                      destructive: true,
                      confirmation: {
                        type: 'red',
                        header: c('areYouSure'),
                        body: t('deleteUserBody'),
                        confirmCallout: () => deleteUserFromAllOrgs(data.id),
                      },
                    },
                  ],
                  disabled: !canWritePrivileges,
                },
                {
                  children: t('viewProfile'),
                  disabled: isActionDisabled(data),
                  onClick: () => {
                    navigate(`/global_users/${data.id}/inactive`)
                  },
                },
              ]}
            />
          ) : (
            <ButtonGroup
              buttons={[
                {
                  actions: [
                    {
                      text: t('deactivateUser'),
                      icon: 'unfollow',
                      destructive: true,

                      confirmation: {
                        type: 'red',
                        header: c('areYouSure'),
                        body: t('deactivateUserBody'),
                        confirmCallout: () =>
                          toggleUserStatusFromAllOrgs(data.id),
                      },
                    },
                  ],
                  disabled: !canWritePrivileges || isActionDisabled(data),
                },
                {
                  children: t('viewProfile'),
                  disabled: isActionDisabled(data),
                  onClick: () => {
                    navigate(`/global_users/${data.id}/active`)
                  },
                },
              ]}
            />
          ),
      },
      noSort: true,
    },
  ]

  const canWritePrivileges = useMemo(
    () => hasWritePermission('global_users', privileges),
    [privileges],
  )

  const bulkActivateDeactivateUsers = () => {
    mutation.mutate({
      method: 'PUT',
      apiUrl: `${getApiUrlPrefix('adminczar')}/api/v1/global_users/bulk_update`,
      params: {
        status: isInactiveTab ? 'active' : 'inactive',
        user_ids: checkedBoxes.map((user) => user.id),
      },
    })
    setTableState((prevState) => ({
      ...prevState,
      checkedBoxes: [],
      isResetCheckboxes: true,
    }))
  }

  const handleCheckedBoxes = (
    checkedBoxes: GlobalUserType[],
    isCheckAll?: boolean,
  ) => {
    if (isCheckAll) {
      setTableState((prevState) => ({
        ...prevState,
        selectAll: true,
        checkedBoxes: checkedBoxes,
      }))
    } else {
      setTableState((prevState) => ({
        ...prevState,
        selectAll: false,
        checkedBoxes: checkedBoxes,
      }))
    }
  }

  return (
    <>
      {/* TODO: REMOVE THIS ONCE ISSUE IS FIXED */}
      <style>{`
        .disable-select-all-checkbox .sticky-table-row:first-child .sticky-table-cell:first-child [data-testid="checkbox-component"] {
          display: none !important;
        }
      `}</style>
      {isBulkUpdateConfirmationEnabled ? (
        <StandardTable
          stickyTableConfig={{ right: 1 }}
          data={globalAdmins}
          config={tableConfig}
          loading={isLoading}
          dataKey={'id'}
          hasData={hasData}
          hasMore={!!hasNextPage}
          customHeight={'auto'}
          successStatus={status === 'success'}
          noDataFields={{
            primaryText: t('noGlobalUsersFound'),
            secondaryText: t('weCouldNotFindAnyGlobalUsersForSelectedCriteria'),
          }}
          tableId={'globalUsersTable'}
          sort={setSortBy}
          sortBy={sortBy}
          getData={fetchNextPage}
          hasCheckboxes
          handleCheckedBoxes={handleCheckedBoxes}
          isResetCheckboxes={isResetCheckboxes}
        />
      ) : (
        <div className='disable-select-all-checkbox'>
          <StandardTable
            stickyTableConfig={{ right: 1 }}
            data={globalAdmins}
            config={tableConfig}
            loading={isLoading}
            dataKey={'id'}
            hasData={hasData}
            hasMore={!!hasNextPage}
            customHeight={'auto'}
            successStatus={status === 'success'}
            noDataFields={{
              primaryText: t('noGlobalUsersFound'),
              secondaryText: t(
                'weCouldNotFindAnyGlobalUsersForSelectedCriteria',
              ),
            }}
            tableId={'globalUsersTable'}
            sort={setSortBy}
            sortBy={sortBy}
            getData={fetchNextPage}
            hasCheckboxes
            handleCheckedBoxes={handleCheckedBoxes}
            isResetCheckboxes={isResetCheckboxes}
          />
        </div>
      )}
      <PageFooter
        rightSection={
          canWritePrivileges
            ? [
                {
                  as: 'confirmation',
                  disabled: !canWritePrivileges || !checkedBoxes.length,
                  confirmation: {
                    type: 'red',
                    header: c('areYouSure'),
                    body: isInactiveTab
                      ? t('activateUsersBody')
                      : t('deactivateUsersBody'),
                    confirmCallout: () => bulkActivateDeactivateUsers(),
                  },
                  children: isInactiveTab
                    ? t('reactivateUsers')
                    : t('deactivateUsers'),
                  styleType: isInactiveTab ? 'primary-green' : 'primary',
                  type: 'button',
                  destructive: isInactiveTab,
                },
              ]
            : []
        }
      />
    </>
  )
}

export default GlobalUsersTable
