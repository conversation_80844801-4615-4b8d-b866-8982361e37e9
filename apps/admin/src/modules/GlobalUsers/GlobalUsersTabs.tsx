import React from 'react'
import { RouterTabs } from '@patterninc/react-ui'
import { NavLink, useLocation, useNavigate } from 'react-router-dom'

import { c } from '../../common/services/TranslationService'

const GlobalUsersTabs = (): React.JSX.Element => {
  const navigate = useNavigate()
  const pathname = useLocation().pathname

  const navigateMobileTabs = (link: string) => {
    navigate(link)
  }

  return (
    <RouterTabs
      mobileConfig={[
        { label: c('active'), link: '/global_users/active' },
        { label: c('inactive'), link: '/global_users/inactive' },
      ]}
      navigate={navigateMobileTabs}
      currentPath={pathname}
    >
      <NavLink to='/global_users/active'>{c('active')}</NavLink>
      <NavLink to='/global_users/inactive'>{c('inactive')}</NavLink>
    </RouterTabs>
  )
}

export default GlobalUsersTabs
