import { useCallback, useContext, useEffect, useState } from 'react'
import { useLocation, useParams } from 'react-router-dom'
import {
  getApiUrlPrefix,
  PageHeader,
  type SortColumnProps,
  TagInput,
} from '@patterninc/react-ui'
import { useQuery } from '@tanstack/react-query'

import { c, useTranslate } from '../../../common/services/TranslationService'
import SecureAxios from '../../../common/services/SecureAxios'
import { ThemeContext } from '../../../Context'
import UserDetailsInfoPane from './UserDetailsInfoPane'
import UserDetailsTable from './UserDetailsTable'
import { type UserType } from './UserDetailsTypes'
import UserDetailsTabs from './UserDetailsTabs'
import styles from '../../Users/<USER>'
import userDetailStyle from './_userDetails.module.scss'

const UserDetails = ({
  isInactiveTab,
}: {
  isInactiveTab?: boolean
}): React.JSX.Element => {
  const { updateBreadcrumbs } = useContext(ThemeContext),
    [sortBy, setSort] = useState({
      prop: isInactiveTab ? 'deactivated_at' : 'created_at',
      flip: false,
    }),
    { pathname } = useLocation(),
    { id } = useParams<{ id: string }>(),
    [searchText, setSearchText] = useState<string[]>([]),
    [totalCount, setTotalCount] = useState<number>(0),
    { t } = useTranslate('globalUsers')

  const formatResponseData = (data: UserType): UserType => {
    return {
      id: data.id,
      name: `${data.given_name || ''} ${data.family_name || ''}`.trim(),
      given_name: data.given_name,
      family_name: data.family_name,
      email: data.email,
    }
  }
  const userApiUrl = `${getApiUrlPrefix('adminczar')}/api/v1/users/${id}`
  const { data: user, refetch } = useQuery({
    queryKey: [id],
    queryFn: async ({ signal }) => {
      const response = await SecureAxios.get(userApiUrl, { signal })
      return formatResponseData(response.data)
    },
  })

  const refetchUser = useCallback(() => {
    refetch()
  }, [refetch])

  useEffect(() => {
    if (user) {
      updateBreadcrumbs({
        name: user.name || t('user'),
        link: pathname,
        changeType: 'tab',
      })
    }
  }, [user, updateBreadcrumbs, pathname, t])

  useEffect(() => {
    setSort((prevState) => ({
      ...prevState,
      prop: isInactiveTab ? 'deactivated_at' : 'created_at',
      flip: false,
    }))
  }, [isInactiveTab])

  const setSortBy: SortColumnProps['sorter'] = (sortObj) => {
    setSort({
      prop: sortObj.activeColumn,
      flip: sortObj.direction,
    })
  }
  return (
    <div className={userDetailStyle.setDimension}>
      <UserDetailsInfoPane user={user} />
      <div>
        <PageHeader
          header={{
            name: `${c('total')} ${isInactiveTab ? c('deactivated') : c('active')} ${c('Records')}`,
            value: totalCount,
          }}
          leftSectionChildren={
            <div className={styles.setDimension}>
              <TagInput
                autoFocus
                placeholder={
                  searchText.length === 0 ? t('searchAppOrganizations') : ''
                }
                tags={searchText}
                setTags={setSearchText}
              />
            </div>
          }
        />
        <UserDetailsTabs />

        <UserDetailsTable
          user={user}
          sortBy={sortBy}
          setSortBy={setSortBy}
          isInactiveTab={isInactiveTab}
          setTotalCount={setTotalCount}
          searchText={searchText}
          refetchUser={refetchUser}
        />
      </div>
    </div>
  )
}

export default UserDetails
