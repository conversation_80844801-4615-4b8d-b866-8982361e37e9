import { Icon, InformationPane, Mdash, Skeleton } from '@patterninc/react-ui'

import { type UserType } from './UserDetailsTypes'
import userDetailStyle from './_userDetails.module.scss'

const renderInfoPane = (
  name: React.JSX.Element | string,
  email: React.JSX.Element | string,
) => (
  <InformationPane>
    <InformationPane.CustomSection
      children={
        <div className={userDetailStyle.userDetailsInfoPane}>
          <Icon color='white' icon='maleUser' iconSize='32px' />
        </div>
      }
    />
    <InformationPane.Section
      data={[
        { label: 'Name', data: name, check: true },
        { label: 'Email', data: email, check: true },
      ]}
    />
  </InformationPane>
)

const UserDetailsInfoPane = ({
  user,
}: {
  user: UserType | undefined
}): React.JSX.Element => {
  return (
    <div className={userDetailStyle.containerStyle}>
      {user
        ? renderInfoPane(user.name || <Mdash />, user.email)
        : renderInfoPane(
            <Skeleton height={20} width={200} />,
            <Skeleton height={20} width={200} />,
          )}
    </div>
  )
}

export default UserDetailsInfoPane
