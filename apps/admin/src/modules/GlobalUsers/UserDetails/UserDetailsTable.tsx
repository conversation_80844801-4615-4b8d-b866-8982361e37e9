import { useInfiniteQuery, useMutation } from '@tanstack/react-query'
import {
  ButtonGroup,
  getApiUrlPrefix,
  hasValue,
  MdashCheck,
  standardSortParams,
  StandardTable,
  Tag,
  toast,
  TrimText,
} from '@patterninc/react-ui'
import { useCallback, useEffect, useMemo, useState } from 'react'
import moment from 'moment'

import SecureAxios from '../../../common/services/SecureAxios'
import { c, useTranslate } from '../../../common/services/TranslationService'
import {
  type AppType,
  type deleteuserparamstypes,
  type FormState,
  type OrgType,
  type paramstypes,
  type UserDetailsTableType,
  type userDetailType,
  type UsersDetailsTableProps,
} from './UserDetailsTypes'
import { type UserType } from '../../Users/<USER>/users-table-config'
import UserFormParams from '../../Users/<USER>/UserFormParams'
import { tryLocalStorageParse } from '../../../common/services/HelperService'

const UserDetailsTable = ({
  user,
  sortBy,
  setSortBy,
  setTotalCount,
  searchText,
  isInactiveTab,
  refetchUser,
}: UsersDetailsTableProps) => {
  const apiUrl = `${getApiUrlPrefix('adminczar')}/api/v1/global_users/${user?.id}`
  const { t } = useTranslate('globalUsers')
  const [reload, setReload] = useState(false),
    [isEditMode, setEditMode] = useState(false),
    [app, setApp] = useState<AppType>({ id: 0, name: '' }),
    [org, setOrg] = useState<OrgType>({ id: 0, name: '' }),
    [selectedUser, setSelectedUser] = useState<UserType | undefined>(),
    [formState, setFormState] = useState<FormState>({
      email: user?.email || '',
      first_name: user?.given_name || '',
      last_name: user?.family_name || '',
      sso_user: false,
      role: {
        id: '',
        value: '',
        text: '',
      },
      region: [],
      allRegion: false,
    }),
    currentUser = tryLocalStorageParse('current_user')

  const [isPrevTabInactive, setIsPrevTabInactive] = useState(isInactiveTab)

  useEffect(() => {
    setIsPrevTabInactive(isInactiveTab)
  }, [isInactiveTab])

  const isSortSetForTab = useMemo(() => {
    const isTabChanged =
      isPrevTabInactive !== isInactiveTab &&
      isInactiveTab &&
      sortBy.prop === 'deactivated_at'
    const isTabUnchanged = isPrevTabInactive === isInactiveTab

    return isTabChanged || isTabUnchanged
  }, [isPrevTabInactive, isInactiveTab, sortBy.prop])
  const {
    data: userDetailsData,
    status,
    isLoading,
    hasNextPage,
    fetchNextPage,
  } = useInfiniteQuery({
    queryKey: [apiUrl, sortBy, reload, searchText, isInactiveTab],
    queryFn: async ({ pageParam = 1, signal }) => {
      const data = await SecureAxios.get(apiUrl, {
        params: {
          page: pageParam ?? 1,
          per_page: 20,
          sort: standardSortParams(sortBy),
          status: isInactiveTab ? 'inactive' : 'active',
          ...(searchText ? { search_for: searchText.join(',') } : {}),
        },
        signal,
      })
      const totalCount = data?.data?.pagination?.count
      setTotalCount(totalCount)
      setReload(false)
      return data
    },
    enabled: isSortSetForTab && !!user,
    initialPageParam: 1,
    getNextPageParam: (previousResponse) => {
      return previousResponse?.data?.pagination?.last_page
        ? undefined
        : previousResponse?.data?.pagination?.next_page
    },
  })
  const userDetails = useMemo(
    () =>
      userDetailsData
        ? userDetailsData?.pages?.flatMap((page) => {
            return page?.data?.data
          })
        : [],
    [userDetailsData],
  )

  const refresh = useCallback(() => {
    setReload(true)
  }, [])

  const editUserProfile = useCallback(
    (userRecord: UserDetailsTableType) => {
      setEditMode(true)
      setSelectedUser({
        id: userRecord.id,
        organization_id: userRecord.org.id,
        user_id: user?.id || 0,
        user: {
          given_name: user?.given_name || '',
          family_name: user?.family_name || '',
          email: user?.email || '',
          sso_user: userRecord.sso_user,
          last_login: userRecord.last_login,
        },
        status: userRecord.status,
        roles: userRecord.roles,
        org_units: userRecord.org_units.map(
          (orgUnit: { id: number; name: string }) => ({
            id: orgUnit.id,
            name: orgUnit.name,
          }),
        ),
        access_to_all_org_units: userRecord.access_to_all_org_units,
        created_at: userRecord.created_at,
        deactivated_at: userRecord.deactivated_at ?? undefined,
        app_id: userRecord.admin_app.id,
      })
      setApp({
        id: userRecord.admin_app.id,
        name: userRecord.admin_app.name,
        metadata: userRecord.admin_app.metadata,
      })
      setOrg({
        id: userRecord.org.id,
        name: userRecord.org.name,
      })
      setFormState({
        ...formState,
        sso_user: userRecord.sso_user,
        role: {
          id: userRecord.roles[0].id,
          value: userRecord.roles[0].id,
          text: userRecord.roles[0].name,
        },
        region: userRecord.org_units.map((org) => org.name),
        allRegion: userRecord.access_to_all_org_units,
      })
    },
    [formState, user?.email, user?.family_name, user?.given_name, user?.id],
  )

  const hasData = !!(status === 'success' && userDetails?.length)
  const isActionDisabled = (user?: userDetailType) => {
    return user?.email === currentUser?.email
  }
  const userAction = (userDetails: UserDetailsTableType, type: string) => {
    const bulkActionParams = {
      params: {
        id: user?.id,
        ...(userDetails.access_to_all_org_units &&
        type !== 'passwordReset' &&
        type !== 'resendActivationMail'
          ? {
              access_to_all_org_units: true,
            }
          : {}),

        ...(type !== 'passwordReset' && type !== 'resendActivationMail'
          ? { status: type }
          : {}),
      },
      apiUrl: `${getApiUrlPrefix('adminczar')}/api/v1/apps/${userDetails.admin_app.id}/orgs/${
        userDetails.org.id
      }/users/${user?.id}${
        type === 'passwordReset'
          ? `/reset_password`
          : type === 'resendActivationMail'
            ? '/resend_activation_email'
            : ''
      }`,
    }
    mutation.mutate(bulkActionParams)
  }

  const mutation = useMutation({
    mutationFn: (bulkActionParams: paramstypes) =>
      SecureAxios.put(bulkActionParams?.apiUrl, bulkActionParams?.params),
    onSuccess: (_, bulkActionParams) => {
      if (
        bulkActionParams?.params?.status ||
        bulkActionParams?.apiUrl?.includes('resend_activation_email')
      ) {
        refresh()
      }
      toast({
        type: 'success',
        message: bulkActionParams?.params?.status
          ? bulkActionParams?.params?.status === 'inactive'
            ? t('users:userSuccessfullyDeactivated')
            : t('users:userSuccessfullyActivated')
          : bulkActionParams?.apiUrl.includes('resend_activation_email')
            ? t('users:activationEmailSent')
            : t('users:passwordResetEmailSent'),
      })
    },
    onError: () => {
      toast({
        type: 'error',
        message: c('somethingWentWrongPleaseTryAgain'),
      })
    },
  })

  const deleteUserMutation = useMutation({
    mutationFn: async (deleteUserParams: deleteuserparamstypes) => {
      return await SecureAxios.delete(deleteUserParams?.apiUrl)
    },
    onSuccess: () => {
      refresh()
      toast({
        type: 'success',
        message: t('users:userDeletedSuccessfully'),
      })
    },
    onError: () => {
      toast({
        type: 'error',
        message: c('somethingWentWrongPleaseTryAgain'),
      })
    },
  })

  const deleteUser = (app_id: number, org_id: number) => {
    const apiUrl = `${getApiUrlPrefix('adminczar')}/api/v1/apps/${app_id}/orgs/${org_id}/users/${user?.id}`
    const deleteUserParams = {
      apiUrl,
    }
    deleteUserMutation.mutate(deleteUserParams)
  }

  const renderActionColumn = (data: UserDetailsTableType) => {
    return isInactiveTab ? (
      <ButtonGroup
        buttons={[
          {
            actions: [
              {
                text: t('users:editProfile'),
                icon: 'pencil',
                callout: () => {
                  editUserProfile(data)
                },
              },
              {
                text: t('users:deleteUser'),
                icon: 'trash',
                destructive: true,
                confirmation: {
                  type: 'red',
                  header: c('areYouSure'),
                  body: t('users:deleteUserBody', { appName: app?.name }),
                  confirmCallout: () => {
                    deleteUser(data.admin_app.id, data.org.id)
                  },
                },
                disabled: { value: isActionDisabled(user) },
              },
            ],
          },
          {
            children: t('users:reactivateUser'),
            onClick: () => userAction(data, 'active'),
            disabled: isActionDisabled(user),
          },
        ]}
      />
    ) : (
      <ButtonGroup
        buttons={[
          {
            actions: [
              data.status === 'onboarding'
                ? {
                    text: t('users:resendActivationEmail'),
                    icon: 'paperPlane',
                    confirmation: {
                      type: 'blue',
                      header: c('areYouSure'),
                      body: t('users:resendActivationEmailBody'),
                      confirmCallout: () =>
                        userAction(data, 'resendActivationMail'),
                    },
                  }
                : {
                    text: t('users:sendPasswordReset'),
                    icon: 'paperPlane',
                    disabled: {
                      value: data.sso_user,
                      tooltip: {
                        tooltipContent: t(
                          'users:thisActionIsNotAllowedForSSOUser',
                        ),
                      },
                    },
                    confirmation: {
                      type: 'blue',
                      header: c('areYouSure'),
                      body: t('users:sendPasswordResetBody'),
                      confirmCallout: () => {
                        userAction(data, 'passwordReset')
                      },
                    },
                  },
              {
                text: t('users:deactivateUser'),
                icon: 'unfollow',
                confirmation: {
                  type: 'red',
                  header: c('areYouSure'),
                  body: t('users:deactivateUserBody', {
                    appName: data.admin_app.name,
                  }),
                  confirmCallout: () => userAction(data, 'inactive'),
                },
                disabled: {
                  value: isActionDisabled(user),
                  tooltip: {
                    tooltipContent: t('users:doNotHaveSuffientPrivilege'),
                  },
                },
              },
            ],
          },
          {
            children: t('users:editProfile'),
            onClick: () => editUserProfile(data),
          },
        ]}
      />
    )
  }

  const actionColumn = {
    name: '',
    label: '',
    noSort: true,
    cell: {
      children: (user: UserDetailsTableType) => {
        return renderActionColumn(user)
      },
    },
  }

  const tableConfig = [
    {
      name: 'app_name',
      label: 'app',
      cell: {
        children: (data: UserDetailsTableType) => (
          <span
            className={`flex align-items-center ${
              sortBy.prop === 'app_name' ? 'fw-semi-bold' : ''
            }`}
          >
            {data?.admin_app?.name}
          </span>
        ),
      },
      mainColumn: true,
    },
    {
      name: 'org_name',
      label: 'org',
      cell: {
        children: (data: UserDetailsTableType) => (
          <span
            className={`flex align-items-center ${
              sortBy.prop === 'org_name' ? 'fw-semi-bold' : ''
            }`}
          >
            {data?.org?.name}
          </span>
        ),
      },
    },
    ...(!isInactiveTab
      ? []
      : [
          {
            name: 'deactivated_at',
            label: c('deactivated'),
            cell: {
              children: (data: UserDetailsTableType) => {
                return (
                  <MdashCheck check={!!hasValue(data.deactivated_at)}>
                    <span
                      className={`flex align-items-center ${
                        sortBy.prop === 'deactivated_at' ? 'fw-semi-bold' : ''
                      }`}
                    >
                      {moment(data.deactivated_at).format('MMMM DD, YYYY')}
                    </span>
                    {data?.deactivated_by && (
                      <TrimText
                        customClass='fs-10 fc-purple'
                        limit={24}
                        text={data.deactivated_by}
                      />
                    )}
                  </MdashCheck>
                )
              },
            },
          },
        ]),
    {
      name: 'created_at',
      label: 'created',
      cell: {
        children: (data: UserDetailsTableType) => (
          <div>
            <span
              className={`flex align-items-center ${
                sortBy.prop === 'created_at' ? 'fw-semi-bold' : ''
              }`}
            >
              {moment(data?.created_at).format('MMMM DD, YYYY')}
            </span>
            {data?.created_by_name && (
              <TrimText
                customClass='fs-10 fc-purple'
                limit={24}
                text={data.created_by_name}
              />
            )}
          </div>
        ),
      },
    },
    {
      name: 'last_login',
      label: 'Last Login',
      cell: {
        children: (data: UserDetailsTableType) => (
          <>
            <div>
              <span
                className={`pat-mb-0.5 ${
                  sortBy.prop === 'last_login' ? 'fw-semi-bold' : ''
                }`}
              >
                <MdashCheck check={hasValue(data.last_login)}>
                  {moment(data.last_login).format('MMMM DD, YYYY')}
                </MdashCheck>
              </span>
              {hasValue(data.last_login) && (
                <span className='fs-10 fc-purple'>
                  {moment(data.last_login).format('hh:mm A')}
                </span>
              )}
            </div>
          </>
        ),
      },
    },
    {
      name: 'role_name',
      label: 'Role',
      cell: {
        children: (data: UserDetailsTableType) => (
          <span
            className={`flex align-items-center ${
              sortBy.prop === 'role_name' ? 'fw-semi-bold' : ''
            }`}
          >
            {data?.roles[0]?.name}
          </span>
        ),
      },
      noSort: true,
    },
    ...(isInactiveTab
      ? []
      : [
          {
            name: 'status',
            label: c('status'),
            noSort: true,
            cell: {
              children: (data: UserDetailsTableType) => {
                return (
                  <span>
                    {data?.status === 'onboarding' ? (
                      data?.invitation_expired ? (
                        <Tag color='light-gray'>{c('invitationExpired')}</Tag>
                      ) : (
                        <Tag color='orange'>{c('pending')}</Tag>
                      )
                    ) : (
                      <Tag color='green'>{data?.status}</Tag>
                    )}
                  </span>
                )
              },
            },
          },
        ]),
    actionColumn,
  ]

  return (
    <div>
      <StandardTable
        stickyTableConfig={{ right: 1 }}
        data={userDetails}
        config={tableConfig}
        loading={user ? isLoading : true}
        dataKey={'id'}
        hasData={hasData}
        hasMore={!!hasNextPage}
        successStatus={status === 'success'}
        noDataFields={{
          primaryText: t('noRecordsFound'),
          secondaryText: t('weCouldNotFindAnyRecordsUsersForSelectedCriteria'),
        }}
        tableId={'globalUsersTable'}
        sort={setSortBy}
        sortBy={sortBy}
        getData={fetchNextPage}
        customHeight={'auto'}
        customWidth={'100%'}
      />
      {isEditMode && selectedUser && (
        <UserFormParams
          isOpen={isEditMode}
          setIsOpen={setEditMode}
          isEditMode
          selectedUser={selectedUser}
          setSelectedUser={setSelectedUser}
          appForUserDetails={app}
          orgForUserDetails={org}
          refreshList={refresh}
          refetchUser={refetchUser}
        />
      )}
    </div>
  )
}
export default UserDetailsTable
