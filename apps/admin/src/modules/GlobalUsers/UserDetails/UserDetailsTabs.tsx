import React from 'react'
import { RouterTabs } from '@patterninc/react-ui'
import { NavLink, useLocation, useNavigate, useParams } from 'react-router-dom'

import { c } from '../../../common/services/TranslationService'

const UserDetailsTabs = (): React.JSX.Element => {
  const navigate = useNavigate()
  const pathname = useLocation().pathname
  const { id } = useParams<{ id: string }>()

  const navigateMobileTabs = (link: string) => {
    navigate(link)
  }

  return (
    <RouterTabs
      mobileConfig={[
        { label: c('active'), link: `/global_users/${id}/active` },
        { label: c('inactive'), link: `/global_users/${id}/inactive` },
      ]}
      navigate={navigateMobileTabs}
      currentPath={pathname}
    >
      <NavLink to={`/global_users/${id}/active`}>{c('active')}</NavLink>
      <NavLink to={`/global_users/${id}/inactive`}>{c('inactive')}</NavLink>
    </RouterTabs>
  )
}

export default UserDetailsTabs
