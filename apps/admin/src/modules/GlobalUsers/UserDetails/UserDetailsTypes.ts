export type UserType = {
  id: number
  name?: string
  email: string
  family_name: string
  given_name: string
}
export type UsersDetailsTableProps = {
  user?: UserType
  searchText?: string[]
  sortBy: { prop: string; flip: boolean }
  setSortBy: (sortObject: { activeColumn: string; direction: boolean }) => void
  isInactiveTab: boolean | undefined
  setTotalCount: (count: number) => void
  refetchUser: () => void
  filterParams?: {
    app_ids: string[]
    org_ids: string[]
  }
}
export type editparamstype = {
  params: {
    id?: string | number
    org_unit_ids?: string[]
    role_ids?: string[]
  }
  apiUrl: string
  editNameReqBody: {
    given_name: string
    family_name: string
  }
  apiUrlEditName: string
  isSsoUser: boolean | undefined
  status: string | undefined
}

export type UserDetailsTableType = {
  id: number
  status: string
  access_to_all_org_units: boolean
  deactivated_at: string | null
  created_at: string
  last_login: string
  created_by_name: string | null
  deactivated_by: string | null
  sso_user: boolean
  roles: {
    id: string
    name: string
  }[]
  org_units: {
    id: number
    name: string
    default: boolean
  }[]
  admin_app: AppType
  org: OrgType
  invitation_expired?: boolean
}

export type regionsType = {
  name: string
}

export type roleType = {
  id: string
  text?: string
  name?: string
  value: string
}
export type selectOptionType = {
  id: string
  value: string
  text: string
}

export type FormState = {
  email: string
  first_name: string
  last_name: string
  sso_user: boolean
  role: selectOptionType
  region: string[]
  allRegion: boolean
}
export type regionsArrType = {
  admin_apps_org_id?: number
  created_at?: string
  description?: string
  id: number
  name: string
  status?: string
  updated_at?: string
}

export type deleteuserparamstypes = {
  apiUrl: string
}

export type AppType = {
  id: number
  name: string
  code?: string
  metadata?: {
    require_org_unit_selection: boolean
    hide_select_all_org_units: boolean
    hide_access_to_all_org_units: boolean
  }
}
export type OrgType = {
  id: number
  name: string
}

export type paramsType = {
  id?: number
  status?: string
}
export type paramstypes = {
  params: paramsType
  apiUrl: string
}

export type userDetailType = {
  id: number
  name?: string
  email: string
  family_name: string
  given_name: string
}
