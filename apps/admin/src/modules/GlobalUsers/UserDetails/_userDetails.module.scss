.setDimension {
  display: grid;
  gap: 16px;
  grid-template-rows: 1fr;
  grid-template-columns: 250px minmax(0, 1fr);
}

.userDetailsInfoPane {
  background-color: #009af0;
  padding: 16px;
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 100px;
  height: 80px;
  width: 80px;
}

.containerStyle [class^='container-rui-xzc_UY'] {
  max-width: 300px;
  height: 100vh;
}
