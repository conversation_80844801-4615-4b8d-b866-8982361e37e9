'use client'

import React from 'react'
import { useState } from 'react'
import {
  <PERSON>Footer,
  getApiUrlPrefix,
  MultiSelect,
  Select,
  SideDrawer,
  Tag,
  TextInput,
  toast,
  useToggle,
} from '@patterninc/react-ui'
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query'

import { useTranslate } from '../../common/services/TranslationService'
import SecureAxios from '../../common/services/SecureAxios'
import { type appsOrgType } from './OrgTypes'
import styles from '../Roles/_roles.module.scss'
import { shouldIncludeApp } from '../../common/services/HelperService'

type AdminApp = {
  app_id: string
  id: number
  org_id: string
  status: string
}

type AppOrgLinkFormProps = {
  isOpen: boolean
  setIsOpen: (isOpen: boolean) => void
  setIsEdit?: (isEdit: boolean) => void
  orgDetails: { id: number; name: string; code: string }
  associateAppToOrg: AdminApp[]
  isEdit?: boolean
  selectedAppOrg?: appsOrgType
}

type Params = {
  apiUrl: string
  params: {
    org?: {
      id: number
      admin_app_ids: string[]
      code: string
      name: string
    }
    app_org?: {
      feature_ids: string[]
      org_type: string
    }
  }
  method: string
}

const AppOrgLinkForm = ({
  isOpen,
  setIsOpen,
  orgDetails,
  associateAppToOrg,
  isEdit,
  selectedAppOrg,
  setIsEdit,
}: AppOrgLinkFormProps) => {
  const { t } = useTranslate('orgs')
  const { t: c } = useTranslate('common')
  const queryClient = useQueryClient()
  const [selectedApp, setSelectedApp] = useState({ label: '', value: '' })
  const isTagInputEnabled = useToggle('tag_input_changes')

  const appsApiUrl = `${getApiUrlPrefix('adminczar')}/api/v1/apps`,
    featuresApiUrl = `${getApiUrlPrefix('adminczar')}/api/v1/apps/${isEdit ? selectedAppOrg?.app_id : selectedApp.value}/features`,
    ORG_TYPES = [
      { id: 1, name: 'Prospect' },
      { id: 2, name: 'Partner' },
      { id: 3, name: 'Internal' },
      { id: 4, name: 'Customer' },
    ]
  const [selectedFeatures, setSelectedFeatures] = useState<
    { id: string; name: string; secondaryOption?: string | React.ReactNode }[]
  >(() => {
    if (isEdit && selectedAppOrg?.features) {
      return selectedAppOrg.features.map((feature) => ({
        ...feature,
        secondaryOption:
          feature.status === 'inactive' &&
          (isTagInputEnabled ? (
            <Tag color='light-gray'>{c('inactive')}</Tag>
          ) : (
            <span className='fc-purple'>{c('inactive')}</span>
          )),
      }))
    }
    return []
  })

  const [selectedOrgType, setSelectedOrgType] = useState<{
    id: number | ''
    name: string
  }>(() =>
    isEdit && selectedAppOrg?.org_type
      ? (ORG_TYPES.find(
          (type) =>
            type.name.toLowerCase() === selectedAppOrg?.org_type?.toLowerCase(),
        ) ?? { id: '', name: '' })
      : { id: '', name: '' },
  )

  const { data: allApps, isFetching } = useQuery({
    queryKey: [appsApiUrl],
    queryFn: async ({ signal }) => {
      const response = await SecureAxios.get(appsApiUrl, {
        params: { auto_provision_orgs: true },
        signal,
      })
      return response.data.data
    },
  })

  const associatedAppIds = new Set(
    associateAppToOrg.map((admin_app) => admin_app.app_id),
  )

  const options =
    allApps
      ?.filter(
        (app: { id: string; name: string; code: string }) =>
          !associatedAppIds.has(app.id) &&
          !shouldIncludeApp({ code: app.code }),
      )
      .map((app: { id: string; name: string }) => ({
        label: app.name,
        value: app.id,
      })) || []

  const { data: featuresResponse, isFetching: isFeatureFetching } = useQuery({
    queryKey: [featuresApiUrl],
    queryFn: async ({ signal }) => {
      const response = await SecureAxios.get(featuresApiUrl, {
        params: {
          filter: {
            status: {
              in: 'active',
            },
          },
        },
        signal,
      })
      return response.data.data
    },
    enabled: !!(selectedApp.value || (isEdit && selectedAppOrg?.app_id)),
  })

  const featuresData = featuresResponse?.map(
    (feature: { id: string; name: string }) => ({
      id: feature.id,
      name: feature.name,
    }),
  )

  const closeDrawer = () => {
    setIsOpen(false)
    setIsEdit?.(false)
  }

  const AppOrgMutation = useMutation({
    mutationFn: (AppOrgLinkParams: Params) => {
      const { apiUrl, params, method } = AppOrgLinkParams
      return SecureAxios[method === 'POST' ? 'post' : 'put'](apiUrl, {
        ...params,
      })
    },
    onSuccess: () => {
      toast({
        message: !isEdit
          ? t('appOrgAssociationBody', { orgName: orgDetails.name })
          : t('appOrgFeatureOrgTypeBody', { orgName: orgDetails.name }),
        type: 'success',
      })
      closeDrawer()
      const appsOrgsApiUrl = `${getApiUrlPrefix('adminczar')}/api/v2/orgs/${orgDetails.id}`
      queryClient.invalidateQueries({ queryKey: [appsOrgsApiUrl] })
    },
    onError: (error: { data?: { message?: string } }) => {
      toast({
        message: error?.data?.message || c('somethingWentWrongPleaseTryAgain'),
        type: 'error',
      })
    },
  })

  const createAppOrgLinkWithFeatures = () => {
    AppOrgMutation.mutate({
      apiUrl: `${getApiUrlPrefix('adminczar')}/api/v1/apps/${selectedApp.value}/orgs/${orgDetails.id}/apps_orgs`,
      params: {
        app_org: {
          feature_ids: selectedFeatures.map((feature) => feature.id),
          org_type: selectedOrgType.name,
        },
      },
      method: 'POST',
    })
  }

  const updateAppOrgLink = () => {
    AppOrgMutation.mutate({
      apiUrl: `${getApiUrlPrefix('adminczar')}/api/v1/apps/${selectedAppOrg?.app_id}/orgs/${selectedAppOrg?.org_id}/apps_orgs/${selectedAppOrg?.id}`,
      params: {
        app_org: {
          feature_ids: selectedFeatures.map((feature) => feature.id),
          org_type: selectedOrgType.name,
        },
      },
      method: 'PUT',
    })
  }
  const savingForm = AppOrgMutation.isPending

  const isFormValid = isEdit
    ? selectedOrgType.id !== ''
    : !!selectedApp.value && selectedOrgType.id !== ''

  return (
    <SideDrawer
      isOpen={isOpen}
      closeCallout={() => {
        closeDrawer()
      }}
      headerContent={isEdit ? t('Edit App-Org') : t('addApp')}
      footerContent={
        <FormFooter
          cancelButtonProps={{
            onClick: () => {
              closeDrawer()
            },
          }}
          saveButtonProps={{
            disabled: !isFormValid || savingForm,
            onClick: () =>
              isEdit ? updateAppOrgLink() : createAppOrgLinkWithFeatures(),
          }}
        />
      }
    >
      <div className='flex flex-direction-column pat-gap-4'>
        <TextInput
          labelText={t('orgName')}
          required
          type='text'
          value={orgDetails.name}
          callout={() => null}
          disabled
        />

        {!isEdit ? (
          <Select
            selectedItem={selectedApp}
            options={options}
            labelKeyName={'label'}
            optionKeyName={'value'}
            onChange={(selectedItem) => setSelectedApp(selectedItem)}
            required
            labelProps={{ label: t('selectApp') }}
            loading={isFetching}
            disabled={!options.length || isEdit}
            placeholder={
              !options.length
                ? t('noAppsFoundPlaceholder')
                : t('selectAppsPlaceholder')
            }
          />
        ) : (
          []
        )}

        <>
          {!(selectedApp.value || isEdit) && (
            <div className={`${styles.errorTextStyle}`}>
              {t('pleaseSelectAppToEnableFeaturesSelection')}
            </div>
          )}
          <MultiSelect
            options={featuresData || []}
            selectedOptions={selectedFeatures || []}
            labelKey='name'
            callout={(selectedList) => {
              setSelectedFeatures(selectedList)
            }}
            selectPlaceholder={t('selectFeatures')}
            formLabelProps={{
              label: t('features'),
            }}
            loading={isFeatureFetching}
            disabled={!selectedApp.value && !isEdit}
          />
          <Select
            options={ORG_TYPES}
            required
            optionKeyName={'id'}
            labelKeyName={'name'}
            onChange={(selectedItem) => setSelectedOrgType(selectedItem)}
            selectedItem={selectedOrgType}
            labelProps={{
              label: t('orgType'),
            }}
          />
        </>
      </div>
    </SideDrawer>
  )
}

export default AppOrgLinkForm
