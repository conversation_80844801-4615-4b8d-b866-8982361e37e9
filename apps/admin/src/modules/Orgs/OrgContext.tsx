import React, { createContext, useReducer } from 'react'

import { tryLocalStorageParse } from '../../common/services/HelperService'

export type AppFilterType = {
  id: string
  name: string
}

export type filterType = {
  apps: AppFilterType[]
}

const defaultFilterState: filterType = {
  apps: [],
}

const initialState = (): filterType => {
  const storedState = tryLocalStorageParse('org_filter')
  if (storedState) {
    return {
      ...defaultFilterState,
      ...storedState,
    }
  }
  return defaultFilterState
}

type OrgContextType = {
  state: filterType
  dispatch: React.Dispatch<{
    type: string
    payload?: { filters?: filterType }
  }>
}

export const OrgContext = createContext<OrgContextType>({
  state: defaultFilterState,
  dispatch: () => null,
})

type OrgProviderProps = {
  children: React.ReactNode
}

const reducer = (
  state: filterType,
  action: { type: string; payload?: { filters?: filterType } },
): filterType => {
  switch (action.type) {
    case 'UPDATE_FILTER':
      if (action.payload?.filters) {
        localStorage.setItem(
          'org_filter',
          JSON.stringify(action.payload.filters),
        )
        return { ...action.payload.filters }
      }
      return state
    case 'RESET_FILTER':
      localStorage.removeItem('org_filter')
      return defaultFilterState
    default:
      return { ...state }
  }
}

export const OrgProvider = ({
  children,
}: OrgProviderProps): React.JSX.Element => {
  const [state, dispatch] = useReducer(reducer, initialState())

  return (
    <OrgContext.Provider value={{ state, dispatch }}>
      {children}
    </OrgContext.Provider>
  )
}
