import { useContext, useEffect, useMemo, useState } from 'react'
import { useLocation, useParams } from 'react-router-dom'
import {
  Button,
  ButtonGroup,
  getApiUrlPrefix,
  hasValue,
  Icon,
  InformationPane,
  Mdash,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  PageFooter,
  PageHeader,
  SideDrawer,
  type SortColumnProps,
  standardSortParams,
  StandardTable,
  Tag,
  toast,
  TrimText,
  useToggle,
} from '@patterninc/react-ui'
import {
  useInfiniteQuery,
  useMutation,
  useQuery,
  useQueryClient,
} from '@tanstack/react-query'
import moment from 'moment'

import { ThemeContext } from '../../Context'
import { useTranslate } from '../../common/services/TranslationService'
import SecureAxios from '../../common/services/SecureAxios'
import { type appsOrgType } from './OrgTypes'
import styles from './_org.module.scss'
import OrgDetailsTabs from './OrgDetailsTabs'
import { hasWritePermission } from '../../Auth/route-permissions'
import OrgForm from './OrgsForm'
import AppOrgLinkForm from './AppOrgLinkForm'
import userDetailStyle from '../GlobalUsers/UserDetails/_userDetails.module.scss'
import { shouldIncludeApp } from '../../common/services/HelperService'

type BulkUpdateParams = {
  apiUrl: string
  params: {
    org: {
      admin_apps_orgs_ids: number[]
      status: string
    }
  }
  method: 'get' | 'post' | 'put' | 'delete'
  isBulkUpdate?: boolean
}

type FeatureDataType = {
  id: string
  name: string
  status: string
  description?: string
}

const OrgDetails = ({
  isInactiveTab,
}: {
  isInactiveTab?: boolean
}): React.JSX.Element => {
  const queryClient = useQueryClient()
  const { updateBreadcrumbs, privileges } = useContext(ThemeContext),
    { pathname } = useLocation(),
    { id } = useParams<{ id: string }>(),
    [searchText, setSearchText] = useState<string>(''),
    { t } = useTranslate('orgs'),
    { t: c } = useTranslate('common'),
    { t: h } = useTranslate('eventHistory'),
    [isEditOrg, setIsEditOrg] = useState(false),
    [reloadOrg, setReloadOrg] = useState(false),
    [isAppFormOpen, setIsAppFormOpen] = useState(false),
    [isEditAppOrg, setIsEditAppOrg] = useState(false),
    [selectedAppOrg, setSelectedAppOrg] = useState<appsOrgType | null>(null)
  const [sortBy, setSort] = useState({
    prop: isInactiveTab ? 'deactivated_at' : 'created_at',
    flip: false,
  })
  const isTagInputEnabled = useToggle('tag_input_changes')
  const searchInputHandler = (query: string) => {
    setSearchText(query)
  }
  useEffect(() => {
    setSort((prevState) => ({
      ...prevState,
      prop: isInactiveTab ? 'deactivated_at' : 'created_at',
      flip: false,
    }))
  }, [isInactiveTab])

  const setSortBy: SortColumnProps['sorter'] = (sortObj) => {
    setSort({
      prop: sortObj.activeColumn,
      flip: sortObj.direction,
    })
  }

  const [isPrevTabInactive, setIsPrevTabInactive] = useState(isInactiveTab)

  useEffect(() => {
    setIsPrevTabInactive(isInactiveTab)
  }, [isInactiveTab])

  const isSortSetForTab = useMemo(() => {
    const isTabChanged =
      isPrevTabInactive !== isInactiveTab &&
      isInactiveTab &&
      sortBy.prop === 'deactivated_at'
    const isTabUnchanged = isPrevTabInactive === isInactiveTab

    return isTabChanged || isTabUnchanged
  }, [isPrevTabInactive, isInactiveTab, sortBy.prop])

  const appsOrgsApiUrl = `${getApiUrlPrefix('adminczar')}/api/v2/orgs/${id}`,
    orgApiUrl = `${getApiUrlPrefix('adminczar')}/api/v1/orgs/${id}`,
    {
      data: appsOrgsPaginatedData,
      status,
      fetchNextPage,
      hasNextPage,
      isLoading,
    } = useInfiniteQuery({
      queryKey: [appsOrgsApiUrl, sortBy, searchText, isInactiveTab],
      queryFn: async ({ pageParam = 1, signal }) => {
        const data = await SecureAxios.get(appsOrgsApiUrl, {
          params: {
            page: pageParam ?? 1,
            per_page: 20,
            sort: standardSortParams(sortBy),
            ...(searchText ? { search_for: searchText } : {}),
            filter: {
              status: {
                in: isInactiveTab
                  ? 'inactive,inactive_pending'
                  : 'active,active_pending',
              },
            },
          },
          signal,
        })
        return data
      },
      enabled: isSortSetForTab,
      initialPageParam: 1,
      gcTime: 1000 * 60 * 60 * 8,
      getNextPageParam: (previousResponse) => {
        return previousResponse?.data?.pagination?.last_page
          ? undefined
          : previousResponse?.data?.pagination?.next_page
      },
    }),
    { data: orgsData } = useQuery({
      queryKey: [id, orgApiUrl, reloadOrg],
      queryFn: async ({ signal }) => {
        const response = await SecureAxios.get(orgApiUrl, { signal })
        return response.data
      },
    })

  const { data: associatedApps } = useInfiniteQuery({
    queryKey: [appsOrgsApiUrl],
    queryFn: async ({ pageParam = 1, signal }) => {
      const data = await SecureAxios.get(appsOrgsApiUrl, {
        params: {
          page: pageParam ?? 1,
          per_page: 20,
          sort: standardSortParams(sortBy),
        },
        signal,
      })
      return data
    },
    initialPageParam: 1,
    gcTime: 1000 * 60 * 60 * 8,
    getNextPageParam: (previousResponse) => {
      return previousResponse?.data?.pagination?.last_page
        ? undefined
        : previousResponse?.data?.pagination?.next_page
    },
  })

  const associatedAppsData = useMemo(
    () =>
      associatedApps
        ? associatedApps?.pages?.flatMap((page) => {
            return page?.data?.data
          })
        : [],
    [associatedApps],
  )

  const appsOrgsData = useMemo(
    () =>
      appsOrgsPaginatedData
        ? appsOrgsPaginatedData?.pages
            ?.flatMap((page) => page?.data?.data)
            ?.map((data) => {
              return {
                ...data,
                isEditDisabled: shouldIncludeApp(data.admin_app),
              }
            })
        : [],
    [appsOrgsPaginatedData],
  )

  const hasData = !!(status === 'success' && appsOrgsData?.length)

  useEffect(() => {
    if (orgsData) {
      updateBreadcrumbs({
        name: `${t('manage')} ${orgsData?.name ?? 'Org'}`,
        link: pathname,
        changeType: 'tab',
      })
    }
  }, [updateBreadcrumbs, pathname, t, orgsData?.name, orgsData])

  const canWriteOrgs = useMemo(
    () => hasWritePermission('orgs', privileges),
    [privileges],
  )

  const config = useMemo(
    () => [
      {
        name: 'features',
        label: t('featureName'),
        cell: {
          children: (data: FeatureDataType) => (
            <span className='flex full-width justify-content-between'>
              <span>{data?.name}</span>
              {data?.status === 'inactive' &&
                (isTagInputEnabled ? (
                  <Tag color='light-gray'>{t('inactive')}</Tag>
                ) : (
                  <span className='fc-purple'>{t('inactive')}</span>
                ))}
            </span>
          ),
        },
      },
      {
        name: 'description',
        label: t('featureDescription'),
        cell: {
          children: (data: FeatureDataType) => (
            <span>{data?.description ?? <Mdash />}</span>
          ),
        },
      },
    ],
    [t, isTagInputEnabled],
  )

  const [activeFeatures, setActiveFeatures] = useState<
    FeatureDataType[] | null
  >(null)

  const showFeaturesSideDrawer = () => {
    return (
      <SideDrawer
        closeCallout={() => setActiveFeatures(null)}
        isOpen={!!activeFeatures}
        headerContent={t('features')}
        size='md'
      >
        <StandardTable
          data={activeFeatures ?? []}
          config={config}
          dataKey='id'
          hasData={!!activeFeatures && activeFeatures.length > 0}
          noDataFields={{
            primaryText: t('noFeatures'),
          }}
          successStatus
          tableId='features_table'
          hasMore={false}
          loading={false}
          getData={() => null}
          customWidth={'auto'}
          customHeight={'auto'}
          noSort
        />
      </SideDrawer>
    )
  }

  const appsOrgsConfig = [
    {
      name: 'app_name',
      label: t('apps'),
      cell: {
        children: (data: appsOrgType) => (
          <span
            className={`flex align-items-center ${
              sortBy.prop === 'app_name' ? 'fw-semi-bold' : ''
            }`}
          >
            {data?.admin_app?.name}
          </span>
        ),
      },
      tooltip: {
        content: <span>{t('appsTooltip')}</span>,
      },
      mainColumn: true,
    },
    {
      name: 'user_count',
      label: t('usersCount'),
      cell: {
        children: (data: appsOrgType) => (
          <span
            className={`flex align-items-center ${
              sortBy.prop === 'user_count' ? 'fw-semi-bold' : ''
            }`}
          >
            {data?.user_count}
          </span>
        ),
      },
      tooltip: {
        content: <span>{t('usersCountTooltip')}</span>,
      },
    },
    ...(!isInactiveTab
      ? []
      : [
          {
            name: 'deactivated_at',
            label: c('deactivated'),
            cell: {
              children: (data: appsOrgType) => {
                return (
                  <MdashCheck check={!!hasValue(data.deactivated_at)}>
                    <span
                      className={`flex align-items-center ${
                        sortBy.prop === 'deactivated_at' ? 'fw-semi-bold' : ''
                      }`}
                    >
                      {moment(data.deactivated_at).format('MMMM DD, YYYY')}
                    </span>
                    {data?.last_deactivated_by_name && (
                      <TrimText
                        customClass='fs-10 fc-purple'
                        limit={24}
                        text={data.last_deactivated_by_name}
                      />
                    )}
                  </MdashCheck>
                )
              },
            },
          },
        ]),
    {
      name: 'created_at',
      label: t('createdAt'),
      cell: {
        children: (data: appsOrgType) => (
          <div>
            <span
              className={`flex align-items-center ${
                sortBy.prop === 'created_at' ? 'fw-semi-bold' : ''
              }`}
            >
              {moment(data.created_at).utc().format('MMMM DD, YYYY')}
            </span>
            {data?.created_by_name && (
              <TrimText
                customClass='fs-10 fc-purple'
                limit={24}
                text={data.created_by_name}
              />
            )}
          </div>
        ),
      },
    },
    {
      name: 'status',
      label: t('status'),
      cell: {
        children: (data: appsOrgType) => (
          <span
            className={`flex align-items-center ${
              sortBy.prop === 'status' ? 'fw-semi-bold' : ''
            }`}
          >
            {['active_pending', 'inactive_pending'].includes(data?.status) ? (
              <Tag color='gray'>
                {data?.status
                  .split('_')
                  ?.map(
                    (word) =>
                      word.charAt(0).toUpperCase() +
                      word.slice(1)?.toLowerCase(),
                  )
                  .join(' ')}
              </Tag>
            ) : data?.status === 'active' ? (
              <Tag color='green'>{data?.status}</Tag>
            ) : (
              <Tag color='orange'>{data?.status}</Tag>
            )}
          </span>
        ),
      },
    },
    {
      name: 'org_type',
      label: t('orgType'),
      cell: {
        children: (data: appsOrgType) => (
          <span
            className={`flex align-items-center ${
              sortBy.prop === 'org_type' ? 'fw-semi-bold' : ''
            }`}
          >
            {data?.org_type ? (
              <Tag color='pink'>{data.org_type}</Tag>
            ) : (
              <Mdash />
            )}
          </span>
        ),
      },
      noSort: true,
    },
    {
      name: 'features',
      label: t('features'),
      cell: {
        children: (data: appsOrgType) => (
          <span>
            {Array.isArray(data?.features) && data.features.length > 0 ? (
              <Button
                as='link'
                styleType='text-blue'
                routerComponent='link'
                onClick={() => setActiveFeatures(data.features || null)}
              >
                {t('viewFeatures')}
              </Button>
            ) : (
              <Mdash />
            )}
          </span>
        ),
      },
      noSort: true,
    },
    ...(canWriteOrgs
      ? [
          {
            name: '',
            label: '',
            noSort: true,
            cell: {
              children: (data: appsOrgType) => {
                return isInactiveTab ? (
                  <Button
                    as='button'
                    onClick={() => toggleAppStatus(data.id)}
                    disabled={data.isEditDisabled}
                    tooltip={{
                      tooltipContent: data.isEditDisabled
                        ? t('youDoNotHaveAccessToEditAppOrg')
                        : '',
                    }}
                  >
                    {t('reactivate')}
                  </Button>
                ) : (
                  <ButtonGroup
                    buttons={[
                      {
                        icon: 'archive',
                        as: 'confirmation',
                        destructive: true,
                        disabled: data.isEditDisabled,
                        confirmation: {
                          type: 'red',
                          header: c('areYouSure'),
                          body: t('appOrgDeactivationBody', {
                            orgName: orgsData?.name,
                          }),
                          confirmCallout: () => {
                            toggleAppStatus(data.id)
                          },
                        },
                        tooltip: {
                          tooltipContent: data.isEditDisabled
                            ? ''
                            : t('deactivateAppOrg'),
                        },
                      },
                      {
                        children: h('Edit'),
                        onClick: () => {
                          setIsEditAppOrg(true)
                          setSelectedAppOrg(data)
                        },
                        disabled: data.isEditDisabled,
                        tooltip: {
                          tooltipContent: data.isEditDisabled
                            ? t('youDoNotHaveAccessToEditAppOrg')
                            : '',
                        },
                      },
                    ]}
                  />
                )
              },
            },
          },
        ]
      : []),
  ]

  const useUpdateMutation = (
    onSuccess: (response: {
      data: { data: { success_count: number; failure_count: number } }
    }) => void,
  ) => {
    return useMutation({
      mutationFn: ({ apiUrl, params, method }: BulkUpdateParams) =>
        SecureAxios[method as 'get' | 'post' | 'put' | 'delete'](apiUrl, {
          ...params,
        }),
      onSuccess,
      onError: (error: { data?: { message?: string } }) => {
        toast({
          message:
            error?.data?.message || c('somethingWentWrongPleaseTryAgain'),
          type: 'error',
        })
      },
    })
  }

  const updateMutation = useUpdateMutation(() => {
    toast({
      message: isInactiveTab
        ? t('appOrgActivationSuccess')
        : t('appOrgDeactivationSuccess'),
      type: 'success',
    })
    queryClient.invalidateQueries({ queryKey: [appsOrgsApiUrl] })
  })

  const toggleAppStatus = (appId: number) => {
    updateMutation.mutate({
      method: 'put',
      apiUrl: `${getApiUrlPrefix('adminczar')}/api/v2/orgs/${id}/bulk_update`,
      params: {
        org: {
          admin_apps_orgs_ids: [appId],
          status: isInactiveTab ? 'active' : 'inactive',
        },
      },
    })
  }

  const totalApps = useMemo(() => appsOrgsData?.length, [appsOrgsData?.length])

  return (
    <div className={userDetailStyle.setDimension}>
      <div className={styles.infoPane}>
        <InformationPane>
          <InformationPane.CustomSection
            children={
              <div className='flex flex-direction-column align-items-center'>
                <span className={styles.orgIcon}>
                  <Icon icon='help' color='white' iconSize='32px' />
                </span>
                <span className='pat-mt-4'>
                  <Button
                    as='button'
                    onClick={() => {
                      setIsEditOrg(true)
                    }}
                  >
                    {t('editOrg')}
                  </Button>
                </span>
              </div>
            }
          />
          <InformationPane.Divider />

          <InformationPane.Section
            data={[
              {
                label: t('orgName'),
                data: orgsData?.name ?? <Mdash />,
                check: true,
              },
              {
                label: t('orgCode'),
                data: orgsData?.code ?? <Mdash />,
                check: true,
              },
            ]}
          />
        </InformationPane>
      </div>
      <div className='full-width'>
        <PageHeader
          header={{
            name: isInactiveTab
              ? `${t('totalInactiveApps')}`
              : `${t('totalActiveApps')}`,
            value: totalApps,
          }}
          search={{
            placeholder: t('searchApps'),
            value: searchText,
            onChange: searchInputHandler,
          }}
        />
        <OrgDetailsTabs />

        <StandardTable
          stickyTableConfig={{ right: 1 }}
          data={appsOrgsData ?? []}
          config={appsOrgsConfig}
          loading={isLoading || updateMutation.isPending}
          dataKey='app_id'
          hasData={hasData}
          hasMore={!!hasNextPage}
          successStatus={status === 'success'}
          noDataFields={{
            primaryText: t('noAppsFound'),
            secondaryText: t('weCouldNotFindAnyAppsForTheSelectedCriteria'),
          }}
          customWidth={'auto'}
          customHeight={'auto'}
          tableId={'apps-orgs-table'}
          sort={setSortBy}
          sortBy={sortBy}
          getData={fetchNextPage}
        />

        <PageFooter
          rightSection={
            canWriteOrgs
              ? isInactiveTab
                ? []
                : [
                    {
                      styleType: 'primary-blue',
                      children: t('addApp'),
                      onClick: () => setIsAppFormOpen(true),
                      disabled: !canWriteOrgs,
                      as: 'button',
                      type: 'button',
                    },
                  ]
              : []
          }
        />
      </div>
      {!!orgsData && (
        <OrgForm
          isOrgFormOpen={isEditOrg}
          setIsOrgFormOpen={setIsEditOrg}
          action={'edit'}
          refreshData={setReloadOrg}
          selectedOrg={orgsData}
        />
      )}
      {(isAppFormOpen || isEditAppOrg) && (
        <AppOrgLinkForm
          isOpen={isAppFormOpen || isEditAppOrg}
          setIsOpen={setIsAppFormOpen}
          orgDetails={orgsData}
          associateAppToOrg={associatedAppsData}
          isEdit={isEditAppOrg}
          setIsEdit={setIsEditAppOrg}
          selectedAppOrg={selectedAppOrg || undefined}
        />
      )}
      {activeFeatures && showFeaturesSideDrawer()}
    </div>
  )
}

export default OrgDetails
