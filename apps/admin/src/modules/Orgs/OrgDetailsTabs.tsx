import React from 'react'
import { RouterTabs } from '@patterninc/react-ui'
import { NavLink, useLocation, useNavigate, useParams } from 'react-router-dom'

import { c } from '../../common/services/TranslationService'

const OrgDetailsTabs = (): React.JSX.Element => {
  const navigate = useNavigate()
  const pathname = useLocation().pathname
  const { id } = useParams<{ id: string }>()

  const navigateMobileTabs = (link: string) => {
    navigate(link)
  }

  return (
    <RouterTabs
      mobileConfig={[
        { label: c('active'), link: `/orgs/${id}/active` },
        { label: c('inactive'), link: `/orgs/${id}/inactive` },
      ]}
      navigate={navigateMobileTabs}
      currentPath={pathname}
    >
      <NavLink to={`/orgs/${id}/active`}>{c('active')}</NavLink>
      <NavLink to={`/orgs/${id}/inactive`}>{c('inactive')}</NavLink>
    </RouterTabs>
  )
}

export default OrgDetailsTabs
