export type OrgType = {
  id: string
  name: string
  code: string
  status: string
  auth0_id: string
  created_at: string
  partner_code: string | null
  accessible_apps?: adminApp[]
  accessible_apps_count?: number
  created_by_name?: string
}

export type adminApp = {
  id: string
  name: string
  code: string
}

export type OrgFormState = {
  orgName: string
  orgCode: string
  partnerCode: string
}

export type ParamsType = {
  method: string
  apiUrl: string
  params: Record<string, unknown>
}

export type appsOrgType = {
  id: number
  org_id: string
  app_id: string
  status: string
  created_at: string
  admin_app: {
    id: string
    name: string
    code: string
  }
  org_type?: string
  deactivated_at: string
  created_by_name: string
  last_deactivated_by_name: string
  user_count?: string
  isEditDisabled?: boolean
  features?:
    | { id: string; name: string; description?: string; status: string }[]
    | undefined
}
