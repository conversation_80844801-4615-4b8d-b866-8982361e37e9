import React, {
  useCallback,
  useContext,
  useEffect,
  useMemo,
  useState,
} from 'react'
import {
  Button,
  type Filter,
  getApiUrlPrefix,
  Icon,
  PageFooter,
  PageHeader,
  type SortColumnProps,
  standardSortParams,
  StandardTable,
  Tooltip,
  TrimText,
} from '@patterninc/react-ui'
import { useInfiniteQuery } from '@tanstack/react-query'
import moment from 'moment'
import { useNavigate } from 'react-router-dom'

import { ThemeContext } from '../../Context'
import SecureAxios from '../../common/services/SecureAxios'
import OrgsTabs from './OrgsTabs'
import { c, useTranslate } from '../../common/services/TranslationService'
import { type adminApp, type OrgType } from './OrgTypes'
import { hasWritePermission } from '../../Auth/route-permissions'
import OrgForm from './OrgsForm'
import { type filterType, OrgContext } from './OrgContext'
import styles from './_org.module.scss'

const Orgs = ({
  isInactiveTab,
}: {
  isInactiveTab?: boolean
}): React.JSX.Element => {
  const { updateBreadcrumbs, privileges, apps } = useContext(ThemeContext),
    { state: orgState, dispatch: orgDispatch } = useContext(OrgContext),
    { t } = useTranslate('orgs'),
    [isOrgFormOpen, setIsOrgFormOpen] = useState<boolean>(false),
    [action, setAction] = useState<string>(''),
    navigate = useNavigate(),
    [filterStateCopy, setFilterStateCopy] = useState(orgState),
    [filtersCount, setFiltersCount] = useState(0)

  useEffect(() => {
    updateBreadcrumbs({
      name: c('orgs'),
      link: '/orgs',
      changeType: 'rootLevel',
    })
  }, [updateBreadcrumbs])

  const [sortBy, setSort] = useState({
      prop: 'created_at',
      flip: false,
    }),
    [searchText, setSearchText] = useState<string>(''),
    [reload, setReload] = useState(false)

  const searchInputHandler = (query: string) => {
    setSearchText(query)
  }

  const setSortBy: SortColumnProps['sorter'] = (sortObj) => {
    setSort({
      prop: sortObj.activeColumn,
      flip: sortObj.direction,
    })
  }

  const createOrg = () => {
    setIsOrgFormOpen(true)
    setAction('create')
  }

  // Filter functions
  const resetFilter = useCallback(() => {
    orgDispatch({ type: 'RESET_FILTER' })
  }, [orgDispatch])

  const updateFilters = useCallback(
    (filter: filterType) => {
      orgDispatch({
        type: 'UPDATE_FILTER',
        payload: {
          filters: filter,
        },
      })
    },
    [orgDispatch],
  )

  const difference = useCallback((filterState: filterType) => {
    if (!filterState) {
      return 0
    }

    let count = 0
    if (filterState.apps.length !== 0) {
      count += 1
    }
    return count
  }, [])

  useEffect(() => {
    setFilterStateCopy(orgState)
    const count = difference(orgState)
    setFiltersCount(count)
  }, [orgState, difference])

  const updateSelect = useCallback(
    (...params: unknown[]) => {
      const stateAttr = params[0] as string
      const value = params[1]

      if (stateAttr === 'apps') {
        const processedApps = apps.filter((app) =>
          (value as string[]).includes(app.name),
        )

        setFilterStateCopy({
          ...filterStateCopy,
          [stateAttr]: processedApps || [],
        })
      } else {
        setFilterStateCopy({
          ...filterStateCopy,
          [stateAttr]: value || [],
        })
      }
    },
    [apps, filterStateCopy, setFilterStateCopy],
  )

  const appsData = useMemo(() => {
    return apps.map((app) => {
      const handleSelectOnly = () => {
        const singleAppSelection = [app.name]
        updateSelect('apps', singleAppSelection)
      }

      return {
        id: app.id,
        name: app.name,
        secondaryOption: (
          <span className={styles.appHoverText} onClick={handleSelectOnly}>
            {t('selectOnly')}
          </span>
        ),
      }
    })
  }, [apps, updateSelect, t])

  const cancelCallout = () => {
    setFilterStateCopy(orgState)
  }

  const applyFilterCallout = () => {
    updateFilters({ ...filterStateCopy })
  }

  const appIds = useMemo(() => {
    return orgState?.apps.map((app) => app.id)
  }, [orgState?.apps])

  const filterParams = useMemo(() => {
    return {
      ...(orgState?.apps?.length !== 0
        ? {
            app_ids: appIds,
          }
        : {}),
    }
  }, [orgState?.apps?.length, appIds])

  // Orgs Table API
  const apiUrl = `${getApiUrlPrefix('adminczar')}/api/v2/orgs`,
    {
      data: orgsPaginatedData,
      status,
      fetchNextPage,
      hasNextPage,
      isLoading,
    } = useInfiniteQuery({
      queryKey: [
        apiUrl,
        sortBy,
        reload,
        searchText,
        isInactiveTab,
        filterParams,
      ],
      queryFn: async ({ pageParam = 1, signal }) => {
        const data = await SecureAxios.get(apiUrl, {
          params: {
            page: pageParam ?? 1,
            per_page: 20,
            sort: standardSortParams(sortBy),
            ...(searchText ? { search_for: searchText } : {}),
            status: isInactiveTab ? 'inactive' : 'active',
            ...filterParams,
          },
          signal,
        })
        setReload(false)
        return data
      },
      initialPageParam: 1,
      gcTime: 1000 * 60 * 60 * 8,
      getNextPageParam: (previousResponse) => {
        return previousResponse?.data?.pagination?.last_page
          ? undefined
          : previousResponse?.data?.pagination?.next_page
      },
    })

  const orgsData = useMemo(
    () =>
      orgsPaginatedData
        ? orgsPaginatedData?.pages?.flatMap((page) => {
            return page?.data?.data
          })
        : [],
    [orgsPaginatedData],
  )

  const totalOrgs = useMemo(
    () => orgsPaginatedData?.pages[0]?.data?.pagination?.count,

    [orgsPaginatedData],
  )

  const hasData = !!(status === 'success' && orgsData?.length)

  const canWriteOrgs = useMemo(
    () => hasWritePermission('orgs', privileges),
    [privileges],
  )

  // Filter configuration
  const appliedFilters = useMemo((): React.ComponentProps<
    typeof Filter
  >['filterStates'] => {
    return {
      apps: {
        type: 'multi-select',
        options: appsData,
        stateName: 'apps',
        labelKey: 'name',
        formLabelProps: {
          label: t('appAccess'),
          tooltip: {
            tooltipContent: t('appAccessTooltip'),
          },
        },
        selectPlaceholder: `-- ${t('selectApps')} --`,
        searchBarProps: {
          placeholder: t('searchApps'),
          value: '',
        },
        selectedOptions: filterStateCopy.apps ?? [],
      },
    }
  }, [appsData, t, filterStateCopy.apps])

  const orgsConfig = [
    {
      name: 'name',
      label: t('orgName'),
      cell: {
        children: (data: OrgType) => (
          <span
            className={`flex align-items-center ${
              sortBy.prop === 'name' ? 'fw-semi-bold' : ''
            }`}
          >
            {data?.name}
          </span>
        ),
      },
      mainColumn: true,
    },
    {
      name: 'accessible_apps_count',
      label: t('apps'),
      cell: {
        children: (data: OrgType) => {
          const apps =
            data.accessible_apps?.map((app: adminApp) => app.name) ?? []
          const displayApps =
            apps.length > 4 ? (
              <>
                {apps.slice(0, 4).join(', ')}
                <Tooltip
                  position='bottom'
                  noPadding
                  maxWidth='300'
                  tooltipContent={
                    <div className='pat-p-2 flex flex-direction-column'>
                      {apps.slice(4).map((item: string) => (
                        <span className='fs-12'>{item}</span>
                      ))}
                    </div>
                  }
                >
                  ..+{apps.length - 4}
                </Tooltip>
              </>
            ) : (
              apps.join(', ')
            )

          return (
            <span
              className={`flex align-items-center ${
                sortBy.prop === 'accessible_apps_count' ? 'fw-semi-bold' : ''
              }`}
            >
              {displayApps}
            </span>
          )
        },
      },
      tooltip: {
        content: <span>{t('appsTooltip')}</span>,
      },
    },
    {
      name: 'created_at',
      label: t('createdAt'),
      cell: {
        children: (data: OrgType) => (
          <div>
            <span
              className={`flex align-items-center ${
                sortBy.prop === 'created_at' ? 'fw-semi-bold' : ''
              }`}
            >
              {moment(data.created_at).utc().format('MMMM DD, YYYY')}
            </span>
            {data?.created_by_name && (
              <TrimText
                customClass='fs-10 fc-purple'
                limit={24}
                text={data.created_by_name}
              />
            )}
          </div>
        ),
      },
    },
    ...(canWriteOrgs
      ? [
          {
            name: '',
            label: '',
            noSort: true,
            isButton: true,
            cell: {
              children: (data: OrgType) => (
                <Button
                  as='button'
                  onClick={() =>
                    navigate(
                      `/orgs/${data.id}/${isInactiveTab ? 'inactive' : 'active'}`,
                    )
                  }
                >
                  {c('manage')}
                </Button>
              ),
            },
          },
        ]
      : []),
  ]

  return (
    <>
      <PageHeader
        header={{
          name: t('totalOrgs'),
          value: totalOrgs,
          tooltip: {
            children: <Icon icon='info' />,
            tooltipContent: <span>{t('totalOrgsTooltip')}</span>,
          },
        }}
        search={{
          placeholder: t('searchOrgsApps'),
          value: searchText,
          onChange: searchInputHandler,
        }}
        pageFilterProps={{
          filterStates: appliedFilters,
          filterCallout: applyFilterCallout,
          resetCallout: resetFilter,
          onChangeCallout: updateSelect,
          cancelCallout: cancelCallout,
          appliedFilters: filtersCount,
        }}
      />
      <OrgsTabs />
      <StandardTable
        data={orgsData}
        config={orgsConfig}
        loading={isLoading}
        dataKey='id'
        hasData={hasData}
        hasMore={!!hasNextPage}
        successStatus={status === 'success'}
        noDataFields={{
          primaryText: t('noOrgsFound'),
          secondaryText: t('weCouldNotFindAnyOrgsForTheSelectedCriteria'),
        }}
        customHeight={'auto'}
        tableId={'orgs-table'}
        sort={setSortBy}
        sortBy={sortBy}
        getData={fetchNextPage}
      />

      {!isInactiveTab && (
        <PageFooter
          rightSection={
            canWriteOrgs
              ? [
                  {
                    as: 'button',
                    onClick: () => createOrg(),
                    disabled: !canWriteOrgs,
                    children: t('createOrg'),
                    styleType: 'primary-green',
                    type: 'button',
                  },
                ]
              : []
          }
        />
      )}
      <OrgForm
        isOrgFormOpen={isOrgFormOpen}
        setIsOrgFormOpen={setIsOrgFormOpen}
        action={action}
        refreshData={setReload}
      />
    </>
  )
}

export default Orgs
