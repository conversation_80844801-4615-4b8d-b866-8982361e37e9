import React, { useCallback, useEffect, useState } from 'react'
import {
  FormFooter,
  getApiUrlPrefix,
  SideDrawer,
  TextInput,
  toast,
} from '@patterninc/react-ui'
import { useMutation } from '@tanstack/react-query'

import { c, useTranslate } from '../../common/services/TranslationService'
import { type OrgFormState, type OrgType, type ParamsType } from './OrgTypes'
import { keyTextConversion } from '../../common/services/HelperService'
import SecureAxios from '../../common/services/SecureAxios'

type OrgFormProps = {
  action?: string
  isOrgFormOpen: boolean
  selectedOrg?: OrgType
  setIsOrgFormOpen: (status: boolean) => void
  refreshData: (action: boolean) => void
}

const OrgForm = ({
  action,
  isOrgFormOpen,
  selectedOrg,
  setIsOrgFormOpen,
  refreshData,
}: OrgFormProps): React.JSX.Element => {
  const { t } = useTranslate('orgs')

  const initialFormState: OrgFormState = {
      orgName: selectedOrg?.name || '',
      orgCode: selectedOrg?.code || '',
      partnerCode: selectedOrg?.partner_code || '',
    },
    [formState, setFormState] = useState<OrgFormState>(initialFormState),
    [errorText, setErrorText] = useState<string | null>(''),
    [isDisabled, setIsDisabled] = useState<boolean>(false)

  useEffect(() => {
    if (action === 'create' && formState.orgName) {
      const orgCode = keyTextConversion(formState.orgName)
      setFormState((prevState) => ({
        ...prevState,
        orgCode: orgCode,
      }))
    }
  }, [action, formState.orgName])

  useEffect(() => {
    setIsDisabled(!!errorText || !(formState.orgName && formState.orgCode))
  }, [errorText, formState.orgCode, formState.orgName])

  const validateOrgName = useCallback(() => {
    if (formState.orgName.length > 50)
      setErrorText(t('orgNameExceedsCharacterLimit')) // Will add more expected validations.
    else setErrorText(null)
  }, [formState.orgName, t])

  const createParams: ParamsType = {
    params: {
      org: {
        code: formState.orgCode,
        name: formState.orgName,
        partner_code: formState.partnerCode,
      },
    },
    apiUrl: `${getApiUrlPrefix('adminczar')}/api/v2/orgs`,
    method: 'POST',
  }

  const updateParams: ParamsType = {
    params: {
      org: {
        code: formState.orgCode,
        name: formState.orgName,
        partner_code: formState.partnerCode,
      },
    },
    apiUrl: `${getApiUrlPrefix('adminczar')}/api/v2/orgs/${selectedOrg?.id}`,
    method: 'PUT',
  }

  const mutation = useMutation({
    mutationFn: (orgParams: ParamsType) => {
      const { method, apiUrl, params } = orgParams
      return SecureAxios[method === 'POST' ? 'post' : 'put'](apiUrl, {
        ...params,
      })
    },
    onSuccess: () => {
      toast({
        message:
          action === 'create'
            ? t('orgCreatedSuccessfully')
            : t('orgUpdatedSuccessfully'),
        type: 'success',
      })
      refreshData(true)
      setIsOrgFormOpen(false)
      setFormState(initialFormState)
    },
    onError: (error: {
      data: {
        message: string
      }
    }) => {
      const errorText = error?.data?.message
      toast({
        message: `${errorText ? errorText : c('somethingWentWrongPleaseTryAgain')}`,
        type: 'error',
        config: {
          closeOnClick: true,
        },
      })
    },
  })

  return (
    <SideDrawer
      isOpen={isOrgFormOpen}
      closeCallout={() => {
        setIsOrgFormOpen(false)
        setFormState(initialFormState)
      }}
      headerContent={`${action === 'create' ? t('createNewOrg') : t('editOrg')}`}
      footerContent={
        <FormFooter
          cancelButtonProps={{
            onClick: () => {
              setIsOrgFormOpen(false)
              setFormState(initialFormState)
            },
          }}
          saveButtonProps={{
            disabled: isDisabled,
            onClick: () =>
              mutation.mutate(
                action === 'create' ? createParams : updateParams,
              ),
          }}
        />
      }
    >
      <div className='flex flex-direction-column pat-gap-4'>
        <TextInput
          labelText={t('orgName')}
          value={formState.orgName || ''}
          callout={(_, value) => {
            setFormState((prevState) => ({
              ...prevState,
              orgName: value.toString().trim(),
            }))
          }}
          type='text'
          stateName='orgName'
          placeholder={t('typeAUniqueOrgName')}
          required
          onBlurCallout={validateOrgName}
          classType={!errorText ? '' : 'error'}
          errorText={errorText ? errorText : ''}
        />

        <TextInput
          labelText={t('orgCode')}
          value={formState.orgCode || ''}
          callout={(_, value) => {
            setFormState((prevState) => ({
              ...prevState,
              orgCode: keyTextConversion(value.toString().trim()),
            }))
          }}
          type='text'
          stateName='orgCode'
          required
          disabled={action === 'edit'}
        />
        <TextInput
          labelText={t('partnerCode')}
          value={formState.partnerCode || ''}
          labelTooltip={{
            tooltipContent: <span>{t('partnerCodeTooltip')}</span>,
            position: 'auto',
          }}
          callout={(_, value) => {
            setFormState((prevState) => ({
              ...prevState,
              partnerCode: value.toString().trim(),
            }))
          }}
          type='text'
          stateName='partnerCode'
        />
      </div>
    </SideDrawer>
  )
}

export default OrgForm
