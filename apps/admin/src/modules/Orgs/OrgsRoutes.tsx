import React from 'react'
import { Navigate, Route, Routes } from 'react-router-dom'

import PrivateRoute from '../../common/components/PrivateRoute/PrivateRoute'
import {
  SecondaryAppContext,
  SecondaryAppProvider,
} from '../contexts/SecondaryAppContext'
import Orgs from './Orgs'
import OrgDetails from './OrgDetails'
import { OrgContext, OrgProvider } from './OrgContext'

const OrgsRoutes = (): React.JSX.Element => {
  const renderOrgsRoutes = () => (
    <Routes>
      <Route
        path=':id/active'
        element={
          <PrivateRoute>
            <OrgDetails />
          </PrivateRoute>
        }
      />
      <Route
        path=':id/inactive'
        element={
          <PrivateRoute>
            <OrgDetails isInactiveTab />
          </PrivateRoute>
        }
      />
      <Route
        path='active'
        element={
          <PrivateRoute>
            <Orgs />
          </PrivateRoute>
        }
      />
      <Route
        path='inactive'
        element={
          <PrivateRoute>
            <Orgs isInactiveTab />
          </PrivateRoute>
        }
      />
      <Route index element={<Navigate to='active' replace />} />
    </Routes>
  )

  return (
    <OrgProvider>
      <OrgContext.Consumer>
        {() => (
          <SecondaryAppProvider>
            <SecondaryAppContext.Consumer>
              {() => renderOrgsRoutes()}
            </SecondaryAppContext.Consumer>
          </SecondaryAppProvider>
        )}
      </OrgContext.Consumer>
    </OrgProvider>
  )
}

export default OrgsRoutes
