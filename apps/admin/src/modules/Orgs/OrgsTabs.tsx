import React from 'react'
import { RouterTabs } from '@patterninc/react-ui'
import { NavLink, useLocation, useNavigate } from 'react-router-dom'

import { c } from '../../common/services/TranslationService'

const OrgsTabs = (): React.JSX.Element => {
  const navigate = useNavigate()
  const pathname = useLocation().pathname

  const navigateMobileTabs = (link: string) => {
    navigate(link)
  }

  return (
    <RouterTabs
      mobileConfig={[
        { label: c('active'), link: '/orgs/active' },
        { label: c('inactive'), link: '/orgs/inactive' },
      ]}
      navigate={navigateMobileTabs}
      currentPath={pathname}
    >
      <NavLink to='/orgs/active'>{c('active')}</NavLink>
      <NavLink to='/orgs/inactive'>{c('inactive')}</NavLink>
    </RouterTabs>
  )
}

export default OrgsTabs
