.infoPane {
  max-width: 300px;

  > :first-child {
    height: 85vh;
  }
}

.tagInputContainer {
  min-width: 300px;
  max-width: 400px;
  background-color: var(--white);
}

.orgIcon {
  background-color: #c3e2fe;
  padding: 16px;
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 100%;
  height: 80px;
  width: 80px;
}

.appHoverText {
  visibility: hidden;
  color: var(--white);
  position: absolute;
  text-align: right;
  right: 8px;
  background-color: var(--black);
  border-radius: 5px;
  padding: 3px;
}

[class*='multiselectionRow']:hover .appHoverText,
[class*='secondaryValue']:hover .appHoverText {
  visibility: visible;
  cursor: pointer;
}
