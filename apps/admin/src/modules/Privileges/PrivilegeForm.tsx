import React from 'react'
import { TextInput } from '@patterninc/react-ui'

import { type ExcludeStatus, type FormState } from './PrivilegeTypes'
import { useTranslate } from '../../common/services/TranslationService'

type PrivilegeFormProps = {
  formState: FormState
  setFormState: React.Dispatch<React.SetStateAction<FormState>>
  errorText: string | null
  errorCodeText: string | null
  validateField: (field: ExcludeStatus) => void
  action: string
  isEditPrivilegeCode: boolean
}

const PrivilegeForm = ({
  formState,
  setFormState,
  errorText,
  errorCodeText,
  validateField,
  isEditPrivilegeCode,
}: PrivilegeFormProps): React.JSX.Element => {
  const { t } = useTranslate('privileges')

  return (
    <div className='flex flex-direction-column pat-gap-4'>
      <TextInput
        labelText={t('privilegeName')}
        value={formState.name || ''}
        callout={(_, value) => {
          setFormState((prevState) => ({
            ...prevState,
            name: value.toString().trim(),
          }))
        }}
        type={'text'}
        stateName='name'
        placeholder={t('typeAUniquePrivilegeName')}
        required
        onBlurCallout={() => validateField('name')}
        classType={!errorText ? '' : 'error'}
        errorText={errorText ? errorText : ''}
      />

      <TextInput
        labelText={t('privilegeCode')}
        value={formState.code || ''}
        callout={(_, value) => {
          setFormState((prevState) => ({
            ...prevState,
            code: value.toString().trim(),
          }))
        }}
        type={'text'}
        stateName='code'
        placeholder={t('typeAUniquePrivilegeCode')}
        disabled={!isEditPrivilegeCode}
        required
        classType={!errorCodeText ? '' : 'error'}
        errorText={errorCodeText ? errorCodeText : ''}
      />

      <TextInput
        labelText={t('privilegeDescription')}
        value={formState.description || ''}
        callout={(_, value) => {
          setFormState((prevState) => ({
            ...prevState,
            description: value.toString().trim(),
          }))
        }}
        required
        type={'text'}
        stateName='description'
      />
    </div>
  )
}

export default PrivilegeForm
