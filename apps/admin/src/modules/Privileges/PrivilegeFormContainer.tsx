import React, { useCallback, useEffect, useMemo, useState } from 'react'
import {
  capitalize,
  FormFooter,
  getApiUrlPrefix,
  notEmpty,
  SideDrawer,
  toast,
} from '@patterninc/react-ui'
import { useMutation } from '@tanstack/react-query'

import {
  type ExcludeStatus,
  type FormState,
  type ParamsType,
  type Privilege,
} from './PrivilegeTypes'
import PrivilegeForm from './PrivilegeForm'
import SecureAxios from '../../common/services/SecureAxios'
import { keyTextConversion } from '../../common/services/HelperService'
import { c, useTranslate } from '../../common/services/TranslationService'

export type PrivilegesFormProps = {
  isOpen: boolean
  closeDrawer: () => void
  action: string
  selectedPrivilege: Privilege | null
  secondaryApp: { id: string; name: string }
  primaryApp: { id: string; name: string }
  setShouldReload: (status: boolean) => void
  isEditPrivilegeCode: boolean
}

export type PrivilegeError = { data: { message: string } }

const PrivilegeFormContainer = ({
  isOpen,
  closeDrawer,
  action,
  secondaryApp,
  primaryApp,
  setShouldReload,
  selectedPrivilege,
  isEditPrivilegeCode,
}: PrivilegesFormProps) => {
  const { t } = useTranslate('privileges')
  const [formState, setFormState] = useState<FormState>({
      name: selectedPrivilege?.name || '',
      description: selectedPrivilege?.description || '',
      code: selectedPrivilege?.code || '',
      status: {
        display: capitalize(selectedPrivilege?.status || 'Active'),
        value: selectedPrivilege?.status || 'active',
      },
    }),
    [disabled, setDisabled] = useState(false),
    [errorText, setErrorText] = useState<string | null>(null),
    [errorCodeText, setErrorCodeText] = useState<string | null>(null)

  useEffect(() => {
    if (isOpen) {
      const { name, code, description } = formState
      if (
        notEmpty(name) &&
        notEmpty(code) &&
        notEmpty(description) &&
        !errorText &&
        !errorCodeText
      ) {
        setDisabled(false)
      } else {
        setDisabled(true)
      }
    }
  }, [
    formState,
    formState.name,
    formState.description,
    isOpen,
    errorText,
    errorCodeText,
  ])

  const isAdminApp = useMemo(
    () => primaryApp.name.toLocaleLowerCase() === 'admin',
    [primaryApp],
  )

  const setError = (field: keyof FormState, message: string | null) => {
    if (field === 'name') {
      setErrorText(message)
    } else if (field === 'code') {
      setErrorCodeText(message)
    }
  }

  const validateField = useCallback(
    (field: ExcludeStatus) => {
      const value = formState[field]

      if (value.length > 50) {
        setError(field, t('fieldExceedsCharacterLimit', { fieldName: field }))
      } else if (
        value.length > 0 &&
        !value.match(/^[a-zA-Z0-9 /[\](){}\-_]+$/)
      ) {
        setError(field, t('specialCharactersAreNotAllowed'))
      } else {
        setError(field, null)
      }
    },
    [formState, t],
  )

  useEffect(() => {
    if (action === 'create' && formState.name) {
      const code = keyTextConversion(formState.name)
      setFormState((prevState) => ({
        ...prevState,
        code: code,
      }))
    }
  }, [formState.name, action])

  const onSubmit = () => {
    const { name, description, code, status } = formState
    const privilege_params = {
      params: {
        privilege: {
          name: name,
          code: code,
          description: description,
          status: status.value,
          ...(action === 'create' ? {} : { id: selectedPrivilege?.id }),
        },
      },
      apiUrl:
        action === 'create'
          ? `${getApiUrlPrefix('adminczar')}/api/v1/apps/${secondaryApp.id}/privileges`
          : `${getApiUrlPrefix('adminczar')}/api/v1/apps/${secondaryApp.id}/privileges/${selectedPrivilege?.id}`,
      method: action === 'create' ? 'POST' : 'PUT',
    }
    mutation.mutate(privilege_params)
  }

  const mutation = useMutation({
    mutationFn: (privilegeParams: ParamsType) => {
      const { method, apiUrl, params } = privilegeParams
      return SecureAxios[method === 'POST' ? 'post' : 'put'](apiUrl, {
        ...params,
        ...(isAdminApp ? { is_admin_app: 'true' } : {}),
      })
    },
    onSuccess: () => {
      toast({
        message:
          action === 'create'
            ? t('privilegeCreatedSuccessfully')
            : t('privilegeUpdatedSuccessfully'),
        type: 'success',
      })
      setShouldReload(true)
      closeDrawer()
    },
    onError: (error: PrivilegeError) => {
      const errorText = error?.data?.message
      toast({
        message: errorText || c('somethingWentWrongPleaseTryAgain'),
        type: 'error',
      })
    },
  })

  return (
    <SideDrawer
      isOpen={isOpen}
      closeCallout={() => closeDrawer()}
      headerContent={`${action === 'create' ? t('createNewPrivilege') : t('editPrivilege')}`}
      footerContent={
        <FormFooter
          cancelButtonProps={{
            onClick: () => {
              closeDrawer()
            },
          }}
          saveButtonProps={{
            disabled: disabled,
            onClick: () => {
              onSubmit()
            },
          }}
        />
      }
    >
      <PrivilegeForm
        formState={formState}
        setFormState={setFormState}
        errorText={errorText}
        errorCodeText={errorCodeText}
        validateField={validateField}
        action={action}
        isEditPrivilegeCode={isEditPrivilegeCode}
      />
    </SideDrawer>
  )
}

export default PrivilegeFormContainer
