export type Privilege = {
  name: string
  id: string
  code: string
  description: string
  status: string
  admin_privilege: boolean
  role_count?: number
}

export type FormState = {
  name: string
  description: string
  code: string
  status: { display: string; value: string }
}

export type ExcludeStatus = Exclude<keyof FormState, 'status'>

export type ParamsType = {
  method: string
  apiUrl: string
  params: Record<string, unknown>
}
