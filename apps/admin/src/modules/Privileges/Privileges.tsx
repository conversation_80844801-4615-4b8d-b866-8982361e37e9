import React, { useContext, useEffect, useMemo, useState } from 'react'
import {
  ButtonGroup,
  getApiUrlPrefix,
  PageFooter,
  PageHeader,
  type SortColumnProps,
  standardSortParams,
  StandardTable,
  toast,
} from '@patterninc/react-ui'
import { useInfiniteQuery, useMutation } from '@tanstack/react-query'

import { ThemeContext } from '../../Context'
import { type ParamsType, type Privilege } from './PrivilegeTypes'
import SecondaryAppDropDown from '../../common/SecondaryAppDropDown'
import SecureAxios from '../../common/services/SecureAxios'
import PrivilegeFormContainer, {
  type PrivilegeError,
} from './PrivilegeFormContainer'
import { hasWritePermission } from '../../Auth/route-permissions'
import PrivilegesTab from './PrivilegesTab'
import { SecondaryAppContext } from '../contexts/SecondaryAppContext'
import { c, useTranslate } from '../../common/services/TranslationService'

type PrivilegeProps = {
  isInactiveTab?: boolean
}

const Privileges = ({ isInactiveTab }: PrivilegeProps): React.JSX.Element => {
  const { t } = useTranslate('privileges')
  const { updateBreadcrumbs, privileges, app } = useContext(ThemeContext),
    [searchText, setSearchText] = useState<string>(''),
    [sortBy, setSort] = useState({
      prop: 'name',
      flip: true,
    }),
    [isSideDrawerOpen, setSideDrawerOpen] = useState<boolean>(false),
    [action, setAction] = useState<string>(''),
    [selectedPrivilege, setSelectedPrivilege] = useState<Privilege | null>(
      null,
    ),
    [shouldReload, setShouldReload] = useState(false)

  const { secondaryApp } = useContext(SecondaryAppContext)

  useEffect(() => {
    updateBreadcrumbs({
      name: c('privileges'),
      link: '/privileges',
      changeType: 'rootLevel',
    })
  }, [updateBreadcrumbs])

  const createNewPrivilege = () => {
    setSideDrawerOpen(true)
    setAction('create')
  }

  const setSortBy: SortColumnProps['sorter'] = (sortObj) => {
    setSort({
      prop: sortObj.activeColumn,
      flip: sortObj.direction,
    })
  }

  const searchInputHandler = (query: string) => {
    setSearchText(query)
  }

  const closeDrawer = () => {
    setSideDrawerOpen(false)
    setAction('')
    setSelectedPrivilege(null)
  }

  const isAdminApp = useMemo(
    () => app.name.toLocaleLowerCase() === 'admin',
    [app],
  )

  const apiUrl = `${getApiUrlPrefix('adminczar')}/api/v1/apps/${secondaryApp.id}/privileges`
  const {
    data: PrivilegesPaginatedData,
    fetchNextPage,
    hasNextPage,
    isLoading,
    status,
  } = useInfiniteQuery({
    queryKey: [
      apiUrl,
      sortBy,
      searchText,
      secondaryApp.id,
      shouldReload,
      isInactiveTab,
      app.id,
    ],
    queryFn: async ({ pageParam = 1, signal }) => {
      const data = await SecureAxios.get(apiUrl, {
        params: {
          page: pageParam ?? 1,
          per_page: 20,
          sort: standardSortParams(sortBy),
          status: isInactiveTab ? 'inactive' : 'active',
          ...(searchText ? { search_for: searchText } : {}),
          ...(isAdminApp ? { is_admin_app: 'true' } : {}),
        },
        signal,
      })
      setShouldReload(false)
      return data
    },
    initialPageParam: 1,
    getNextPageParam: (previousResponse) => {
      return previousResponse?.data?.pagination?.last_page
        ? undefined
        : previousResponse?.data?.pagination?.next_page
    },
    enabled: !!secondaryApp.id,
  })

  const privilegesData = useMemo(
    () =>
      PrivilegesPaginatedData
        ? PrivilegesPaginatedData?.pages?.flatMap((page) => {
            return page?.data?.data
          })
        : [],
    [PrivilegesPaginatedData],
  )
  const hasData = !!(status === 'success' && privilegesData?.length),
    totalCount = PrivilegesPaginatedData?.pages[0].data.pagination.count

  const deleteMutation = useMutation({
    mutationFn: async (privilege: Privilege) => {
      const url = `${getApiUrlPrefix('adminczar')}/api/v1/apps/${secondaryApp.id}/privileges/${privilege?.id}`
      return await SecureAxios.delete(url, {
        params: {
          ...(isAdminApp ? { is_admin_app: 'true' } : {}),
        },
      })
    },
    onSuccess: () => {
      setShouldReload(true)
      toast({
        type: 'success',
        message: t('privilegeSuccessfullyDeleted'),
      })
    },
    onError: (error: PrivilegeError) => {
      const errorText = error?.data?.message
      toast({
        message: errorText || c('somethingWentWrongPleaseTryAgain'),
        type: 'error',
      })
    },
  })

  const canWritePrivileges = useMemo(
    () => hasWritePermission('privileges', privileges),
    [privileges],
  )

  const isEditPrivilegeCode = useMemo(
    () => hasWritePermission('privileges_edit_code', privileges),
    [privileges],
  )

  const getIsDisabled = (data: Privilege): boolean => {
    return (
      !canWritePrivileges || (data.role_count !== 0 && data.admin_privilege)
    )
  }

  const updateMutation = useMutation({
    mutationFn: async (updationParams: ParamsType) => {
      const { apiUrl, params } = updationParams
      return await SecureAxios.put(apiUrl, params)
    },
    onSuccess: () => {
      setShouldReload(true)
      toast({
        type: 'success',
        message: t('privilegeUpdatedSuccessfully'),
      })
    },
    onError: (error: { data: { message: string } }) => {
      const errorText = error?.data?.message
      toast({
        message: `${errorText ? errorText : c('somethingWentWrongPleaseTryAgain')}`,
        type: 'error',
      })
    },
  })

  const updatePrivilege = (privilege: Privilege, status: string) => {
    const privilege_params = {
      params: {
        privilege: {
          status: status,
          id: privilege?.id,
        },
        ...(isAdminApp ? { is_admin_app: 'true' } : {}),
      },
      apiUrl: `${getApiUrlPrefix('adminczar')}/api/v1/apps/${secondaryApp.id}/privileges/${privilege?.id}`,
      method: 'PUT',
    }

    updateMutation.mutate(privilege_params)
  }

  const renderActionColumn = (data: Privilege) => {
    return isInactiveTab ? (
      <ButtonGroup
        buttons={[
          {
            actions: [
              {
                text: t('editPrivilege'),
                icon: 'pencil',
                callout: () => {
                  setSelectedPrivilege(data)
                  setSideDrawerOpen(true)
                  setAction('edit')
                },
                disabled: {
                  value: getIsDisabled(data),
                },
              },
              {
                text: t('deletePrivilege'),
                icon: 'trash',
                destructive: true,
                disabled: {
                  value: getIsDisabled(data),
                },
                confirmation: {
                  type: 'red',
                  header: t('deletePrivilege'),
                  body: (
                    <span>
                      {t('thisPrivilegeWillBeDeletedThisCannotBeUndone')}
                    </span>
                  ),
                  confirmCallout: () => {
                    deleteMutation.mutate(data)
                  },
                },
              },
            ],
          },
          {
            children: t('reactivatePrivilege'),
            onClick: () => updatePrivilege(data, 'active'),
            disabled: !canWritePrivileges,
          },
        ]}
      />
    ) : (
      <ButtonGroup
        buttons={[
          {
            actions: [
              {
                text: t('deactivatePrivilege'),
                icon: 'unfollow',
                confirmation: {
                  type: 'red',
                  header: c('areYouSure'),
                  body: t(
                    'thisPrivilegeWillNoLongerHaveAccessToAppAreYouSure',
                    { appName: secondaryApp?.name },
                  ),
                  confirmCallout: () => updatePrivilege(data, 'inactive'),
                },
              },
            ],
          },
          {
            children: t('editPrivilege'),
            disabled: getIsDisabled(data),

            onClick: () => {
              setSelectedPrivilege(data)
              setSideDrawerOpen(true)
              setAction('edit')
            },
          },
        ]}
      />
    )
  }

  const privilegesConfig = [
    {
      name: 'name',
      label: t('privilegeName'),
      cell: {
        children: (data: Privilege) => (
          <span
            className={`flex align-items-center ${
              sortBy.prop === 'name' ? 'fw-semi-bold' : ''
            }`}
          >
            {data?.name}
          </span>
        ),
      },
      mainColumn: true,
    },
    {
      name: 'code',
      label: t('privilegeCode'),
      cell: {
        children: (data: Privilege) => (
          <span
            className={`flex align-items-center ${
              sortBy.prop === 'code' ? 'fw-semi-bold' : ''
            }`}
          >
            {data?.code}
          </span>
        ),
      },
    },
    {
      name: 'description',
      label: t('privilegeDescription'),
      cell: {
        children: (data: Privilege) => (
          <span
            className={`flex align-items-center ${
              sortBy.prop === 'code' ? 'fw-semi-bold' : ''
            }`}
          >
            {data?.description}
          </span>
        ),
      },
    },
    ...(!canWritePrivileges
      ? []
      : [
          {
            name: '',
            label: '',
            noSort: true,
            cell: {
              children: (data: Privilege) => {
                return renderActionColumn(data)
              },
            },
          },
        ]),
  ]

  return (
    <>
      <PageHeader
        header={{
          name: c('privileges'),
          value: totalCount,
        }}
        search={{
          placeholder: t('searchPrivileges'),
          value: searchText,
          onChange: searchInputHandler,
        }}
        rightSectionChildren={<SecondaryAppDropDown />}
      />
      <PrivilegesTab />
      <StandardTable
        data={privilegesData}
        config={privilegesConfig}
        loading={isLoading}
        dataKey={'id'}
        hasData={hasData}
        hasMore={!!hasNextPage}
        successStatus={status === 'success'}
        noDataFields={{
          primaryText: t('noPrivilegesFound'),
          secondaryText: t('weCouldNotFindAnyPrivilegesForSelectedCriteria'),
        }}
        tableId={'privileges-table'}
        customHeight={'auto'}
        sort={setSortBy}
        sortBy={sortBy}
        getData={fetchNextPage}
      />
      {!isInactiveTab && (
        <PageFooter
          rightSection={
            canWritePrivileges
              ? [
                  {
                    as: 'button',
                    disabled: !canWritePrivileges,
                    onClick: () => createNewPrivilege(),
                    children: t('createNewPrivilege'),
                    styleType: 'primary-green',
                    type: 'button',
                  },
                ]
              : []
          }
        />
      )}

      {isSideDrawerOpen && (
        <PrivilegeFormContainer
          isOpen={isSideDrawerOpen}
          closeDrawer={closeDrawer}
          action={action}
          selectedPrivilege={selectedPrivilege}
          secondaryApp={secondaryApp}
          primaryApp={app}
          setShouldReload={setShouldReload}
          isEditPrivilegeCode={isEditPrivilegeCode}
        />
      )}
    </>
  )
}

export default Privileges
