import React from 'react'
import { Navigate, Route, Routes } from 'react-router-dom'

import PrivateRoute from '../../common/components/PrivateRoute/PrivateRoute'
import Privileges from './Privileges'
import {
  SecondaryAppContext,
  SecondaryAppProvider,
} from '../contexts/SecondaryAppContext'

const PrivilegesRoutes = (): React.JSX.Element => {
  const renderPrivilegesRoute = () => (
    <Routes>
      <Route
        path='active'
        element={
          <PrivateRoute>
            <Privileges />
          </PrivateRoute>
        }
      />
      <Route
        path='inactive'
        element={
          <PrivateRoute>
            <Privileges isInactiveTab />
          </PrivateRoute>
        }
      />
      <Route index element={<Navigate to='active' replace />} />
    </Routes>
  )

  return (
    <SecondaryAppProvider>
      <SecondaryAppContext.Consumer>
        {() => renderPrivilegesRoute()}
      </SecondaryAppContext.Consumer>
    </SecondaryAppProvider>
  )
}

export default PrivilegesRoutes
