import React from 'react'
import { RouterTabs } from '@patterninc/react-ui'
import { NavLink, useLocation, useNavigate } from 'react-router-dom'

import { c } from '../../common/services/TranslationService'

const PrivilegesTab = (): React.JSX.Element => {
  const navigate = useNavigate()
  const pathname = useLocation().pathname

  const navigateMobileTabs = (link: string) => {
    navigate(link)
  }

  return (
    <RouterTabs
      mobileConfig={[
        { label: c('active'), link: '/privileges/active' },
        { label: c('inactive'), link: '/privileges/inactive' },
      ]}
      navigate={navigateMobileTabs}
      currentPath={pathname}
    >
      <NavLink to='/privileges/active'>{c('active')}</NavLink>
      <NavLink to='/privileges/inactive'>{c('inactive')}</NavLink>
    </RouterTabs>
  )
}

export default PrivilegesTab
