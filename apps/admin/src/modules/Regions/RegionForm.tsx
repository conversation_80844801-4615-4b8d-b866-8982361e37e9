import React, { useCallback, useMemo, useState } from 'react'
import { Select, Switch, TextInput, toast } from '@patterninc/react-ui'

import { type FormState } from './RegionFormParams'
import { type RegionType } from './regions-table-config'
import { type selectedRegionType } from './RegionsTable'
import { useTranslate } from '../../common/services/TranslationService'

type RegionFormProps = {
  drawerMode: string | null
  formState: FormState
  allRegions: selectedRegionType[] | undefined
  setFormState: (
    key: keyof FormState,
    value: string | boolean | undefined,
  ) => void
  selectedRegion: RegionType | null | undefined
  setNewPrimaryRegion: (region: selectedRegionType | undefined) => void
  newPrimaryRegion: selectedRegionType | undefined
  isInactiveTab?: boolean
}

const RegionForm = ({
  drawerMode,
  formState,
  setFormState,
  allRegions,
  selectedRegion,
  setNewPrimaryRegion,
  newPrimaryRegion,
  isInactiveTab,
}: RegionFormProps): React.JSX.Element => {
  const { t } = useTranslate('regions')
  const [errorText, setErrorText] = useState<string | null>(null)

  const onBlurCallout = useCallback(() => {
    if (formState.regionName.length > 50)
      setErrorText(t('regionNameExceedsCharacterLimit'))
    else if (
      formState.regionName.length > 0 &&
      !formState.regionName.match(/^[a-zA-Z0-9 /[\](){}\-_]+$/)
    )
      setErrorText(t('specialCharactersAreNotAllowed'))
    else setErrorText(null)
  }, [formState.regionName, t])

  const primaryRegionToggleCheck = useMemo(
    () => selectedRegion?.default && formState.primaryRegion,
    [formState.primaryRegion, selectedRegion?.default],
  )
  const primaryRegionSelection = () => {
    return (
      <>
        <span>{t('youMustHaveAPrimaryOrgUnitPleaseSelect')}</span>
        <div>
          <div className='pat-mt-4'>
            <Select
              labelProps={{
                label: t('primaryOrgUnit'),
              }}
              options={allRegions || []}
              labelKeyName='name'
              optionKeyName='name'
              selectedItem={{
                id: newPrimaryRegion?.id || (allRegions?.[0]?.id as string),
                name: newPrimaryRegion?.name || allRegions?.[0]?.name,
              }}
              onChange={(value) => {
                if (value) {
                  const option: selectedRegionType = {
                    id: value?.id,
                    name: value?.name,
                  }
                  setNewPrimaryRegion(option)
                }
              }}
              disabled={allRegions?.length === 0}
              required
              noMenuPortal
            />
          </div>
        </div>
      </>
    )
  }

  return (
    <div className='flex flex-direction-column pat-gap-4'>
      {!!drawerMode && (
        <TextInput
          labelText={t('orgUnitName')}
          value={formState.regionName || ''}
          callout={(_, value) => {
            setFormState('regionName', value.toString().trim())
          }}
          type={'text'}
          stateName='regionName'
          placeholder={t('typeAUniqueOrgUnitName')}
          required
          onBlurCallout={onBlurCallout}
          classType={!errorText ? '' : 'error'}
          errorText={errorText ? errorText : ''}
        />
      )}
      {!isInactiveTab && (
        <div className='single-filter select flex align-items-center fs-12 fw-regular fc-purple'>
          <Switch
            className='pat-mr-2'
            checked={formState.primaryRegion ?? false}
            disabled={allRegions?.length === 0}
            {...(primaryRegionToggleCheck
              ? {
                  confirmation: {
                    type: 'blue',
                    header: t('setAnotherPrimaryOrgUnit'),
                    body: primaryRegionSelection(),
                    cancelCallout: () => setNewPrimaryRegion(undefined),
                    confirmCallout: () => {
                      if (newPrimaryRegion || selectedRegion?.default) {
                        setFormState(
                          'newPrimaryRegionId',
                          newPrimaryRegion?.id || allRegions?.[0].id,
                        )
                        toast({
                          type: 'warning',
                          message: `${newPrimaryRegion?.name || allRegions?.[0].name} will be set as the new Primary Org unit once you click on Save button`,
                          config: {
                            autoClose: 1500,
                          },
                        })
                        setFormState('primaryRegion', !formState.primaryRegion)
                      }
                    },
                    confirmButtonText: t('setPrimary'),
                  },
                }
              : {
                  callout: () => {
                    setFormState('primaryRegion', !formState.primaryRegion)
                    if (
                      !formState.primaryRegion &&
                      formState.newPrimaryRegionId
                    ) {
                      setFormState('newPrimaryRegionId', undefined)
                      setNewPrimaryRegion(undefined)
                    }
                  },
                })}
          />
          {t('setAsPrimary')}
        </div>
      )}
    </div>
  )
}

export default RegionForm
