import React, { useEffect, useState } from 'react'
import { <PERSON><PERSON>ooter, SideDrawer, toast } from '@patterninc/react-ui'
import { useMutation } from '@tanstack/react-query'

import RegionForm from './RegionForm'
import { type RegionError, type RegionType } from './regions-table-config'
import SecureAxios from '../../common/services/SecureAxios'
import { type selectedRegionType } from './RegionsTable'
import { useTranslate } from '../../common/services/TranslationService'

export type FormState = {
  regionName: string
  primaryRegion?: boolean
  newPrimaryRegionId?: string
}

type createActionError = { data: { message: string } }

export interface paramsType {
  params: {
    org_unit: {
      name?: string
      default?: boolean
      code?: string
      new_default_id?: string | undefined
    }
  }
}

type RegionFormbaseProps = {
  apiUrl: string
  drawerMode: string | null
  allRegions: selectedRegionType[] | undefined
  refresh: () => void
  setDrawerMode: (mode: string | null) => void
  selectedRegion?: RegionType | null
  setSelectedRegion: (region: RegionType | null) => void
  isInactiveTab?: boolean
}

const RegionFormParams = ({
  apiUrl,
  drawerMode,
  allRegions,
  selectedRegion,
  setDrawerMode,
  setSelectedRegion,
  refresh,
  isInactiveTab,
}: RegionFormbaseProps): React.JSX.Element => {
  const { t } = useTranslate('regions')
  const [formState, setFormState] = useState<FormState>({
      regionName: '',
    }),
    [disabled, setDisabled] = useState<boolean>(false)
  const [newPrimaryRegion, setNewPrimaryRegion] = useState<
    selectedRegionType | undefined
  >(undefined)

  useEffect(
    () =>
      setFormState((prevState) => ({
        ...prevState,
        regionName: selectedRegion?.name || '',
        primaryRegion: selectedRegion?.default,
        newPrimaryRegionId: newPrimaryRegion?.id,
      })),
    [newPrimaryRegion?.id, selectedRegion?.default, selectedRegion?.name],
  )
  const updateForm = (
    key: keyof FormState,
    value: string | boolean | undefined,
  ) => {
    setFormState((prevState) => ({
      ...prevState,
      [key]: value,
    }))
  }
  useEffect(() => {
    if (drawerMode === 'create')
      setFormState({ regionName: '', primaryRegion: undefined })
  }, [drawerMode])

  useEffect(() => {
    if (
      formState.regionName.length > 0 &&
      formState.regionName.length <= 50 &&
      formState.regionName.match(/^[a-zA-Z0-9 /[\](){}\-_]+$/)
    )
      setDisabled(false)
    else setDisabled(true)
  }, [formState, setDisabled])

  const formSubmit = () => {
    const editActionParams = {
      params: {
        org_unit: {
          name: formState.regionName,
          ...(!isInactiveTab
            ? {
                default: formState.primaryRegion,
                new_default_id: formState.newPrimaryRegionId,
              }
            : {}),
        },
      },
    }
    const createActionParams = {
      params: {
        org_unit: {
          name: formState.regionName,
          default: formState.primaryRegion,
        },
      },
    }

    if (drawerMode === 'create') createMutation.mutate(createActionParams)
    else if (drawerMode === 'edit') editMutation.mutate(editActionParams)
  }

  const createMutation = useMutation({
    mutationFn: (createActionParams: paramsType) =>
      SecureAxios.post(apiUrl, createActionParams.params),
    onSuccess: () => {
      refresh()
      toast({
        message: t('orgUnitCreatedSuccessfully'),
        type: 'success',
      })
      setDrawerMode(null)
    },
    onError: (error: createActionError) => {
      toast({
        message: error?.data?.message.split(',')?.[0],
        type: 'error',
      })
    },
  })

  const editMutation = useMutation({
    mutationFn: (editActionParams: paramsType) =>
      SecureAxios.put(
        `${apiUrl}/${selectedRegion?.id}`,
        editActionParams.params,
      ),
    onSuccess: () => {
      refresh()
      toast({
        message: t('changesSavedSuccessfully'),
        type: 'success',
      })
      setDrawerMode(null)
    },
    onError: (error: RegionError) => {
      toast({
        message: error?.data?.message.split(',')?.[0],
        type: 'error',
      })
    },
  })

  return (
    <SideDrawer
      isOpen={!!drawerMode}
      closeCallout={() => {
        setDrawerMode(null)
        setSelectedRegion(null)
      }}
      headerContent={`${
        drawerMode === 'create' ? t('createNewOrgUnit') : t('editOrgUnit')
      }`}
      footerContent={
        <FormFooter
          cancelButtonProps={{
            onClick: () => {
              setDrawerMode(null)
              setSelectedRegion(null)
            },
          }}
          saveButtonProps={{
            disabled: disabled,
            onClick: () => {
              formSubmit()
              setDrawerMode(null)
            },
          }}
        />
      }
    >
      <RegionForm
        drawerMode={drawerMode}
        allRegions={allRegions}
        formState={formState}
        setFormState={updateForm}
        selectedRegion={selectedRegion}
        setNewPrimaryRegion={setNewPrimaryRegion}
        newPrimaryRegion={newPrimaryRegion}
        isInactiveTab={isInactiveTab}
      />
    </SideDrawer>
  )
}

export default RegionFormParams
