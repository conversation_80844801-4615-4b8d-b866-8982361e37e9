import React from 'react'
import { Navigate, Route, Routes } from 'react-router-dom'

import PrivateRoute from '../../common/components/PrivateRoute/PrivateRoute'
import Regions from './Regions'

const RegionRoutes = (): React.JSX.Element => {
  const renderRegionRoutes = () => (
    <Routes>
      <>
        <Route
          path='active'
          element={
            <PrivateRoute>
              <Regions />
            </PrivateRoute>
          }
        />
        <Route
          path='inactive'
          element={
            <PrivateRoute>
              <Regions isInactiveTab />
            </PrivateRoute>
          }
        />
        <Route index element={<Navigate to='active' replace />} />{' '}
      </>
    </Routes>
  )

  return renderRegionRoutes()
}

export default RegionRoutes
