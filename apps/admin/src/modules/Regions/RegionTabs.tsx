import React from 'react'
import { RouterTabs } from '@patterninc/react-ui'
import { NavLink, useLocation, useNavigate } from 'react-router-dom'

import { c } from '../../common/services/TranslationService'

const RegionTabs = (): React.JSX.Element => {
  const navigate = useNavigate()
  const pathname = useLocation().pathname

  const navigateMobileTabs = (link: string) => {
    navigate(link)
  }

  return (
    <RouterTabs
      mobileConfig={[
        { label: c('active'), link: '/org_units/active' },
        { label: c('inactive'), link: '/org_units/inactive' },
      ]}
      navigate={navigateMobileTabs}
      currentPath={pathname}
    >
      <NavLink to='/org_units/active'>{c('active')}</NavLink>
      <NavLink to='/org_units/inactive'>{c('inactive')}</NavLink>
    </RouterTabs>
  )
}

export default RegionTabs
