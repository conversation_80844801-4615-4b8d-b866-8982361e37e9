import React, {
  useCallback,
  useContext,
  useEffect,
  useMemo,
  useState,
} from 'react'
import {
  getApiUrlPrefix,
  PageHeader,
  type SortColumnProps,
  standardSortParams,
  useIsMobileView,
} from '@patterninc/react-ui'
import { useInfiniteQuery } from '@tanstack/react-query'

import { ThemeContext } from '../../Context'
import OrganizationDropDownNew from '../../common/OrganizationDropDownNew'
import RegionsTable from './RegionsTable'
import SecureAxios from '../../common/services/SecureAxios'
import RegionTabs from './RegionTabs'
import { c, useTranslate } from '../../common/services/TranslationService'

type RegionProps = {
  isInactiveTab?: boolean
}
const Regions = ({ isInactiveTab }: RegionProps): React.JSX.Element => {
  const { updateBreadcrumbs, organization, app } = useContext(ThemeContext),
    { t } = useTranslate('regions'),
    isMobileView = useIsMobileView()

  useEffect(() => {
    updateBreadcrumbs({
      name: c('orgUnits'),
      link: '/org_units',
      changeType: 'rootLevel',
    })
  }, [updateBreadcrumbs])

  const [sortBy, setSort] = useState({
      prop: 'name',
      flip: true,
    }),
    [searchText, setSearchText] = useState<string>(''),
    [reload, setReload] = useState(false)
  const setSortBy: SortColumnProps['sorter'] = (sortObj) => {
    setSort({
      prop: sortObj.activeColumn,
      flip: sortObj.direction,
    })
  }

  const searchInputHandler = (query: string) => {
    setSearchText(query)
  }

  const refreshRegions = useCallback(() => setReload(true), [])

  // Regions Table API
  const apiUrl = `${getApiUrlPrefix('adminczar')}/api/v1/apps/${app.id}/orgs/${
      organization.id
    }/org_units`,
    {
      data: regionsPaginatedData,
      status,
      fetchNextPage,
      hasNextPage,
      isLoading,
    } = useInfiniteQuery({
      queryKey: [
        apiUrl,
        sortBy,
        reload,
        organization.id,
        searchText,
        isInactiveTab,
      ],
      queryFn: async ({ pageParam = 1, signal }) => {
        const data = await SecureAxios.get(apiUrl, {
          params: {
            page: pageParam ?? 1,
            per_page: 20,
            sort: standardSortParams(sortBy),
            ...(searchText ? { search_for: searchText } : {}),
            status: isInactiveTab ? 'inactive' : 'active',
          },
          signal,
        })
        setReload(false)
        return data
      },
      initialPageParam: 1,
      gcTime: 1000 * 60 * 60 * 8,
      getNextPageParam: (previousResponse) => {
        return previousResponse?.data?.pagination?.last_page
          ? undefined
          : previousResponse?.data?.pagination?.next_page
      },
    })

  const regionsData = useMemo(
    () =>
      regionsPaginatedData
        ? regionsPaginatedData?.pages?.flatMap((page) => {
            return page?.data?.data
          })
        : [],
    [regionsPaginatedData],
  )

  const totalRegions = useMemo(
    () => regionsPaginatedData?.pages[0]?.data?.pagination?.count,

    [regionsPaginatedData],
  )
  const hasData = !!(status === 'success' && regionsData?.length)

  return (
    <>
      <PageHeader
        header={{
          name: c('orgUnits'),
          value: totalRegions,
        }}
        search={{
          placeholder: t('searchOrgUnits'),
          value: searchText,
          onChange: searchInputHandler,
        }}
        tooltip={{
          tooltipContent: (
            <div className='flex flex-direction-column pat-gap-4'>
              <span className='fs-18'>{t('orgUnitsList')}</span>
              <div>
                <span>
                  {t('theTableBelowContainsAllOrgUnits', {
                    type: isInactiveTab ? c('inactive') : c('active'),
                  })}
                </span>
                <br />
                <span>{t('forFastestNavigationSearchForOrg')}</span>
              </div>
            </div>
          ),
        }}
        {...(isMobileView
          ? { bottomSectionChildren: <OrganizationDropDownNew /> }
          : { rightSectionChildren: <OrganizationDropDownNew /> })}
      />
      <RegionTabs />

      <RegionsTable
        setSortBy={setSortBy}
        organizationId={organization.id}
        apiUrl={apiUrl}
        sortBy={sortBy}
        regionsData={regionsData}
        refreshRegions={refreshRegions}
        hasData={hasData}
        status={status}
        fetchNextPage={fetchNextPage}
        hasNextPage={hasNextPage}
        isLoading={isLoading}
        reload={reload}
        isInactiveTab={isInactiveTab}
      />
    </>
  )
}

export default Regions
