import React, { useContext, useMemo, useState } from 'react'
import {
  type ConfigItemType,
  PageFooter,
  standardSortParams,
  StandardTable,
} from '@patterninc/react-ui'
import { useQuery } from '@tanstack/react-query'

import { GenerateTableConfig, type RegionType } from './regions-table-config'
import RegionFormParams from './RegionFormParams'
import SecureAxios from '../../common/services/SecureAxios'
import { hasWritePermission } from '../../Auth/route-permissions'
import { ThemeContext } from '../../Context'
import { useTranslate } from '../../common/services/TranslationService'

type RegionsTableProps = {
  sortBy: { prop: string; flip: boolean }
  setSortBy: (sortObject: { activeColumn: string; direction: boolean }) => void
  apiUrl: string
  regionsData: Array<{
    id: string
    name: string
    description?: string
    status: string
    default: boolean
    created_at: string
    created_by_name: string
    users_with_access: number
  }>
  refreshRegions: () => void
  hasData: boolean
  status: string
  fetchNextPage: () => void
  hasNextPage?: boolean
  isLoading: boolean
  reload: boolean
  organizationId: string
  isInactiveTab?: boolean
}

export type selectedRegionType = {
  id: string
  name?: string
}

const RegionsTable = ({
  sortBy,
  setSortBy,
  apiUrl,
  regionsData,
  refreshRegions,
  hasData,
  status,
  fetchNextPage,
  hasNextPage,
  isLoading,
  reload,
  organizationId,
  isInactiveTab,
}: RegionsTableProps): React.JSX.Element => {
  const stickyTableConfig = {
      right: 1,
    },
    { t } = useTranslate('regions'),
    { privileges } = useContext(ThemeContext),
    [drawerMode, setDrawerMode] = useState<string | null>(null),
    [selectedRegion, setSelectedRegion] = useState<RegionType | null>(null)

  // Get All other regions for setting a new primary region
  const { data: allRegionsResponse } = useQuery({
    queryKey: ['active-org-units', reload, organizationId],
    queryFn: async () => {
      return await SecureAxios.get(apiUrl, {
        params: {
          sort: standardSortParams({
            prop: 'users_with_access',
            flip: false,
          }),
          status: 'active',
        },
      })
    },
  })

  const allRegions = useMemo(() => {
    if (allRegionsResponse) {
      const regions: selectedRegionType[] = []
      allRegionsResponse?.data?.data?.map((region: RegionType) => {
        if (!region.default) {
          regions?.push({
            id: region?.id,
            name: region?.name,
          })
        }
        return null
      })
      return regions
    }
  }, [allRegionsResponse])

  const canWriteOrgUnits = hasWritePermission('org_units', privileges)

  return (
    <>
      <StandardTable
        data={regionsData ?? []}
        stickyTableConfig={stickyTableConfig}
        config={
          GenerateTableConfig({
            sortBy,
            refreshRegions,
            apiUrl,
            setDrawerMode,
            allRegions,
            setSelectedRegion,
            isInactiveTab,
          }) as ConfigItemType<unknown, Record<string, unknown>>[]
        }
        hasData={hasData}
        loading={isLoading}
        hasMore={!!hasNextPage}
        showGroups={true}
        groups={[
          {
            groupHeader: t('primaryOrgUnit'),

            check: (region) => region.default,
          },
          {
            groupHeader: t('otherOrgUnits'),
            check: (region) => !region.default,
          },
        ]}
        sort={setSortBy}
        sortBy={sortBy}
        getData={fetchNextPage}
        successStatus={status === 'success'}
        dataKey={'name'}
        tableId={'regions_table'}
        customHeight={'auto'}
        noDataFields={{
          primaryText: t('noOrgUnitsFound'),
          secondaryText: t('weCouldNoteFindAnyOrgUnitsForTheSelectedCriteria'),
        }}
      />

      <PageFooter
        rightSection={
          !canWriteOrgUnits || isInactiveTab
            ? []
            : [
                {
                  as: 'button',
                  onClick: () => setDrawerMode('create'),
                  children: t('createNewOrgUnit'),
                  styleType: 'primary-green',
                  type: 'button',
                  disabled: !canWriteOrgUnits,
                },
              ]
        }
      />

      <RegionFormParams
        apiUrl={apiUrl}
        allRegions={allRegions}
        refresh={refreshRegions}
        drawerMode={drawerMode}
        selectedRegion={selectedRegion}
        setSelectedRegion={setSelectedRegion}
        setDrawerMode={setDrawerMode}
        isInactiveTab={isInactiveTab}
      />
    </>
  )
}

export default RegionsTable
