import React, { useContext, useMemo, useState } from 'react'
import {
  ButtonGroup,
  type ConfigItemType,
  Select,
  Tag,
  toast,
  TrimText,
} from '@patterninc/react-ui'
import moment from 'moment'
import { useMutation } from '@tanstack/react-query'

import SecureAxios from '../../common/services/SecureAxios'
import { type selectedRegionType } from './RegionsTable'
import { ThemeContext } from '../../Context'
import { getCurrentEnv } from '../../App'
import { hasWritePermission } from '../../Auth/route-permissions'
import { c, useTranslate } from '../../common/services/TranslationService'

export type RegionType = {
  id: string
  name: string
  description: string
  status: string
  created_at: string
  default: boolean
  created_by_name: string
  users_with_access: number
}

export type RegionError = { data: { message: string } }

type TableConfigProps = {
  sortBy: { prop: string; flip: boolean }
  refreshRegions: () => void
  apiUrl: string
  setDrawerMode: (mode: string) => void
  allRegions: selectedRegionType[] | undefined
  setSelectedRegion: (region: RegionType | null) => void
  isInactiveTab?: boolean
}

export const GenerateTableConfig = ({
  sortBy,
  refreshRegions,
  apiUrl,
  allRegions,
  setDrawerMode,
  setSelectedRegion,
  isInactiveTab,
}: TableConfigProps): ConfigItemType<RegionType, Record<string, unknown>>[] => {
  const { t } = useTranslate('regions')

  const [newPrimaryRegion, setNewPrimaryRegion] = useState<
    selectedRegionType | undefined
  >(undefined)

  const { organization, privileges } = useContext(ThemeContext),
    environmentName = getCurrentEnv(),
    enableDeleteInOrg = useMemo(
      () =>
        organization.name === 'Automation Org' && environmentName === 'staging',
      [environmentName, organization.name],
    )
  const deleteMutation = useMutation({
    mutationFn: async ({
      region,
      isDelete = false,
    }: {
      region: RegionType
      isDelete?: boolean
    }) => {
      const newPrimaryRegionParams =
        newPrimaryRegion || region.default
          ? {
              org_unit: {
                new_default_id: newPrimaryRegion?.id || allRegions?.[0].id,
              },
            }
          : null

      return enableDeleteInOrg && isDelete
        ? await SecureAxios.delete(`${apiUrl}/${region.id}`, {
            params: newPrimaryRegionParams,
          })
        : await SecureAxios.put(
            `${apiUrl}/${region.id}/toggle_status`,
            !isInactiveTab ? newPrimaryRegionParams : null,
          )
    },
    onSuccess: (_, { region }) => {
      refreshRegions()
      if ((newPrimaryRegion || region.default) && !isInactiveTab) {
        toast({
          type: 'success',
          message: `${newPrimaryRegion?.name || allRegions?.[0].name} ${t('hasBeenSetAsTheNewPrimaryOrgUnit')}`,
        })
      }
      toast({
        type: 'success',
        message: `${t('orgUnitSuccessfully')} ${isInactiveTab ? (enableDeleteInOrg && !!deleteMutation.variables?.isDelete ? c('deleted') : c('activated')) : c('deactivated')}`,
      })
    },
    onError: (error: RegionError) => {
      if (error) {
        toast({
          type: 'error',
          message: error?.data?.message.split(',')?.[0],
        })
      }
    },
  })

  const canWriteOrgUnits = hasWritePermission('org_units', privileges)

  const primaryRegionSelection = () => {
    return (
      <div>
        <span>{t('pleaseSelectANewPrimaryOrgUnit')}</span>
        <div className='pat-mt-4'>
          <Select
            labelProps={{ label: t('primaryOrgUnit') }}
            options={allRegions || []}
            labelKeyName='name'
            optionKeyName='name'
            selectedItem={{
              id: newPrimaryRegion?.id || (allRegions?.[0]?.id as string),
              name: newPrimaryRegion?.name || allRegions?.[0]?.name,
            }}
            onChange={(value) => {
              if (value) {
                const option: selectedRegionType = {
                  id: value?.id,
                  name: value?.name,
                }
                setNewPrimaryRegion(option)
              }
            }}
            disabled={allRegions?.length === 0}
            required
            noMenuPortal
          />
        </div>
      </div>
    )
  }

  const isActionDisabled = !canWriteOrgUnits

  return [
    {
      name: 'name',
      label: t('orgUnitName'),
      cell: {
        children: (region: RegionType) => (
          <div className='flex align-items-center align-items-center '>
            <span className={sortBy.prop === 'name' ? 'fw-semi-bold' : ''}>
              {region?.name}
            </span>
            {region?.default && (
              <span className='pat-ml-6'>
                <Tag color='green'>{t('primary')}</Tag>
              </span>
            )}
          </div>
        ),
      },
      mainColumn: true,
    },
    {
      name: 'users_with_access',
      label: t('userWithAccess'),
      cell: {
        children: (region: RegionType) => (
          <span
            className={
              sortBy.prop === 'users_with_access' ? 'fw-semi-bold' : ''
            }
          >
            {region?.users_with_access}
          </span>
        ),
      },
    },
    {
      name: 'created_at',
      label: c('created'),
      cell: {
        children: (region: RegionType) => (
          <div>
            <span
              className={`flex align-items-center ${
                sortBy.prop === 'created_at' ? 'fw-semi-bold' : ''
              }`}
            >
              {moment(region.created_at).format('MMMM DD, YYYY')}
            </span>
            {region.created_by_name && (
              <TrimText
                customClass='fs-10 fc-purple'
                limit={24}
                text={region.created_by_name}
              />
            )}
          </div>
        ),
      },
    },
    ...(!canWriteOrgUnits
      ? []
      : [
          {
            name: '',
            label: '',
            noSort: true,
            isButton: true,
            cell: {
              children: (region: RegionType) =>
                isInactiveTab ? (
                  <ButtonGroup
                    buttons={[
                      {
                        actions: [
                          {
                            text: t('editOrgUnit'),
                            icon: 'pencil',
                            callout: () => {
                              setDrawerMode('edit')
                              setSelectedRegion(region)
                            },
                            disabled: { value: isActionDisabled },
                          },
                          {
                            text: t('deleteOrgUnit'),
                            icon: 'trash',
                            destructive: true,
                            disabled: { value: !enableDeleteInOrg },
                            confirmation: {
                              type: 'red',
                              header: t('deleteOrgUnit'),
                              body: (
                                <span>{t('thisOrgUnitWillBeDeleted')}</span>
                              ),
                              confirmCallout: () =>
                                deleteMutation.mutate({
                                  region,
                                  isDelete: true,
                                }),
                            },
                          },
                        ],
                      },
                      {
                        children: t('reactivateOrgUnit'),
                        onClick: () => deleteMutation.mutate({ region }),
                        disabled: isActionDisabled,
                      },
                    ]}
                  />
                ) : (
                  <ButtonGroup
                    buttons={[
                      {
                        icon: 'archive',
                        as: 'confirmation',
                        destructive: true,
                        disabled:
                          !canWriteOrgUnits ||
                          !(allRegions && allRegions?.length > 0),
                        confirmation: {
                          type: 'red',
                          header: t('deactivateOrgUnit'),
                          body: (
                            <>
                              <span>{t('thisOrgUnitWillBeDeactivated')}</span>
                              {region.default && primaryRegionSelection()}
                            </>
                          ),
                          cancelCallout: () => setNewPrimaryRegion(undefined),
                          confirmCallout: () =>
                            deleteMutation.mutate({ region }),
                        },
                        tooltip: {
                          tooltipContent: t('deactivateOrgUnit'),
                        },
                      },
                      {
                        onClick: () => {
                          setDrawerMode('edit')
                          setSelectedRegion(region)
                        },
                        disabled: isActionDisabled,
                        children: t('editOrgUnit'),
                      },
                    ]}
                  />
                ),
            },
          },
        ]),
  ]
}
