import React, { useContext, useEffect, useMemo, useState } from 'react'
import {
  getApiUrlPrefix,
  MultiSelect,
  Switch,
  TextInput,
} from '@patterninc/react-ui'
import { useQuery } from '@tanstack/react-query'

import {
  ACCESSIBLE_PRIVILEGES,
  ADMIN_ACCESS_PRIVILEGE,
  type FormState,
  type PrivilegesOptions,
  type Role,
} from './RoleTypes'
import { SecondaryAppContext } from '../contexts/SecondaryAppContext'
import SecureAxios from '../../common/services/SecureAxios'
import { ThemeContext } from '../../Context'
import styles from './_roles.module.scss'
import { useTranslate } from '../../common/services/TranslationService'

type RoleFormProps = {
  formState: FormState
  setFormState: React.Dispatch<React.SetStateAction<FormState>>
  errorText: string | null
  validateRoleName: () => void
  privilegesOptions: PrivilegesOptions
  isPrivilegesLoading: boolean
  action: string
}

const RoleForm = ({
  formState,
  setFormState,
  errorText,
  validateRoleName,
  privilegesOptions,
  isPrivilegesLoading,
}: RoleFormProps): React.JSX.Element => {
  const { t } = useTranslate('roles')
  // ROLE MANAGEMENT STATE
  const [accessibleRoles, setAccessibleRoles] = useState<
    Array<{ name: string }>
  >([])
  const { app } = useContext(ThemeContext)
  const { secondaryApp } = useContext(SecondaryAppContext)
  const apiUrl = `${getApiUrlPrefix('adminczar')}/api/v1/apps/${secondaryApp.id}/roles`
  const isAdminApp = useMemo(
    () => app.name.toLocaleLowerCase() === 'admin',
    [app],
  )

  const shouldDisplayRolesToggle = useMemo(() => {
    const privileges = formState.systemRolePrivileges

    return privileges.some((privilege) =>
      ACCESSIBLE_PRIVILEGES.includes(privilege.name),
    )
  }, [formState.systemRolePrivileges])

  const { data: rolesData = [], isLoading } = useQuery({
    queryKey: [apiUrl, 'roles'],
    queryFn: async ({ signal }) => {
      const data = await SecureAxios.get(apiUrl, {
        params: {
          status: ['active', 'inactive'],
          ...(isAdminApp ? { is_admin_app: 'true' } : {}),
          get_all_roles: true,
        },
        signal,
      })
      return data.data.data
    },
  })

  useEffect(() => {
    if (formState.accessibleRoles?.length >= 0 && rolesData.length > 0) {
      const accessibleRoles = formState.accessibleRoles
        .map((roleId) => {
          const role = rolesData.find((r: Role) => r.id === roleId)
          return role?.name
            ? {
                name: role.name,
                secondaryOption:
                  role.status === 'inactive' ? t('inactive') : '',
              }
            : null
        })
        .filter(Boolean) as Array<{ name: string }>

      setAccessibleRoles(accessibleRoles)
    }
  }, [rolesData, formState.accessibleRoles, t])

  const activeRoleOptions = useMemo(
    () =>
      rolesData
        .filter((role: Role) => role.status === 'active')
        .map((role: Role) => ({ name: role.name })),
    [rolesData],
  )

  const renderRoleManagement = () => {
    return (
      <>
        {formState.systemPrivilegeToggle && shouldDisplayRolesToggle && (
          <div className='single-filter select flex align-items-center fs-12 fw-regular fc-purple'>
            <Switch
              className='pat-mr-2'
              checked={formState.accessToAllRoles}
              callout={() => {
                setFormState((prevState) => ({
                  ...prevState,
                  accessToAllRoles: !formState.accessToAllRoles,
                }))
              }}
            />

            {t('manageAllCurrentAndFutureRoles')}
          </div>
        )}

        {formState.systemPrivilegeToggle &&
          shouldDisplayRolesToggle &&
          !formState.accessToAllRoles && (
            <>
              {formState.accessibleRoles.length === 0 && (
                <div className={`${styles.errorTextStyle}`}>
                  {t('pleaseSelectAtLeastOneAccessibleRole')}
                </div>
              )}
              <MultiSelect
                selectedOptions={accessibleRoles}
                options={activeRoleOptions}
                formLabelProps={{
                  label: t('roleManagement'),
                  required: true,
                }}
                callout={(selectedList) => {
                  setAccessibleRoles(selectedList)
                  setFormState((prevState) => ({
                    ...prevState,
                    accessibleRoles: selectedList.map(
                      (role) =>
                        rolesData.find((r: Role) => r.name === role.name)?.id,
                    ),
                  }))
                }}
                selectPlaceholder={`-- ${t('selectAccessibleRole')} --`}
                labelKey='name'
                loading={isLoading}
                searchBarProps={{
                  placeholder: t('searchAccessibleRoles'),
                  show: true,
                }}
              />
            </>
          )}
      </>
    )
  }

  const renderPrivilegeManagement = () => {
    return (
      <>
        <div className='single-filter select flex align-items-center fs-12 fw-regular fc-purple'>
          <Switch
            className='pat-mr-2'
            checked={formState.systemPrivilegeToggle}
            callout={() => {
              setFormState((prevState) => {
                return {
                  ...prevState,
                  systemPrivilegeToggle: !formState.systemPrivilegeToggle,
                  systemRolePrivileges: !formState.systemPrivilegeToggle
                    ? []
                    : formState.systemRolePrivileges,
                  accessibleRoles: !formState.systemPrivilegeToggle
                    ? []
                    : formState.accessibleRoles,
                }
              })
            }}
          />
          {t('adminAccessPrivilege')}
        </div>

        {formState.systemPrivilegeToggle && (
          <>
            {formState.systemRolePrivileges.length === 0 && (
              <div className={`${styles.errorTextStyle}`}>
                {t('pleaseSelectAtLeastOnePrivilege')}
              </div>
            )}
            <MultiSelect
              selectedOptions={formState.systemRolePrivileges}
              loading={isPrivilegesLoading}
              options={privilegesOptions.filter(
                (privilege) =>
                  privilege.admin_privilege === true &&
                  privilege.name !== ADMIN_ACCESS_PRIVILEGE,
              )}
              formLabelProps={{
                label: t('adminSystemPrivileges'),
                required: true,
              }}
              callout={(selectedList) => {
                setFormState((prevState) => ({
                  ...prevState,
                  systemRolePrivileges: selectedList,
                }))
              }}
              exposed
              labelKey='name'
              searchBarProps={{
                placeholder: t('searchAdminSystemPrivileges'),
              }}
            />
          </>
        )}
      </>
    )
  }

  return (
    <div className='flex flex-direction-column pat-gap-4'>
      <TextInput
        labelText={t('roleName')}
        value={formState.roleName || ''}
        callout={(_, value) => {
          setFormState((prevState) => ({
            ...prevState,
            roleName: value.toString().trim(),
          }))
        }}
        type='text'
        stateName='roleName'
        placeholder={t('typeAUniqueRoleName')}
        required
        onBlurCallout={validateRoleName}
        classType={!errorText ? '' : 'error'}
        errorText={errorText ? errorText : ''}
      />

      <TextInput
        labelText={t('roleCode')}
        value={formState.roleCode || ''}
        callout={(_, value) => {
          setFormState((prevState) => ({
            ...prevState,
            roleCode: value.toString().trim(),
          }))
        }}
        disabled
        type='text'
        stateName='roleCode'
        required
      />

      {secondaryApp.name !== t('common:admin') && (
        <MultiSelect
          selectedOptions={formState.rolePrivileges}
          loading={isPrivilegesLoading}
          options={privilegesOptions.filter(
            (privilege) => privilege.admin_privilege === false,
          )}
          formLabelProps={{
            label: t('applicationPrivileges'),
          }}
          callout={(selectedList) => {
            setFormState((prevState) => ({
              ...prevState,
              rolePrivileges: selectedList,
            }))
          }}
          exposed
          labelKey='name'
          searchBarProps={{
            placeholder: t('searchApplicationPrivileges'),
          }}
        />
      )}
      {renderPrivilegeManagement()}

      {renderRoleManagement()}
    </div>
  )
}

export default RoleForm
