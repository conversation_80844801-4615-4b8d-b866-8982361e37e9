import React, { useC<PERSON>back, useEffect, useMemo, useState } from 'react'
import {
  capitalize,
  FormFooter,
  getApiUrlPrefix,
  Icon,
  notEmpty,
  SideDrawer,
  Tag,
  toast,
  Tooltip,
  useToggle,
} from '@patterninc/react-ui'
import { useMutation, useQuery } from '@tanstack/react-query'
import { uniqBy } from 'lodash'

import SecureAxios from '../../common/services/SecureAxios'
import {
  ACCESSIBLE_PRIVILEGES,
  ADMIN_ACCESS_PRIVILEGE,
  type FormState,
  type ParamsType,
  type PrivilegeType,
  type Role,
} from './RoleTypes'
import RoleForm from './RoleForm'
import { keyTextConversion } from '../../common/services/HelperService'
import { c, useTranslate } from '../../common/services/TranslationService'

export type RoleFormProps = {
  isOpen: boolean
  closeDrawer: () => void
  action: string
  selectedRole: Role | null
  secondaryApp: { id: string; name: string }
  primaryApp: { id: string; name: string }
  setShouldReload: (status: boolean) => void
  duplicateRole?: boolean
}

export type RoleError = {
  data: {
    message: string
    duplicate_role?: boolean
    data?: { id: number; name: string }[]
  }
}

const RoleFormContainer = ({
  isOpen,
  closeDrawer,
  action,
  selectedRole,
  secondaryApp,
  primaryApp,
  setShouldReload,
  duplicateRole,
}: RoleFormProps): React.JSX.Element => {
  const { t } = useTranslate('roles')
  const isTagInputEnabled = useToggle('tag_input_changes')
  const privilegeDescription = useCallback(
    (privilege: PrivilegeType) => (
      <div className='flex align-items-center pat-gap-4'>
        {privilege.status === 'inactive' &&
          (isTagInputEnabled ? (
            <Tag color='light-gray'>{t('inactive')}</Tag>
          ) : (
            <span className='fc-purple'>{t('inactive')}</span>
          ))}
        {privilege.description && (
          <Tooltip
            tooltipContent={
              <div className='fs-12'>{privilege.description}</div>
            }
          >
            <Icon icon='info' iconSize='16px' color='dark-blue' />
          </Tooltip>
        )}
      </div>
    ),
    [isTagInputEnabled, t],
  )

  const mapPrivileges = useCallback(
    ({
      privileges,
      hasSystemPrivilege,
    }: {
      privileges: PrivilegeType[]
      hasSystemPrivilege: boolean
    }) => {
      return privileges
        .filter(
          (privilege) =>
            privilege.admin_privilege === hasSystemPrivilege &&
            privilege.name !== ADMIN_ACCESS_PRIVILEGE,
        )
        .map((privilege) => {
          return {
            name: privilege.name,
            admin_privilege: privilege.admin_privilege,
            secondaryOption: privilegeDescription(privilege),
          }
        })
    },
    [privilegeDescription],
  )

  const isAdminApp = useMemo(
    () => primaryApp.name.toLocaleLowerCase() === 'admin',
    [primaryApp],
  )
  const [formState, setFormState] = useState<FormState>({
      roleName: selectedRole?.name || '',
      roleCode: selectedRole?.code || '',
      rolePrivileges: selectedRole?.privileges
        ? mapPrivileges({
            privileges: selectedRole.privileges,
            hasSystemPrivilege: false,
          })
        : [],
      systemRolePrivileges: selectedRole?.privileges
        ? mapPrivileges({
            privileges: selectedRole.privileges,
            hasSystemPrivilege: true,
          })
        : [],
      systemPrivilegeToggle: !!(
        selectedRole?.privileges &&
        mapPrivileges({
          privileges: selectedRole.privileges,
          hasSystemPrivilege: true,
        }).length > 0
      ),
      status: {
        display: capitalize(selectedRole?.status || 'Active'),
        value: selectedRole?.status || 'active',
      },
      accessToAllRoles: selectedRole?.access_to_all_roles ?? false,
      accessibleRoles: selectedRole?.accessible_roles || [],
    }),
    [disabled, setDisabled] = useState(false),
    [privileges, setPrivileges] = useState<PrivilegeType[]>([]),
    [errorText, setErrorText] = useState<string | null>(null),
    [allowDuplicatePrivileges, setAllowDuplicatePrivileges] = useState(false)
  const apiUrl = `${getApiUrlPrefix(
      'adminczar',
    )}/api/v1/apps/${secondaryApp.id}/privileges/privileges_with_admin_app`,
    { isLoading, data: privilegesResponse } = useQuery({
      queryKey: [apiUrl, secondaryApp.id],
      queryFn: async ({ signal }) => {
        const data = await SecureAxios.get(apiUrl, {
          signal,
          params: {
            ...(isAdminApp ? { is_admin_app: 'true' } : {}),
            sort: 'name',
            filter: {
              status: {
                eql: 'active',
              },
            },
          },
        })
        return data
      },
      enabled: !!secondaryApp.id,
    })

  const mergeUniquePrivileges = (arr1: [], arr2: PrivilegeType[]) => {
    const mergedData = new Map()

    ;[...arr1, ...arr2].forEach((item) => {
      mergedData.set(item['id'], item)
    })

    return Array.from(mergedData.values())
  }

  const privilegesOptions = useMemo(() => {
    const response = privilegesResponse?.data?.data || []
    const selectedPrivileges = selectedRole?.privileges || []
    if (action === 'edit' && selectedRole?.privileges) {
      setFormState((prevState) => ({
        ...prevState,
        rolePrivileges: mapPrivileges({
          privileges: selectedRole.privileges,
          hasSystemPrivilege: false,
        }),
      }))
    }
    const roleWithResponsePrivileges = mergeUniquePrivileges(
      response,
      selectedPrivileges,
    )
    setPrivileges?.(roleWithResponsePrivileges || [])
    if (!roleWithResponsePrivileges) return []

    return roleWithResponsePrivileges.map((privilege: PrivilegeType) => {
      return {
        name: privilege.name,
        admin_privilege: privilege.admin_privilege,
        secondaryOption: privilegeDescription(privilege),
      }
    })
  }, [
    action,
    privilegesResponse?.data?.data,
    selectedRole?.privileges,
    mapPrivileges,
    privilegeDescription,
  ])

  const validateRoleName = useCallback(() => {
    if (formState.roleName.length > 50)
      setErrorText(t('regionNameExceedsCharacterLimit'))
    else if (
      formState.roleName.length > 0 &&
      !formState.roleName.match(/^[a-zA-Z0-9 /[\](){}\-_]+$/)
    )
      setErrorText(t('specialCharactersAreNotAllowed'))
    else setErrorText(null)
  }, [formState.roleName, t])

  const hasUserPrivileges = useMemo(() => {
    const privileges = formState.systemRolePrivileges
    return privileges.some((privilege) =>
      ACCESSIBLE_PRIVILEGES.includes(privilege.name),
    )
  }, [formState.systemRolePrivileges])

  useEffect(() => {
    if (isOpen) {
      const {
        roleCode,
        roleName,
        accessToAllRoles,
        accessibleRoles,
        systemPrivilegeToggle,
        systemRolePrivileges,
      } = formState

      const isRoleInfoValid = notEmpty(roleCode) && notEmpty(roleName)
      const hasInvalidPrivileges =
        systemPrivilegeToggle &&
        ((hasUserPrivileges &&
          !accessToAllRoles &&
          accessibleRoles.length === 0) ||
          systemRolePrivileges.length === 0)

      setDisabled(!isRoleInfoValid || hasInvalidPrivileges)
    }
  }, [
    formState,
    formState.roleCode,
    formState.roleName,
    hasUserPrivileges,
    isOpen,
  ])

  useEffect(() => {
    if (formState.roleName) {
      const roleCode = keyTextConversion(formState.roleName)
      setFormState((prevState) => ({
        ...prevState,
        roleCode: roleCode,
      }))
    }
  }, [formState.roleName])

  useEffect(() => {
    if (duplicateRole && selectedRole) {
      setFormState((prevState) => ({
        ...prevState,
        roleName: selectedRole.name.concat('(Copy)'),
      }))
    }
  }, [duplicateRole, selectedRole])

  const onSubmit = () => {
    const {
      roleName,
      roleCode,
      rolePrivileges,
      systemRolePrivileges,
      status,
      systemPrivilegeToggle,
    } = formState
    let aggregatedPrivileges = systemPrivilegeToggle
      ? (() => {
          const combinedPrivileges = [
            ...rolePrivileges,
            ...systemRolePrivileges,
          ]
          const uniqueNames = new Set(
            combinedPrivileges.map((privilege) => privilege.name),
          )
          const privilegeMap = Object.fromEntries(
            combinedPrivileges.map((privilege) => [privilege.name, privilege]),
          )

          return Array.from(uniqueNames).map((name) => privilegeMap[name])
        })()
      : rolePrivileges

    if (systemPrivilegeToggle) {
      const adminPrivilege = privileges.find(
        (privilege: PrivilegeType) => privilege.name === ADMIN_ACCESS_PRIVILEGE,
      )
      if (adminPrivilege) {
        const newPrivilege = {
          name: adminPrivilege.name,
          admin_privilege: adminPrivilege.admin_privilege,
        }
        aggregatedPrivileges = aggregatedPrivileges.concat(newPrivilege)
      }
    }
    const assignedPrivilegesIds: { id: string }[] =
      aggregatedPrivileges
        ?.map((rolePrivilege) => {
          return privileges.find(
            (privilege: PrivilegeType) =>
              privilege?.name === rolePrivilege.name,
          )
        })
        .filter((privilege): privilege is PrivilegeType => Boolean(privilege))
        .map((privilege: PrivilegeType) => ({ id: privilege.id })) || []

    const uniquePrivilegeIds = uniqBy(assignedPrivilegesIds, 'id').map(
      (privilege) => privilege.id,
    )
    const role_params = {
      params: {
        role: {
          name: roleName,
          code: roleCode,
          privilege_ids: uniquePrivilegeIds,
          status: status.value,
          ...(action === 'create' || duplicateRole
            ? {}
            : { id: selectedRole?.id }),
          ...(!formState.accessToAllRoles &&
          formState.accessibleRoles?.length > 0
            ? { accessible_role_ids: formState.accessibleRoles }
            : { accessible_role_ids: [] }),
          access_to_all_roles:
            systemPrivilegeToggle && !!formState.accessToAllRoles,
        },
        ...(allowDuplicatePrivileges
          ? { allow_duplicate_privileges: true }
          : {}),
      },
      apiUrl:
        action === 'create' || duplicateRole
          ? `${getApiUrlPrefix('adminczar')}/api/v1/apps/${secondaryApp.id}/roles`
          : `${getApiUrlPrefix('adminczar')}/api/v1/apps/${secondaryApp.id}/roles/${selectedRole?.id}`,
      method: action === 'create' || duplicateRole ? 'POST' : 'PUT',
    }
    mutation.mutate(role_params)
  }

  const mutation = useMutation({
    mutationFn: (roleParams: ParamsType) => {
      const { method, apiUrl, params } = roleParams
      return SecureAxios[method === 'POST' ? 'post' : 'put'](apiUrl, {
        ...params,
        ...(isAdminApp ? { is_admin_app: 'true' } : {}),
      })
    },
    onSuccess: () => {
      toast({
        message:
          action === 'create' || duplicateRole
            ? t('roleCreatedSuccessfully')
            : t('roleUpdatedSuccessfully'),
        type: 'success',
      })
      setShouldReload(true)
      closeDrawer()
    },
    onError: (error: RoleError) => {
      const errorText = error?.data?.message
      if (error?.data?.duplicate_role) {
        const duplicateRoleNames = error?.data?.data?.map((role) => role.name)
        toast({
          message: `${errorText ? t('roleIsDuplicate', { roles: duplicateRoleNames }) : c('somethingWentWrongPleaseTryAgain')}`,
          type: 'error',
          config: {
            closeOnClick: true,
          },
          buttons: [
            {
              children: t('continueWithDuplicatePrivilege'),
              onClick: () => {
                setAllowDuplicatePrivileges(true)
                setDisabled(false)
              },
            },
          ],
        })
      } else {
        toast({
          message: `${errorText ? errorText : c('somethingWentWrongPleaseTryAgain')}`,
          type: 'error',
          ...(duplicateRole
            ? {
                config: {
                  position: 'top-center',
                },
              }
            : {}),
        })
      }
    },
  })

  return (
    <SideDrawer
      isOpen={isOpen}
      closeCallout={() => closeDrawer()}
      headerContent={`${duplicateRole ? t('duplicateRole') : action === 'create' ? t('createNewRole') : t('editRole')}`}
      footerContent={
        <FormFooter
          cancelButtonProps={{
            onClick: () => {
              closeDrawer()
            },
          }}
          saveButtonProps={{
            disabled: disabled || isLoading,
            onClick: () => {
              onSubmit()
            },
          }}
        />
      }
    >
      <RoleForm
        formState={formState}
        setFormState={setFormState}
        errorText={errorText}
        validateRoleName={validateRoleName}
        privilegesOptions={privilegesOptions}
        isPrivilegesLoading={isLoading}
        action={action}
      />
    </SideDrawer>
  )
}

export default RoleFormContainer
