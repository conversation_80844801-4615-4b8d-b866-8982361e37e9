export type Role = {
  id: string
  name: string
  description: string
  status: string
  code: string
  user_count: number
  privilege_count: number
  privileges: PrivilegeType[]
  accessible_roles: string[]
  systemRolePrivileges: PrivilegesOptions
  access_to_all_roles: boolean
}

export type ParamsType = {
  method: string
  apiUrl: string
  params: Record<string, unknown>
}

export type PrivilegeType = {
  secondaryOption?: React.ReactNode
  id: string
  name: string
  admin_privilege: boolean
  status: string
  description: string
}

export type PrivilegesOptions = Array<{
  name: string
  admin_privilege: boolean
  secondaryOption?: React.ReactNode
}>

export type FormState = {
  roleName: string
  roleCode: string
  rolePrivileges: PrivilegesOptions
  systemRolePrivileges: PrivilegesOptions
  systemPrivilegeToggle: boolean
  status: { display: string; value: string }
  accessToAllRoles: boolean
  accessibleRoles: string[]
}

export const ACCESSIBLE_PRIVILEGES = [
  'Read Users',
  'Write Users',
  'Create Users',
  'Manage Users',
]
export const ADMIN_ACCESS_PRIVILEGE = 'Access'
