import React, { useContext, useEffect, useMemo, useState } from 'react'
import {
  ButtonGroup,
  getApiUrl<PERSON>refix,
  PageFooter,
  PageHeader,
  type SortColumnProps,
  standardSortParams,
  StandardTable,
  toast,
  useToggle,
} from '@patterninc/react-ui'
import { useInfiniteQuery, useMutation } from '@tanstack/react-query'

import { ThemeContext } from '../../Context'
import { type ParamsType, type Role } from './RoleTypes'
import SecureAxios from '../../common/services/SecureAxios'
import RoleFormContainer, { type RoleError } from './RoleFormContainer'
import SecondaryAppDropDown from '../../common/SecondaryAppDropDown'
import { hasWritePermission } from '../../Auth/route-permissions'
import RolesTab from './RolesTab'
import { SecondaryAppContext } from '../contexts/SecondaryAppContext'
import { c, useTranslate } from '../../common/services/TranslationService'
type RolesProps = {
  isInactiveTab?: boolean
}

const Roles = ({ isInactiveTab }: RolesProps): React.JSX.Element => {
  const { t } = useTranslate('roles'),
    { updateBreadcrumbs, privileges, app } = useContext(ThemeContext),
    [searchText, setSearchText] = useState<string>(''),
    [sortBy, setSort] = useState({
      prop: 'name',
      flip: true,
    }),
    [isSideDrawerOpen, setSideDrawerOpen] = useState<boolean>(false),
    [duplicateRole, setDuplicateRole] = useState<boolean>(false),
    [action, setAction] = useState<string>(''),
    [selectedRole, setSelectedRole] = useState<Role | null>(null),
    [shouldReload, setShouldReload] = useState(false),
    isAccessibleRolesEnabled = useToggle('roles_with_accessible_users')

  const { secondaryApp } = useContext(SecondaryAppContext)
  const isAdminApp = useMemo(
    () => app.name.toLocaleLowerCase() === 'admin',
    [app],
  )

  useEffect(() => {
    updateBreadcrumbs({
      name: c('roles'),
      link: '/roles',
      changeType: 'rootLevel',
    })
  }, [updateBreadcrumbs])

  const apiUrl = `${getApiUrlPrefix('adminczar')}/api/v1/apps/${secondaryApp.id}/roles`
  const {
    data: rolesPaginatedData,
    fetchNextPage,
    hasNextPage,
    isLoading,
    status,
  } = useInfiniteQuery({
    queryKey: [
      apiUrl,
      sortBy,
      searchText,
      secondaryApp.id,
      shouldReload,
      isInactiveTab,
      app.id,
    ],
    queryFn: async ({ pageParam = 1, signal }) => {
      const data = await SecureAxios.get(apiUrl, {
        params: {
          page: pageParam,
          per_page: 20,
          sort: standardSortParams(sortBy),
          status: isInactiveTab ? 'inactive' : 'active',
          ...(searchText ? { search_for: searchText } : {}),
          ...(isAdminApp ? { is_admin_app: 'true' } : {}),
          ...(isAccessibleRolesEnabled ? { get_all_roles: true } : {}),
        },
        signal,
      })
      setShouldReload(false)
      return data
    },
    initialPageParam: 1,
    getNextPageParam: (previousResponse) => {
      return previousResponse?.data?.pagination?.last_page
        ? undefined
        : previousResponse?.data?.pagination?.next_page
    },
    enabled: !!secondaryApp.id && !!app.id,
  })

  const rolesData = useMemo(
    () =>
      rolesPaginatedData
        ? rolesPaginatedData?.pages?.flatMap((page) => {
            return page?.data?.data
          })
        : [],
    [rolesPaginatedData],
  )
  const hasData = !!(status === 'success' && rolesData?.length),
    totalCount = rolesPaginatedData?.pages[0].data.pagination.count

  const setSortBy: SortColumnProps['sorter'] = (sortObj) => {
    setSort({
      prop: sortObj.activeColumn,
      flip: sortObj.direction,
    })
  }

  const deleteMutation = useMutation({
    mutationFn: async (role: Role) => {
      const url = `${getApiUrlPrefix('adminczar')}/api/v1/apps/${secondaryApp.id}/roles/${role?.id}`
      return await SecureAxios.delete(url, {
        params: {
          ...(isAdminApp ? { is_admin_app: 'true' } : {}),
        },
      })
    },
    onSuccess: () => {
      setShouldReload(true)
      toast({
        type: 'success',
        message: t('roleSuccessfullyDeleted'),
      })
    },
    onError: (error: RoleError) => {
      const errorText = error?.data?.message
      toast({
        message: errorText || c('somethingWentWrongPleaseTryAgain'),
        type: 'error',
      })
    },
  })

  const canWriteRoles = useMemo(
    () => hasWritePermission('roles', privileges),
    [privileges],
  )

  const updateMutation = useMutation({
    mutationFn: async (updationParams: ParamsType) => {
      const { apiUrl, params } = updationParams
      return await SecureAxios.put(apiUrl, params)
    },
    onSuccess: () => {
      setShouldReload(true)
      toast({
        type: 'success',
        message: t('roleSuccessfullyUpdated'),
      })
    },
    onError: (error: { data: { message: string } }) => {
      const errorText = error?.data?.message
      toast({
        message: `${errorText ? errorText : c('somethingWentWrongPleaseTryAgain')}`,
        type: 'error',
      })
    },
  })

  const updateRoleStatus = (role: Role, status: string) => {
    const statusParams = {
      method: 'PUT',
      apiUrl: `${getApiUrlPrefix('adminczar')}/api/v1/apps/${secondaryApp.id}/roles/${role?.id}`,
      params: {
        role: {
          status: status,
          id: role?.id,
        },
        ...(isAdminApp ? { is_admin_app: 'true' } : {}),
      },
    }

    updateMutation.mutate(statusParams)
  }

  const renderActionColumn = (data: Role) => {
    return isInactiveTab ? (
      <ButtonGroup
        buttons={[
          {
            actions: [
              {
                text: t('editRole'),
                icon: 'pencil',
                callout: () => {
                  setSelectedRole(data)
                  setSideDrawerOpen(true)
                  setAction('edit')
                },
                disabled: { value: !canWriteRoles },
              },
              {
                text: t('deleteRole'),
                icon: 'trash',
                destructive: true,
                disabled: {
                  value: data?.user_count > 0 || !canWriteRoles,
                },
                confirmation: {
                  type: 'red',
                  header: t('deleteRole'),
                  body: (
                    <span>{t('thisRoleWillBeDeletedThisCannotBeUndone')}</span>
                  ),
                  confirmCallout: () => {
                    deleteMutation.mutate(data)
                  },
                },
              },
            ],
          },
          {
            children: t('reactivateRole'),
            onClick: () => updateRoleStatus(data, 'active'),
            disabled: !canWriteRoles,
          },
        ]}
      />
    ) : (
      <ButtonGroup
        buttons={[
          {
            actions: [
              {
                text: t('duplicateRole'),
                icon: 'documents',
                callout: () => {
                  setSelectedRole(data)
                  setDuplicateRole(true)
                  setSideDrawerOpen(true)
                },
                disabled: { value: !canWriteRoles },
              },
              {
                text: t('deactivateRole'),
                icon: 'unfollow',
                confirmation: {
                  type: 'red',
                  header: c('areYouSure'),
                  body: t('thisRoleWillNoLongerHaveAccessToAppAreYouSure', {
                    appName: secondaryApp?.name,
                  }),
                  confirmCallout: () => updateRoleStatus(data, 'inactive'),
                },
                disabled: { value: !canWriteRoles },
              },
            ],
          },
          {
            children: t('editRole'),
            disabled: !canWriteRoles,
            onClick: () => {
              setSelectedRole(data)
              setSideDrawerOpen(true)
              setAction('edit')
            },
          },
        ]}
      />
    )
  }

  const rolesConfig = [
    {
      name: 'name',
      label: t('roleName'),
      cell: {
        children: (data: Role) => (
          <span
            className={`flex align-items-center ${
              sortBy.prop === 'name' ? 'fw-semi-bold' : ''
            }`}
          >
            {data?.name}
          </span>
        ),
      },
      mainColumn: true,
    },
    {
      name: 'code',
      label: t('roleCode'),
      cell: {
        children: (data: Role) => (
          <span
            className={`flex align-items-center ${
              sortBy.prop === 'code' ? 'fw-semi-bold' : ''
            }`}
          >
            {data?.code}
          </span>
        ),
      },
    },
    {
      name: 'user_count',
      label: t('#ofUsers'),
      cell: {
        children: (data: Role) => <span>{data?.user_count}</span>,
      },
    },
    {
      name: 'privilege_count',
      label: t('privilegesCount'),
      cell: {
        children: (data: Role) => <span>{data?.privilege_count}</span>,
      },
    },
    ...(!canWriteRoles
      ? []
      : [
          {
            name: '',
            label: '',
            noSort: true,
            cell: {
              children: (data: Role) => {
                return renderActionColumn(data)
              },
            },
          },
        ]),
  ]

  const searchInputHandler = (query: string) => {
    setSearchText(query)
  }

  const createNewRole = () => {
    setSideDrawerOpen(true)
    setAction('create')
  }

  const closeDrawer = () => {
    setSideDrawerOpen(false)
    setAction('')
    setSelectedRole(null)
    setDuplicateRole(false)
  }

  return (
    <>
      <PageHeader
        header={{
          name: c('roles'),
          value: totalCount,
        }}
        search={{
          placeholder: t('searchRoles'),
          value: searchText,
          onChange: searchInputHandler,
        }}
        rightSectionChildren={<SecondaryAppDropDown />}
      />
      <RolesTab />
      <StandardTable
        data={rolesData}
        config={rolesConfig}
        loading={isLoading}
        dataKey='id'
        hasData={hasData}
        hasMore={!!hasNextPage}
        successStatus={status === 'success'}
        noDataFields={{
          primaryText: t('noRolesFound'),
          secondaryText: t('weCouldNotFindAnyRolesForTheSelectedCriteria'),
        }}
        tableId={'roles-table'}
        customHeight={'auto'}
        sort={setSortBy}
        sortBy={sortBy}
        getData={fetchNextPage}
      />
      {!isInactiveTab && (
        <PageFooter
          rightSection={
            canWriteRoles
              ? [
                  {
                    as: 'button',
                    onClick: () => createNewRole(),
                    disabled: !canWriteRoles,
                    children: t('createNewRole'),
                    styleType: 'primary-green',
                    type: 'button',
                  },
                ]
              : []
          }
        />
      )}

      {isSideDrawerOpen && (
        <RoleFormContainer
          isOpen={isSideDrawerOpen}
          closeDrawer={closeDrawer}
          action={action}
          selectedRole={selectedRole}
          secondaryApp={secondaryApp}
          primaryApp={app}
          setShouldReload={setShouldReload}
          duplicateRole={duplicateRole}
        />
      )}
    </>
  )
}

export default Roles
