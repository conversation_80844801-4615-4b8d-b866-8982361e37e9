import React from 'react'
import { Navigate, Route, Routes } from 'react-router-dom'

import PrivateRoute from '../../common/components/PrivateRoute/PrivateRoute'
import Roles from './Roles'
import {
  SecondaryAppContext,
  SecondaryAppProvider,
} from '../contexts/SecondaryAppContext'

const RolesRoutes = (): React.JSX.Element => {
  const renderRolesRoute = () => (
    <Routes>
      <Route
        path='active'
        element={
          <PrivateRoute>
            <Roles />
          </PrivateRoute>
        }
      />
      <Route
        path='inactive'
        element={
          <PrivateRoute>
            <Roles isInactiveTab />
          </PrivateRoute>
        }
      />
      <Route index element={<Navigate to='active' replace />} />
    </Routes>
  )

  return (
    <SecondaryAppProvider>
      <SecondaryAppContext.Consumer>
        {() => renderRolesRoute()}
      </SecondaryAppContext.Consumer>
    </SecondaryAppProvider>
  )
}

export default RolesRoutes
