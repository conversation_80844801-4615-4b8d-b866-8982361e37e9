import React from 'react'
import { RouterTabs } from '@patterninc/react-ui'
import { NavLink, useLocation, useNavigate } from 'react-router-dom'

import { c } from '../../common/services/TranslationService'

const RolesTab = (): React.JSX.Element => {
  const navigate = useNavigate()
  const pathname = useLocation().pathname

  const navigateMobileTabs = (link: string) => {
    navigate(link)
  }

  return (
    <RouterTabs
      mobileConfig={[
        { label: c('active'), link: '/roles/active' },
        { label: c('inactive'), link: '/roles/inactive' },
      ]}
      navigate={navigateMobileTabs}
      currentPath={pathname}
    >
      <NavLink to='/roles/active'>{c('active')}</NavLink>
      <NavLink to='/roles/inactive'>{c('inactive')}</NavLink>
    </RouterTabs>
  )
}

export default RolesTab
