import React, { type SyntheticEvent, useState } from 'react'
import {
  Button,
  EmptyState,
  getApiUrlPrefix,
  hasValue,
  ListLoading,
  notEmpty,
  TextInput,
  toast,
} from '@patterninc/react-ui'
import { useSearchParams } from 'react-router-dom'
import { useQuery } from '@tanstack/react-query'

import styles from './_sign-up.module.scss'
import SecureAxios from '../../common/services/SecureAxios'
import { c, useTranslate } from '../../common/services/TranslationService'

interface FormState {
  password: string
  confirmPassword: string
}

interface FormValidity {
  password: boolean
  confirmPassword: boolean
}
type StateNameType = string | number | undefined

export const passwordLength = 12

const ChangePassword = (): React.JSX.Element => {
  const { t } = useTranslate('signup')

  const [state, setState] = useState<FormState>({
    password: '',
    confirmPassword: '',
  })

  const [formValidity, setFormValidity] = useState<FormValidity>({
    password: true,
    confirmPassword: true,
  })
  const [buttonDisabled, setButtonDisabled] = useState<boolean>(true)
  const [password, setPassword] = useState('')
  const [searchParams] = useSearchParams()
  const invitation_code = searchParams.get('token')

  const inputHandler = (name: string, value: string) => {
    if (value || value === '') {
      const requiredFields: string[] = ['password', 'confirmPassword']
      const updatedState: { [key: string]: string } = { ...state }
      const passwordsMatch = (
        password: string,
        confirmPassword: string,
      ): boolean => {
        return password === confirmPassword
      }
      const updatedFormState: FormState = {
        ...state,
        [name]: value,
        password: name === 'password' ? value : state.password,
        confirmPassword:
          name === 'confirmPassword' ? value : state.confirmPassword,
      }
      if (name === 'password') {
        const confirmPassword = state.confirmPassword
        setFormValidity({
          ...formValidity,
          [name]: notEmpty(value) && value.length >= passwordLength,
          confirmPassword: confirmPassword
            ? passwordsMatch(value, confirmPassword)
            : true,
        })
      } else if (name === 'confirmPassword') {
        const password = state.password
        setFormValidity({
          ...formValidity,
          confirmPassword: password ? passwordsMatch(password, value) : true,
        })
      } else {
        const check: boolean[] = requiredFields.map((e: string): boolean => {
          return notEmpty(updatedState[e])
        })
        setFormValidity({
          ...formValidity,
          [name]: check.includes(false) ? false : true,
        })
      }
      setState(updatedFormState)
    }
    if (hasValue(value)) {
      setState((prevState) => ({
        ...prevState,
        [name]: value,
      }))
    }
  }

  const validate = (
    stateName: StateNameType,
    value: string | SyntheticEvent,
  ) => {
    if (stateName === 'password') {
      setPassword(value.toString())
      setFormValidity({
        ...formValidity,
        password: notEmpty(value) && value.toString().length >= passwordLength,
      })
    } else if (stateName === 'confirmPassword') {
      setFormValidity({
        ...formValidity,
        confirmPassword: value === password,
      })
      setButtonDisabled(
        !formValidity.password ||
          !formValidity.confirmPassword ||
          formValidity.password !== formValidity.confirmPassword,
      )
    } else {
      setFormValidity({
        ...formValidity,
        [stateName as string]: notEmpty(value),
      })
    }
  }

  const apiUrl = `${getApiUrlPrefix(
      'adminczar',
    )}/api/v1/users/by_reset_token?reset_token=${invitation_code}`,
    {
      data: userData,
      status,
      error,
    } = useQuery({
      queryKey: [apiUrl],
      queryFn: async () => {
        return await SecureAxios.get(apiUrl)
      },
      refetchOnWindowFocus: false,
      retry: false,
    })

  const handleSubmit = () => {
    if (formValidity.password && invitation_code) {
      const params = {
        user: {
          password: state?.password,
          reset_token: invitation_code,
          email: userData?.data?.email,
        },
      }
      const changePasswordUrl = `${getApiUrlPrefix('adminczar')}/api/v1/users/${
        userData?.data?.id
      }/update_password`
      SecureAxios.put(changePasswordUrl, params)
        .then((response) => response.data)
        .then((responseData) => {
          toast({
            type: 'success',
            message: t('passwordHasBeenChangedSuccessfully'),
          })
          const urlList: string[] =
            responseData?.admin_apps_orgs.map(
              (app: { app_org_url: string }) => app.app_org_url,
            ) || []
          const amplifiUrl: string | undefined = urlList.find((url: string) =>
            url.includes('amplifi'),
          )
          if (amplifiUrl) {
            window.location.href = amplifiUrl
          } else if (responseData?.admin_apps_orgs.length > 0) {
            window.location.href = responseData.admin_apps_orgs[0].app_org_url
          }
        })
        .catch(() => {
          toast({
            message: c('somethingWentWrongPleaseTryAgain'),
            type: 'error',
          })
        })
    }
  }

  const appLogo =
    userData?.data?.admin_app?.logo_url ??
    'https://images.pattern.com/pattern_logo.svg'

  return (
    <div className={`${styles.mainContainer} pat-pt-8`}>
      {status === 'pending' ? (
        <div className={styles.container}>
          <ListLoading numberOfRows={3} />
        </div>
      ) : (
        <>
          <img alt='amplifi-logo' className={styles.logo} src={appLogo} />
          <div className={styles.container}>
            {hasValue(error) ? (
              <div className='text-center'>
                <EmptyState
                  primaryText={t('sorryTheLinkIsNoLongerValid')}
                  secondaryText={t(
                    'theLinkYouAreAttemptingToAccessHasExpiredOrBeenUsed',
                  )}
                  icon='link'
                />
              </div>
            ) : (
              <>
                <div
                  className={`${styles.textUnderlineHeading} fc-dark-purple pat-mb-8`}
                >
                  <div className='fs-16 fw-bold'>{t('changePassword')}</div>
                </div>
                <TextInput
                  labelText={t('newPassword')}
                  required
                  value={state.password}
                  callout={(_, value) =>
                    inputHandler('password', value.toString())
                  }
                  onBlurCallout={validate}
                  classType={!formValidity.password ? 'error' : ''}
                  type='password'
                  stateName='password'
                  containerClassName='pat-mb-4'
                  disabled={hasValue(error)}
                  errorText={
                    !formValidity.password
                      ? t('passwordIsRequiredAndMustBeLengthCharactersLong', {
                          length: passwordLength,
                        })
                      : ''
                  }
                />
                <TextInput
                  labelText={t('confirmPassword')}
                  required
                  value={state.confirmPassword}
                  callout={(_, value) =>
                    inputHandler('confirmPassword', value.toString())
                  }
                  onBlurCallout={validate}
                  classType={!formValidity.confirmPassword ? 'error' : ''}
                  type='password'
                  stateName='confirmPassword'
                  containerClassName='pat-mb-4'
                  disabled={hasValue(error)}
                  errorText={
                    !formValidity.confirmPassword
                      ? t('passwordsDoNotMatch')
                      : ''
                  }
                />
                <Button
                  className={styles.createAccountButton}
                  styleType='primary-green'
                  disabled={buttonDisabled}
                  onClick={() => handleSubmit()}
                >
                  {t('changePassword')}
                </Button>
              </>
            )}
          </div>
        </>
      )}
    </div>
  )
}

export default ChangePassword
