import React, { type SyntheticEvent, useMemo, useState } from 'react'
import {
  Button,
  Checkbox,
  EmptyState,
  getApiUrlPrefix,
  hasValue,
  ListLoading,
  notEmpty,
  TextInput,
  toast,
} from '@patterninc/react-ui'
import { NavLink, useSearchParams } from 'react-router-dom'
import { useQuery } from '@tanstack/react-query'

import styles from './_sign-up.module.scss'
import SecureAxios from '../../common/services/SecureAxios'
import { useAuth } from '../../context/auth-context'
import { passwordLength } from './ChangePassword'
import { c, useTranslate } from '../../common/services/TranslationService'

interface FormState {
  given_name: string
  family_name: string
  password: string
  confirmPassword: string
  name: string
  email: string
  acceptterms: boolean
  [key: string]: string | boolean
}

interface FormValidity {
  given_name: boolean
  family_name: boolean
  password: boolean
  confirmPassword: boolean
  name: boolean
  email: boolean
  acceptterms: boolean
}
type StateNameType = string | number | undefined

const SignUp = (): React.JSX.Element => {
  const { t } = useTranslate('signup'),
    { login } = useAuth()

  const [state, setState] = useState<FormState>({
    name: '',
    email: '',
    given_name: '',
    family_name: '',
    password: '',
    confirmPassword: '',
    acceptterms: false,
  })

  const [formValidity, setFormValidity] = useState<FormValidity>({
    name: true,
    email: true,
    given_name: true,
    family_name: true,
    password: true,
    confirmPassword: true,
    acceptterms: false,
  })
  const isButtonDisabled = useMemo(() => {
    return !(
      state.password &&
      state.confirmPassword &&
      state.given_name &&
      state.family_name &&
      state.acceptterms &&
      state.password === state.confirmPassword &&
      state.password.length &&
      state.confirmPassword.length >= passwordLength
    )
  }, [state])
  const [password, setPassword] = useState('')
  const inputHandler = (name: string, value: string) => {
    if (value || value === '') {
      const requiredFields: string[] = [
        'email',
        'name',
        'given_name',
        'family_name',
        'password',
        'confirmPassword',
        'acceptterms',
      ]

      const updatedState: { [key: string]: string | boolean } = { ...state }
      const passwordsMatch = (
        password: string,
        confirmPassword: string,
      ): boolean => {
        return password === confirmPassword
      }
      const updatedFormState: FormState = {
        ...state,
        [name]: value,
        password: name === 'password' ? value : state.password,
        confirmPassword:
          name === 'confirmPassword' ? value : state.confirmPassword,
      }
      if (name === 'password') {
        const confirmPassword = state.confirmPassword
        setFormValidity({
          ...formValidity,
          [name]: notEmpty(value) && value.length >= passwordLength,
          confirmPassword: confirmPassword
            ? passwordsMatch(value, confirmPassword)
            : true,
        })
      } else if (name === 'confirmPassword') {
        const password = state.password
        setFormValidity({
          ...formValidity,
          confirmPassword: password ? passwordsMatch(password, value) : true,
        })
      } else {
        const check: boolean[] = requiredFields.map((e: string): boolean => {
          return notEmpty(updatedState[e])
        })
        setFormValidity({
          ...formValidity,
          [name]: check.includes(false) ? false : true,
        })
      }
      setState(updatedFormState)
    }
    if (hasValue(value)) {
      setState((prevState) => ({
        ...prevState,
        [name]: value,
      }))
    }
  }
  const validate = (
    stateName: StateNameType,
    value: string | SyntheticEvent,
  ) => {
    if (stateName === 'password') {
      setPassword(value.toString())
      setFormValidity({
        ...formValidity,
        password: notEmpty(value) && value.toString().length >= passwordLength,
      })
    } else if (stateName === 'confirmPassword') {
      setFormValidity({
        ...formValidity,
        confirmPassword: value === password,
      })
    } else {
      setFormValidity({
        ...formValidity,
        [stateName as string]: notEmpty(value),
      })
    }
  }

  const [searchParams] = useSearchParams()
  const invitation_code = searchParams.get('code')

  const apiUrl = `${getApiUrlPrefix(
      'adminczar',
    )}/api/v1/users/by_invitation_code?invitation_code=${invitation_code}`,
    {
      data: userData,
      status,
      error,
    } = useQuery({
      queryKey: [apiUrl],
      queryFn: async () => {
        return await SecureAxios.get(apiUrl)
      },
      refetchOnWindowFocus: false,
      retry: false,
    })

  const handleSubmit = () => {
    if (
      formValidity.name &&
      formValidity.given_name &&
      formValidity.family_name &&
      formValidity.password &&
      formValidity.confirmPassword
    ) {
      const params = {
        user: {
          given_name: state?.given_name,
          family_name: state?.family_name,
          password: state?.password,
          invitation_code: invitation_code,
          email: userData?.data?.user?.email,
        },
      }
      const signupUrl = `${getApiUrlPrefix('adminczar')}/api/v1/users/${
        userData?.data?.user?.id
      }/signup`
      SecureAxios.post(signupUrl, params)
        .then((response) => response.data)
        .then((responseData) => {
          const redirectUrl = responseData?.admin_apps_org?.app_org_url
          window.location.href = redirectUrl
          return
        })
        .catch((error) => {
          toast({
            message:
              error?.data?.error || c('somethingWentWrongPleaseTryAgain'),
            type: 'error',
          })
        })
    } else {
      toast({
        message: t('pleaseFillOutAllRequiredFields'),
        type: 'error',
      })
    }
  }

  const appLogo =
    userData?.data?.admin_app?.logo_url ??
    'https://images.pattern.com/pattern_logo.svg'

  return (
    <div className={`${styles.mainContainer} pat-pt-8`}>
      {status === 'pending' ? (
        <div className={styles.container}>
          <ListLoading numberOfRows={3} />
        </div>
      ) : (
        <>
          <img alt='amplifi-logo' className={styles.logo} src={appLogo} />
          <div className={styles.container}>
            {hasValue(error) ? (
              <div className='text-center'>
                <EmptyState
                  primaryText={t('sorryTheLinkIsNoLongerValid')}
                  secondaryText={t(
                    'theLinkYouAreAttemptingToAccessHasExpiredOrBeenUsed',
                  )}
                  icon='link'
                />
              </div>
            ) : (
              <>
                <div
                  className={`${styles.textUnderlineHeading} fc-dark-purple pat-mb-8`}
                >
                  <div className='fs-16 fw-bold'>{t('createYourAccount')}</div>
                </div>
                <TextInput
                  labelText={t('emailAddress')}
                  disabled
                  value={userData?.data?.user?.email}
                  callout={(_, value) =>
                    inputHandler('email', value.toString())
                  }
                  type='text'
                  debounce={250}
                  stateName='email'
                  required
                  containerClassName='pat-mb-4'
                />
                <TextInput
                  labelText={c('firstName')}
                  value={state.given_name}
                  callout={(_, value) =>
                    inputHandler('given_name', value.toString())
                  }
                  onBlurCallout={validate}
                  classType={!formValidity.given_name ? 'error' : ''}
                  type='text'
                  stateName='given_name'
                  required
                  containerClassName='pat-mb-4'
                  disabled={hasValue(error)}
                />
                <TextInput
                  labelText={c('lastName')}
                  value={state.family_name}
                  callout={(_, value) =>
                    inputHandler('family_name', value.toString())
                  }
                  classType={!formValidity.family_name ? 'error' : ''}
                  type='text'
                  stateName='family_name'
                  required
                  containerClassName='pat-mb-4'
                  onBlurCallout={validate}
                  disabled={hasValue(error)}
                />
                <TextInput
                  labelText={t('password')}
                  required
                  value={state.password}
                  callout={(_, value) =>
                    inputHandler('password', value.toString())
                  }
                  onBlurCallout={validate}
                  classType={!formValidity.password ? 'error' : ''}
                  type='password'
                  stateName='password'
                  containerClassName='pat-mb-4'
                  errorText={
                    !formValidity.password
                      ? t('passwordIsRequiredAndMustBeLengthCharactersLong', {
                          length: passwordLength,
                        })
                      : ''
                  }
                  disabled={hasValue(error)}
                />
                <TextInput
                  labelText={t('confirmPassword')}
                  required
                  value={state.confirmPassword}
                  callout={(_, value) =>
                    inputHandler('confirmPassword', value.toString())
                  }
                  onBlurCallout={validate}
                  classType={!formValidity.confirmPassword ? 'error' : ''}
                  type='password'
                  stateName='confirmPassword'
                  containerClassName='pat-mb-4'
                  errorText={
                    !formValidity.confirmPassword
                      ? t('passwordsDoNotMatch')
                      : ''
                  }
                  disabled={hasValue(error)}
                />
                <div className='flex pat-mt-2'>
                  <Checkbox
                    name='acceptterms'
                    callout={(_, value) =>
                      setState((prevState) => ({
                        ...prevState,
                        acceptterms: value,
                      }))
                    }
                    label={t('iAcceptThe')}
                    disabled={hasValue(error)}
                  />
                  <span className={`${styles.termsLink} pat-ml-1`}>
                    <NavLink to='#'>{t('termsOfUse')}</NavLink>
                  </span>
                </div>
                <Button
                  className={styles.createAccountButton}
                  styleType='primary-green'
                  disabled={isButtonDisabled}
                  onClick={() => handleSubmit()}
                >
                  {t('createAccount')}
                </Button>

                <span className={`${styles.horizontalLine} fs-14`}>Or</span>
                <div className={styles.existingAccountButton}>
                  <Button styleType='secondary' onClick={login}>
                    {t('logInToExistingAccount')}
                  </Button>
                </div>
              </>
            )}
          </div>
        </>
      )}
    </div>
  )
}

export default SignUp
