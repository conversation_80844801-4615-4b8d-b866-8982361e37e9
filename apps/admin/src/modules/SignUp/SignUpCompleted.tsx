import React from 'react'
import { But<PERSON> } from '@patterninc/react-ui'
import { useSearchParams } from 'react-router-dom'

import styles from './_sign-up.module.scss'
import { useAuth } from '../../context/auth-context'
import { useTranslate } from '../../common/services/TranslationService'

const SignUp = (): React.JSX.Element => {
  const { t } = useTranslate('signup'),
    [searchParams] = useSearchParams()
  const role = searchParams.get('role')
  const { login } = useAuth()
  return (
    <div className='pat-pt-8'>
      <img
        alt='amplifi-logo'
        className={styles.logo}
        src='https://images.pattern.com/amplifi/amplifi-logo.svg'
      />

      <div className={`text-center ${styles.container}`}>
        <h3>{t('yourAccountHasBeenCreatedSuccessfully')}</h3>
        {(role === 'Admin' || role === 'Super Admin') && (
          <>
            <p className='pat-pb-4'>{t('clickLoginToEnter')}</p>
            <Button styleType='primary-blue' onClick={() => login()}>
              {t('login')}
            </Button>
          </>
        )}
      </div>
    </div>
  )
}

export default SignUp
