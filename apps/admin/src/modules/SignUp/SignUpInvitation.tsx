import React, { useCallback, useEffect } from 'react'
import {
  Button,
  getApiUrlPrefix,
  hasValue,
  ListLoading,
  toast,
} from '@patterninc/react-ui'
import { useSearchParams } from 'react-router-dom'
import { useQuery } from '@tanstack/react-query'

import styles from './_sign-up.module.scss'
import { useAuth } from '../../context/auth-context'
import SecureAxios from '../../common/services/SecureAxios'
import { c, useTranslate } from '../../common/services/TranslationService'

const SignUpInvitation = (): React.JSX.Element => {
  const { t } = useTranslate('signup'),
    [searchParams] = useSearchParams()
  const role = searchParams.get('role')
  const { login } = useAuth()
  const roleList = ['Admin', 'Super Admin']
  const invitation_code = searchParams.get('code')

  const apiUrl = `${getApiUrlPrefix(
      'adminczar',
    )}/api/v1/users/by_invitation_code?invitation_code=${invitation_code}`,
    {
      data: userData,
      status,
      error,
    } = useQuery({
      queryKey: [apiUrl, invitation_code],
      queryFn: async () => {
        return await SecureAxios.get(apiUrl)
      },
      refetchOnWindowFocus: false,
      retry: false,
    })

  const handleSubmit = useCallback(() => {
    if (invitation_code) {
      const params = {
        user: {
          invitation_code: invitation_code,
          email: userData?.data?.user?.email,
        },
      }
      const ApiUrl = `${getApiUrlPrefix('adminczar')}/api/v1/users/${
        userData?.data?.user?.id
      }/accept_invite`
      SecureAxios.put(ApiUrl, params)
        .then(() => {
          toast({
            type: 'success',
            message: t('invitationAcceptedSuccessfully'),
          })
        })
        .catch(() => {
          toast({
            message: c('somethingWentWrongPleaseTryAgain'),
            type: 'error',
          })
        })
    }
  }, [
    invitation_code,
    userData?.data?.user?.email,
    userData?.data?.user?.id,
    t,
  ])

  useEffect(() => {
    if (userData?.data?.user?.id) {
      handleSubmit()
    }
  }, [userData?.data?.user?.id, handleSubmit])

  const appLogo =
    userData?.data?.admin_app?.logo_url ??
    'https://images.pattern.com/pattern_logo.svg'

  return (
    <div className={`${styles.mainContainer} pat-pt-8`}>
      {status === 'pending' ? (
        <div className={styles.container}>
          <ListLoading numberOfRows={3} />
        </div>
      ) : (
        <>
          <img alt='amplifi-logo' className={styles.logo} src={appLogo} />

          <div className={`text-center ${styles.container}`}>
            {hasValue(error) ? (
              <p>{c('somethingWentWrongPleaseTryAgain')}</p>
            ) : (
              <>
                <h3 className='pat-p-0 pat-m-0'>
                  {t('invitationAcceptedSuccessfully')}
                </h3>
                {role && roleList.includes(role) && (
                  <>
                    <p className='pat-pb-4'>
                      {t('clickOnTheLoginButtonToAccessYourAccount')}
                    </p>
                    <Button styleType='primary-blue' onClick={() => login()}>
                      {t('login')}
                    </Button>
                  </>
                )}
              </>
            )}
          </div>
        </>
      )}
    </div>
  )
}

export default SignUpInvitation
