import React from 'react'
import { Route, Routes } from 'react-router-dom'

import SignUp from './SignUp'
import SignUpCompleted from './SignUpCompleted'
import ChangePassword from './ChangePassword'
import SignUpInvitation from './SignUpInvitation'

const SignUpRoutes = (): React.JSX.Element => {
  return (
    <>
      <Routes>
        <Route index element={<SignUp />} />
        <Route path='/completed' element={<SignUpCompleted />} />
        <Route path='/invitation' element={<SignUpInvitation />} />
        <Route path='/change-password' element={<ChangePassword />} />
      </Routes>
    </>
  )
}

export default SignUpRoutes
