.mainContainer {
  //Color Variable is not available so using Hex code.
  background: #f0f2fc;
  display: flex;
  flex-direction: column;
  position: relative;
  overflow: auto;
  height: 100vh;
  padding-bottom: 32px;
  .container {
    color: var(--purple);
    max-width: 380px;
    width: 100%;
    margin: 0 auto;
    background: var(--white);
    box-shadow: 0px 4px 12px #********;
    border-radius: 4px;
    opacity: 1;
    padding: 30px 30px;

    @media only screen and (max-width: 576px) {
      max-width: 100%;
    }

    .termsLink a {
      color: var(--blue);
      font-size: var(--font-size-12);
      line-height: 22px;
    }

    .createAccountButton {
      display: block;
      margin: 0 auto;
      margin-top: 30px;
    }

    .horizontalLine {
      display: flex;
      flex-direction: row;
      margin: 16px 0;
    }

    .horizontalLine:before,
    .horizontalLine:after {
      content: '';
      flex: 1 1;
      border-bottom: 1px solid var(--light-gray);
      margin: auto;
    }

    .horizontalLine:before {
      margin-right: 10px;
    }

    .horizontalLine:after {
      margin-left: 10px;
    }

    .existingAccountButton {
      width: 208px;
      margin: 0 auto;
    }

    .textUnderlineHeading {
      span {
        font-size: var(--font-size-22);
        font-weight: var(--font-weight-bold);
      }
    }
  }
}
.logo {
  display: block;
  margin: 0 auto;
  width: auto;
  height: 40px;
  padding-bottom: 50px;
}
