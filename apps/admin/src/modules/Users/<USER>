import React from 'react'
import { RouterTabs } from '@patterninc/react-ui'
import { NavLink, useLocation, useNavigate } from 'react-router-dom'

import { c } from '../../common/services/TranslationService'

const UsersTabs = (): React.JSX.Element => {
  const navigate = useNavigate()
  const pathname = useLocation().pathname

  const navigateMobileTabs = (link: string) => {
    navigate(link)
  }

  return (
    <RouterTabs
      mobileConfig={[
        { label: c('active'), link: '/users/active' },
        { label: c('inactive'), link: '/users/inactive' },
      ]}
      navigate={navigateMobileTabs}
      currentPath={pathname}
    >
      <NavLink to='/users/active'>{c('active')}</NavLink>
      <NavLink to='/users/inactive'>{c('inactive')}</NavLink>
    </RouterTabs>
  )
}

export default UsersTabs
