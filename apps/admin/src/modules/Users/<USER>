import React, {
  useCallback,
  useContext,
  useEffect,
  useMemo,
  useState,
} from 'react'
import {
  type CsvExportProps,
  type Filter,
  getApiUrlPrefix,
  Icon,
  PageHeader,
  type SortColumnProps,
  Tag,
  TagInput,
  Tooltip,
  useIsMobileView,
  useToggle,
} from '@patterninc/react-ui'
import { useQuery } from '@tanstack/react-query'
import moment from 'moment'

import SecureAxios from '../../common/services/SecureAxios'
import { ThemeContext } from '../../Context'
import UsersTable from './Table/UsersTable'
import { type filterType, UserContext } from './UserContext'
import OrganizationDropDownNew from '../../common/OrganizationDropDownNew'
import UsersTabs from './UsersTabs'
import { tryLocalStorageParse } from '../../common/services/HelperService'
import styles from './_users.module.scss'
import { c, t, useTranslate } from '../../common/services/TranslationService'
type UserProps = {
  isInactiveTab?: boolean
}

type RolesType = { id: string; name: string; status: string }

type PrivilegeType = {
  id: string
  name: string
  status: string
  description?: string
  admin_privilege?: boolean
}

type DateRange = {
  start_date: moment.Moment | string | null
  end_date: moment.Moment | string | null
}

const INACTIVE_TOOLTIP = (
    <div>
      <span>{t('users:theTableBelowContainsAllInactivatedSystemUsers')}</span>
      <br />
      <span>{t('users:searchForADesiredUserOrEmailInTheSearchBox')}</span>
      <br />
      <span>{t('users:ifAReactivationIsRequiredReactivateUser')}</span>
    </div>
  ),
  ACTIVE_TOOLTIP = (
    <div>
      <span>{t('users:theTableBelowContainsAllCurrentAndActiveUsers')}</span>
      <br />
      <span>{t('users:forFastestNavigationSearchForTheDesiredUser')}</span>
    </div>
  )

const Users = ({ isInactiveTab }: UserProps): React.JSX.Element => {
  const { updateBreadcrumbs, organization, adminApp, orgUnits, app } =
      useContext(ThemeContext),
    { state: userState, dispatch: userDispatch } = useContext(UserContext),
    { t } = useTranslate('users'),
    [sortBy, setSort] = useState({
      prop: isInactiveTab ? 'deactivated_at' : 'created_at',
      flip: false,
    }),
    [searchText, setSearchText] = useState<string>(''),
    [bulkSearchUsers, setBulkSearchUsers] = useState<string[]>([]),
    [filterStateCopy, setFilterStateCopy] = useState(userState),
    [filtersCount, setFiltersCount] = useState(0),
    [usersCount, setUsersCount] = useState<string>(''),
    isMobileView = useIsMobileView(),
    isTagInputEnabled = useToggle('tag_input_changes')
  useEffect(() => {
    updateBreadcrumbs({
      name: c('users'),
      link: '/users',
      changeType: 'rootLevel',
    })
  }, [updateBreadcrumbs])

  // resetting searchText on tab change
  useEffect(() => {
    setSearchText('')
    setBulkSearchUsers([])
    setSort((prevState) => ({
      ...prevState,
      prop: isInactiveTab ? 'deactivated_at' : 'created_at',
      flip: false,
    }))
  }, [isInactiveTab])

  const setSortBy: SortColumnProps['sorter'] = (sortObj) => {
    setSort({
      prop: sortObj.activeColumn,
      flip: sortObj.direction,
    })
  }

  const searchInputHandler = (query: string) => {
    setSearchText(query)
  }

  useEffect(() => {
    setSearchText(bulkSearchUsers.join(','))
  }, [bulkSearchUsers])

  const updateFilters = useCallback(
    (filter: filterType) => {
      userDispatch({
        type: 'UPDATE_FILTER',
        payload: {
          filters: filter,
        },
      })
    },
    [userDispatch],
  )

  const difference = useCallback(
    (filterState: filterType) => {
      let count = 0
      if (filterState?.roles?.length !== 0) {
        count += 1
      }
      if (filterState?.orgUnits?.length !== 0) {
        count += 1
      }
      if (!isInactiveTab && filterState?.status.id !== 0) {
        count += 1
      }

      if (filterState?.created?.start_date || filterState?.created?.end_date) {
        count += 1
      }
      if (
        filterState?.lastLogin?.start_date ||
        filterState?.lastLogin?.end_date
      ) {
        count += 1
      }
      if (filterState?.privileges?.length !== 0) {
        count += 1
      }

      return count
    },
    [isInactiveTab],
  )

  const resetFilter = useCallback(() => {
    userDispatch({ type: 'RESET_FILTER' })
  }, [userDispatch])

  const updateSelect = (...params: unknown[]) => {
    const stateAttr = params[0] as string
    if (stateAttr === 'roles') {
      const roleNames = params[1] as string[]
      const values = rolesData.filter((role) => roleNames.includes(role.name))
      setFilterStateCopy({
        ...filterStateCopy,
        [stateAttr]: values,
      })
    } else if (stateAttr === 'privileges') {
      const privilegeNames = params[1] as string[]
      const values = privilegesData
        .filter((privilege) => privilegeNames.includes(privilege.name))
        .map((privilege) => ({
          id: privilege.id,
          name: privilege.name,
        }))

      setFilterStateCopy({
        ...filterStateCopy,
        [stateAttr]: values,
      })
    } else {
      const value = params[1]
      setFilterStateCopy({
        ...filterStateCopy,
        [stateAttr]: value,
      })
    }
  }

  const cancelCallout = () => {
    setFilterStateCopy(userState)
  }

  useEffect(() => {
    const storedState = tryLocalStorageParse('user_filter')
    if (storedState && storedState?.appId !== app.id) {
      resetFilter()
    }
  }, [app.id, resetFilter])

  useEffect(() => {
    setFilterStateCopy(userState)
    const count = difference(userState)
    setFiltersCount(count)
  }, [difference, userState])

  const orgUnitIds = useMemo(() => {
    const selectedIds = userState?.orgUnits.map((unit) => {
      return orgUnits.find((orgUnit) => orgUnit?.name === unit)?.id
    })
    return selectedIds
  }, [orgUnits, userState?.orgUnits])

  const rolesApiUrl = `${getApiUrlPrefix('adminczar')}/api/v1/apps/${
      app?.id
    }/roles`,
    { data: rolesResponse, status: roleApiStatus } = useQuery({
      queryKey: [
        rolesApiUrl,
        app?.id,
        adminApp?.id,
        organization.id,
        'allRoles',
      ],
      queryFn: async ({ signal }) => {
        return await SecureAxios.get(rolesApiUrl, {
          params: {
            status: ['active', 'inactive'],
            sort: 'name',
            org_id: organization.id,
          },
          signal,
        })
      },
      gcTime: 1000 * 60 * 60 * 8,
    })

  const rolesData = useMemo(
    () =>
      roleApiStatus === 'success'
        ? [
            ...(rolesResponse?.data?.data || []).map((role: RolesType) => ({
              id: role?.id,
              name: role?.name,
              secondaryOption:
                role?.status === 'inactive' ? (
                  isTagInputEnabled ? (
                    <Tag color='light-gray'>{t('inactive')}</Tag>
                  ) : (
                    <span className='fc-purple'>{t('inactive')}</span>
                  )
                ) : (
                  ''
                ),
            })),
          ]
        : [],
    [roleApiStatus, rolesResponse?.data?.data, t, isTagInputEnabled],
  )

  const roleIds = useMemo(() => {
    const selectedIds = userState?.roles.map((selectedRole) => {
      return rolesData.find((role) => role?.name === selectedRole.name)?.id
    })
    return selectedIds
  }, [rolesData, userState])

  const privilegesApiUrl = `${getApiUrlPrefix('adminczar')}/api/v1/apps/${
      app?.id
    }/privileges/privileges_with_admin_app`,
    { status: privilegesApiStatus, data: privilegesResponse } = useQuery({
      queryKey: [privilegesApiUrl, app.id, organization.code],
      queryFn: async ({ signal }) => {
        const data = await SecureAxios.get(privilegesApiUrl, {
          signal,
          params: {
            is_users_context: true,
            org_code: organization.code,
            sort: 'name',
          },
        })
        return data
      },
      enabled: !!organization.code,
      gcTime: 1000 * 60 * 60 * 8,
    })

  const privilegeDescription = useCallback(
    (privilege: PrivilegeType) => (
      <div className='flex align-items-center pat-gap-4'>
        {privilege.admin_privilege ? (
          privilege.status === 'inactive' ? (
            isTagInputEnabled ? (
              <Tag color='light-gray'>{c('adminInactive')}</Tag>
            ) : (
              <span className='fc-purple'>{c('adminInactive')}</span>
            )
          ) : isTagInputEnabled ? (
            <Tag color='light-gray'>{c('admin')}</Tag>
          ) : (
            <span className='fc-purple'>{c('admin')}</span>
          )
        ) : privilege.status === 'inactive' ? (
          isTagInputEnabled ? (
            <Tag color='light-gray'>{c('inactive')}</Tag>
          ) : (
            <span className='fc-purple'>{c('inactive')}</span>
          )
        ) : null}
        {privilege.description && (
          <Tooltip
            tooltipContent={
              <div className='fs-12'>{privilege.description}</div>
            }
          >
            <Icon icon='info' iconSize='16px' color='dark-blue' />
          </Tooltip>
        )}
      </div>
    ),
    [isTagInputEnabled],
  )

  const privilegesData = useMemo(
    () =>
      privilegesApiStatus === 'success'
        ? [
            ...(privilegesResponse?.data?.data || []).map(
              (privilege: PrivilegeType) => ({
                id: privilege?.id,
                name: privilege?.name,
                secondaryOption: privilegeDescription(privilege),
              }),
            ),
          ]
        : [],
    [privilegeDescription, privilegesApiStatus, privilegesResponse?.data?.data],
  )

  const privilegeIds = useMemo(() => {
    const selectedIds = userState?.privileges.map((selectedPrivilege) => {
      return privilegesData.find(
        (privilege) => privilege?.name === selectedPrivilege.name,
      )?.id
    })
    return selectedIds
  }, [privilegesData, userState])

  const formatDateParams = (
    createdDates?: DateRange,
    lastLoginDates?: DateRange,
  ): Record<string, Record<string, string>> => {
    const dateParams: Record<string, Record<string, string>> = {}

    const addDateRange = (key: string, dateRange?: DateRange) => {
      if (!dateRange?.start_date && !dateRange?.end_date) return

      dateParams[key] = {
        ...(dateRange.start_date && {
          start_date: moment(dateRange.start_date)
            .startOf('day')
            .utc()
            .toISOString(),
        }),
        ...(dateRange.end_date && {
          end_date: moment(dateRange.end_date).endOf('day').utc().toISOString(),
        }),
      }
    }

    addDateRange('created', createdDates)
    addDateRange('last_login', lastLoginDates)

    return dateParams
  }

  const filterParams = useMemo(() => {
    const dateParams = formatDateParams(
      userState?.created,
      userState?.lastLogin,
    )

    return {
      ...(userState?.roles?.length !== 0
        ? {
            role_ids: roleIds,
          }
        : {}),
      ...(userState?.orgUnits?.length !== 0
        ? {
            org_unit_ids: orgUnitIds,
          }
        : {}),
      ...(userState?.privileges?.length !== 0
        ? {
            privilege_ids: privilegeIds,
          }
        : {}),
      ...(userState.status.name === 'Active'
        ? {
            status: isInactiveTab ? 'inactive' : 'active',
          }
        : userState.status.name === 'Pending'
          ? {
              status: isInactiveTab ? 'inactive' : 'onboarding',
              invitation_expired: false,
            }
          : userState.status.name === 'Invitation Expired'
            ? {
                status: isInactiveTab ? 'inactive' : 'onboarding',
                invitation_expired: true,
              }
            : {}),
      ...dateParams,
    }
  }, [
    userState?.created,
    userState?.lastLogin,
    userState?.roles?.length,
    userState?.orgUnits?.length,
    userState?.privileges?.length,
    userState.status.name,
    roleIds,
    orgUnitIds,
    privilegeIds,
    isInactiveTab,
  ])

  const csvDownloadUrl = `${getApiUrlPrefix('adminczar')}/api/v1/apps/${
    adminApp?.id
  }/orgs/${organization.id}/users`

  const csvName = `${isInactiveTab ? t('deactivatedAdminUsers') : t('adminUsers')}}`
  const csvDownloadOptions: CsvExportProps['csvDownloadOptions'] = [
    {
      linkName: csvName,
      csvName: csvName,
      csvFormat: {
        api: (csvParams) =>
          SecureAxios.get(csvDownloadUrl, { params: csvParams }).then(
            (response) => {
              return response.data
            },
          ),
        params: {
          csv_name: csvName,
          format: 'csv',
          status: isInactiveTab ? 'inactive' : ['onboarding', 'active'],
          ...filterParams,
          ...(searchText
            ? {
                bulk_search: searchText,
              }
            : {}),
          ...(isInactiveTab ? { inactive: true } : {}),
        },
      },
    },
  ]

  const applyFilterCallout = () => {
    updateFilters({ ...filterStateCopy, appId: app.id })
  }

  const appliedFilters = useMemo((): React.ComponentProps<
    typeof Filter
  >['filterStates'] => {
    return {
      roles: {
        type: 'multi-select',
        options: rolesData,
        stateName: 'roles',
        labelKey: 'name',
        formLabelProps: {
          label: c('roles'),
        },
        selectPlaceholder: `-- ${t('selectRoles')} --`,
        searchBarProps: {
          placeholder: t('searchRole'),
          value: '',
        },
        selectedOptions: filterStateCopy.roles ?? [],
      },
      orgUnits: {
        type: 'multi-select',
        options: orgUnits,
        stateName: 'orgUnits',
        labelKey: 'name',
        formLabelProps: {
          label: c('orgUnits'),
        },
        selectPlaceholder: `-- ${t('selectOrgUnits')} --`,
        searchBarProps: {
          placeholder: t('searchOrgUnit'),
          value: '',
        },
        selectedOptions:
          filterStateCopy.orgUnits.map((o) => ({ name: o })) ?? [],
      },
      privileges: {
        type: 'multi-select',
        options: privilegesData,
        stateName: 'privileges',
        labelKey: 'name',
        formLabelProps: {
          label: c('privileges'),
        },
        selectPlaceholder: `-- ${t('selectPrivileges')} --`,
        searchBarProps: {
          placeholder: t('searchPrivileges'),
          value: '',
        },
        selectedOptions: filterStateCopy.privileges ?? [],
      },
      ...(isInactiveTab
        ? {}
        : {
            status: {
              type: 'select',
              defaultValue: filterStateCopy.status,
              options: [
                {
                  id: 0,
                  name: 'All',
                },
                {
                  id: 1,
                  name: 'Pending',
                },
                {
                  id: 2,
                  name: 'Active',
                },
                {
                  id: 3,
                  name: 'Invitation Expired',
                },
              ],
              stateName: 'status',
              optionKeyName: 'name',
              labelText: c('status'),
            },
          }),

      created: {
        type: 'dates',
        labelText: t('createdAt'),
        defaultValue: {
          start_date: filterStateCopy?.created?.start_date,
          end_date: filterStateCopy?.created?.end_date,
        },
        stateName: 'created',
        hideCustomDateSearch: true,
      },
      lastLogin: {
        type: 'dates',
        labelText: t('lastLogin'),
        defaultValue: {
          start_date: filterStateCopy?.lastLogin?.start_date,
          end_date: filterStateCopy?.lastLogin?.end_date,
        },
        stateName: 'lastLogin',
        hideCustomDateSearch: true,
      },
    }
  }, [rolesData, t, filterStateCopy, orgUnits, privilegesData, isInactiveTab])

  return (
    <div className='flex flex-direction-column pat-gap-4'>
      <PageHeader
        header={{
          name: `${c('total')} ${isInactiveTab ? c('deactivated') : c('active')} ${c('users')}`,
          value: usersCount,
        }}
        {...{
          leftSectionChildren: (
            <div className={styles.setDimension}>
              <TagInput
                autoFocus
                placeholder={
                  bulkSearchUsers.length === 0 ? t('searchUsers') : ''
                }
                tags={bulkSearchUsers}
                setTags={setBulkSearchUsers}
              />
            </div>
          ),
        }}
        download={{
          csvDownloadOptions: csvDownloadOptions,
          show: true,
          initialDisplay: true,
        }}
        tooltip={{
          tooltipContent: (
            <div className='flex flex-direction-column pat-gap-4'>
              <span className='fs-18'>
                {isInactiveTab ? t('inactiveUserList') : t('activeUserList')}
              </span>
              {isInactiveTab ? INACTIVE_TOOLTIP : ACTIVE_TOOLTIP}
            </div>
          ),
        }}
        pageFilterProps={{
          filterStates: appliedFilters,
          filterCallout: applyFilterCallout,
          resetCallout: resetFilter,
          onChangeCallout: updateSelect,
          cancelCallout: cancelCallout,
          appliedFilters: filtersCount,
        }}
        {...(isMobileView
          ? {
              bottomSectionChildren: (
                <OrganizationDropDownNew userDispatch={userDispatch} />
              ),
            }
          : {
              rightSectionChildren: (
                <OrganizationDropDownNew userDispatch={userDispatch} />
              ),
            })}
      />
      <div>
        <UsersTabs />
        <UsersTable
          setUsersCount={setUsersCount}
          usersCount={parseInt(usersCount || '0', 10)}
          isInactiveTab={isInactiveTab}
          setSortBy={setSortBy}
          sortBy={sortBy}
          searchText={searchText}
          searchInputHandler={searchInputHandler}
          filterParams={filterParams}
        />
      </div>
    </div>
  )
}

export default Users
