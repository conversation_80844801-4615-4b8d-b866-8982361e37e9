.setDimension [class^='container-rui-HpKpFh'] {
  max-height: 22px;
  min-width: 300px;
  max-width: 500px;
}

.setDimension [class^='tagInput-rui-ghcSr2'] {
  background-color: inherit;
}

.setDimensionAlt [class^='tagInput-rui-ghcSr2'] {
  width: 63%;
}

.buttonStyle {
  white-space: wrap;
  @media (max-width: 400px) {
    font-size: 10px;
  }
}

// Above class name is only for the TagInput component because it does not have any prop to style that component.
// For this reason it is necessory to change some of configuration in the TagInput component.
