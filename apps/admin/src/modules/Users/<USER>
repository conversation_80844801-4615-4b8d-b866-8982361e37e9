import React from 'react'
import { Navigate, Route, Routes } from 'react-router-dom'

import PrivateRoute from '../../common/components/PrivateRoute/PrivateRoute'
import { UserContext, UserContextProvider } from './UserContext'
import Users from './Users'

const UsersRoutes = (): React.JSX.Element => {
  const renderUserRoutes = () => (
    <>
      <Routes>
        <Route
          path='active'
          element={
            <PrivateRoute>
              <Users />
            </PrivateRoute>
          }
        />
        <Route
          path='inactive'
          element={
            <PrivateRoute>
              <Users isInactiveTab />
            </PrivateRoute>
          }
        />
        <Route index element={<Navigate to='active' replace />} />
      </Routes>
    </>
  )

  return (
    <UserContextProvider>
      <UserContext.Consumer>{() => renderUserRoutes()}</UserContext.Consumer>
    </UserContextProvider>
  )
}

export default UsersRoutes
