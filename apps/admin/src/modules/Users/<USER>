import React, { createContext, type Dispatch, useReducer } from 'react'

import { tryLocalStorageParse } from '../../common/services/HelperService'

export type FilterAction =
  | { type: 'UPDATE_FILTER'; payload: { filters: filterType } }
  | { type: 'RESET_FILTER' }

type DateRange = {
  start_date: moment.Moment | string | null
  end_date: moment.Moment | string | null
}

export type filterType = {
  roles: { id: string; name: string; secondaryOption?: string }[]
  status: { id: number; name: string }
  orgUnits: []
  privileges: { id: string; name: string; secondaryOption?: string }[]
  appId?: string
  created?: DateRange
  lastLogin?: DateRange
}

export const defaultFilter: filterType = {
  roles: [],
  status: { id: 0, name: 'All' },
  orgUnits: [],
  privileges: [],
  created: {
    start_date: null,
    end_date: null,
  },
  lastLogin: {
    start_date: null,
    end_date: null,
  },
}

const initialState = (): filterType => {
  const storedState = tryLocalStorageParse('user_filter')
  if (storedState) {
    return {
      ...defaultFilter,
      ...storedState,
    }
  }
  return defaultFilter
}

const UserContext = createContext<{
  state: filterType
  dispatch: Dispatch<FilterAction>
}>({
  state: defaultFilter,
  dispatch: () => null,
})
const { Provider } = UserContext

const reducer = (state: filterType, action: FilterAction): filterType => {
  switch (action.type) {
    case 'UPDATE_FILTER':
      localStorage.setItem(
        'user_filter',
        JSON.stringify({
          ...state,
          ...action.payload.filters,
        }),
      )
      return {
        ...state,
        ...action.payload.filters,
      }
    case 'RESET_FILTER':
      localStorage.setItem('user_filter', JSON.stringify(defaultFilter))
      return { ...defaultFilter }
    default:
      return { ...state }
  }
}

const UserContextProvider = ({ children }: { children: React.ReactNode }) => {
  const [state, dispatch] = useReducer(reducer, initialState())
  return <Provider value={{ state, dispatch }}>{children}</Provider>
}

export { UserContext, UserContextProvider, reducer }
