import React, { use<PERSON><PERSON>back, useContext, useMemo, useState } from 'react'
import {
  type Button,
  type ButtonGroup,
  getApiUrlPrefix,
  type <PERSON>u<PERSON>rops,
  PageFooter,
  toast,
} from '@patterninc/react-ui'

import { type UserType } from './users-table-config'
import SecureAxios from '../../../common/services/SecureAxios'
import { ThemeContext } from '../../../Context'
import { tryLocalStorageParse } from '../../../common/services/HelperService'
import { hasWritePermission } from '../../../Auth/route-permissions'
import UserFormParams from './UserFormParams'
import { c, useTranslate } from '../../../common/services/TranslationService'
import styles from '../_users.module.scss'
type UserBulkActionProps = {
  checkedBoxes: UserType[]
  checkAll: boolean
  refreshList: () => void
  isInactivePage?: boolean
  searchText?: string
  filterParams: Record<string, unknown>
  totalUsers?: number
}

// Button Props are not exported from PageFooter hence it is required to create here.
type ButtonType = React.ComponentProps<typeof Button>
type ButtonGroupType = React.ComponentProps<typeof ButtonGroup>

type PageFooterButtonProp = {
  type: 'button'
} & ButtonType

type PageFooterButtonGroupProps = {
  type: 'buttonGroup'
} & ButtonGroupType

type PageFooterButtonProps = PageFooterButtonProp | PageFooterButtonGroupProps

const UserBulkAction = ({
  checkedBoxes,
  checkAll,
  refreshList,
  isInactivePage,
  searchText,
  filterParams,
  totalUsers,
}: UserBulkActionProps): React.JSX.Element => {
  const { organization, app, privileges } = useContext(ThemeContext)
  const { t } = useTranslate('users')
  const [bulkUserAction, setBulkUserAction] = useState<string>('edit')
  const [drawerOpen, setDrawerOpen] = useState(false)
  const currentUser = tryLocalStorageParse('current_user'),
    [selectedUsers, setselectedUsers] = useState<UserType[]>([])

  const Ids: number[] = useMemo(() => {
    const users: number[] = []
    checkedBoxes.map((user) => {
      return currentUser?.id !== user?.user_id && users.push(user.user_id)
    })
    return users
  }, [checkedBoxes, currentUser?.id])

  const isLoggedUserSelected: boolean[] = useMemo(() => {
    const users: boolean[] = []
    checkedBoxes?.filter((user) => {
      return currentUser?.id === user?.user_id
        ? users.push(true)
        : users.push(false)
    })
    return users
  }, [checkedBoxes, currentUser?.id])

  const currentUserSelectedOnPage = useMemo(
    () => checkedBoxes.some((u) => u.user_id === currentUser?.id),
    [checkedBoxes, currentUser?.id],
  )

  const selectedUsersCount = useMemo(() => {
    const baseCount = checkAll ? (totalUsers ?? 0) : Ids.length
    const shouldExcludeCurrentUser = checkAll
      ? currentUserSelectedOnPage
      : false

    return Math.max(0, baseCount - (shouldExcludeCurrentUser ? 1 : 0))
  }, [checkAll, totalUsers, Ids, currentUserSelectedOnPage])

  const getMSG = useCallback(
    (name: string, type: string, count: number) => {
      let msg = null
      switch (name) {
        case 'resend_activation_mail':
          msg =
            type === 'success' ? (
              <span>{t('activationEmailSentToUsers', { count })}</span>
            ) : (
              <span>
                {t('activationEmailCouldNotBeSentToUsers', { count })}
              </span>
            )
          break
        case 'send_password':
          msg =
            type === 'success' ? (
              <span>{t('passwordResetMessageSentToUsers', { count })}</span>
            ) : (
              <span>
                {t('passwordResetMessageCouldNotBeSentToUsers', { count })}
              </span>
            )
          break

        case 'deactivated':
          msg =
            type === 'success' ? (
              <span>{t('userSuccessfullyDeactivated', { count })}</span>
            ) : (
              <span>{t('userCouldNotBeDeactivated', { count })}</span>
            )
          break

        case 'reactivated':
          msg =
            type === 'success' ? (
              <span>{t('userSuccessfullyReactivated', { count })}</span>
            ) : (
              <span>{t('userCouldNotBeReactivated', { count })}</span>
            )
          break
      }
      return msg
    },
    [t],
  )
  // TODO - Need to modify the params as per backend
  const commonParams = useMemo(() => {
    return {
      apps_orgs_user: {
        status: isInactivePage ? 'active' : 'inactive',
        ...(checkAll
          ? {
              filters: {
                status: isInactivePage ? 'inactive' : ['onboarding', 'active'],
                ...filterParams,
              },
            }
          : {
              ids: Ids,
            }),
      },
      ...(checkAll
        ? {
            ...(searchText ? { bulk_search: searchText } : {}),
          }
        : {}),
    }
  }, [Ids, checkAll, filterParams, isInactivePage, searchText])

  const commonParamsUserPassword = useMemo(() => {
    return {
      apps_orgs_user: {
        ...(checkAll
          ? {
              filters: {
                status: isInactivePage ? 'inactive' : ['onboarding', 'active'],
                ...filterParams,
              },
            }
          : {
              ids: Ids,
            }),
      },
      ...(checkAll
        ? {
            ...(searchText ? { bulk_search: searchText } : {}),
          }
        : {}),
    }
  }, [checkAll, isInactivePage, filterParams, Ids, searchText])

  const formAction = useCallback(
    (name: string) => {
      const endPoint =
        name === 'deactivated' || name === 'reactivated'
          ? 'bulk_update_status'
          : ''
      const settingsApi = `${getApiUrlPrefix('adminczar')}/api/v1/apps/${
        app?.id
      }/orgs/${organization.id}/users/${
        name === 'send_password'
          ? 'bulk_reset_password'
          : name === 'resend_activation_mail'
            ? 'bulk_resend_activation_email'
            : `${endPoint}`
      }`

      SecureAxios.put(
        settingsApi,
        name === 'send_password' || name === 'resend_activation_mail'
          ? commonParamsUserPassword
          : commonParams,
      )
        .then((res) => {
          if (res?.data?.data?.failed_count) {
            toast({
              message: getMSG(name, 'error', res?.data?.data?.failed_count),
              type: 'error',
            })
          }
          if (res?.data?.data?.success_count) {
            toast({
              message: getMSG(name, 'success', res?.data?.data?.success_count),
              type: 'success',
            })
          }
          refreshList()
        })
        .catch(() => {
          toast({
            message: c('somethingWentWrongPleaseTryAgain'),
            type: 'error',
          })
        })
    },
    [
      app?.id,
      commonParams,
      commonParamsUserPassword,
      getMSG,
      organization.id,
      refreshList,
    ],
  )

  const handleInsightsActions = useCallback(
    ({ type }: { type: string }) => {
      switch (type) {
        case 'edit_role':
        case 'edit_region':
          setDrawerOpen(true)
          setselectedUsers(!checkAll ? checkedBoxes : [])
          setBulkUserAction(type)
          break
        case 'resendActivationMail':
          formAction('resend_activation_mail')
          break
        case 'send_password':
          formAction('send_password')
          break

        case 'deactivated':
          formAction('deactivated')
          break

        case 'reactivated':
          formAction('reactivated')
          break

        default:
          setDrawerOpen(true)
          setselectedUsers(!checkAll ? checkedBoxes : [])
          setBulkUserAction(type)
          break
      }
    },
    [checkAll, checkedBoxes, formAction],
  )

  const canManageUsers = useMemo(
    () => hasWritePermission('manage_users', privileges),
    [privileges],
  )

  const canCreateUsers = useMemo(
    () => hasWritePermission('create_users', privileges),
    [privileges],
  )

  const secondaryActionButtonDisabled =
    checkedBoxes?.length === 0 || !isLoggedUserSelected?.includes(false)

  const confirmCallout = () => {
    return handleInsightsActions({ type: 'reactivated' })
  }

  const isCreateButtonVisible = useMemo(() => {
    return canCreateUsers && secondaryActionButtonDisabled
  }, [canCreateUsers, secondaryActionButtonDisabled])

  const isDynamicButtonVisible = useMemo(() => {
    return canManageUsers && !secondaryActionButtonDisabled
  }, [canManageUsers, secondaryActionButtonDisabled])

  const actions = useMemo(() => {
    const allActions = [
      {
        action: {
          text: t('editRole'),
          icon: 'pencil',
          callout: () => handleInsightsActions({ type: 'edit_role' }),
        },
        requiredPermission: 'canCreateUsersAndCanManageUsers',
      },
      {
        action: {
          text: t('createNewUser(s)'),
          icon: 'follow',
          callout: () => handleInsightsActions({ type: 'add' }),
          disabled: {
            value: !canCreateUsers,
            tooltip: {
              tooltipContent: t('users:doNotHaveSuffientPrivilege'),
            },
          },
        },
        requiredPermission: isDynamicButtonVisible
          ? 'canCreateUsersOrCanManageUsers'
          : 'canManageUsersOnly',
      },
      {
        action: {
          text: t('editOrgUnits'),
          icon: 'pencil',
          callout: () => handleInsightsActions({ type: 'edit_region' }),
        },
        requiredPermission: isDynamicButtonVisible
          ? 'canCreateUsersAndCanManageUsers'
          : 'canManageUsers',
      },
      {
        action: {
          text: t('sendPasswordReset'),
          icon: 'paperPlane',
          confirmation: {
            header: c('areYouSure'),
            body: t('passwordResetBody', {
              count: selectedUsersCount,
            }),
            type: 'blue',
            confirmButtonText: c('confirm'),
            confirmCallout: () =>
              handleInsightsActions({ type: 'send_password' }),
          },
        },
        requiredPermission: 'canManageUsers',
      },
      {
        action: {
          text: t('resendActivationEmail'),
          icon: 'paperPlane',
          confirmation: {
            header: c('areYouSure'),
            body: t('resendActivationEmailBody', {
              count: selectedUsersCount,
            }),
            type: 'blue',
            confirmButtonText: c('confirm'),
            confirmCallout: () =>
              handleInsightsActions({ type: 'resendActivationMail' }),
          },
        },
        requiredPermission: 'canCreateUsersOrCanManageUsers',
      },
      {
        action: {
          text: t('deactivateUser'),
          icon: 'unfollow',
          confirmation: {
            header: c('areYouSure'),
            body: t('deactivateUserBody', {
              appName: app?.name,
              count: selectedUsersCount,
            }),
            type: 'red',
            confirmButtonText: t('deactivateUser'),
            confirmCallout: () =>
              handleInsightsActions({ type: 'deactivated' }),
          },
        },
        requiredPermission: 'canManageUsers',
      },
    ]

    // Filter actions based on user's permissions
    const filteredActions = allActions
      .filter(({ requiredPermission }) => {
        switch (requiredPermission) {
          case 'canManageUsers':
            return canManageUsers
          case 'canCreateUsersOrCanManageUsers':
            return canCreateUsers || canManageUsers
          case 'canManageUsersOnly':
            return canManageUsers && !canCreateUsers
          case 'canCreateUsersAndCanManageUsers':
            return canCreateUsers && canManageUsers && !isDynamicButtonVisible
          default:
            return false
        }
      })
      .map(({ action }) => action)

    return filteredActions as MenuProps['actions']
  }, [
    app?.name,
    handleInsightsActions,
    t,
    canCreateUsers,
    canManageUsers,
    isDynamicButtonVisible,
    selectedUsersCount,
  ])

  const getReactivateButton = (): PageFooterButtonProps => {
    return {
      type: 'button',
      styleType: 'primary-green',
      children: t('reactivateUser(s)'),
      as: 'confirmation',
      confirmation: {
        type: 'red',
        header: c('areYouSure'),
        body: t('reactivateUserBody', {
          appName: app?.name,
          count: selectedUsersCount,
        }),
        confirmButtonText: 'Activate',
        confirmCallout: confirmCallout,
      },
    }
  }

  const getSecondaryButton = (): PageFooterButtonProps => {
    if (isCreateButtonVisible) {
      return {
        type: 'button',
        children: t('createNewUser(s)'),
        onClick: () => handleInsightsActions({ type: 'add' }),
      }
    } else {
      return {
        type: 'button',
        children: t('users:more'),
        disabled: true,
        ...(secondaryActionButtonDisabled && {
          tooltip: {
            tooltipContent: t('users:pleaseSelectAtLeastOneUser'),
          },
        }),
      }
    }
  }

  // Create the button group for active page
  const getButtonGroup = () => {
    return {
      type: 'buttonGroup',
      buttons: [
        {
          actions,
          disabled:
            secondaryActionButtonDisabled ||
            !(canManageUsers || canCreateUsers),
        },
        getSecondaryButton(),
      ],
      styleType:
        isDynamicButtonVisible ||
        (!secondaryActionButtonDisabled && !canManageUsers)
          ? 'secondary'
          : 'primary-green',
    }
  }

  const getDynamicButtons = () => {
    if (!isDynamicButtonVisible) return []

    return [
      {
        type: 'button',
        styleType: 'secondary',
        children: t('editOrgUnits'),
        onClick: () => handleInsightsActions({ type: 'edit_region' }),
        className: styles.buttonStyle,
      },
      {
        type: 'button',
        styleType: 'secondary',
        children: t('editRoles'),
        onClick: () => handleInsightsActions({ type: 'edit_role' }),
        className: styles.buttonStyle,
      },
    ]
  }

  // Determine all right section buttons
  const getRightSectionButtons = (): PageFooterButtonProps[] => {
    if (!(canManageUsers || canCreateUsers)) {
      return []
    }

    if (isInactivePage) {
      return canManageUsers ? [getReactivateButton()] : []
    }

    return [
      getButtonGroup() as PageFooterButtonProps,
      ...(isDynamicButtonVisible
        ? getDynamicButtons().map((btn) => btn as PageFooterButtonProps)
        : []),
    ]
  }

  return (
    <>
      <PageFooter rightSection={getRightSectionButtons()} />

      {drawerOpen && (
        <UserFormParams
          setIsOpen={setDrawerOpen}
          isOpen={drawerOpen}
          selectedUsers={selectedUsers}
          isEditMode={false}
          bulkUserAction={bulkUserAction}
          refreshList={refreshList}
          isInactivePage={isInactivePage}
          Ids={Ids}
          checkAll={checkAll}
          searchText={searchText}
          filterParams={filterParams}
          selectedUsersCount={selectedUsersCount}
          setSelectedUser={() => null}
        />
      )}
    </>
  )
}

export default UserBulkAction
