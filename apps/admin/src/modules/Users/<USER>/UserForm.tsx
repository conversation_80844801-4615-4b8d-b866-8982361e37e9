import React, {
  use<PERSON><PERSON>back,
  useContext,
  useEffect,
  useMemo,
  useState,
} from 'react'
import {
  getApiUrlPrefix,
  hasValue,
  MultiSelect,
  Select,
  Separators,
  standardSortParams,
  Switch,
  TagInput,
  TextInput,
} from '@patterninc/react-ui'
import { useQuery } from '@tanstack/react-query'

import { type FormState, type selectOptionType } from './UserFormParams'
import SecureAxios from '../../../common/services/SecureAxios'
import { ThemeContext } from '../../../Context'
import { type UserType } from './users-table-config'
import styles from '../_users.module.scss'
import { c, useTranslate } from '../../../common/services/TranslationService'

export type regionsArrType = {
  admin_apps_org_id: number
  created_at: string
  description: string
  id: number
  name: string
  status: string
  updated_at: string
}
export type emailValidateType = {
  count: number
  invalidEmails: string[]
}

type UserFormProps = {
  isEditMode: boolean
  formState: FormState
  setFormState: (
    key: keyof FormState,
    value: string | selectOptionType | string[] | boolean,
  ) => void
  bulkUserAction?: string
  regionList?: (regions: Array<regionsArrType>) => void
  emailValidateCallback?: (data: emailValidateType) => void
  maxEmailAllowed: number
  selectedUser?: UserType
  appForUserDetails?: AppType
  orgForUserDetails?: OrgType
}

type AppType = {
  id: number
  name: string
  metadata?: {
    require_org_unit_selection: boolean
    hide_select_all_org_units: boolean
    hide_access_to_all_org_units: boolean
  }
}

type OrgType = {
  id: number
  name: string
}

type roleType = {
  id: string
  text?: string
  name?: string
  value: string
}

type regionsType = {
  name: string
}

const UserForm = ({
  isEditMode,
  formState,
  bulkUserAction,
  setFormState,
  regionList,
  emailValidateCallback,
  maxEmailAllowed,
  selectedUser,
  appForUserDetails,
  orgForUserDetails,
}: UserFormProps): React.JSX.Element => {
  const { t } = useTranslate('users'),
    { adminApp } = useContext(ThemeContext),
    appContext = useContext(ThemeContext).app,
    orgContext = useContext(ThemeContext).organization,
    app = appForUserDetails || appContext,
    organization = orgForUserDetails || orgContext

  const sortBy = {
    prop: 'name',
    flip: true,
  }
  // Get Roles By App id
  const ApiUrl = `${getApiUrlPrefix('adminczar')}/api/v1/apps/${app?.id}`
  const RolesApiUrl = `${ApiUrl}/roles`,
    { data: rolesResponse, isLoading } = useQuery({
      queryKey: [RolesApiUrl, app?.id, adminApp?.id, organization.id],
      queryFn: async () => {
        return await SecureAxios.get(RolesApiUrl, {
          params: {
            sort: standardSortParams(sortBy),
            org_id: organization.id,
          },
        })
      },
    })

  const roles = useMemo(() => {
    if (rolesResponse) {
      const tempRoles: roleType[] = []
      rolesResponse?.data?.data?.map((role: roleType) => {
        return tempRoles?.push({
          id: role?.id,
          text: role?.name,
          value: role?.id,
        })
      })
      return tempRoles
    }
  }, [rolesResponse])

  // Get Region By Admin App id
  const regionApiUrl = `${ApiUrl}/orgs/${organization.id}/org_units`,
    { data: regionsResponse } = useQuery({
      queryKey: ['active-and-user-org-units', regionApiUrl, organization.id],
      queryFn: async () => {
        return await SecureAxios.get(regionApiUrl, {
          params: {
            sort: standardSortParams(sortBy),
            ...(selectedUser
              ? { user_id: selectedUser.user_id }
              : { status: 'active' }),
          },
        })
      },
    })

  const regions = useMemo(() => {
    const res = regionsResponse?.data?.data
    regionList?.(res)

    if (regionsResponse) {
      const tempRegions: string[] = []
      res?.map((role: regionsType) => {
        return tempRegions?.push(role?.name)
      })

      return tempRegions
    }
  }, [regionList, regionsResponse])

  const [emailValidate, setEmailValidate] = useState<emailValidateType>({
    count: 0,
    invalidEmails: [],
  })

  const [emailTags, setEmailTags] = useState<string[]>(
    formState.email ? formState.email.split(',') : [],
  )

  useEffect(() => {
    if (formState.email) {
      const email_ids = formState.email.split(',')
      setEmailValidate((prevState) => ({
        ...prevState,
        count: email_ids?.length,
      }))
    }
  }, [formState.email])

  useEffect(() => {
    if (emailValidate) {
      emailValidateCallback?.(emailValidate)
    }
  }, [emailValidate, emailValidateCallback])

  useEffect(() => {
    if (!isEditMode) {
      // Validate emails whenever emailTags change
      const invalidEmails = emailTags.filter((email) => validateEmail(email))
      const count = emailTags.length

      setEmailValidate({
        count,
        invalidEmails,
      })
    }
  }, [emailTags, emailValidateCallback, isEditMode])

  const validateEmail = (email: string) => {
    const trimEmail = email.trim()
    const validate = (trimEmail: string) => {
      return String(trimEmail)
        .toLowerCase()
        .match(
          // eslint-disable-next-line no-control-regex
          /(?:[a-z0-9!#$%&'*+/=?^_`{|}~-]+(?:\.[a-z0-9!#$%&'*+/=?^_`{|}~-]+)*|"(?:[\x01-\x08\x0b\x0c\x0e-\x1f\x21\x23-\x5b\x5d-\x7f]|\\[\x01-\x09\x0b\x0c\x0e-\x7f])*")@(?:(?:[a-z0-9](?:[a-z0-9-]*[a-z0-9])?\.)+[a-z0-9](?:[a-z0-9-]*[a-z0-9])?|\[(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?|[a-z0-9-]*[a-z0-9]:(?:[\x01-\x08\x0b\x0c\x0e-\x1f\x21-\x5a\x53-\x7f]|\\[\x01-\x09\x0b\x0c\x0e-\x7f])+)\])/,
        )
    }
    const isValid = validate(trimEmail)
    if (hasValue(isValid?.input)) {
      return ''
    } else {
      return trimEmail
    }
  }

  const onBlurCallout = useCallback(() => {
    if (formState.email) {
      const email_ids = formState.email.split(',')
      if (email_ids?.length > 0) {
        const invalidEmails: string[] = []
        email_ids?.map((email_id) => {
          const isValid = validateEmail(email_id)
          return isValid.length > 0 && invalidEmails.push(isValid)
        })
        setEmailValidate((prevState) => ({
          ...prevState,
          invalidEmails: invalidEmails,
        }))
      }
    }
  }, [formState.email])

  // Update formState.email whenever emailTags change
  useEffect(() => {
    if (!isEditMode) {
      const joinedEmails = emailTags.join(',')
      if (formState.email !== joinedEmails) {
        setFormState('email', joinedEmails)
      }
    }
  }, [emailTags, isEditMode, setFormState, formState.email])

  useEffect(() => {
    setEmailTags(formState.email ? formState.email.split(',') : [])
  }, [formState.email])

  return (
    <div className='flex flex-direction-column pat-gap-4'>
      {bulkUserAction !== 'edit' &&
        bulkUserAction !== 'edit_role' &&
        bulkUserAction !== 'edit_region' && (
          <>
            {!isEditMode ? (
              <div className={styles.setDimensionAlt}>
                <TagInput
                  tags={emailTags}
                  setTags={setEmailTags}
                  tagLimit={maxEmailAllowed}
                  separators={[
                    Separators.COMMA,
                    Separators.SPACE,
                    Separators.ENTER,
                  ]}
                  placeholder={
                    emailTags.length === 0
                      ? t('enterEmailsSeparatedByACommaOrSpace')
                      : ''
                  }
                  formLabelProps={{
                    label: c('email'),
                    required: true,
                    error:
                      emailValidate?.count > maxEmailAllowed ||
                      emailValidate?.invalidEmails?.length !== 0,
                    disabled: !!isEditMode,
                  }}
                  name='email'
                  id='email'
                />
                {emailValidate?.count > maxEmailAllowed && (
                  <div className='fc-red fs-12'>
                    {t('noMoreThanMaxEmailsCanBeAddedAtASingleTime', {
                      max: maxEmailAllowed,
                    })}
                  </div>
                )}
                {emailValidate?.invalidEmails?.length !== 0 && (
                  <div className='fc-red fs-12'>
                    {`${t('pleaseEnterValidEmailId(s)')} ${emailValidate?.invalidEmails.join(', ')}`}
                  </div>
                )}
              </div>
            ) : (
              <TextInput
                labelText={c('email')}
                value={formState.email}
                callout={(_, value) => {
                  setFormState('email', value.toString())
                }}
                type={
                  bulkUserAction === 'edit' || isEditMode ? 'text' : 'textarea'
                }
                stateName='email'
                placeholder={t('enterEmailsSeparatedByAComma')}
                required
                disabled={!!isEditMode}
                onBlurCallout={onBlurCallout}
                classType={
                  emailValidate?.count > maxEmailAllowed
                    ? 'error'
                    : emailValidate?.invalidEmails?.length !== 0
                      ? 'error'
                      : ''
                }
                errorText={
                  emailValidate?.count > maxEmailAllowed
                    ? t('noMoreThanMaxEmailsCanBeAddedAtASingleTime', {
                        max: maxEmailAllowed,
                      })
                    : emailValidate?.invalidEmails?.length !== 0
                      ? `${t('pleaseEnterValidEmailId')} ${emailValidate?.invalidEmails.join(
                          ',',
                        )}`
                      : ''
                }
              />
            )}
            {isEditMode && (
              <>
                <TextInput
                  disabled={formState.sso_user}
                  type='text'
                  labelText={c('firstName')}
                  placeholder={
                    !formState.sso_user ? t('enterYourFirstName') : ''
                  }
                  value={formState.first_name}
                  stateName='first_name'
                  callout={(_, value) => {
                    setFormState('first_name', value.toString())
                  }}
                />
                <TextInput
                  disabled={formState.sso_user}
                  type={'text'}
                  labelText={c('lastName')}
                  placeholder={
                    !formState.sso_user ? t('enterYourLastName') : ''
                  }
                  value={formState.last_name}
                  stateName='last_name'
                  callout={(_, value) => {
                    setFormState('last_name', value.toString())
                  }}
                />
              </>
            )}
          </>
        )}
      {(bulkUserAction === 'edit_role' || bulkUserAction === 'edit_region') &&
      bulkUserAction !== undefined ? (
        <>
          {bulkUserAction === 'edit_role' && (
            <div className='single-filter select'>
              <Select
                labelProps={{ label: c('role') }}
                options={roles ?? []}
                optionKeyName='text'
                labelKeyName='text'
                selectedItem={{
                  id: formState.role.id,
                  value: formState.role.value,
                  text: formState.role.text,
                }}
                onChange={(value) => {
                  if (value) {
                    const option: selectOptionType = {
                      id: value?.id,
                      text: value?.text ?? '',
                      value: value?.value.toString(),
                    }
                    setFormState('role', option)
                  }
                }}
                required
                disabled={isLoading || roles?.length === 0}
              />
            </div>
          )}
          {bulkUserAction === 'edit_region' && (
            <>
              {!app?.metadata?.hide_access_to_all_org_units && (
                <div className='single-filter select flex align-items-center fs-12 fw-regular fc-purple'>
                  <Switch
                    className='pat-mr-2'
                    checked={formState.allRegion}
                    callout={() => {
                      setFormState('allRegion', !formState.allRegion)
                    }}
                  />{' '}
                  {t('accessToAllCurrentAndFutureOrgUnits')}
                </div>
              )}
              {!formState.allRegion && (
                <div className='single-filter select'>
                  <MultiSelect
                    formLabelProps={{
                      label: t('orgUnit(s)'),
                      required: app?.metadata?.require_org_unit_selection,
                    }}
                    hideSelectAll={!!app?.metadata?.hide_select_all_org_units}
                    options={
                      regions?.map((option) => ({ label: option })) ?? []
                    }
                    selectedOptions={formState.region.map((option) => ({
                      label: option,
                    }))}
                    selectPlaceholder={t('selectOrgUnit(s)')}
                    callout={(value) =>
                      setFormState(
                        'region',
                        value.map((option) => option.label as string),
                      )
                    }
                    labelKey='label'
                  />
                </div>
              )}
            </>
          )}
        </>
      ) : (
        <>
          <div className='single-filter select'>
            <Select
              labelProps={{ label: c('role') }}
              options={roles ?? []}
              optionKeyName={'text'}
              labelKeyName={'text'}
              selectedItem={{
                id: formState.role.id,
                value: formState.role.value,
                text: formState.role.text,
              }}
              onChange={(value) => {
                if (value) {
                  const option: selectOptionType = {
                    id: value?.id,
                    text: value?.text ?? '',
                    value: value?.value.toString(),
                  }
                  setFormState('role', option)
                }
              }}
              required={true}
              disabled={isLoading || roles?.length === 0}
            />
          </div>
          {!app?.metadata?.hide_access_to_all_org_units && (
            <div className='single-filter select flex align-items-center fs-12 fw-regular fc-purple'>
              <Switch
                className='pat-mr-2'
                checked={formState.allRegion}
                callout={() => {
                  setFormState('allRegion', !formState.allRegion)
                }}
              />{' '}
              {t('accessToAllCurrentAndFutureOrgUnits')}
            </div>
          )}
          {!formState.allRegion && (
            <div className='single-filter select'>
              <MultiSelect
                formLabelProps={{
                  label: t('orgUnit(s)'),
                  required: app?.metadata?.require_org_unit_selection,
                }}
                hideSelectAll={!!app?.metadata?.hide_select_all_org_units}
                options={regions?.map((option) => ({ label: option })) ?? []}
                selectedOptions={formState.region.map((option) => ({
                  label: option,
                }))}
                selectPlaceholder={t('selectOrgUnit(s)')}
                callout={(value) =>
                  setFormState(
                    'region',
                    value.map((option) => option.label as string),
                  )
                }
                labelKey='label'
              />
            </div>
          )}
        </>
      )}
    </div>
  )
}

export default UserForm
