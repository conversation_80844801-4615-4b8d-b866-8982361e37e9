import React, { use<PERSON><PERSON>back, useContext, useEffect, useState } from 'react'
import {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>er,
  getApiUrlPrefix,
  hasValue,
  SideDrawer,
  toast,
  useToggle,
} from '@patterninc/react-ui'
import { useMutation } from '@tanstack/react-query'

import UserForm, {
  type emailValidateType,
  type regionsArrType,
} from './UserForm'
import { type UserType } from './users-table-config'
import SecureAxios from '../../../common/services/SecureAxios'
import { ThemeContext } from '../../../Context'
import { c, useTranslate } from '../../../common/services/TranslationService'

type UserFormBaseProps = {
  isOpen: boolean
  setIsOpen: (isOpen: boolean) => void
  isEditMode: boolean
  selectedUsers?: Array<UserType>
  selectedUser?: UserType
  bulkUserAction?: string
  refreshList: () => void
  checkAll?: boolean
  isInactivePage?: boolean
  Ids?: number[]
  searchText?: string
  filterParams?: Record<string, unknown>
  setSelectedUser: (user: UserType | undefined) => void
  refetchUser?: () => void
  appForUserDetails?: AppType
  orgForUserDetails?: OrgType
  selectedUsersCount?: number
}

type AppType = {
  id: number
  name: string
  metadata?: {
    require_org_unit_selection: boolean
    hide_select_all_org_units: boolean
    hide_access_to_all_org_units: boolean
  }
}
type OrgType = {
  id: number
  name: string
}
export type selectOptionType = {
  id: string
  value: string
  text: string
}

export type FormState = {
  email: string
  first_name: string
  last_name: string
  sso_user: boolean
  role: selectOptionType
  region: string[]
  allRegion: boolean
}

interface paramstype {
  params: {
    apps_orgs_user: {
      id?: string | number
      app_id: string | number
      emails?: string[]
      org_unit_ids?: string[]
      role_ids?: string[]
      region_ids?: string[]
    }
  }
  apiUrl: string
}

interface editparamstype {
  params: {
    id?: string | number
    org_unit_ids?: string[]
    role_ids?: string[]
  }
  apiUrl: string
  editNameReqBody: {
    given_name: string
    family_name: string
  }
  apiUrlEditName: string
  isSsoUser: boolean | undefined
  status: string | undefined
}

const UserFormParams = ({
  isOpen,
  setIsOpen,
  isEditMode,
  selectedUser,
  bulkUserAction,
  refreshList,
  checkAll,
  isInactivePage,
  Ids,
  filterParams,
  searchText,
  setSelectedUser,
  appForUserDetails,
  orgForUserDetails,
  refetchUser,
  selectedUsersCount,
}: UserFormBaseProps): React.JSX.Element => {
  const { t } = useTranslate('users'),
    appContext = useContext(ThemeContext).app,
    orgContext = useContext(ThemeContext).organization,
    app = appForUserDetails || appContext,
    isBulkUpdateConfirmationEnabled = useToggle('bulk_update_confirmation'),
    organization = orgForUserDetails || orgContext
  const [regionList, setRegionList] = useState<Array<regionsArrType>>()
  const [disabled, setDisabled] = useState(true)
  const [formState, setFormState] = useState<FormState>({
    email: '',
    first_name: '',
    last_name: '',
    sso_user: false,
    role: {
      id: '',
      text: t('selectRole'),
      value: '',
    },
    region: [],
    allRegion: false,
  })
  const maxEmailAllowed = 50

  const [emailValidate, setEmailValidate] = useState<emailValidateType>({
    count: 0,
    invalidEmails: [],
  })

  useEffect(() => {
    const { user } = selectedUser || {},
      selectedRole = selectedUser?.roles[0]
    const regions = selectedUser?.org_units?.map((region) => {
      return region?.name
    })
    setFormState((prevState) => ({
      ...prevState,
      email: user?.email || '',
      first_name: user?.given_name || '',
      last_name: user?.family_name || '',
      sso_user: user?.sso_user || false,
      region: regions ?? [],
      role: {
        id: selectedRole?.id || '',
        text: selectedRole?.name || '',
        value: selectedRole?.name || '',
      },
      allRegion: selectedUser?.access_to_all_org_units || false,
    }))
  }, [isEditMode, selectedUser])

  const updateForm = (
    key: keyof FormState,
    value: string | selectOptionType | string[] | boolean,
  ) => {
    setFormState((prevState) => ({
      ...prevState,
      [key]: value,
    }))
  }

  useEffect(() => {
    const isOrgUnitSelectionRequired = app?.metadata?.require_org_unit_selection
    if (
      (bulkUserAction === 'add' &&
        hasValue(formState?.email) &&
        formState?.role?.text.length &&
        emailValidate?.count >= 1 &&
        emailValidate?.count <= maxEmailAllowed &&
        emailValidate?.invalidEmails?.length === 0 &&
        (formState?.region?.length > 0 ||
          formState?.allRegion ||
          !isOrgUnitSelectionRequired)) ||
      (bulkUserAction === 'edit_region' &&
        (formState?.region?.length > 0 ||
          formState?.allRegion ||
          !isOrgUnitSelectionRequired)) ||
      (bulkUserAction === 'edit_role' &&
        formState?.role?.text !== t('selectRole')) ||
      (isEditMode &&
        (formState?.region?.length > 0 ||
          formState?.allRegion ||
          !isOrgUnitSelectionRequired) &&
        formState?.role?.text !== t('selectRole'))
    ) {
      setDisabled(false)
    } else {
      setDisabled(true)
    }
  }, [
    app?.metadata?.require_org_unit_selection,
    bulkUserAction,
    emailValidate?.count,
    emailValidate?.invalidEmails,
    emailValidate?.invalidEmails?.length,
    formState,
    isEditMode,
    t,
  ])

  const formSubmit = () => {
    const email_ids = formState.email.split(',').map((email) => email.trim())
    const regionIds: string[] = []
    const regionsOri = formState?.region?.map((region) => {
      return regionList?.find((region1) => region1.name === region)
    })
    regionsOri.map((region) => {
      return regionIds.push(region?.id.toString() ?? '0')
    })
    const editActionParams = {
      params: {
        id: selectedUser?.user_id,
        role_ids: [formState.role?.id.toString()],
        access_to_all_org_units: formState?.allRegion,
        org_unit_ids: regionIds,
      },
      apiUrl: `${getApiUrlPrefix('adminczar')}/api/v1/apps/${app?.id}/orgs/${
        organization.id
      }/users/${selectedUser?.user_id}`,

      editNameReqBody: {
        given_name: formState.first_name,
        family_name: formState.last_name,
      },
      apiUrlEditName: `${getApiUrlPrefix('adminczar')}/api/v1/users/${selectedUser?.user_id}`,
      isSsoUser: selectedUser?.user?.sso_user,
      status: selectedUser?.status,
    }
    const bulkActionParams = {
      params: {
        apps_orgs_user: {
          app_id: app?.id,
          emails: email_ids,
          role_ids: [formState.role?.id.toString()],
          ...(formState?.allRegion
            ? {
                access_to_all_org_units: formState?.allRegion,
              }
            : { org_unit_ids: regionIds }),
        },
      },
      apiUrl: `${getApiUrlPrefix('adminczar')}/api/v1/apps/${app?.id}/orgs/${
        organization.id
      }/users/bulk_create`,
    }
    if (isEditMode) {
      editmutation.mutate(editActionParams)
    } else {
      mutation.mutate(bulkActionParams)
    }
  }

  const bulkUpdate = () => {
    const regionIds: string[] = []
    const regionsOri = formState?.region?.map((region) => {
      return regionList?.find((region1) => region1.name === region)
    })
    regionsOri.map((region) => {
      return regionIds.push(region?.id?.toString() ?? '0')
    })
    const bulkActionParams = {
      params: {
        apps_orgs_user: {
          app_id: app?.id,
          ...(checkAll
            ? {
                filters: {
                  status: isInactivePage
                    ? 'inactive'
                    : ['onboarding', 'active'],
                  ...filterParams,
                },
              }
            : {
                ids: Ids,
              }),
          ...(bulkUserAction === 'edit_role'
            ? {
                role_ids: [formState.role?.id.toString()],
              }
            : {}),
          ...(bulkUserAction === 'edit_region'
            ? {
                access_to_all_org_units: formState?.allRegion,
                org_unit_ids: regionIds,
              }
            : {}),
        },
        ...(checkAll
          ? {
              ...(searchText ? { bulk_search: searchText } : {}),
            }
          : {}),
      },
      apiUrl: `${getApiUrlPrefix('adminczar')}/api/v1/apps/${app?.id}/orgs/${
        organization.id
      }/users/bulk_update`,
    }
    mutation.mutate(bulkActionParams)
  }

  const mutation = useMutation({
    mutationFn: (bulkActionParams: paramstype) =>
      SecureAxios.post(bulkActionParams?.apiUrl, bulkActionParams?.params),
    onSuccess: (res) => {
      refreshList()
      const successCount = res?.data?.data?.success_count
      const failedCount = res?.data?.data?.failed_count
      const failedEmails = res?.data?.data?.failed_emails
      if (successCount) {
        toast({
          message:
            bulkUserAction === 'add' ? (
              <span>
                {t('userSuccessfullyCreated', { count: successCount })}
              </span>
            ) : (
              <span>
                {t('userSuccessfullyUpdated', { count: successCount })}
              </span>
            ),
          type: 'success',
          config: {
            autoClose: 3000,
          },
        })
      }

      if (failedCount) {
        const uniqueFailedEmails = Array.from(new Set(failedEmails))
        const displayedUsers = uniqueFailedEmails.slice(0, 3)
        const remainingCount = uniqueFailedEmails.length - displayedUsers.length
        const existingUsersMessage = `${displayedUsers.join(', ')}${remainingCount > 0 ? `, and ${remainingCount} more` : ''} already existed in the system.`
        const toastConfig = successCount === 0 ? {} : { delay: 3500 }
        const message =
          bulkUserAction === 'add' ? (
            <span>{existingUsersMessage}</span>
          ) : (
            <span>{t('unableToUpdateUser', { count: failedCount })}</span>
          )
        toast({
          message: message,
          type: 'error',
          config: toastConfig,
          buttons: [
            {
              children: t('copyExistingUsersToClipboard'),
              onClick: () => {
                navigator.clipboard
                  .writeText(uniqueFailedEmails.join(', '))
                  .then(() => {
                    toast({
                      message: t('emailsCopiedToClipboardSuccessfully'),
                      type: 'success',
                    })
                  })
                  .catch(() => {
                    toast({
                      message: t('failedToCopyEmailsToClipboard'),
                      type: 'error',
                    })
                  })
              },
            },
          ],
        })
      }
      setIsOpen(false)
    },
    onError: () => {
      toast({
        message: c('somethingWentWrongPleaseTryAgain'),
        type: 'error',
      })
    },
  })

  const editmutation = useMutation({
    mutationFn: async (editActionParams: editparamstype) => {
      if (!editActionParams?.isSsoUser) {
        if (
          (!editActionParams?.editNameReqBody.given_name ||
            !editActionParams?.editNameReqBody.family_name) &&
          editActionParams?.status !== 'onboarding'
        ) {
          throw new Error(t('firstNameOrLastNameCannotBeBlank'))
        }
        await SecureAxios.patch(
          editActionParams?.apiUrlEditName,
          editActionParams?.editNameReqBody,
        )
      }
      await SecureAxios.put(editActionParams?.apiUrl, editActionParams?.params)
    },
    onSuccess: () => {
      refreshList()
      if (refetchUser) {
        refetchUser()
      }
      toast({
        message: t('changesSaved'),
        type: 'success',
      })
      setIsOpen(false)
    },
    onError: (error) => {
      toast({
        message:
          error instanceof Error
            ? error.message
            : c('somethingWentWrongPleaseTryAgain'),
        type: 'error',
      })
    },
  })

  const emailValidateCallback = useCallback((data: emailValidateType) => {
    return setEmailValidate((prevState) => ({
      ...prevState,
      ...data,
    }))
  }, [])

  const savingForm = mutation.isPending || editmutation.isPending

  return (
    <SideDrawer
      isOpen={isOpen}
      closeCallout={() => {
        setIsOpen(false)
        setSelectedUser(undefined)
      }}
      headerContent={`${
        bulkUserAction === 'add' ? t('createNewUser(s)') : t('editProfile')
      }`}
      footerContent={
        <FormFooter
          cancelButtonProps={{
            onClick: () => {
              setIsOpen(false)
              setSelectedUser(undefined)
            },
          }}
          saveButtonProps={{
            disabled: disabled || savingForm,
            ...(!isBulkUpdateConfirmationEnabled ||
            bulkUserAction === 'add' ||
            isEditMode
              ? {
                  onClick: () =>
                    bulkUserAction === 'add' || isEditMode
                      ? formSubmit()
                      : bulkUpdate(), //remove this when bulk_update_confirmation toggle archived
                }
              : {
                  confirmation: {
                    header: c('areYouSure'),
                    type: 'blue',
                    body: (
                      <span>
                        {t('bulkUpdateConfirmation', {
                          count: selectedUsersCount,
                        })}
                      </span>
                    ),
                    confirmButtonText: c('confirm'),
                    confirmCallout: () => bulkUpdate(),
                  },
                }),
            ...(bulkUserAction === 'add' && { children: t('createUser(s)') }),
            ...(savingForm && {
              children: (
                <span>
                  {c('saving')}
                  <Ellipsis />
                </span>
              ),
            }),
          }}
        />
      }
    >
      <UserForm
        isEditMode={isEditMode}
        formState={formState}
        setFormState={updateForm}
        bulkUserAction={bulkUserAction}
        regionList={setRegionList}
        emailValidateCallback={emailValidateCallback}
        maxEmailAllowed={maxEmailAllowed}
        selectedUser={selectedUser}
        appForUserDetails={appForUserDetails}
        orgForUserDetails={orgForUserDetails}
      />
    </SideDrawer>
  )
}

export default UserFormParams
