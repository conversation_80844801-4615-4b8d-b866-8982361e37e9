import React, {
  use<PERSON><PERSON>back,
  useContext,
  useEffect,
  useMemo,
  useState,
} from 'react'
import { useInfiniteQuery } from '@tanstack/react-query'
import {
  type ConfigItemType,
  getApiUrlPrefix,
  hasValue,
  standardSortParams,
  StandardTable,
  useToggle,
} from '@patterninc/react-ui'

import { GenerateTableConfig, type UserType } from './users-table-config'
import UserBulkAction from './UserBulkAction'
import UserFormParams from './UserFormParams'
import SecureAxios from '../../../common/services/SecureAxios'
import { ThemeContext } from '../../../Context'
import { c, useTranslate } from '../../../common/services/TranslationService'

type UsersTableProps = {
  sortBy: { prop: string; flip: boolean }
  setSortBy: (sortObject: { activeColumn: string; direction: boolean }) => void
  searchText: string
  searchInputHandler: (search: string) => void
  isInactiveTab?: boolean
  refreshSummary?: (status: boolean) => void
  filterParams: Record<string, unknown>
  setUsersCount: (count: string) => void
  usersCount?: number
}

export type TableStateType = {
  checkedBoxes: UserType[]
  selectAll: boolean
  isResetCheckboxes: boolean
}

const UsersTable = ({
  sortBy,
  setSortBy,
  searchText,
  isInactiveTab,
  filterParams,
  setUsersCount,
  usersCount,
}: UsersTableProps): React.JSX.Element => {
  const { t } = useTranslate('users'),
    { organization, app } = useContext(ThemeContext),
    [isEditMode, setEditMode] = useState<boolean>(false),
    [selectedUser, setSelectedUser] = useState<UserType | undefined>(),
    [reload, setReload] = useState(false),
    isBulkUpdateConfirmationEnabled = useToggle('bulk_update_confirmation'),
    stickyTableConfig = {
      right: 1,
    }
  const [isPrevTabInactive, setIsPrevTabInactive] = useState(isInactiveTab)

  useEffect(() => {
    setIsPrevTabInactive(isInactiveTab)
  }, [isInactiveTab])

  const isSortSetForTab = useMemo(() => {
    const isTabChanged =
      isPrevTabInactive !== isInactiveTab &&
      isInactiveTab &&
      sortBy.prop === 'deactivated_at'
    const isTabUnchanged = isPrevTabInactive === isInactiveTab

    return isTabChanged || isTabUnchanged
  }, [isPrevTabInactive, isInactiveTab, sortBy.prop])

  const [tableState, setTableState] = useState<TableStateType>({
    checkedBoxes: [],
    selectAll: false,
    isResetCheckboxes: false,
  })
  const { checkedBoxes, selectAll, isResetCheckboxes } = tableState

  // Users Table API
  const apiUrl = `${getApiUrlPrefix('adminczar')}/api/v1/apps/${app.id}/orgs/${
      organization.id
    }/users`,
    updatedParams = {
      status: isInactiveTab ? 'inactive' : ['onboarding', 'active'],
    },
    {
      data: usersPaginatedData,
      status,
      fetchNextPage,
      hasNextPage,
      isLoading,
    } = useInfiniteQuery({
      queryKey: [
        apiUrl,
        isInactiveTab,
        sortBy,
        reload,
        organization.id,
        searchText,
        filterParams,
      ],
      queryFn: async ({ pageParam = 1, signal }) => {
        const data = await SecureAxios.get(apiUrl, {
          params: {
            ...updatedParams,
            page: pageParam ?? 1,
            per_page: 20,
            sort: standardSortParams(sortBy),
            ...(searchText ? { bulk_search: searchText } : {}),
            ...filterParams,
          },
          signal,
        })
        setUsersCount(data?.data?.pagination?.count)
        setReload(false)
        setTableState((prevState) => ({
          ...prevState,
          isResetCheckboxes: false,
        }))
        return data
      },
      initialPageParam: 1,
      gcTime: 1000 * 60 * 60 * 8,
      enabled: !!app.id && !!organization.id && isSortSetForTab,
      getNextPageParam: (previousResponse) => {
        return previousResponse?.data?.pagination?.last_page
          ? undefined
          : previousResponse?.data?.pagination?.next_page
      },
    })

  const usersData = useMemo(
    () =>
      usersPaginatedData
        ? usersPaginatedData?.pages?.flatMap((page) => {
            return page?.data?.data
          })
        : [],
    [usersPaginatedData],
  )
  const hasData = !!(status === 'success' && usersData?.length)

  const refreshCheckboxes = useCallback(() => {
    setTableState((prevState) => ({
      ...prevState,
      selectAll: false,
      checkedBoxes: [],
      isResetCheckboxes: true,
    }))
  }, [])

  // reset state on tab & org change
  useEffect(() => {
    refreshCheckboxes()
  }, [isInactiveTab, refreshCheckboxes, organization])

  const refreshList = useCallback(() => {
    setReload(true)
    refreshCheckboxes()
  }, [refreshCheckboxes])

  const editUserProfile = useCallback((user: UserType) => {
    setEditMode(true)
    setSelectedUser(user)
  }, [])

  const refresh = useCallback(() => {
    refreshList()
  }, [refreshList])

  const groupHeaders = useMemo(
    () => [
      {
        groupHeader: (tableCount: number) =>
          !selectAll ? `${c('selected')} (${tableCount})` : c('selectedAll'),

        includeClearButton: true,
        clearCallout: () =>
          setTableState((prevState) => ({
            ...prevState,
            selectAll: false,
            checkedBoxes: [],
            isResetCheckboxes: true,
          })),
        check: (dataItem: UserType, checkedBoxes?: UserType[]): boolean => {
          return (
            selectAll ||
            hasValue(
              checkedBoxes?.find((c) => {
                return c.id === dataItem.id
              }),
            )
          )
        },
      },
      {
        groupHeader: c('unselected'),
        check: (dataItem: UserType, checkedBoxes?: UserType[]): boolean => {
          return (
            !selectAll &&
            !hasValue(
              checkedBoxes?.find((c) => {
                return c.id === dataItem.id
              }),
            )
          )
        },
      },
    ],
    [selectAll],
  )

  const handleCheckedBoxes = (
    checkedBoxes: UserType[],
    isCheckAll?: boolean,
  ) => {
    if (isCheckAll) {
      setTableState((prevState) => ({
        ...prevState,
        selectAll: true,
        checkedBoxes: checkedBoxes,
      }))
    } else {
      setTableState((prevState) => ({
        ...prevState,
        selectAll: false,
        checkedBoxes: checkedBoxes,
      }))
    }
  }
  return (
    <>
      {/* TODO: REMOVE THIS ONCE ISSUE IS FIXED */}
      <style>{`
        .disable-select-all-checkbox .sticky-table-row:first-child .sticky-table-cell:first-child [data-testid="checkbox-component"] {
          display: none !important;
        }
      `}</style>
      {isBulkUpdateConfirmationEnabled ? (
        <StandardTable
          data={usersData ?? []}
          stickyTableConfig={stickyTableConfig}
          config={
            GenerateTableConfig({
              sortBy,
              isInactiveTab,
              editUserProfile,
              refresh,
            }) as ConfigItemType<unknown, Record<string, unknown>>[]
          }
          key={`${searchText}-${isResetCheckboxes}`}
          hasCheckboxes={true}
          showGroups={true}
          groups={groupHeaders}
          handleCheckedBoxes={handleCheckedBoxes}
          isResetCheckboxes={isResetCheckboxes}
          hasData={hasData}
          loading={isLoading}
          hasMore={!!hasNextPage}
          sort={setSortBy}
          sortBy={sortBy}
          getData={fetchNextPage}
          successStatus={status === 'success'}
          dataKey='id'
          tableId='users_table'
          customHeight={'auto'}
          noDataFields={{
            primaryText: t('noUsersFound'),
            secondaryText: t('weCouldNotFindAnyUsersForTheSelectedCriteria'),
          }}
        />
      ) : (
        <div className='disable-select-all-checkbox'>
          <StandardTable
            data={usersData ?? []}
            stickyTableConfig={stickyTableConfig}
            config={
              GenerateTableConfig({
                sortBy,
                isInactiveTab,
                editUserProfile,
                refresh,
              }) as ConfigItemType<unknown, Record<string, unknown>>[]
            }
            key={`${searchText}-${isResetCheckboxes}`}
            hasCheckboxes={true}
            showGroups={true}
            groups={groupHeaders}
            handleCheckedBoxes={handleCheckedBoxes}
            isResetCheckboxes={isResetCheckboxes}
            hasData={hasData}
            loading={isLoading}
            hasMore={!!hasNextPage}
            sort={setSortBy}
            sortBy={sortBy}
            getData={fetchNextPage}
            successStatus={status === 'success'}
            dataKey='id'
            tableId='users_table'
            customHeight={'auto'}
            noDataFields={{
              primaryText: t('noUsersFound'),
              secondaryText: t('weCouldNotFindAnyUsersForTheSelectedCriteria'),
            }}
          />
        </div>
      )}
      <UserBulkAction
        refreshList={refreshList}
        checkedBoxes={checkedBoxes}
        checkAll={selectAll}
        isInactivePage={isInactiveTab}
        searchText={searchText}
        filterParams={filterParams}
        totalUsers={usersCount}
      />

      <UserFormParams
        isOpen={isEditMode}
        setIsOpen={setEditMode}
        isEditMode
        selectedUser={selectedUser}
        setSelectedUser={setSelectedUser}
        refreshList={refreshList}
        searchText={searchText}
        filterParams={filterParams}
      />
    </>
  )
}

export default UsersTable
