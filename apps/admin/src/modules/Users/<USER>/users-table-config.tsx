import React, { use<PERSON>ontext, useMemo } from 'react'
import {
  ButtonGroup,
  type ConfigItemType,
  getApiUrlPrefix,
  has<PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>,
  toast,
  <PERSON><PERSON><PERSON>,
  TrimText,
} from '@patterninc/react-ui'
import moment from 'moment'
import { useMutation } from '@tanstack/react-query'

import SecureAxios from '../../../common/services/SecureAxios'
import { ThemeContext } from '../../../Context'
import { tryLocalStorageParse } from '../../../common/services/HelperService'
import { hasWritePermission } from '../../../Auth/route-permissions'
import { c, t } from '../../../common/services/TranslationService'

export type UserType = {
  id: number | undefined
  organization_id: number
  user_id: number
  last_login?: string | null
  user: User
  status: string
  created_at: string
  deactivated_at: undefined | string
  deactivated_by?: string
  created_by?: string
  created_by_name?: string
  roles: Array<{ id: string; name: string }>
  org_units?: Array<{ id: number; name: string }>
  access_to_all_org_units: boolean
  app_id: number
  invitation_expired?: boolean
}

export type User = {
  given_name: string
  family_name: string
  email: string
  roles?: Array<{ id: number; name: string }>
  org_units?: Array<{ id: number; name: string }>
  sso_user: boolean
  last_login: string | null
}

type paramsType = {
  id: number
  status?: string
}

type TableConfigProps = {
  sortBy: { prop: string; flip: boolean }
  isInactiveTab?: boolean
  editUserProfile: (user: UserType) => void
  refresh: (status: boolean) => void
}

export interface paramstypes {
  params: paramsType
  apiUrl: string
}

interface deleteuserparamstypes {
  apiUrl: string
}

export const GenerateTableConfig = ({
  sortBy,
  isInactiveTab,
  editUserProfile,
  refresh,
}: TableConfigProps): ConfigItemType<UserType, Record<string, unknown>>[] => {
  const { organization, app, privileges } = useContext(ThemeContext)
  const currentUser = tryLocalStorageParse('current_user')

  const userAction = (userDetails: UserType, type: string) => {
    const bulkActionParams = {
      params: {
        id: userDetails.user_id,
        ...(userDetails.access_to_all_org_units &&
        type !== 'passwordReset' &&
        type !== 'resendActivationMail'
          ? {
              access_to_all_org_units: true,
            }
          : {}),

        ...(type !== 'passwordReset' && type !== 'resendActivationMail'
          ? { status: type }
          : {}),
      },
      apiUrl: `${getApiUrlPrefix('adminczar')}/api/v1/apps/${app?.id}/orgs/${
        organization.id
      }/users/${userDetails.user_id}${
        type === 'passwordReset'
          ? `/reset_password`
          : type === 'resendActivationMail'
            ? '/resend_activation_email'
            : ''
      }`,
    }
    mutation.mutate(bulkActionParams)
  }

  const mutation = useMutation({
    mutationFn: (bulkActionParams: paramstypes) =>
      SecureAxios.put(bulkActionParams?.apiUrl, bulkActionParams?.params),
    onSuccess: (_, bulkActionParams) => {
      if (
        bulkActionParams?.params?.status ||
        bulkActionParams?.apiUrl.includes('resend_activation_email')
      ) {
        refresh(true)
      }
      toast({
        type: 'success',
        message: bulkActionParams?.params?.status
          ? bulkActionParams?.params?.status === 'inactive'
            ? t('users:userSuccessfullyDeactivated')
            : t('users:userSuccessfullyActivated')
          : bulkActionParams?.apiUrl.includes('resend_activation_email')
            ? t('users:activationEmailSent')
            : t('users:passwordResetEmailSent'),
      })
    },
    onError: () => {
      toast({
        type: 'error',
        message: c('somethingWentWrongPleaseTryAgain'),
      })
    },
  })

  const deleteUserMutation = useMutation({
    mutationFn: async (deleteUserParams: deleteuserparamstypes) => {
      return await SecureAxios.delete(deleteUserParams?.apiUrl)
    },
    onSuccess: () => {
      refresh(true)
      toast({
        type: 'success',
        message: t('users:userDeletedSuccessfully'),
      })
    },
    onError: () => {
      toast({
        type: 'error',
        message: c('somethingWentWrongPleaseTryAgain'),
      })
    },
  })

  const deleteUser = (user_id: number) => {
    const apiUrl = `${getApiUrlPrefix('adminczar')}/api/v1/apps/${app?.id}/orgs/${organization?.id}/users/${user_id}`
    const deleteUserParams = {
      apiUrl,
    }
    deleteUserMutation.mutate(deleteUserParams)
  }

  const canManageUsers = useMemo(
    () => hasWritePermission('manage_users', privileges),
    [privileges],
  )

  const canCreateUsers = useMemo(
    () => hasWritePermission('create_users', privileges),
    [privileges],
  )

  const isActionDisabled = (user: UserType) => {
    return user?.user?.email === currentUser?.email || !canManageUsers
  }

  const renderActionColumn = (user: UserType) => {
    if (isInactiveTab && !canManageUsers) {
      return null
    }
    return isInactiveTab ? (
      <ButtonGroup
        buttons={[
          {
            actions: [
              {
                text: t('users:editProfile'),
                icon: 'pencil',
                callout: () => editUserProfile(user),
                disabled: { value: !canManageUsers },
              },
              {
                text: t('users:deleteUser'),
                icon: 'trash',
                destructive: true,
                confirmation: {
                  type: 'red',
                  header: c('areYouSure'),
                  body: t('users:deleteUserBody', { appName: app?.name }),
                  confirmCallout: () => {
                    deleteUser(user?.user_id)
                  },
                },
                disabled: { value: isActionDisabled(user) },
              },
            ],
          },
          {
            children: t('users:reactivateUser'),
            onClick: () => userAction(user, 'active'),
            disabled: isActionDisabled(user),
          },
        ]}
      />
    ) : (
      <ButtonGroup
        buttons={[
          {
            actions: [
              user.status === 'onboarding'
                ? {
                    text: t('users:resendActivationEmail'),
                    icon: 'paperPlane',
                    confirmation: {
                      type: 'blue',
                      header: c('areYouSure'),
                      body: t('users:resendActivationEmailBody'),
                      confirmCallout: () =>
                        userAction(user, 'resendActivationMail'),
                    },
                    disabled: {
                      value: !(canManageUsers || canCreateUsers),
                    },
                  }
                : {
                    text: t('users:sendPasswordReset'),
                    icon: 'paperPlane',
                    disabled: {
                      value: user?.user?.sso_user || !canManageUsers,
                      tooltip: {
                        tooltipContent: t(
                          'users:thisActionIsNotAllowedForSSOUser',
                        ),
                      },
                    },
                    confirmation: {
                      type: 'blue',
                      header: c('areYouSure'),
                      body: t('users:sendPasswordResetBody'),
                      confirmCallout: () => userAction(user, 'passwordReset'),
                    },
                  },
              {
                text: t('users:deactivateUser'),
                icon: 'unfollow',
                confirmation: {
                  type: 'red',
                  header: c('areYouSure'),
                  body: t('users:deactivateUserBody', { appName: app?.name }),
                  confirmCallout: () => userAction(user, 'inactive'),
                },
                disabled: {
                  value: isActionDisabled(user),
                  tooltip: {
                    tooltipContent: t('users:doNotHaveSuffientPrivilege'),
                  },
                },
              },
            ],
          },
          {
            children: t('users:editProfile'),
            onClick: () => editUserProfile(user),
            disabled: isActionDisabled(user),
            ...(canCreateUsers &&
              !canManageUsers && {
                tooltip: {
                  tooltipContent: t('users:doNotHaveSuffientPrivilege'),
                },
              }),
          },
        ]}
      />
    )
  }

  const nameColumn = {
    name: 'name',
    label: c('name'),
    noSort: true,
    style: { minWidth: '50px' },
    cell: {
      children: (data: UserType) => (
        <span
          className={`flex align-items-center ${
            sortBy.prop === 'name' ? 'fw-semi-bold' : ''
          }`}
        >
          {data?.user?.given_name}
          {` `}
          {data?.user?.family_name}
        </span>
      ),
    },
    mainColumn: true,
  }

  const emailColumn = {
    name: 'email',
    label: c('email'),
    noSort: true,
    cell: {
      children: (data: UserType) => (
        <span className={`${sortBy.prop === 'email' ? 'fw-semi-bold' : ''}`}>
          {data?.user?.email}
        </span>
      ),
    },
  }

  const roleColumn = {
    name: 'role',
    label: c('role'),
    noSort: true,
    style: { minWidth: '150px' },
    cell: {
      children: (data: UserType) => (
        <span className={`${sortBy.prop === 'role' ? 'fw-semi-bold' : ''}`}>
          {data?.roles?.[0]?.name}
        </span>
      ),
    },
  }

  const regionColumn = {
    name: 'region',
    label: c('orgUnit'),
    noSort: true,
    cell: {
      children: (data: UserType) => {
        const org_units = data?.org_units
        return org_units?.length === 1 ||
          data?.access_to_all_org_units ||
          org_units?.length === 0 ? (
          <span className={`${sortBy.prop === 'region' ? 'fw-semi-bold' : ''}`}>
            {data?.access_to_all_org_units ? (
              c('all')
            ) : org_units?.length === 0 ? (
              <Mdash />
            ) : org_units?.length === 1 ? (
              org_units[0].name
            ) : (
              org_units?.length
            )}
          </span>
        ) : (
          <Tooltip
            position='left'
            noPadding
            maxWidth='300'
            tooltipContent={
              <div className='pat-p-4'>
                <p className='fw-bold pat-m-0 pat-pb-2'>Org Units</p>
                {org_units?.map((item) => {
                  return (
                    <p key={item.id} className='pat-m-0'>
                      {item.name}
                    </p>
                  )
                })}
              </div>
            }
          >
            <span
              className={`${sortBy.prop === 'region' ? 'fw-semi-bold' : ''}`}
            >
              {data?.access_to_all_org_units ? (
                c('all')
              ) : org_units?.length === 0 ? (
                <Mdash />
              ) : org_units?.length === 1 ? (
                org_units[0].name
              ) : (
                org_units?.length
              )}
            </span>
          </Tooltip>
        )
      },
    },
  }

  const createdAtColumn = {
    name: 'created_at',
    label: c('created'),
    cell: {
      children: (data: UserType) => (
        <div>
          <span
            className={`flex align-items-center ${
              sortBy.prop === 'created_at' ? 'fw-semi-bold' : ''
            }`}
          >
            {moment(data.created_at).format('MMMM DD, YYYY')}
          </span>
          {data?.created_by_name && (
            <TrimText
              customClass='fs-10 fc-purple'
              limit={24}
              text={data.created_by_name}
            />
          )}
        </div>
      ),
    },
  }

  const deactivatedAtColumn = {
    name: 'deactivated_at',
    label: c('deactivated'),
    cell: {
      children: (data: UserType) => {
        return (
          <MdashCheck check={!!hasValue(data.deactivated_at)}>
            <span
              className={`flex align-items-center ${
                sortBy.prop === 'deactivated_at' ? 'fw-semi-bold' : ''
              }`}
            >
              {moment(data.deactivated_at).format('MMMM DD, YYYY')}
            </span>
            {data?.deactivated_by && (
              <TrimText
                customClass='fs-10 fc-purple'
                limit={24}
                text={data.deactivated_by}
              />
            )}
          </MdashCheck>
        )
      },
    },
  }

  const lastLoginColumn = {
    name: 'last_login',
    label: t('users:lastLogin'),
    cell: {
      children: (data: UserType) => {
        return (
          <>
            <div>
              <span
                className={`max-content-width pat-mb-0.5 ${
                  sortBy.prop === 'last_login' ? 'fw-semi-bold' : ''
                }`}
              >
                <MdashCheck check={hasValue(data.last_login)}>
                  {moment(data.last_login).format('MMMM DD, YYYY')}
                </MdashCheck>
              </span>
              {hasValue(data.last_login) && (
                <span className='fs-10 fc-purple'>
                  {moment(data.last_login).format('hh:mm A')}
                </span>
              )}
            </div>
          </>
        )
      },
    },
  }

  const statusColumn = {
    name: 'status',
    label: c('status'),
    noSort: true,
    cell: {
      children: (data: UserType) => {
        return (
          <span
            className={`${sortBy.prop === 'last_login' ? 'fw-semi-bold' : ''}`}
          >
            {data?.status === 'onboarding' ? (
              data?.invitation_expired ? (
                <Tag color='light-gray'>{c('invitationExpired')}</Tag>
              ) : (
                <Tag color='orange'>{c('pending')}</Tag>
              )
            ) : (
              <Tag color='green'>{data?.status}</Tag>
            )}
          </span>
        )
      },
    },
  }

  const actionColumn = {
    name: '',
    label: '',
    noSort: true,
    cell: {
      children: (user: UserType) => {
        return renderActionColumn(user)
      },
    },
  }

  const columns: ConfigItemType<UserType, Record<string, unknown>>[] = [
    nameColumn,
    emailColumn,
    roleColumn,
    regionColumn,
    // Swap 'created_at' and 'deactivated_at' based on 'isInactiveTab'
    ...(isInactiveTab ? [deactivatedAtColumn] : [createdAtColumn]),
    lastLoginColumn,
    ...(isInactiveTab ? [createdAtColumn] : []),
    ...(isInactiveTab ? [] : [statusColumn]),
    ...((isInactiveTab ? canManageUsers : canCreateUsers || canManageUsers)
      ? [actionColumn]
      : []),
  ]

  return columns
}
