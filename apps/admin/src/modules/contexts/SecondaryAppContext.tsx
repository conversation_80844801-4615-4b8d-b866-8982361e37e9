import React, { createContext, useEffect, useState } from 'react'
import { useQuery } from '@tanstack/react-query'
import { getApiUrlPrefix } from '@patterninc/react-ui'

import { type ObjectType } from '../../Context'
import { tryLocalStorageParse } from '../../common/services/HelperService'
import SecureAxios from '../../common/services/SecureAxios'

export const SecondaryAppContext = createContext<{
  secondaryApp: { id: string; name: string }
  appsList: { id: string; name: string }[]
  updateSecondaryApp: (app: { id: string; name: string }) => void
}>({
  secondaryApp: { id: '', name: '' },
  appsList: [],
  updateSecondaryApp: () => null,
})

export const SecondaryAppProvider = ({
  children,
}: {
  children: React.ReactNode
}) => {
  const defaultSecondaryApp = (tryLocalStorageParse(
    'secondaryApp',
  ) as ObjectType) || { id: '', name: '' }

  const [secondaryApp, setSecondaryApp] = useState<{
    id: string
    name: string
  }>(defaultSecondaryApp)

  const updateSecondaryApp = (app: { id: string; name: string }) => {
    localStorage.setItem('secondaryApp', JSON.stringify(app))
    setSecondaryApp(app)
  }

  const appApiUrl = `${getApiUrlPrefix('adminczar')}/api/v1/apps?all_apps=true`

  const { data: appsList = [], isSuccess } = useQuery({
    queryKey: [appApiUrl],
    queryFn: async ({ signal }) => {
      const { data } = await SecureAxios.get(appApiUrl, { signal })
      return data.data.map(({ id, name }: { id: string; name: string }) => ({
        id,
        name,
      }))
    },
    refetchOnWindowFocus: false,
    retry: false,
    gcTime: 1000 * 60 * 60 * 8,
  })

  useEffect(() => {
    if (isSuccess && appsList.length && secondaryApp.id === '') {
      const appToSet = appsList[0]
      localStorage.setItem('secondaryApp', JSON.stringify(appToSet))
      setSecondaryApp(appToSet)
    }
  }, [isSuccess, appsList, secondaryApp.id])

  return (
    <SecondaryAppContext.Provider
      value={{ secondaryApp, appsList, updateSecondaryApp }}
    >
      {children}
    </SecondaryAppContext.Provider>
  )
}
