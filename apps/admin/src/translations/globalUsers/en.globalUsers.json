{"activateUserBody": "This will grant access to all apps and organizations for the user. Are you sure you want to proceed?", "activateUsersBody": "This will grant access to all apps and organizations for the selected user(s). Are you sure you want to proceed?", "apps": "Apps", "appsAccessTooltip": "A list of applications that a user has access to", "deactivateUser": "Deactivate User", "deactivateUserBody": "This will revoke access to all apps and organizations for the user. Are you sure you want to continue?", "deactivateUsers": "Deactivate User(s)", "deactivateUsersBody": "This will revoke access to all apps and organizations for the selected user(s). Are you sure you want to continue?", "deleteUser": "Delete User", "deleteUserBody": "This will delete the user from  all apps and organizations. Are you sure you want to continue?", "email": "Email", "name": "Name", "noGlobalUsersFound": "No Global Users Found", "noRecordsFound": "No Records Found", "organizations": "Organizations", "orgs": "Orgs", "orgsAccessCountAcrossApps": "Orgs Access Count (Across Apps)", "orgsCountTooltip": "A count of organizations a user has access to across applications", "privilegesCountTooltip": "A count of privileges a user has across applications and organizations", "reactivateUser": "Reactivate User", "reactivateUsers": "Reactivate User(s)", "searchAppOrganizations": "Search App, Organization", "searchApps": "Search Apps", "searchGlobalUsers": "Search Global Users", "searchNameEmail": "Search Name, Email", "searchOrganizations": "Search Organizations", "selectApps": "Select Apps", "selectOrganizations": "Select Organizations", "totalPermissionsCountAcrossApps": "Total Permissions Count (Across Apps)", "totalRecords": "Total Records", "user": "User", "userActivated": "User(s) successfully activated", "userDeactivated": "User(s) successully deactivated", "userDeleted": "User successfully deleted", "viewProfile": "View Profile", "weCouldNotFindAnyGlobalUsersForSelectedCriteria": "We could not find any global users for selected criteria", "weCouldNotFindAnyRecordsUsersForSelectedCriteria": "We could not find any records users for selected criteria"}