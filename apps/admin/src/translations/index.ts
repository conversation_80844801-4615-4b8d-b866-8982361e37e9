import { type TranslationNamespace } from '../common/services/TranslationService'
import EN_COMMON from './common/en.common.json'
import EN_USERS from './users/en.users.json'
import EN_REGIONS from './regions/en.regions.json'
import EN_EVENT_HISTORY from './eventHistory/en.eventHistory.json'
import EN_ROLES from './roles/en.roles.json'
import EN_PRIVILEGES from './privileges/en.privileges.json'
import EN_SIGNUP from './signup/en.signup.json'
import EN_GLOBAL_USERS from './globalUsers/en.globalUsers.json'
import EN_ORGS from './orgs/en.orgs.json'
import EN_APPS from './apps/en.apps.json'
import EN_DASHBOARD from './dashboard/en.dashboard.json'

export const RESOURCES: Record<
  string,
  Record<TranslationNamespace, Record<string, string>>
> = {
  en: {
    common: EN_COMMON,
    eventHistory: EN_EVENT_HISTORY,
    privileges: EN_PRIVILEGES,
    regions: EN_REGIONS,
    roles: EN_ROLES,
    signup: EN_SIGNUP,
    users: EN_USERS,
    globalUsers: EN_GLOBAL_USERS,
    orgs: EN_ORGS,
    apps: EN_APPS,
    dashboard: EN_DASHBOARD,
  },
} as const
