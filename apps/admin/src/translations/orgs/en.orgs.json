{"'orgType'": "Org Type", "addApp": "Add App", "appAccess": "App Access", "appAccessTooltip": "The list of organizations displayed will be filtered to just those with access to the selected applications.", "appOrgActivationSuccess": "App-Org association activated successfully", "appOrgAssociationBody": "App-Org association for {{orgName}} created successfully.", "appOrgDeactivationBody": "This app-org association will no longer have access to {{orgName}}. Are you sure you want to continue?", "appOrgDeactivationSuccess": "App-Org association is deactivated successfully", "appOrgFeatureOrgTypeBody": "App-Org association for {{orgName}} updated successfully.", "apps": "Apps", "appsTooltip": "All applications, a given organization has access to.", "bulkActivateAppOrg": "{{count}} app-org association successfully activated", "bulkDeactivateAppOrg": "{{count}} app-org association successfully deactivated", "createdAt": "Created", "createNewOrg": "Create New Org", "createOrg": "Create Org", "deactivateAppBody": "This app will no longer have access to {{orgName}}. Are you sure you want to continue?", "deactivateAppOrg": "Deactivate App-Org Association", "editFeatureAccess": "Edit Feature Access", "editOrg": "<PERSON>g", "editOrgType": "Edit Org Type", "featureDescription": "Feature Description", "featureName": "Feature Name", "inactive": "INACTIVE", "manage": "Manage", "noAppsFound": "No Apps Found", "noAppsFoundPlaceholder": "This organization has access to all apps", "noFeatures": "No Features", "noOrgsFound": "No Orgs Found", "orgCode": "Org Code", "orgCreatedSuccessfully": "Org created successfully", "orgName": "Org Name", "orgNameExceedsCharacterLimit": "Org Name exceeds character limit.", "orgType": "Org Type", "orgTypeAccessUpdatedSuccessfully": "App-Org association's Org Type updated", "orgUpdatedSuccessfully": "Org updated successfully", "partnerCode": "Partner Code (Optional)", "partnerCodeTooltip": "A unique identifier assigned to each partner in product catalog and used across internal applications. This is created & viewable in product catalog.", "pleaseSelectAppToEnableFeaturesSelection": "Please Select an App to Enable Features Selection.", "reactivate": "Reactivate", "remove": "Remove", "searchApps": "Search Apps", "searchOrgsApps": "Search Orgs, Apps", "selectApp": "Select App", "selectApps": "Select Apps", "selectAppsPlaceholder": "Select...", "selectFeatures": "Select Features", "selectOnly": "Only", "selectOrgType": "Select Org Type", "totalActiveApps": "Total Active Apps", "totalInactiveApps": "Total Inactive Apps", "totalOrgs": "Total Orgs", "totalOrgsTooltip": "This is a count of all organizations that exist across all Pattern applications. Organizations are not double counted if they have access to multiple applications.", "typeAUniqueOrgName": "Type a unique org name.", "usersCount": "User Count", "usersCountTooltip": "User count for associated app-org with active and onboarding statuses.", "viewFeatures": "View Features", "weCouldNotFindAnyAppsForTheSelectedCriteria": "We could not find any apps for the selected criteria.", "weCouldNotFindAnyOrgsForTheSelectedCriteria": "We could not find any orgs for the selected criteria.", "youDoNotHaveAccessToEditAppOrg": "You do not have access to edit app-org association."}