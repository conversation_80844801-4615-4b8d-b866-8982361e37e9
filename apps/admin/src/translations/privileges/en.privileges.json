{"createNewPrivilege": "Create New Privilege", "deactivatePrivilege": "Deactivate Privilege", "deletePrivilege": "Delete Privilege", "editPrivilege": "Edit Privilege", "fieldExceedsCharacterLimit": "{{fieldName}} exceeds character limit.", "noPrivilegesFound": "No privileges found", "privilegeCode": "Privilege Code", "privilegeCreatedSuccessfully": "Privilege created successfully", "privilegeDescription": "Privilege Description", "privilegeName": "Privilege Name", "privilegeSuccessfullyDeleted": "Privilege successfully deleted", "privilegeSuccessfullyUpdated": "Privilege successfully updated", "privilegeUpdatedSuccessfully": "Privilege updated successfully", "reactivatePrivilege": "Reactivate Privilege", "searchPrivileges": "Search Privileges", "specialCharactersAreNotAllowed": "Special characters are not allowed.", "thisPrivilegeWillBeDeletedThisCannotBeUndone": "This privilege will be deleted. This action cannot be undone.", "thisPrivilegeWillNoLongerHaveAccessToAppAreYouSure": "This privilege will no longer be active in {{appName}}. Are you sure you want to continue?", "typeAUniquePrivilegeCode": "Type a unique privilege code", "typeAUniquePrivilegeName": "Type a unique privilege name", "weCouldNotFindAnyPrivilegesForSelectedCriteria": "We could not find any privileges for the selected criteria."}