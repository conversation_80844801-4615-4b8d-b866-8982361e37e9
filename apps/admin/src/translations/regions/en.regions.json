{"changesSavedSuccessfully": "Changes saved successfully", "createNewOrgUnit": "Create New Org Unit", "deactivateOrgUnit": "Deactivate Org Unit", "deleteOrgUnit": "Delete Org Unit", "editOrgUnit": "Edit Org Unit", "forFastestNavigationSearchForOrg": "For fastest navigation, search for the desired org unit in the search box.", "hasBeenSetAsTheNewPrimaryOrgUnit": "has been set as the new Primary Org Unit", "noOrgUnitsFound": "No Org Units Found", "orgUnitCreatedSuccessfully": "Org unit created successfully", "orgUnitName": "Org Unit Name", "orgUnitsList": "Org Units List", "orgUnitSuccessfully": "Org Unit successfully", "otherOrgUnits": "Other Org Units", "pleaseSelectANewPrimaryOrgUnit": "Please select a new Primary Org Unit to replace this one.", "primary": "Primary", "primaryOrgUnit": "Primary Org Unit", "reactivateOrgUnit": "Reactivate Org Unit", "regionNameExceedsCharacterLimit": "Region name exceeds character limit.", "searchOrgUnits": "Search Org Units", "setAnotherPrimaryOrgUnit": "Set another primary org unit", "setAsPrimary": "Set as Primary", "setPrimary": "Set Primary", "specialCharactersAreNotAllowed": "Special characters are not allowed.", "theTableBelowContainsAllOrgUnits": "The table below contains all {{type}} org units.", "thisOrgUnitWillBeDeactivated": "This Org Unit will be deactivated.", "thisOrgUnitWillBeDeleted": "This Org Unit will be deleted. This action cannot be undone.", "typeAUniqueOrgUnitName": "Type a unique org unit name.", "userWithAccess": "User With Access", "weCouldNoteFindAnyOrgUnitsForTheSelectedCriteria": "We could not find any org units for the selected criteria.", "willBeSetAsTheNewPrimaryOrgUnit": "will be set as the new Primary Org unit once you click on Save button", "youMustHaveAPrimaryOrgUnitPleaseSelect": "   You must have a Primary Org Unit. Please select a new Primary Org Unit since you have turned this one off."}