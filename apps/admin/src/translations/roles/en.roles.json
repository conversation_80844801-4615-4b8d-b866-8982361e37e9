{"#ofUsers": "# of Users", "adminAccessPrivilege": "Admin Access Privilege", "adminSystemPrivileges": "Admin System Privileges", "applicationPrivileges": "Application Privileges", "continueWithDuplicatePrivilege": "continue", "createNewRole": "Create New Role", "deactivateRole": "Deactivate Role", "deleteRole": "Delete Role", "duplicateRole": "Duplicate Role", "editRole": "Edit Role", "inactive": "Inactive", "manageAllCurrentAndFutureRoles": "Manage All Current & Future Roles", "noRolesFound": "No Roles Found", "pleaseSelectAtLeastOneAccessibleRole": "Please select at least one accessible role", "pleaseSelectAtLeastOnePrivilege": "Please select at least one Privilege", "privilegesCount": "Privileges Count", "reactivateRole": "Reactivate Role", "regionNameExceedsCharacterLimit": "Region Name exceeds character limit.", "roleCode": "Role Code", "roleCreatedSuccessfully": "Role created successfully", "roleIsDuplicate": "This role has identical privileges to {{roles}}. Are you sure you want to save?", "roleManagement": "Role Management", "roleName": "Role Name", "roleSuccessfullyDeleted": "Role successfully deleted", "roleSuccessfullyUpdated": "Role successfully updated", "roleUpdatedSuccessfully": "Role updated successfully", "searchAccessibleRoles": "Search Accessible Roles", "searchAdminSystemPrivileges": "Search Admin System Privileges", "searchApplicationPrivileges": "Search Application Privileges", "searchRoles": "Search Roles", "selectAccessibleRole": "Select Accessible Role", "selectPrivileges": "Select Privileges", "specialCharactersAreNotAllowed": "Special characters are not allowed.", "thisRoleWillBeDeletedThisCannotBeUndone": "This role will be deleted. This action cannot be undone.", "thisRoleWillNoLongerHaveAccessToAppAreYouSure": "This role will no longer have access to {{appName}}. Are you sure you want to continue?", "typeAUniqueRoleName": "Type a unique role name.", "weCouldNotFindAnyRolesForTheSelectedCriteria": "We could not find any roles for the selected criteria."}