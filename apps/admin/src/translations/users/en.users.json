{"accessToAllCurrentAndFutureOrgUnits": "Access to all current and future org units", "activationEmailCouldNotBeSentToUsers_one": "Activation email could not be sent to {{count}} user. Please try again.", "activationEmailCouldNotBeSentToUsers_other": "Activation email could not be sent to {{count}} user. Please try again.", "activationEmailSent": "Activation Email Sent", "activationEmailSentToUsers_one": "Activation email sent to {{count}} user", "activationEmailSentToUsers_other": "Activation email sent to {{count}} users", "activeUserList": "Active User List", "admin": "Admin", "adminInactive": "<PERSON><PERSON>(Inactive)", "adminUsers": "Admin Users", "bulkUpdateConfirmation_one": "You are updating {{count}} user. Are you sure you want to continue?", "bulkUpdateConfirmation_other": "You are updating {{count}} users. Are you sure you want to continue?", "changesSaved": "Changes Saved.", "copyExistingUsersToClipboard": "Copy Existing Users to Clipboard", "createdAt": "Created At", "createNewUser(s)": "Create New User(s)", "createUser(s)": "Create User(s)", "deactivatedAdminUsers": "Deactivated Admin Users", "deactivateUser": "Deactivate User", "deactivateUserBody": "This user will no longer have access to {{appName}}. Are you sure you want to continue?", "deactivateUserBody_one": "{{count}} user will no longer have access to {{appName}}. Are you sure you want to continue?", "deactivateUserBody_other": "{{count}} users will no longer have access to {{appName}}. Are you sure you want to continue?", "deleteUser": "Delete User", "deleteUserBody": "This user will be deleted and cannot have access to {{appName}} Are you sure you want to continue?", "doNotHaveSuffientPrivilege": "You do not have sufficient privileges to perform this action. Please contact your administrator.", "editOrgUnits": "Edit Org Unit(s)", "editProfile": "Edit Profile", "editRole": "Edit Role", "editRoles": "Edit Role(s)", "emailsCopiedToClipboardSuccessfully": "Emails copied to clipboard successfully!", "enterEmailsSeparatedByAComma": "Enter emails separated by a comma", "enterEmailsSeparatedByACommaOrSpace": "Enter emails separated by a comma or space", "enterYourFirstName": "Enter your first name", "enterYourLastName": "Enter your last name", "failedToCopyEmailsToClipboard": "Failed to copy emails to clipboard.", "firstNameOrLastNameCannotBeBlank": "First Name or Last Name cannot be blank.", "forFastestNavigationSearchForTheDesiredUser": "For fastest navigation, search for the desired user or email in the search box.", "ifAReactivationIsRequiredReactivateUser": "If a reactivation is required, select the corresponding user and click reactivate user", "inactiveUserList": "Inactive User List", "lastLogin": "Last Login", "more": "More...", "noMoreThanMaxEmailsCanBeAddedAtASingleTime": "No more than {{max}} emails can be added at a single time.", "noUsersFound": "No Users Found", "orgUnit(s)": "Org Unit(s)", "passwordResetBody": "This will email the user a link to reset their password. Would you like to continue?", "passwordResetBody_one": "This will email {{count}} user a link to reset their password. Would you like to continue?", "passwordResetBody_other": "This will email {{count}} users a link to reset their passwords. Would you like to continue?", "passwordResetEmailSent": "Password Reset Email <PERSON>", "passwordResetMessageCouldNotBeSentToUsers_one": "Password reset message could not be sent to {{count}} user. Please try again.", "passwordResetMessageCouldNotBeSentToUsers_other": "Password reset message could not be sent to {{count}} user. Please try again.", "passwordResetMessageSentToUsers_one": "Password reset message sent to {{count}} user", "passwordResetMessageSentToUsers_other": "Password reset message sent to {{count}} users", "pleaseEnterValidEmailId": "Please enter valid email id:", "pleaseEnterValidEmailId(s)": "Please enter valid email id(s):", "pleaseSelectAtLeastOneUser": "Please select at least one user.", "privileges": "Privileges", "reactivateUser": "Reactivate User", "reactivateUser(s)": "Reactivate User(s)", "reactivateUserBody": "This user will once again have access to {{appName}}. Are you sure you want to continue?", "reactivateUserBody_one": "{{count}} user will once again have access to {{appName}}. Are you sure you want to continue?", "reactivateUserBody_other": "{{count}} users will once again have access to {{appName}}. Are you sure you want to continue?", "resendActivationEmail": "Resend Activation Email", "resendActivationEmailBody": "This will email the user a link to activate their account. Would you like to continue?", "resendActivationEmailBody_one": "This will email {{count}} user a link to activate their account. Would you like to continue?", "resendActivationEmailBody_other": "This will email {{count}} users a link to activate their accounts. Would you like to continue?", "searchForADesiredUserOrEmailInTheSearchBox": "Search for a desired user or email in the search box.", "searchOrgUnit": "Search Org Unit", "searchPrivileges": "Search Privileges", "searchRole": "Search Role", "searchUsers": "Search Users", "selectOrgUnit(s)": "Select Org Unit(s)", "selectOrgUnits": "Select Org Units", "selectPrivileges": "Select Privileges", "selectRole": "Select Role", "selectRoles": "Select Roles", "sendPasswordReset": "Send Password Reset", "sendPasswordResetBody": "This will email the user a link to reset their password. Would you like to continue?", "specialCharactersAreNotAllowed": "Special characters are not allowed.", "theTableBelowContainsAllCurrentAndActiveUsers": "The table below contains all current and active system users.", "theTableBelowContainsAllInactivatedSystemUsers": "The table below contains all inactivated system users.", "thisActionIsNotAllowedForSSOUser": "This action is not allowed for sso user.", "totalActiveUsers": "Total Active Users", "totalDeactivatedUsers": "Total Deactivated Users", "unableToUpdateUser_one": "Unable to update {{count}} user", "unableToUpdateUser_other": "Unable to update {{count}} users", "userCouldNotBeDeactivated_one": "{{count}} user could not be deactivated. Please try again.", "userCouldNotBeDeactivated_other": "{{count}} users could not be deactivated. Please try again.", "userCouldNotBeReactivated_one": "{{count}} user could not be reactivated. Please try again.", "userCouldNotBeReactivated_other": "{{count}} users could not be reactivated. Please try again.", "userDeletedSuccessfully": "User deleted successfully", "userSuccessfullyActivated": "User successfully activated", "userSuccessfullyCreated_one": "{{count}} user successfully created", "userSuccessfullyCreated_other": "{{count}} users successfully created", "userSuccessfullyDeactivated": "User successfully deactivated", "userSuccessfullyDeactivated_one": "{{count}} user successfully deactivated", "userSuccessfullyDeactivated_other": "{{count}} users successfully deactivated", "userSuccessfullyReactivated_one": "{{count}} user successfully reactivated", "userSuccessfullyReactivated_other": "{{count}} users successfully reactivated", "userSuccessfullyUpdated_one": "{{count}} user successfully updated", "userSuccessfullyUpdated_other": "{{count}} users successfully updated", "weCouldNotFindAnyUsersForTheSelectedCriteria": "We could not find any users for the selected criteria."}