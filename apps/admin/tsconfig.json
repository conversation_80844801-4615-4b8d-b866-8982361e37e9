{"compilerOptions": {"jsx": "react-jsx", "allowJs": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "strict": true, "moduleResolution": "node", "resolveJsonModule": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true}, "extends": "../../tsconfig.base.json", "files": [], "include": ["typings/**/*", "src/**/*", "global.d.ts"], "references": [{"path": "./tsconfig.app.json"}]}