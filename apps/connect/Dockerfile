FROM public.ecr.aws/patterninc/parent-image/nodejs:20 AS build

ARG IMPORT_MAP_SUFFIX

ENV IMPORT_MAP_SUFFIX $IMPORT_MAP_SUFFIX

WORKDIR /app
COPY package.json pnpm-lock.yaml .pnpmversion ./
RUN pnpm_version=$(cat .pnpmversion) && \
    npm install -g pnpm@$pnpm_version
COPY . ./
RUN pnpm install
RUN pnpm run build

FROM nginx:alpine
# Copy the write-dotenv.sh script into the Docker image
RUN rm /etc/nginx/conf.d/default.conf
COPY --from=build /app/dist /usr/share/nginx/html
COPY nginx.conf /etc/nginx/conf.d

EXPOSE 80

CMD ["nginx", "-g", "daemon off;"]