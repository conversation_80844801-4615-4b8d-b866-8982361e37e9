[{"source": "/user-settings/<*>", "target": "https://api.pattern.com/user-settings/<*>", "status": "200", "condition": null}, {"source": "/staging-user-settings/<*>", "target": "https://stage-api.pattern.com/user-settings/<*>", "status": "200", "condition": null}, {"source": "/authorization/<*>", "target": "https://api.pattern.com/connect/auth/oauth/<*>", "status": "200", "condition": null}, {"source": "/connect-auth/<*>", "target": "https://api.pattern.com/connect/ui/auth/<*>", "status": "200", "condition": null}, {"source": "/connect-engine/<*>", "target": "https://api.pattern.com/connect/ui/job/<*>", "status": "200", "condition": null}, {"source": "/connect-syndication/<*>", "target": "https://api.pattern.com/connect/ui/syndication/<*>", "status": "200", "condition": null}, {"source": "/cms/<*>", "target": "https://stage-api.pattern.com/connect/cms/api/<*>", "status": "200", "condition": null}, {"source": "/staging-authorization/<*>", "target": "https://stage-api.pattern.com/connect/auth/oauth/<*>", "status": "200", "condition": null}, {"source": "/staging-connect-auth/<*>", "target": "https://stage-api.pattern.com/connect/ui/auth/<*>", "status": "200", "condition": null}, {"source": "/staging-connect-engine/<*>", "target": "https://stage-api.pattern.com/connect/ui/job/<*>", "status": "200", "condition": null}, {"source": "/staging-connect-syndication/<*>", "target": "https://stage-api.pattern.com/connect/ui/syndication/<*>", "status": "200", "condition": null}, {"source": "/staging-cms/<*>", "target": "https://stage-api.pattern.com/connect/cms/api/<*>", "status": "200", "condition": null}, {"source": "/demo-user-settings/<*>", "target": "https://stage-api.pattern.com/user-settings/<*>", "status": "200", "condition": null}, {"source": "/toggle/<*>", "target": "https://toggle-api.usepredict.com/<*>", "status": "200", "condition": null}, {"source": "/staging-toggle/<*>", "target": "https://stage-toggle-api.usepredict.com/<*>", "status": "200", "condition": null}, {"source": "/manifest.json", "target": "/manifest.json", "status": "200", "condition": null}, {"source": "/images/<*>", "target": "/images/<*>", "status": "200", "condition": null}, {"source": "/<*>.csv", "target": "/<*>.csv", "status": "200", "condition": null}, {"source": "</^[^.]+$|\\.(?!(css|gif|ico|jpg|js|png|txt|svg|woff|ttf|map|json)$)([^.]+$)/>", "target": "/index.html", "status": "200", "condition": null}, {"source": "/<*>", "target": "/index.html", "status": "404-200", "condition": null}]