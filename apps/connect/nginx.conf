server {
    listen 80;
    resolver *******;

    location / {
        root /usr/share/nginx/html;
        try_files $uri $uri/ /index.html;
        proxy_set_header Host $host;        
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_set_header Access-Control-Allow-Origin *;
    }
 
    # Production Proxy Settings
    location ~ ^/user-settings/(.*)$ {
        proxy_pass https://api.pattern.com/user-settings/$1$is_args$args;
    }

    # Connect auth, engine, and syndication
    location ~ ^/connect-auth/(.*)$ {
        proxy_pass https://api.pattern.com/connect/ui/auth/$1$is_args$args;
    }

    location ~ ^/connect-engine/(.*)$ {
        proxy_pass https://api.pattern.com/connect/ui/job/$1$is_args$args;
    }

    location ~ ^/connect-syndication/(.*)$ {
        proxy_pass https://api.pattern.com/connect/ui/syndication/$1$is_args$args;
    }

    location ~ ^/cms/(.*)$ {
        proxy_pass https://stage-api.pattern.com/connect/cms/api/$1$is_args$args;
    }
    
    # Toggle API
    location ~ ^/toggle/(.*)$ {
        proxy_pass https://toggle-api.usepredict.com/$1$is_args$args;
    }

    # Staging Proxy Settings
    location ~ ^/staging-connect-auth/(.*)$ {
        proxy_pass https://stage-api.pattern.com/connect/ui/auth/$1$is_args$args;
    }
    
    location ~ ^/staging-user-settings/(.*)$ {
        proxy_pass https://stage-api.pattern.com/user-settings/$1$is_args$args;
    }

    location ~ ^/staging-connect-engine/(.*)$ {
        proxy_pass https://stage-api.patternasia.com.cn/connect/ui/job/$1$is_args$args;
    } 

    location ~ ^/staging-connect-syndication/(.*)$ {
        proxy_pass https://stage-api.pattern.com/connect/ui/syndication/$1$is_args$args;
    }

    location ~ ^/staging-cms/(.*)$ {
        proxy_pass https://stage-api.pattern.com/connect/cms/api/$1$is_args$args;
    }

    location ~ ^/staging-toggle/(.*)$ {
        proxy_pass https://stage-toggle-api.usepredict.com/$1$is_args$args;
    }

    location = /manifest.json {
        root /usr/share/nginx/html;
    }

    location ~ ^/images/ {
        root /usr/share/nginx/html;
    }

    location ~* \.(csv|css|gif|ico|jpg|js|png|txt|svg|woff|ttf|map|json)$ {
        root /usr/share/nginx/html;
    }
    
}