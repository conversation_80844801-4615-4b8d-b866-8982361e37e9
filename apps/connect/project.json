{"$schema": "../../node_modules/nx/schemas/project-schema.json", "name": "connect", "projectType": "application", "sourceRoot": "apps/connect/src", "tags": ["scope:ui", "type:lib"], "targets": {"serve-static": {"executor": "@nx/web:file-server", "dependsOn": ["build"], "options": {"buildTarget": "connect:build", "spa": true}}, "get-types": {"command": "pnpm update @patterninc/react-ui"}, "update-s3-test": {"executor": "nx:run-commands", "options": {"command": "node apps/connect/update-s3.mjs"}}, "prettier": {"executor": "nx:run-commands", "options": {"command": "prettier --write 'apps/connect/src/**/*' --ignore-path apps/connect/.prettierignore || true"}}, "prettier-check": {"executor": "nx:run-commands", "options": {"command": "prettier --check 'apps/connect/src/**/*'"}}, "chromatic": {"executor": "nx:run-commands", "options": {"commands": ["nx run connect:build-storybook", ". .env && pnpm dlx chromatic --project-token=$CONNECT_CHROMATIC_TOKEN --only-changed --storybook-build-dir=apps/connect/storybook-static"], "parallel": false}}}}