import path from 'path'

import { defineConfig } from '@rsbuild/core'
import { pluginReact } from '@rsbuild/plugin-react'
import { pluginSass } from '@rsbuild/plugin-sass'
import { pluginSvgr } from '@rsbuild/plugin-svgr'
import { pluginAssetsRetry } from '@rsbuild/plugin-assets-retry'
import { pluginTypeCheck } from '@rsbuild/plugin-type-check'
const shouldKeepBrowserClosed = process.env.OPEN_BROWSER === 'false'

export default defineConfig({
  plugins: [
    pluginReact(),
    pluginSvgr({
      svgrOptions: {
        exportType: 'default',
      },
    }),
    pluginSass(),
    pluginAssetsRetry(),
    pluginTypeCheck(),
  ],

  resolve: {
    extensions: ['.tsx', '.ts', '.js', '.jsx', '.json'],
  },

  source: {
    define: {
      'process.env': JSON.stringify(process.env),
    },
  },

  output: {
    distPath: { root: path.resolve(__dirname, '../../dist/apps/connect') },
    cleanDistPath: true,
  },

  server: {
    // https: true, // uncomment this line if you want to use overrides for stage/prod
    port: 7070,
    host: 'connect.localhost',
    historyApiFallback: true,
    open: !shouldKeepBrowserClosed,
    compress: true,
    headers: { 'Access-Control-Allow-Origin': '*' },
    proxy: [
      // PRODUCTION
      {
        context: ['/user-settings'],
        target: 'https://api.pattern.com/user-settings',
        pathRewrite: { '^/user-settings': '' },
        changeOrigin: true,
        secure: false,
      },
      {
        context: ['/toggle'],
        target: 'https://toggle-api.usepredict.com',
        pathRewrite: { '^/toggle': '' },
        changeOrigin: true,
        secure: false,
      },
      {
        context: ['/authorization'],
        target: 'https://api.pattern.com/connect/auth/oauth',
        pathRewrite: { '^/authorization': '' },
        changeOrigin: true,
        secure: false,
      },
      {
        context: ['/connect-auth'],
        target: 'https://api.pattern.com/connect/ui/auth',
        pathRewrite: { '^/connect-auth': '' },
        changeOrigin: true,
        secure: false,
      },
      {
        context: ['/connect-engine'],
        target: 'https://api.pattern.com/connect/ui/job',
        pathRewrite: { '^/connect-auth': '' },
        changeOrigin: true,
        secure: false,
      },
      {
        context: ['/connect-syndication'],
        target: 'https://api.pattern.com/connect/ui/syndication',
        pathRewrite: { '^/connect-syndication': '' },
        changeOrigin: true,
        secure: false,
      },
      {
        context: ['/cms'],
        target: 'https://stage-api.pattern.com/connect/cms/api',
        pathRewrite: { '^/cms': '' },
        changeOrigin: true,
        secure: false,
      },
      // STAGING
      {
        context: ['/staging-user-settings'],
        target: 'https://stage-api.pattern.com/user-settings',
        pathRewrite: { '^/staging-user-settings': '' },
        changeOrigin: true,
        secure: false,
      },
      {
        context: ['/staging-toggle'],
        target: 'https://stage-toggle-api.usepredict.com',
        pathRewrite: { '^/staging-toggle': '' },
      },
      {
        context: ['/staging-authorization'],
        target: 'https://stage-api.pattern.com/connect/auth/oauth',
        pathRewrite: { '^/staging-authorization': '' },
        changeOrigin: true,
        secure: false,
      },
      {
        context: ['/staging-connect-auth'],
        target: 'https://stage-api.pattern.com/connect/ui/auth',
        pathRewrite: { '^/staging-connect-auth': '' },
        changeOrigin: true,
        secure: false,
      },
      {
        context: ['/staging-connect-engine'],
        target: 'https://stage-api.pattern.com/connect/ui/job',
        pathRewrite: { '^/staging-connect-engine': '' },
        changeOrigin: true,
        secure: false,
      },
      {
        context: ['/staging-connect-syndication'],
        target: 'https://stage-api.pattern.com/connect/ui/syndication',
        pathRewrite: { '^/staging-connect-syndication': '' },
        changeOrigin: true,
        secure: false,
      },
      {
        context: ['/staging-cms'],
        target: 'https://stage-api.pattern.com/connect/cms/api',
        pathRewrite: { '^/staging-cms': '' },
        changeOrigin: true,
        secure: false,
      },
    ],
  },

  tools: {
    htmlPlugin: {
      title: 'Connect',
      favicon: './favicon.ico',
    },
  },
})
