import {
  APP_LOGOS,
  DevTools,
  getEnvironmentName,
  PatternToastContainer,
  ToggleProvider,
} from '@patterninc/react-ui'
import React, { useState } from 'react'

import ScreenLoader from './common/components/ScreenLoader/ScreenLoader'
import './common/scss/main.scss'
import { useUser } from './context/user-context'
import './common/services/datadog'
import { OrgProvider } from './context/org-context'

const AuthenticatedApp = React.lazy(
  () => import(/* webpackChunkName: "authenticatedApp" */ './AuthenticatedApp'),
)
const UnauthenticatedApp = React.lazy(
  () =>
    import(/* webpackChunkName: "unauthenticatedApp" */ './UnauthenticatedApp'),
)

const CONNECT_DISTRIBUTION_KEY = 'e70eaa21-eb57-45c6-976f-d164c8a215db'

export function getCurrentEnv(): 'development' | 'staging' | 'production' {
  const environmentName = getEnvironmentName()
  if (environmentName === 'demo' || environmentName === 'stage') {
    return 'staging'
  }
  return environmentName
}

export default function App(): React.JSX.Element {
  const user = useUser(),
    [, setRerender] = useState({}),
    [areTogglesLoaded, setAreTogglesLoaded] = useState(false),
    loader = <ScreenLoader logo={APP_LOGOS.CONNECT.logo} />

  return (
    <ToggleProvider
      distributionKey={CONNECT_DISTRIBUTION_KEY}
      environment={getCurrentEnv()}
      finishedLoadingCallback={setAreTogglesLoaded}
    >
      {!areTogglesLoaded ? (
        loader
      ) : (
        <React.Suspense fallback={loader}>
          {user ? (
            <OrgProvider>
              <AuthenticatedApp />
            </OrgProvider>
          ) : (
            <UnauthenticatedApp />
          )}
        </React.Suspense>
      )}
      <DevTools
        backendNames={['connect-auth', 'connect-engine', 'authorization']}
        rerenderCallout={setRerender}
      />
      <PatternToastContainer />
    </ToggleProvider>
  )
}
