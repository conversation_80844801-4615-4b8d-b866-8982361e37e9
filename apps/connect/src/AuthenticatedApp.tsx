import React, { useEffect } from 'react'
import { <PERSON>rowserRouter } from 'react-router-dom'
import { getEnvironmentName, TranslationTools } from '@patterninc/react-ui'

import { ThemeProvider } from './Context'
import Main from './common/components/Main'
import ScrollToTop from './common/ScrollToTop'
import { useOrg } from './context/org-context'
import { RESOURCES } from './translations'

function AuthenticatedApp(): React.JSX.Element {
  const { setupOrg } = useOrg()

  useEffect(() => {
    setupOrg()
  }, [setupOrg])

  if (
    ['development', 'stage'].includes(getEnvironmentName()) &&
    localStorage.getItem('pattern-devtools') === 'true' &&
    localStorage.getItem('translations-check') === 'true'
  ) {
    return <TranslationTools resources={RESOURCES} languagesToCheck={['es']} />
  }

  return (
    <BrowserRouter
      future={{
        v7_relativeSplatPath: true,
        v7_startTransition: true,
      }}
    >
      <ThemeProvider>
        <ScrollToTop>
          <Main />
        </ScrollToTop>
      </ThemeProvider>
    </BrowserRouter>
  )
}

export default AuthenticatedApp
