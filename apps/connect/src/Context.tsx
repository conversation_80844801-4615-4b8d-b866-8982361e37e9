import React, { createContext, useCallback, useMemo, useState } from 'react'
import {
  addNewBreadcrumb,
  breadcrumbNavigation,
  type BreadcrumbType,
} from '@patterninc/react-ui'

type ThemeContextType = {
  breadcrumbs: Array<BreadcrumbType>
  updateBreadcrumbs: (breadcrumb: BreadcrumbType) => void
  breadcrumbCallout: (breadcrumb: BreadcrumbType) => void
}

export const ThemeContext = createContext<ThemeContextType>({
  breadcrumbs: [
    {
      name: '',
      link: '',
    },
  ],
  updateBreadcrumbs: () => null,
  breadcrumbCallout: () => null,
})
const { Provider } = ThemeContext

export const ThemeConsumer = ThemeContext.Consumer

type ThemeProviderProps = {
  children: React.JSX.Element
}

const pageNameMap = {
  '/jobs': 'Jobs',
  '/connections': 'Connections',
  '/apitraffic': 'API Traffic',
  '/mappings': 'Mappings',
  '/support': 'Support',
}

const ThemeProvider = ({ children }: ThemeProviderProps): React.JSX.Element => {
  const defaultStartingBreadcrumbs: BreadcrumbType[] = useMemo(() => {
    /**
     * We're using window.location.href because useLocation can only be used in a router context.
     * The regex here finds the first part of what would be the pathName from useLocation (up to
     * but not including the second slash). Given 'https://connect.pattern.com/jobs/some-job-id',
     * the regex match will just return '/jobs'.
     */
    const basePath =
      window.location.href.match(/(?<!\/)\/(?!\/)[^/]*/)?.[0] || ''
    return [
      {
        name: pageNameMap[basePath as keyof typeof pageNameMap],
        link: basePath,
        changeType: 'rootLevel',
      },
    ]
  }, [])
  if (sessionStorage.getItem('breadcrumbs') === null) {
    sessionStorage.setItem(
      'breadcrumbs',
      JSON.stringify(defaultStartingBreadcrumbs),
    )
  }

  const [breadcrumbs, setBreadcrumbs] = useState<BreadcrumbType[]>(
    JSON.parse(
      sessionStorage.getItem('breadcrumbs') ??
        JSON.stringify(defaultStartingBreadcrumbs),
    ),
  )

  const updateBreadcrumbs = useCallback((breadcrumb: BreadcrumbType) => {
    setBreadcrumbs((prevState) =>
      addNewBreadcrumb({
        breadcrumb,
        breadcrumbs: prevState,
      }),
    )
  }, [])

  const breadcrumbCallout = (breadcrumb: BreadcrumbType) => {
    setBreadcrumbs((prevState) =>
      breadcrumbNavigation({
        breadcrumb,
        breadcrumbs: prevState,
      }),
    )
  }

  return (
    <Provider
      value={{
        breadcrumbs,
        updateBreadcrumbs,
        breadcrumbCallout,
      }}
    >
      {children}
    </Provider>
  )
}

export { ThemeProvider }
