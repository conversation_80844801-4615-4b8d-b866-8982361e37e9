import React, { useEffect, useMemo } from 'react'
import { APP_LOGOS } from '@patterninc/react-ui'
import { <PERSON><PERSON>erRouter } from 'react-router-dom'

import ScreenLoader from './common/components/ScreenLoader/ScreenLoader'
import { useAuth } from './context/auth-context'
import PreSalesOnboardingRoutes from './modules/PreSalesOnboarding/PreSalesOnboardingRoutes'

const REDIRECT_URL_KEY = 'redirect_url'

const UnauthenticatedApp = () => {
  const { login, handleAuthentication, setUser, isAuthenticated, getUser } =
    useAuth()
  const pathname = window.location.pathname,
    hash = window.location.hash
  const isPreSalesOnboarding = useMemo(() => {
    return pathname.includes('onboarding')
  }, [pathname])

  useEffect(() => {
    if (
      pathname === '/authenticate' &&
      /access_token|id_token|error/.test(hash)
    ) {
      if (isAuthenticated()) {
        const redirectUrl = localStorage.getItem(REDIRECT_URL_KEY)
          ? localStorage.getItem(REDIRECT_URL_KEY)
          : ''
        window.location.replace(`${window.location.origin}${redirectUrl}`)
      } else {
        handleAuthentication(() => setUser(getUser))
      }
    } else if (!isPreSalesOnboarding) {
      if (isAuthenticated()) {
        setUser(getUser())
      } else {
        login()
      }
    }
  }, [
    pathname,
    hash,
    handleAuthentication,
    setUser,
    getUser,
    isAuthenticated,
    login,
    isPreSalesOnboarding,
  ])

  return (
    <div className='unauthenticated-screen'>
      {isPreSalesOnboarding ? (
        <BrowserRouter
          future={{
            v7_relativeSplatPath: true,
            v7_startTransition: true,
          }}
        >
          <PreSalesOnboardingRoutes />
        </BrowserRouter>
      ) : (
        <ScreenLoader logo={APP_LOGOS.CONNECT.logo} />
      )}
    </div>
  )
}

export default UnauthenticatedApp
