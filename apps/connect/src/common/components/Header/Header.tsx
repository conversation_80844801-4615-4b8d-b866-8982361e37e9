import React, { useContext } from 'react'
import {
  Breadcrumbs,
  envColorMap,
  envName,
  Tag,
  useIsMobileView,
} from '@patterninc/react-ui'
import { useLocation, useNavigate } from 'react-router-dom'

import styles from './_header.module.scss'
import { ThemeContext } from '../../../Context'
import { getCurrentEnv } from '../../../App'
import OrgPicker from '../OrgPicker/OrgPicker'

const Header = (): React.JSX.Element => {
  const { breadcrumbs, breadcrumbCallout } = useContext(ThemeContext),
    navigate = useNavigate(),
    screenIsMobile = useIsMobileView(),
    environment = getCurrentEnv(),
    location = useLocation()

  return (
    // .header class comes from react-ui and needed to style the layout of the app
    <div className={`header ${styles.headerContainer}`}>
      <Breadcrumbs
        breadcrumbs={breadcrumbs}
        callout={(breadcrumb) => {
          navigate(breadcrumb.link)
          breadcrumbCallout(breadcrumb)
        }}
      />
      {!location.pathname.includes('/mappings') && <OrgPicker />}
      {screenIsMobile && environment !== 'production' ? (
        // The 2 different color options for Connect should be red (Staging) and yellow (Development)
        <Tag color={envColorMap[environment] as 'red' | 'yellow'}>
          {envName[environment]}
        </Tag>
      ) : null}
    </div>
  )
}

export default Header
