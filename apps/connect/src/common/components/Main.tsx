import React, { useRef } from 'react'

import AppRoutes from '../../modules/AppRoutes'
import Header from './Header/Header'
import SidebarMenu from './Sidebar/Sidebar'

const Main = (): React.JSX.Element => {
  const appWindowRef = useRef(null)

  return (
    <div className='App relative'>
      <SidebarMenu />
      <div
        className='app-content-layout'
        id='app-content-layout'
        ref={appWindowRef}
      >
        <Header />
        <div className='App-content'>
          <div className='page-container'>
            <AppRoutes />
          </div>
        </div>
      </div>
    </div>
  )
}

export default Main
