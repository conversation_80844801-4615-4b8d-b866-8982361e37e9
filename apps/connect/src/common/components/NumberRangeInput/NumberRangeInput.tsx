import React from 'react'
import { FormLabel, TextInput } from '@patterninc/react-ui'

type inputProps = {
  filterStates: {
    labelText: string
    minimum: rangeInputType
    maximum: rangeInputType
  }
  onChangeCallout?: (...params: unknown[]) => void
}

type rangeInputType = {
  defaultValue?: number | string
  placeholder?: string
  stateName?: string
  onlyPositiveNumbers?: boolean
  fullWidth?: boolean
}

const NumberRangeInput = ({
  filterStates,
  onChangeCallout,
}: inputProps): React.JSX.Element => {
  return (
    <div>
      <FormLabel label={filterStates?.labelText} />
      <div className='flex flex-column pat-gap-2'>
        <TextInput
          callout={(stateName, value) => onChangeCallout?.(stateName, value)}
          placeholder={filterStates.minimum?.placeholder}
          stateName={filterStates.minimum?.stateName}
          type='number'
          value={filterStates.minimum?.defaultValue || ''}
          onlyPositiveNumbers={filterStates.minimum?.onlyPositiveNumbers}
          fullWidth={filterStates.minimum?.fullWidth}
        />
        <TextInput
          callout={(stateName, value) => onChangeCallout?.(stateName, value)}
          placeholder={filterStates.maximum?.placeholder}
          stateName={filterStates.maximum?.stateName}
          type='number'
          value={filterStates.maximum?.defaultValue || ''}
          onlyPositiveNumbers={filterStates.maximum?.onlyPositiveNumbers}
          fullWidth={filterStates.maximum?.fullWidth}
        />
      </div>
    </div>
  )
}

export default NumberRangeInput
