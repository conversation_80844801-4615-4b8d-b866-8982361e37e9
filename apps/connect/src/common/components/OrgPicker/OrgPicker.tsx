import React, {
  useCallback,
  useContext,
  useEffect,
  useMemo,
  useRef,
} from 'react'
import { DrawerSelect, toast } from '@patterninc/react-ui'
import { useLocation, useNavigate, useSearchParams } from 'react-router-dom'

import { useOrg } from '../../../context/org-context'
import sortOptions from '../../services/SortOptions'
import { OrgContext } from '../../../context/org-context'

const OrgPicker = (): React.JSX.Element => {
  const { org, setOrg, userOrgs: orgs } = useOrg(),
    { isOrgSwitched, setIsOrgSwitched } = useContext(OrgContext),
    [searchParams, setSearchParams] = useSearchParams(),
    toastShownRef = useRef(false),
    navigate = useNavigate(),
    { pathname } = useLocation(),
    orgOptions = useMemo((): { code: string; name: string }[] => {
      return sortOptions<{ name: string; code: string }>(orgs, 'name')
    }, [orgs]),
    handleOrgSelected = useCallback(
      (orgOption: { code: string; name: string }) => {
        setSearchParams('')
        setIsOrgSwitched(!isOrgSwitched)
        if (org.code === orgOption.code) return

        setOrg(orgOption)
        if (pathname.includes('connections/')) {
          navigate('/connections')
        } else if (pathname.includes('jobs/')) {
          navigate('/jobs')
        } else if (pathname.includes('destinations/')) {
          navigate('/destinations')
        }
      },
      [
        setSearchParams,
        setIsOrgSwitched,
        isOrgSwitched,
        org,
        setOrg,
        pathname,
        navigate,
      ],
    )

  useEffect(() => {
    const client_id = searchParams.get('client_id')?.toLowerCase()
    if (client_id) {
      const orgExists = orgs.some(
        (org: { code: string; name: string }) => org.code === client_id,
      )
      if (orgExists) {
        setOrg({
          code: client_id,
          name: client_id.toUpperCase(),
        })
      } else if (!toastShownRef.current) {
        toastShownRef.current = true
        toast({
          type: 'error',
          message: `Error: Access to organization ${client_id} is denied.`,
        })
        setSearchParams('')
      }
    }
  }, [orgs, searchParams, setOrg, setSearchParams])

  return (
    <DrawerSelect
      header='Organizations'
      options={orgOptions.map((org) => ({
        ...org,
        name: org.code,
        label: (org.name || org.code) ?? '',
      }))}
      setActive={handleOrgSelected}
      active={org}
      placeholder={org?.name || org?.code}
      disabled={orgOptions.length === 1}
    />
  )
}

export default OrgPicker
