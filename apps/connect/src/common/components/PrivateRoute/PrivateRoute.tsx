import React from 'react'
import { Navigate, Route, useLocation } from 'react-router-dom'

import { routePermissions } from '../../../Auth/route-permissions'
import { useUser } from '../../../context/user-context'
import { haveRoles } from '../../services/HelperService'
interface PrivateRouteProps {
  /** Component to render */
  children: React.JSX.Element
  /** Auth roles to check */
  authRoles?: string[]
  /** Route to redirect to if auth checks fail */
  redirectTo?: string
}

const PrivateRoute = ({
  authRoles = [],
  redirectTo = '/',
  children,
}: PrivateRouteProps): React.JSX.Element => {
  const user = useUser(),
    { pathname } = useLocation()

  const basePath = pathname.split('/')[1],
    baseRouteRequiredRoles = routePermissions[basePath] ?? [],
    passedRequiredRoles = authRoles,
    noRolesRequired =
      baseRouteRequiredRoles.length === 0 && passedRequiredRoles.length === 0,
    userHasRequiredRoles =
      noRolesRequired ||
      // If roles are required, check that the user has the base route permissions and the passed, required permissions
      (haveRoles(user, baseRouteRequiredRoles) &&
        haveRoles(user, passedRequiredRoles))

  return userHasRequiredRoles ? (
    children
  ) : (
    <Route element={<Navigate to={redirectTo} />} />
  )
}

export default PrivateRoute
