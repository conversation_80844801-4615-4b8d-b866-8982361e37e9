import React, { useContext, useMemo } from 'react'
import { APP_LOGOS, LeftNav } from '@patterninc/react-ui'
import { Link, useNavigate } from 'react-router-dom'

import { sidebarContent } from './sidebar-content'
import { ThemeContext } from '../../../Context'
import { useUser } from '../../../context/user-context'
import { useAuth, useHasPatternAdminRole } from '../../../context/auth-context'

const SidebarMenu = (): React.JSX.Element => {
  const { breadcrumbs } = useContext(ThemeContext),
    navigate = useNavigate(),
    user = useUser(),
    { logout } = useAuth(),
    roles = useMemo(() => {
      return user.orgs.map(
        (org: { code: string; role: string }) => `${org.code}_${org.role}`,
      )
    }, [user]),
    isPatternAdmin = useHasPatternAdminRole()

  return (
    <LeftNav
      leftNavLinks={sidebarContent(isPatternAdmin)}
      breadcrumbs={breadcrumbs}
      logo={{
        url: APP_LOGOS.CONNECT.logo,
        abbreviatedUrl: APP_LOGOS.CONNECT.abbr,
        isolatedUrl: APP_LOGOS.CONNECT.isolated,
      }}
      navigate={() => navigate('/')}
      userPermissions={roles}
      routerComponent={Link}
      routerProp='to'
      accountPopoverProps={{
        name: user.nickname ?? user.name,
        options: [
          {
            icon: 'logout',
            label: 'Logout',
            callout: logout,
          },
        ],
      }}
    />
  )
}

export default SidebarMenu
