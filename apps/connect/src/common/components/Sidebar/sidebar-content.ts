import { type LeftNavLinkObj } from '@patterninc/react-ui'

export const sidebarContent = (isPatternAdmin: boolean): LeftNavLinkObj[] => {
  const items: LeftNavLinkObj[] = [
    {
      name: 'Connections',
      link: '/connections',
      icon: 'sellers',
      // permissions: [] - This is not needed for now since there are no permissions, but keeping it here for future reference. If permissions are added, this will need to be an array of strings from the user "roles".
    },
    {
      name: 'Jobs',
      link: '/jobs',
      icon: 'clipboard',
    },
    {
      name: 'Mappings',
      link: '/mappings',
      icon: 'shuffle',
    },
    {
      name: 'API Traffic',
      link: '/apitraffic',
      icon: 'barChart',
    },
  ]

  if (isPatternAdmin) {
    items.push({
      name: 'Destinations',
      link: '/destinations',
      icon: 'bag',
    })
  }

  items.push({
    name: 'Support',
    link: '/support',
    icon: 'info',
    footerLink: true,
  })

  return items
}
