import React from 'react'
import { Alert, ListLoading, MultiSelect } from '@patterninc/react-ui'

import { type SlackChannel } from '../../../modules/Connections/types'
import { useConnectionDetails } from '../../../context/connection-details'

type SlackChannelSelectorProps = {
  onChange: (selected: SlackChannel[]) => void
  selectedChannels: SlackChannel[]
}

const SlackChannelSelector = ({
  onChange,
  selectedChannels,
}: SlackChannelSelectorProps): React.JSX.Element => {
  // Use slack channels from context
  const { slackChannels, isSlackConnected, isSlackLoading } =
    useConnectionDetails()

  return isSlackConnected ? (
    <>
      {isSlackLoading ? (
        <ListLoading />
      ) : (
        <MultiSelect
          formLabelProps={{ label: 'Select Slack Channels' }}
          options={slackChannels}
          selectedOptions={selectedChannels}
          selectPlaceholder='-- Select Channels --'
          callout={(selectedChannels) => {
            const selectedNames = selectedChannels.map((c) => c.name)
            onChange(
              slackChannels.filter(
                (channel) =>
                  selectedNames.findIndex((name) => name === channel.name) !==
                  -1,
              ),
            )
          }}
          labelKey={'name'}
        />
      )}
    </>
  ) : (
    <Alert text='Not connected to Slack' type='info' />
  )
}

export default SlackChannelSelector
