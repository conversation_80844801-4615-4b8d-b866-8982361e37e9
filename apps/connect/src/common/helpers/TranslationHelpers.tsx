import {
  newTranslationInstance,
  tr,
  useTranslation,
} from '@patterninc/react-ui'

import { RESOURCES } from '../../translations'

export const LOCAL_STORAGE_LANGUAGE_SETTING_KEY = 'language_setting'

/** i18n instance for the whole Connect app */
export const i18nConnectInstance = newTranslationInstance(RESOURCES)

export const initConnectI18nInstance = () => {
  i18nConnectInstance.init()
  changeLanguageTo(getLocalStorageLanguageTag())
}

export const changeLanguageTo = (languageTag: string) => {
  const isDebugMode = localStorage.getItem('debug-translations') === 'true'
  i18nConnectInstance.changeLanguage(isDebugMode ? 'cimode' : languageTag)
}

export const NS = {
  apiTraffic: 'apiTraffic',
  common: 'common',
  connections: 'connections',
  destinations: 'destinations',
  jobs: 'jobs',
  mappings: 'mappings',
  support: 'support',
  preSalesOnboarding: 'preSalesOnboarding',
} as const

export type TranslationNamespace = keyof typeof NS
export type TranslationKey = `${TranslationNamespace}:${string}`

/**
 * Translation helper function. Alternative to the useTranslate hook in places where hooks aren't easily available (outside of components)
 * @param key - namespace:key of the translation (in the format 'namespace:key')
 * @param values - (optional) values to be interpolated into the translated string
 * @returns translated string
 **/
export const t = (
  namespaceAndKey: TranslationKey,
  values?: Record<string, unknown>,
) => tr(namespaceAndKey, i18nConnectInstance, values)

/**
 * Translation helper function, shorthand for using the "common" namespace `t('common:key')`
 * @param key - key of the translation
 * @param values - (optional) values to be interpolated into the translated string
 * @returns translated string
 **/
export const c = (key: string, values?: Record<string, unknown>) =>
  t(`common:${key}`, values)

export const getLocalStorageLanguageTag = () => {
  return localStorage.getItem(LOCAL_STORAGE_LANGUAGE_SETTING_KEY) ?? 'en'
}

export const useTranslate = (
  namespace: TranslationNamespace,
): ReturnType<typeof useTranslation> => useTranslation(namespace)
