import moment from 'moment'

export const filterEndDate = (requested: moment.Moment): moment.Moment => {
  // Account for the fact that datepicker spits out noon instead of the start of the day by default
  const requestedDateAdjusted = requested.clone().subtract(12, 'hours')
  const startOfRequestedDay = moment(requested.format()).startOf('day')
  const startOfToday = moment().startOf('day')
  if (
    requestedDateAdjusted.isSame(startOfRequestedDay) &&
    requestedDateAdjusted.isSame(startOfToday)
  ) {
    return moment()
  }
  if (requestedDateAdjusted.isSame(startOfRequestedDay)) {
    return moment(requested.format('YYYY-MM-DD')).startOf('day').add('1', 'day')
  }
  return requested
}
