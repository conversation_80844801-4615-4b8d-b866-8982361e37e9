import { notEmpty, toast } from '@patterninc/react-ui'

const missingPermissionToast = (
  permission: string,
  organization: { code: string; name: string },
): void => {
  if (notEmpty(organization)) {
    toast({
      type: 'error',
      message: `You do not have the '${permission}' permission for the '${organization.name}' organization`,
      config: {
        autoClose: 10000,
        toastId: `${organization}-${permission}`,
      },
    })
  }
}

export { missingPermissionToast }
