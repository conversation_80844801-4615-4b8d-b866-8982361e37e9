import { useQuery } from '@tanstack/react-query'
import { useMemo } from 'react'

import { marketplaceConnectionApi } from '../services/ConnectAuthService'
import { type MarketConnectionType } from '../../modules/ApiTraffic/types'
import SecureAxios from '../services/SecureAxios'
import { useOrg } from '../../context/org-context'
import { type OptionType } from '../../common/types'
import SortOptions from '../services/SortOptions'

/**
 * retrieves a list of all connections associated with the given marketplaces
 * @param marketplaceNames a list of marketplace names to include connections for (name, not display name)
 * @returns an alphabetically sorted list of all account options for given marketplaces
 */
const useFilteredConnections = (marketplaceNames: string[]): OptionType[] => {
  const { org } = useOrg(),
    parseMarketplace = (url: string) => {
      return url.split('/').slice(-1)[0]
    },
    { data: marketConnectionData } = useQuery<MarketConnectionType[]>({
      queryKey: [marketplaceConnectionApi, org],
      queryFn: async () => {
        const result = await SecureAxios.get<Record<string, string>>(
          marketplaceConnectionApi,
          { params: { organization_code: org.code } },
        )
        return Object.entries(result.data).map((entry) => ({
          connection: entry[0],
          marketplace: parseMarketplace(entry[1]),
          endpoint: entry[1],
        }))
      },
    }),
    formatOptions = (arr: string[]) => {
      const distinct = arr.filter((c, index) => {
        return arr.indexOf(c) === index
      })
      return distinct.map((row, index) => {
        return {
          id: index,
          text: row,
          value: row,
        }
      })
    },
    connectionOptions = useMemo(() => {
      const filterData = marketConnectionData || []
      return formatOptions(
        filterData
          .filter((connection) => {
            return (
              marketplaceNames.length === 0 ||
              marketplaceNames.includes(connection.marketplace)
            )
          })
          .map((row) => {
            return row.connection
          }),
      )
    }, [marketConnectionData, marketplaceNames])
  return SortOptions(connectionOptions, 'text')
}

export default useFilteredConnections
