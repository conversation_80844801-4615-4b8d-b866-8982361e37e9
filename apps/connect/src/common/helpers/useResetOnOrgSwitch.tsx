import { useContext, useEffect, useRef } from 'react'

import { OrgContext } from '../../context/org-context'

const useResetOnOrgSwitch = (
  setSearchQuery?: (query: string) => void,
  resetCallout?: () => void,
) => {
  const { isOrgSwitched } = useContext(OrgContext)
  const prevIsOrgSwitched = useRef(isOrgSwitched)

  useEffect(() => {
    if (prevIsOrgSwitched.current !== isOrgSwitched) {
      if (setSearchQuery) {
        setSearchQuery('')
      }
      if (resetCallout) {
        resetCallout()
      }
      prevIsOrgSwitched.current = isOrgSwitched
    }
  }, [isOrgSwitched, setSearchQuery, resetCallout])
}

export default useResetOnOrgSwitch
