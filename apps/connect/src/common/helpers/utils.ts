export const isValidJson = (str: string): boolean => {
  try {
    JSON.parse(str)
    return true
  } catch {
    return false
  }
}

export const copyToClipboard = async (text: string) => {
  try {
    await navigator.clipboard.writeText(text)
  } catch (e) {
    console.error('Failed to copy Job ID to clipboard:', e)
  }
}

export const convertToTitleCase = (str: string): string => {
  if (!str) {
    return ''
  }
  return str
    .toLowerCase()
    .replace(/_/g, ' ')
    .replace(/\b\w/g, (s) => s.toUpperCase())
}
