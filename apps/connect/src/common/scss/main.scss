@use 'base/module' as baseModule;
@use '../components/module' as componentsModule;
@use '@patterninc/react-ui/dist/variables' as variables;

.has-info-pane {
  width: calc(100vw - 108px);
  display: grid;
  grid-gap: 16px;
  grid-template-rows: repeat(2, auto);
  @media only screen and (min-width: variables.$breakpoint-md) {
    grid-template-rows: 1fr;
    grid-template-columns: 300px calc(100% - 300px - 8px); // 300px for sideBar and 8px for grid-gap
  }
}
