import { getCurrentEnv } from '../../App'

const BaseUrl =
    getCurrentEnv() === 'production'
      ? 'https://adminczar.usepredict.com/api/v1'
      : 'https://stage-adminczar.usepredict.com/api/v1',
  UserinfoApi = `${BaseUrl}/userinfo`,
  AppsApi = `${BaseUrl}/apps`,
  AppsOrgsApi = (appId: string) => `${BaseUrl}/apps/${appId}/orgs`,
  WG_APP_ID =
    getCurrentEnv() === 'production'
      ? 'coming_soon'
      : '2c125996-95b0-4b25-aabf-f9d07de697f4'

export { UserinfoApi, AppsApi, AppsOrgsApi, WG_APP_ID }
