import { getApiUrlPrefix } from '@patterninc/react-ui'
import axios from 'axios'
import qs from 'qs'
import { cacheAdapterEnhancer, throttleAdapterEnhancer } from 'axios-extensions'

import { type supportCategory } from '../../modules/Support/types'

const supportPages = `${getApiUrlPrefix('cms')}/support-pages`,
  supportPageApi = (id: string): string => {
    return `${supportPages}/${id}`
  },
  supportCategoryParams = (
    category: supportCategory,
  ): Record<string, string> => {
    return {
      'filters[Type][$eq]': category,
    }
  },
  populateParams = {
    populate: {
      Content: {
        populate: 'image',
      },
    },
  },
  readToken =
    '9d672ba89b807412d65c70fabd09ad22d4667942fc24b610737ac0b3857ddcbea82688ccaa1017ce9d70125a8b9c7cf07192335e4d78dfe1fb43f6cffe4bb9edd715f031d1c3efc5cc097f94d900fcbe68b356db928f3cf40739c7cd6b81e92b6c084f11c177b2077cbb8a0974842d530585cc68a35122a06f4381b85a6341b2',
  StrapiAxios = axios.create({
    baseURL: '/',
    headers: {
      'Cache-Control': 'no-cache',
    },
    adapter: axios.defaults.adapter
      ? throttleAdapterEnhancer(
          cacheAdapterEnhancer(axios.defaults.adapter, {
            enabledByDefault: false,
          }),
          { threshold: 1000 },
        )
      : undefined,
    paramsSerializer: function (params) {
      return qs.stringify(params, {
        arrayFormat: 'brackets',
        encodeValuesOnly: true,
      })
    },
  })

StrapiAxios.interceptors.request.use((config) => {
  config.headers = { ...config.headers, Authorization: `Bearer ${readToken}` }
  return config
})

export {
  supportPages,
  supportPageApi,
  supportCategoryParams,
  populateParams,
  StrapiAxios,
}
