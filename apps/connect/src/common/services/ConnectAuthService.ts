import { getApiUrlPrefix, getEnvironmentName } from '@patterninc/react-ui'

const connectAuthBaseUrl = `${getApiUrlPrefix('connect-auth')}/v1`,
  slackCallbackUri = `https://${
    getEnvironmentName() === 'production' ? '' : 'stage-'
  }api.pattern.com/connect/auth/oauth/callback/slack`,
  authorizeByNonce = `${getApiUrlPrefix('authorization')}/callback/by_nonce`,
  connections = `${connectAuthBaseUrl}/credentials`,
  supportIdSuggestion = `${connections}/unique_credential_name`,
  connectionDetails = (id: string): string => {
    return `${connections}/${id}`
  },
  connectionHistories = (id: string): string => {
    return `${connectionDetails(id)}/histories`
  },
  connectionSlackChannels = (id: string): string => {
    return `${connectionDetails(id)}/slack_channels`
  },
  connectionAccess = (id: string): string => {
    return `${connectionDetails(id)}/access`
  },
  marketplacesApi = `${connectAuthBaseUrl}/marketplaces`,
  destinationTypesApi = `${marketplacesApi}/destination_types`,
  destinationDetails = (id: string): string => {
    return `${marketplacesApi}/${id}`
  },
  updateLocationKey = (key: string): string => {
    return `${connectAuthBaseUrl}/credential_locations/${key}/update_key`
  },
  destinationSettings = (id: string, setting_id?: string): string => {
    return `${destinationDetails(id)}/settings${setting_id ? `/${setting_id}` : ''}`
  },
  marketplace = (name: string): string => {
    return `${marketplacesApi}/${name}`
  },
  credentialDefinition = (marketplace_name: string): string => {
    return `${marketplace(marketplace_name)}/credential_definition`
  },
  marketplaceSupportedCountries = (marketplace_name: string): string => {
    return `${marketplace(marketplace_name)}/supported_countries`
  },
  credentialSettings = (id: string): string => {
    return `${connectAuthBaseUrl}/credential_locations/${id}`
  },
  requiredSettings = (marketplace_name: string): string => {
    return `${marketplace(marketplace_name)}/required_settings`
  },
  // this is the old endpoint, will be removed with feature toggle `three_tiered_settings`
  connectionSettings = (key: string, setting_name?: string): string => {
    return `${connections}/${key}/settings${setting_name ? `/${setting_name}` : ''}`
  },
  connectionSettingsNew = (key: string, setting_name?: string): string => {
    return `${connections}/${key}/setting${setting_name ? `/${setting_name}` : ''}`
  },
  locationSettings = (key: string, setting_name?: string): string => {
    return `${credentialSettings(key)}/setting${setting_name ? `/${setting_name}` : ''}`
  },
  credentialStates = `${connections}/credential_states`,
  usersApi = `${connectAuthBaseUrl}/users`,
  regionsApi = `${marketplacesApi}/regions`,
  countriesApi = `${marketplacesApi}/countries`,
  categoriesApi = `${marketplacesApi}/categories`,
  marketplaceConnectionApi = `${connectAuthBaseUrl}/account_map`,
  settings = `${connectAuthBaseUrl}/settings`,
  channels = `${connectAuthBaseUrl}/channels`,
  organizations = `${connectAuthBaseUrl}/organizations`

export {
  connections,
  supportIdSuggestion,
  slackCallbackUri,
  authorizeByNonce,
  connectionDetails,
  connectionSlackChannels,
  connectionHistories,
  connectionAccess,
  marketplacesApi,
  destinationTypesApi,
  marketplace,
  credentialDefinition,
  marketplaceSupportedCountries,
  credentialSettings,
  requiredSettings,
  usersApi,
  regionsApi,
  countriesApi,
  categoriesApi,
  marketplaceConnectionApi,
  connectionSettings,
  connectionSettingsNew,
  locationSettings,
  destinationDetails,
  updateLocationKey,
  destinationSettings,
  credentialStates,
  settings,
  channels,
  organizations,
}
