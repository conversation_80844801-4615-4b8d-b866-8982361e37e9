import { getApiUrlPrefix } from '@patterninc/react-ui'

const connectEngineBaseUrl = `${getApiUrlPrefix('connect-engine')}/v1`,
  jobs_url = `${connectEngineBaseUrl}/job`,
  job_types = `${jobs_url}/job_types`,
  jobDetails = (id: string): string => {
    return `${jobs_url}/${id}`
  },
  resendCallback = (id: string): string => {
    return `${jobDetails(id)}/resend_callback`
  },
  rerunJob = (id: string): string => {
    return `${jobDetails(id)}/rerun`
  },
  subjobs = (id: string): string => {
    return `${jobDetails(id)}/sub_jobs`
  },
  jobSummary = `${jobs_url}/summary`,
  jobChart = `${jobs_url}/chart`,
  jobBreakdown = `${jobs_url}/breakdown`,
  createJob = (connection: string): string => {
    return `${connectEngineBaseUrl}/accounts/${connection}/job`
  },
  getFullBody = (
    bodyType: 'full_request_body' | 'full_response_body',
    id: string,
  ): string => {
    return `${jobDetails(id)}/${bodyType}`
  }

export {
  jobs_url,
  jobDetails,
  resendCallback,
  rerunJob,
  subjobs,
  job_types,
  jobSummary,
  jobBreakdown,
  jobChart,
  createJob,
  getFullBody,
}
