import { getApiUrlPrefix } from '@patterninc/react-ui'

const connectSyndicationBaseUrl = `${getApiUrlPrefix('connect-syndication')}/v1`,
  channels = `${connectSyndicationBaseUrl}/channels`,
  channelDetailsSummary = (id: string) => `${channels}/${id}`,
  channelDetails = (id: string) => `${channels}/${id}/fields`,
  marketplaces = `${connectSyndicationBaseUrl}/marketplaces`,
  connectFields = `${connectSyndicationBaseUrl}/connect_fields`,
  marketplaceFields = (id: string) =>
    `${connectFields}/${id}/marketplace_fields`,
  suggestions = `${connectFields}/suggestions`,
  connectMappings = (id: string) => `${channels}/${id}/connect_mappings`

export {
  channels,
  channelDetailsSummary,
  channelDetails,
  marketplaces,
  connectFields,
  marketplaceFields,
  suggestions,
  connectMappings,
}
