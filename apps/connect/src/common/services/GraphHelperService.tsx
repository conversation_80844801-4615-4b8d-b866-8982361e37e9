import moment from 'moment'

class GraphHelperService {
  abbreviateNumber = (num: number) => {
    if (num >= 1000000000) {
      return (num / 1000000000).toFixed(1).replace(/\.0$/, '') + 'B'
    }
    if (num >= 1000000) {
      return (num / 1000000).toFixed(1).replace(/\.0$/, '') + 'M'
    }
    if (num >= 1000) {
      return (num / 1000).toFixed(1).replace(/\.0$/, '') + 'K'
    }
    return num
  }
  formatTick = (dateString: string) => {
    return moment.utc(dateString).format('MMM, D')
  }
  formatLabel = (dateString: string) => {
    return moment.utc(dateString).format('MMM D, HH:mm:ss')
  }
}

const graphHelperService = new GraphHelperService()
export default graphHelperService
