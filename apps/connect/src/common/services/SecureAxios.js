import axios from 'axios'
import { cacheAdapterEnhancer, throttleAdapterEnhancer } from 'axios-extensions'
import qs from 'qs'

import Auth from '../../Auth/Auth'

const SecureAxios = axios.create({
  baseURL: '/',
  headers: {
    'Cache-Control': 'no-cache',
  },
  adapter: throttleAdapterEnhancer(
    cacheAdapterEnhancer(axios.defaults.adapter, { enabledByDefault: false }),
    { threshold: 1000 },
  ),
  paramsSerializer: function (params) {
    return qs.stringify(params, {
      arrayFormat: 'brackets',
      encodeValuesOnly: true,
    })
  },
})

// Do something with request header
SecureAxios.interceptors.request.use(
  function (config) {
    const auth = new Auth()
    const token = auth.getIdToken()
    if (token != null) {
      config.headers.Authorization = `Bearer ${token}`
    }
    return config
  },
  function (err) {
    return Promise.reject(err)
  },
)

// Do something with response error
SecureAxios.interceptors.response.use(
  function (response) {
    return response
  },
  function (error) {
    let user
    try {
      const fullUser = JSON.parse(localStorage.getItem('user'))
      user = {
        email: fullUser.email,
        id: fullUser.id,
      }
    } catch (error) {
      console.log(error)
      // don't do anything - if it has an error then don't worry about it at this point
    }
    let structuredMessage = ''
    const message =
      typeof error?.message === 'string'
        ? error?.message
        : JSON.stringify(error?.message)

    try {
      structuredMessage = JSON.stringify({
        message: message,
        endpointUrl: error?.config?.url,
        status: error?.response?.status,
        pageUrl: window.location.pathname,
        params: error?.config?.params,
        user,
      })
    } catch (error) {
      console.log(error)
      // oh well we tried
    }

    if (axios.isCancel(error)) {
      return Promise.reject(error)
    }
    if (error?.response?.status === 401) {
      const auth = new Auth()
      if (auth.isAuthenticated()) {
        console.log(error)
        // auth.logout()
      } else {
        auth.login()
      }
    } else if (error?.response && window.onerror) {
      const reportedError = new Error(
        `API Error ${
          error?.response?.status || '(Unknown)'
        }: ${structuredMessage}`,
      )
      reportedError.stack = error.stack
      window.onerror(
        `API Error ${
          error?.response?.status || '(Unknown)'
        }: ${structuredMessage}`,
        '',
        '',
        '',
        reportedError,
      )
    }
    return Promise.reject(error?.response)
  },
)

export default SecureAxios
