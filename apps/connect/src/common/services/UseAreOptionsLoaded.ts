import { type FilterProps } from '@patterninc/react-ui'
import { useMemo } from 'react'

/**
 * Checks if all relevant filter types within filterStates have non-empty 'options' arrays.
 * This hook specifically checks filters of type 'select'.
 * @param {FilterProps<OptionType>['filterStates']} filterStates - An object representing various filter configurations,
 * each containing an 'options' array among other properties.
 * @returns {boolean} True if all 'options' arrays in relevant filters are non-empty, false otherwise.
 */

const useAreOptionsLoaded = <OptionType>(
  filterStates: FilterProps<OptionType>['filterStates'],
): boolean => {
  return useMemo(() => {
    for (const key in filterStates) {
      const filter = filterStates[key]
      if (filter.type === 'select') {
        if (!filter.options || filter.options.length === 0) {
          return false
        }
      }
    }
    return true
  }, [filterStates])
}

export default useAreOptionsLoaded
