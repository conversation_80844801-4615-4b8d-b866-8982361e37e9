import { getApiUrlPrefix, toast } from '@patterninc/react-ui'
import { jwtDecode, type JwtPayload } from 'jwt-decode'

import SecureAxios from './SecureAxios'

type JwtPayloadNew = JwtPayload & {
  ['https://pattern.com/metadata']: { id: string }
}

export const getUserSettingEndpoint = (
  /** To have a unique key. Eg: connections:details:table */
  key: string,
): { endpoint: string; decoded: JwtPayloadNew } => {
  const token = localStorage.getItem('id_token'),
    decoded = jwtDecode<JwtPayloadNew>(token ?? ''),
    userId = decoded['https://pattern.com/metadata'].id
  return {
    endpoint: `${getApiUrlPrefix('user-settings')}/v1/${userId}/connect/${key}`,
    decoded,
  }
}

type selected = {
  metrics: string[]
  showEventMarker: boolean
  isSingleYAxis: boolean
}

export type customizationProps = {
  /** The api url */
  api: string
  /** An array of the selected items */
  selected: string[] | selected[]
  /** A list of the types that can be customized */
  type: 'table' | 'header-metric' // We can add more types in the future. Specifically kept this as an array of strings instead of a boolean so that we can add more types.
  /** Flag to determine if we are using the default settings. This helps with constructing the message in the toast. */
  setToDefault?: boolean
}

export const saveCustomization = ({
  api,
  selected,
  setToDefault,
  type,
}: customizationProps): void => {
  // TODO: These checks will need to get updated when new types are added in the future.
  const messagePrefix = type === 'table' ? 'Table column' : 'Summary metric'
  const message = setToDefault
      ? `${messagePrefix} preferences have been set to the default.`
      : `${messagePrefix} preferences have been updated.`,
    whereToSave = type === 'table' ? 'selectedColumns' : 'selectedMetrics'

  SecureAxios.put(api, {
    [whereToSave]: [...selected],
  })
    .then(() => {
      toast({
        type: 'success',
        message: message,
      })
    })
    .catch((error) => {
      if (error) {
        toast({
          type: 'error',
          message: 'Something went wrong. Please try again.',
        })
      }
    })
}
