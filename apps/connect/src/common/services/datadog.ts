import { datadogRum } from '@datadog/browser-rum'
import { getEnvironmentName } from '@patterninc/react-ui'

datadogRum.init({
  applicationId: 'fde23a30-900d-4d9a-88fa-9754dd8bb4b1',
  clientToken: 'pub5dec9ebdba9e5277f70b872c91f6fd48',
  site: 'datadoghq.com',
  service:
    getEnvironmentName() === 'production' ? 'connect' : 'staging-connect',
  env: getEnvironmentName(),
  // Specify a version number to identify the deployed version of the application in Datadog
  version: '1.0.0',
  sessionSampleRate: 100,
  sessionReplaySampleRate: 20,
  trackUserInteractions: true,
  trackResources: true,
  trackLongTasks: true,
  defaultPrivacyLevel: 'mask-user-input',
  allowedTracingUrls: [
    {
      match: (url: string) => url.startsWith(getBaseUrl(getEnvironmentName())),
      propagatorTypes: ['tracecontext', 'datadog', 'b3', 'b3multi'],
    },
  ],
})

try {
  const fullUser = JSON.parse(localStorage.getItem('user') || '')
  const user = {
    email: fullUser.email,
    id: fullUser.id,
  }
  datadogRum.setUser(user)
} catch {
  // don't do anything - if it has an error then don't worry about it at this point
}

datadogRum.startSessionReplayRecording()

const getBaseUrl = (environment: string) => {
  if (environment === 'production') {
    return 'https://connect.pattern.com'
  } else if (environment === 'stage') {
    return 'https://connect-stage.pattern.com'
  } else {
    return 'http://connect.localhost:7070'
  }
}
