import { type Dispatch } from 'react'

import { type ChannelsFilterStateType } from '../modules/Mappings/MappingsChannels/ChannelsFilter'

export type OptionType = {
  id: number
  text: string
  value: string
}

type DateRangeFilter = {
  start_date: moment.Moment | ''
  end_date: moment.Moment | ''
}

type numberRangeFilter = {
  min: number | string
  max: number | string
}

export type JobFilterType = {
  marketplaces: string[]
  statuses: string[]
  createdBy: string[]
  sellerAccounts: string[]
  jobTypes: string[]
  creationDateRange: DateRangeFilter
  completionDateRange?: DateRangeFilter
  runTimeRange: numberRangeFilter
}

export type JobsFilterContextType = {
  appliedFilters: JobFilterType
  setAppliedFilters: (f: JobFilterType, r?: boolean) => void
  searchQuery: string
  setSearchQuery: Dispatch<React.SetStateAction<string>>
  exactJobSearch: boolean
  setExactJobSearch: (value: boolean) => void
}

export type MappingsFilterContextType = {
  appliedFilters: ChannelsFilterStateType
  setAppliedFilters: (f: ChannelsFilterStateType, r?: boolean) => void
  searchQuery: string
  setSearchQuery: Dispatch<React.SetStateAction<string>>
  cfnSearchQuery: string
  setCfnSearchQuery: Dispatch<React.SetStateAction<string>>
}

export type ApiErrorType = {
  message?: string
  statusText?: string
  status?: number
  data: {
    error?: string
    message?: string
  }
}
