import React, { useState } from 'react'
import { notEmpty } from '@patterninc/react-ui'

import Auth from '../Auth/Auth'
import { missingPermissionToast } from '../common/helpers/missingPermissionToast'

const AuthContext = React.createContext()

const auth = new Auth()

function AuthProvider(props) {
  const [user, setUser] = useState(null)
  const [org, setOrg] = useState(null)

  const login = auth.login
  const logout = auth.logout
  const handleAuthentication = auth.handleAuthentication
  const isAuthenticated = auth.isAuthenticated
  const getUser = auth.getUser

  return (
    <AuthContext.Provider
      value={{
        user,
        setUser,
        login,
        logout,
        handleAuthentication,
        isAuthenticated,
        getUser,
        org,
        setOrg,
      }}
      {...props}
    />
  )
}

function useAuth() {
  const context = React.useContext(AuthContext)
  if (context === undefined) {
    throw new Error(`useAuth must be used within a AuthProvider`)
  }
  return context
}

function useHasPatternAdminRole() {
  const context = React.useContext(AuthContext)
  if (context === undefined) {
    throw new Error(`useAllowedRoles must be used within an AuthProvider`)
  }
  if (!notEmpty(context.org) || !notEmpty(context.user)) {
    return false
  }
  const authorizedOrg = context.user.orgs.find(
    (o) =>
      o.code === 'pattern' && o.role.code.includes(ROLES.ORGANIZATION_ADMIN),
  )
  const allowed = notEmpty(authorizedOrg)
  return allowed
}

/**
 * returns true if the user has the requested role on the currently
 * selected org. Otherwise, returns false.
 * @param {string} role
 * @returns {boolean}
 */
function useHasRole(role) {
  const context = React.useContext(AuthContext)
  if (context === undefined) {
    throw new Error(`useAllowedRoles must be used within an AuthProvider`)
  }
  if (!notEmpty(context.org) || !notEmpty(context.user)) {
    return false
  }
  const authorizedOrg = context.user.orgs.find(
    (o) => o.code === context.org.code && o.role.code === role,
  )
  const allowed = notEmpty(authorizedOrg)
  return allowed
}

/**
 * returns true if the user has the requested privilege on the currently
 * selected org. Otherwise, returns false, and if showToast is true, the
 * function will also alert the user of the missing privilege.
 * @param {string} privilege
 * @param {boolean} showToast
 * @returns {boolean}
 */
function useHasPrivilege(privilege, showToast = false) {
  const context = React.useContext(AuthContext)
  if (context === undefined) {
    throw new Error(`useAllowedRoles must be used within an AuthProvider`)
  }
  if (!notEmpty(context.org) || !notEmpty(context.user)) {
    return false
  }
  const authorizedOrg = context.user.orgs.find(
    (o) =>
      o.code === context.org.code &&
      notEmpty(o.role.privileges) &&
      o.role.privileges.includes(privilege),
  )
  const allowed = notEmpty(authorizedOrg)
  if (showToast && !allowed) missingPermissionToast(privilege, context.org)
  return allowed
}

const PRIVILEGES = {
  VIEW_CONNECTIONS: 'view_connections',
  VIEW_OTHERS_CONNECTIONS: 'view_others_connections',
  DEACTIVATE_CONNECTION: 'deactivate_connections',
  DELETE_CONNECTION: 'delete_connection',
  CREATE_CONNECTION: 'create_connection',
  VIEW_CONNECTION_DETAILS: 'view_connection_details',
  EDIT_CONNECTION: 'edit_connection',
  EDIT_CONNECTION_NOTIFICATIONS: 'edit_connection_notifications',
  VIEW_JOBS: 'view_jobs',
  VIEW_OTHERS_JOBS: 'view_others_jobs',
  CREATE_JOB: 'create_job',
  VIEW_JOB_DETAILS: 'view_job_details',
  RERUN_JOB: 'rerun_job',
  RERUN_AS_NEW_JOB: 'rerun_as_new_job',
  VIEW_METRICS: 'view_metrics',
  VIEW_OTHERS_METRICS: 'view_others_metrics',
  VIEW_NORMALIZATION: 'view_normalization',
  VIEW_OTHERS_ORGS: 'view_others_orgs',
  MANAGE_CONNECT_FIELDS: 'manage_connect_fields',
  MANAGE_CONNECT_MAPPINGS: 'manage_connect_mappings',
  CREATE_WHITE_GLOVE_ACCOUNT_SETUP: 'create_white_glove_account_setup',
}

const ROLES = {
  ORGANIZATION_ADMIN: 'organization_admin',
  WHITE_GLOVE_ORGANIZATION_ADMIN: 'white_glove_organization_admin',
}

export {
  AuthProvider,
  useAuth,
  useHasPrivilege,
  PRIVILEGES,
  useHasPatternAdminRole,
  useHasRole,
  ROLES,
}
