import React, {
  createContext,
  type ReactNode,
  useContext,
  useEffect,
  useMemo,
  useState,
} from 'react'
import { useInfiniteQuery, useQuery } from '@tanstack/react-query'
import { useParams } from 'react-router-dom'
import { notEmpty, toast } from '@patterninc/react-ui'

import {
  connectionDetails,
  connectionSlackChannels,
  credentialSettings,
} from '../common/services/ConnectAuthService'
import {
  type Location,
  type RawConnectionDetails,
  type RawCredentialSettingsType,
  type SlackChannel,
} from '../modules/Connections/types'
import SecureAxios from '../common/services/SecureAxios'
import { PRIVILEGES, useHasPrivilege } from './auth-context'
import { c } from '../common/helpers/TranslationHelpers'
import sortOptions from '../common/services/SortOptions'

// Define the context type
interface ConnectionDetailsContextType {
  connectionDetails: RawConnectionDetails | undefined
  fetchingConnectionDetails: boolean
  refetchConnectionDetails: () => void
  connectionSettings: RawCredentialSettingsType | undefined
  fetchingConnectionSettings: boolean
  refetchConnectionSettings: () => void
  locations: Location[]
  // Slack connection state
  isSlackConnected: boolean
  setIsSlackConnected: React.Dispatch<React.SetStateAction<boolean>>
  slackChannels: SlackChannel[]
  isSlackLoading: boolean
  setIsSlackLoading: React.Dispatch<React.SetStateAction<boolean>>
  needsRefetchSlack: boolean
  setNeedsRefetchSlack: React.Dispatch<React.SetStateAction<boolean>>
  refetchSlackChannels: () => void
}

type SlackChannelResponse = {
  channels: SlackChannel[]
  next_cursor?: string
}

// Create the context with a default value
const ConnectionDetailsContext = createContext<
  ConnectionDetailsContextType | undefined
>(undefined)

interface ConnectionDetailsProviderProps {
  children: ReactNode
}

export const ConnectionDetailsProvider = ({
  children,
}: ConnectionDetailsProviderProps) => {
  const id = useParams<{ id: string }>().id,
    canViewConnectionDetails = useHasPrivilege(
      PRIVILEGES.VIEW_CONNECTION_DETAILS,
    ),
    // Slack state
    [isSlackConnected, setIsSlackConnected] = useState(false),
    [isSlackLoading, setIsSlackLoading] = useState(true),
    [needsRefetchSlack, setNeedsRefetchSlack] = useState(false),
    {
      data: connectionDetailsData,
      isFetching: connectionDetailsFetching,
      refetch: refetchConnectionDetails,
    } = useQuery({
      queryKey: ['connectionDetails', id],
      queryFn: async () => {
        const result = await SecureAxios.get<RawConnectionDetails>(
          connectionDetails(id || ''),
        )
        return result.data
      },
      enabled: canViewConnectionDetails,
    }),
    {
      data: connectionSettingsData,
      refetch: refetchConnectionSettings,
      isFetching: connectionSettingsFetching,
    } = useQuery({
      queryKey: ['table_data', id],
      queryFn: async ({ signal }) => {
        if (typeof id === 'undefined') {
          toast({ type: 'error', message: c('missingCredentialIdInPath') })
          return
        }
        const response = await SecureAxios.get<RawCredentialSettingsType>(
          credentialSettings(id),
          { signal },
        )
        return response.data
      },
      enabled: canViewConnectionDetails,
    }),
    connectionName = useMemo(() => {
      return connectionDetailsData?.name || ''
    }, [connectionDetailsData]),
    {
      data: slackData,
      status: slackStatus,
      fetchNextPage: fetchNextSlackPage,
      hasNextPage: hasNextSlackPage,
      isFetchingNextPage: isFetchingNextSlackPage,
      refetch: refetchSlackChannels,
    } = useInfiniteQuery({
      queryKey: ['list_slack_channels', connectionName],
      queryFn: async ({ pageParam = undefined }) => {
        const slackChannelResponse =
          await SecureAxios.get<SlackChannelResponse>(
            connectionSlackChannels(connectionName),
            { params: { cursor: pageParam } },
          )
        return slackChannelResponse
      },
      initialPageParam: undefined as string | undefined,
      staleTime: 1000 * 60 * 5,
      gcTime: 1000 * 60 * 5,
      retry: (failureCount, error: { status: number }) => {
        if (error.status === 404) return false
        return failureCount < 3
      },
      getNextPageParam: (previousPage) => {
        return notEmpty(previousPage?.data.next_cursor)
          ? previousPage?.data.next_cursor
          : undefined
      },
      enabled: notEmpty(connectionName),
    })

  const locations = useMemo(() => {
    return connectionDetailsData?.locations || []
  }, [connectionDetailsData])

  const slackChannels = useMemo(() => {
    return sortOptions<SlackChannel>(
      slackData?.pages.flatMap((page) => page?.data.channels || []) || [],
      'name',
      true,
    )
  }, [slackData])

  // Handle Slack channels loading
  useEffect(() => {
    if (
      hasNextSlackPage &&
      slackStatus !== 'pending' &&
      !isFetchingNextSlackPage
    ) {
      fetchNextSlackPage()
    } else if (slackStatus !== 'pending' && !isFetchingNextSlackPage) {
      setIsSlackLoading(false)
    }
  }, [
    hasNextSlackPage,
    fetchNextSlackPage,
    slackStatus,
    setIsSlackLoading,
    isFetchingNextSlackPage,
  ])

  // Handle Slack refetch requests
  useEffect(() => {
    if (needsRefetchSlack) {
      refetchSlackChannels()
      setNeedsRefetchSlack(false)
    }
  }, [needsRefetchSlack, refetchSlackChannels])

  // Update Slack connection status
  useEffect(() => {
    setIsSlackConnected(slackChannels.length > 0)
  }, [slackChannels])

  const value = {
    connectionDetails: connectionDetailsData,
    fetchingConnectionDetails: connectionDetailsFetching,
    refetchConnectionDetails,
    connectionSettings: connectionSettingsData,
    fetchingConnectionSettings: connectionSettingsFetching,
    refetchConnectionSettings: refetchConnectionSettings,
    locations,
    // Slack connection state
    isSlackConnected,
    setIsSlackConnected,
    slackChannels,
    isSlackLoading,
    setIsSlackLoading,
    needsRefetchSlack,
    setNeedsRefetchSlack,
    refetchSlackChannels,
  }

  return (
    <ConnectionDetailsContext.Provider value={value}>
      {children}
    </ConnectionDetailsContext.Provider>
  )
}

// Custom hook to use the connection details context
export const useConnectionDetails = () => {
  const context = useContext(ConnectionDetailsContext)
  if (context === undefined) {
    throw new Error(
      'useConnectionDetails must be used within a ConnectionDetailsProvider',
    )
  }
  return context
}
