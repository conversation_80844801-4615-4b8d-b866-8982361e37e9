import React, {
  createContext,
  type ReactNode,
  useCallback,
  useContext,
  useEffect,
  useMemo,
  useState,
} from 'react'
import { useQuery } from '@tanstack/react-query'
import { notEmpty, toast } from '@patterninc/react-ui'
import { useSearchParams } from 'react-router-dom'

import {
  type CountryInfoType,
  type CredentialDefinition,
} from '../modules/Connections/types'
import SecureAxios from '../common/services/SecureAxios'
import {
  channels,
  credentialDefinition as credentialDefinitionApi,
  marketplace,
  marketplaceSupportedCountries,
} from '../common/services/ConnectAuthService'
import { useOrg } from './org-context'
import { PRIVILEGES, ROLES, useHasPrivilege, useHasRole } from './auth-context'
import { c } from '../common/helpers/TranslationHelpers'

interface ConnectionFormContextType {
  // White glove supported
  destinationIsWhiteGloveSupported: boolean | undefined
  isWhiteGloveLoading: boolean
  // Credential definition
  rawCredentialDefinition: CredentialDefinition | undefined
  credentialDefinitionStatus: 'pending' | 'error' | 'success' | 'idle'
  // Supported countries
  supportedCountries: CountryInfoType[]
  setSupportedCountries?: (supported: CountryInfoType[]) => void
  // White glove access
  hasWhiteGloveAccess: boolean
  // Destination type
  destinationType: string
  setDestinationType: React.Dispatch<React.SetStateAction<string>>
  resetDestinationType: () => void
  // Marketplace identifiers
  marketplaceDisplayName: string
  setMarketplaceDisplayName: React.Dispatch<React.SetStateAction<string>>
  marketplaceName: string
  setMarketplaceName: React.Dispatch<React.SetStateAction<string>>
  marketplaceId: string | null
  setMarketplaceId: React.Dispatch<React.SetStateAction<string | null>>
  // Marketplace form options
  currentStep: number
  setCurrentStep: (step: number) => void
  isFormOpen: boolean
  setIsFormOpen: (open: boolean) => void
}

const ConnectionFormContext = createContext<
  ConnectionFormContextType | undefined
>(undefined)

interface ConnectionFormProviderProps {
  children: ReactNode
}

export const ConnectionFormProvider = ({
  children,
}: ConnectionFormProviderProps) => {
  const { org } = useOrg()
  const hasRole = useHasRole(ROLES.WHITE_GLOVE_ORGANIZATION_ADMIN)
  const hasPrivilege = useHasPrivilege(
    PRIVILEGES.CREATE_WHITE_GLOVE_ACCOUNT_SETUP,
  )
  const hasWhiteGloveAccess = hasRole && hasPrivilege

  const [destinationType, setDestinationType] = useState('1')

  const resetDestinationType = useCallback(() => {
    setDestinationType('1')
  }, [])

  const [marketplaceDisplayName, setMarketplaceDisplayName] = useState('')
  const [marketplaceName, setMarketplaceName] = useState('')

  const [marketplaceId, setMarketplaceId] = useState<string | null>(null)

  // Query for supported countries
  const { data: supportedCountriesResponse } = useQuery({
    queryKey: [
      'supported_countries',
      marketplaceSupportedCountries,
      marketplaceName,
    ],
    queryFn: async ({ signal }) => {
      const result = await SecureAxios.get<CountryInfoType[]>(
        marketplaceSupportedCountries(marketplaceName.toLowerCase()),
        { signal },
      )
      return result.data
    },
    enabled: notEmpty(marketplaceName),
  })

  const supportedCountries = useMemo(() => {
    return supportedCountriesResponse || []
  }, [supportedCountriesResponse])

  // Query for white glove support
  const {
    data: destinationIsWhiteGloveSupported,
    isLoading: isWhiteGloveLoading,
  } = useQuery({
    queryKey: [
      'white_glove_supported',
      marketplaceName,
      marketplaceId,
      org.code,
    ],
    queryFn: async ({ signal }) => {
      const result = await SecureAxios.get(channels, {
        params: {
          support_for: 'white_glove',
        },
        signal,
      })
      return notEmpty(
        result.data.find((i: string) => i.includes(marketplaceName)),
      )
    },
  })

  const [searchParams] = useSearchParams()

  const [isFormOpen, setIsFormOpen] = useState(false)

  const [currentStep, setCurrentStep] = useState(1)

  const toastShownRef = React.useRef(false)

  useEffect(() => {
    if (searchParams.get('create_connection') === 'true') {
      setIsFormOpen(true)
      let marketplaceCode = searchParams.get('marketplace_code')
      if (marketplaceCode) {
        const regex = /_[a-zA-Z]{2}$/
        if (regex.test(marketplaceCode)) {
          marketplaceCode = marketplaceCode.replace(regex, '')
        }
        setCurrentStep(2)
        SecureAxios.get(marketplace(marketplaceCode))
          .then((result) => {
            setMarketplaceName(marketplaceCode as string)
            setMarketplaceId(result.data.id)
          })
          .catch((error) => {
            setCurrentStep(1)
            if (!toastShownRef.current) {
              toast({
                type: 'error',
                message: c('errorMessage', { message: error.data.error }),
              })
              toastShownRef.current = true
            }
          })
      }
    }
  }, [searchParams, setMarketplaceId, setMarketplaceName])

  // Query for credential definition
  const { data: rawCredentialDefinition, status: credentialDefinitionStatus } =
    useQuery({
      queryKey: [
        'get_credential_definition',
        marketplaceName,
        credentialDefinitionApi,
      ],
      queryFn: async ({ signal }) => {
        if (notEmpty(marketplaceName)) {
          const result = await SecureAxios.get<CredentialDefinition>(
            credentialDefinitionApi(marketplaceName),
            { signal },
          )
          return result.data
        }
      },
      enabled: notEmpty(marketplaceName),
    })

  const value = {
    destinationIsWhiteGloveSupported,
    isWhiteGloveLoading,
    rawCredentialDefinition,
    credentialDefinitionStatus,
    supportedCountries,
    hasWhiteGloveAccess,
    destinationType,
    setDestinationType,
    resetDestinationType,
    marketplaceDisplayName,
    setMarketplaceDisplayName,
    marketplaceName,
    setMarketplaceName,
    marketplaceId,
    setMarketplaceId,
    currentStep,
    setCurrentStep,
    isFormOpen,
    setIsFormOpen,
  }

  return (
    <ConnectionFormContext.Provider value={value}>
      {children}
    </ConnectionFormContext.Provider>
  )
}

export const useConnectionForm = () => {
  const context = useContext(ConnectionFormContext)
  if (context === undefined) {
    throw new Error(
      'useConnectionForm must be used within a ConnectionFormProvider',
    )
  }
  return context
}
