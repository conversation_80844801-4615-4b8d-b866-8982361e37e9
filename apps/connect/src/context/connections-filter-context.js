import React, { useState } from 'react'

const ConnectionsFilterContext = React.createContext()

function ConnectionsFilterProvider(props) {
  const [appliedFilters, setAppliedFilters] = useState(null)
  const [searchQuery, setSearchQuery] = useState('')

  return (
    <ConnectionsFilterContext.Provider
      value={{ appliedFilters, setAppliedFilters, searchQuery, setSearchQuery }}
      {...props}
    />
  )
}

export { ConnectionsFilterProvider, ConnectionsFilterContext }
