import React, { useCallback, useState } from 'react'
import { useNavigate } from 'react-router-dom'

import { type JobFilterType, type JobsFilterContextType } from '../common/types'

const initialFilters: JobFilterType = {
  marketplaces: [],
  statuses: [],
  createdBy: [],
  sellerAccounts: [],
  jobTypes: [],
  creationDateRange: {
    start_date: '',
    end_date: '',
  },
  completionDateRange: {
    start_date: '',
    end_date: '',
  },
  runTimeRange: {
    min: '',
    max: '',
  },
}

const JobsFilterContext = React.createContext<JobsFilterContextType>({
  appliedFilters: initialFilters,
  setAppliedFilters: () => {
    return
  },
  searchQuery: '',
  setSearchQuery: () => {
    return
  },
  exactJobSearch: true,
  setExactJobSearch: () => {
    return
  },
})

const JobsFilterProvider = ({
  children,
}: {
  children: React.ReactNode
}): React.JSX.Element => {
  const [appliedFilters, setAppliedFilters] = useState(initialFilters),
    [searchQuery, setSearchQuery] = useState(''),
    [exactJobSearch, setExactJobSearch] = useState(true),
    navigate = useNavigate(),
    handleNewFilters = useCallback(
      (filters: JobFilterType, resetQueryParams = false) => {
        setAppliedFilters(filters)
        if (resetQueryParams) {
          navigate('/jobs')
        }
      },
      [navigate, setAppliedFilters],
    )

  return (
    <JobsFilterContext.Provider
      value={{
        appliedFilters,
        setAppliedFilters: handleNewFilters,
        searchQuery,
        setSearchQuery,
        exactJobSearch,
        setExactJobSearch,
      }}
    >
      {children}
    </JobsFilterContext.Provider>
  )
}

export { JobsFilterProvider, JobsFilterContext, initialFilters }
