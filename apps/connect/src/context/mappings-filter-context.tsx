import React, { useCallback, useState } from 'react'

import { type MappingsFilterContextType } from '../common/types'
import { type ChannelsFilterStateType } from '../modules/Mappings/MappingsChannels/ChannelsFilter'

const initialFilters: ChannelsFilterStateType = {
  marketplaces: [],
  status: [],
}

const MappingsFilterContext = React.createContext<MappingsFilterContextType>({
  appliedFilters: initialFilters,
  setAppliedFilters: () => {
    return
  },
  cfnSearchQuery: '',
  setCfnSearchQuery: () => {
    return
  },
  searchQuery: '',
  setSearchQuery: () => {
    return
  },
})

const MappingsFilterProvider = ({
  children,
}: {
  children: React.ReactNode
}): React.JSX.Element => {
  const [appliedFilters, setAppliedFilters] = useState(initialFilters),
    [searchQuery, setSearchQuery] = useState(''),
    [cfnSearchQuery, setCfnSearchQuery] = useState(''),
    handleNewFilters = useCallback(
      (filters: ChannelsFilterStateType) => {
        setAppliedFilters(filters)
      },
      [setAppliedFilters],
    )

  return (
    <MappingsFilterContext.Provider
      value={{
        appliedFilters,
        setAppliedFilters: handleNewFilters,
        searchQuery,
        setSearchQuery,
        cfnSearchQuery,
        setCfnSearchQuery,
      }}
    >
      {children}
    </MappingsFilterContext.Provider>
  )
}

export { MappingsFilterProvider, MappingsFilterContext, initialFilters }
