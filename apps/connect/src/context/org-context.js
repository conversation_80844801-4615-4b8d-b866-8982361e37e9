import React, { useCallback, useMemo, useState } from 'react'
import { notEmpty, useToggle } from '@patterninc/react-ui'
import { useQuery } from '@tanstack/react-query'

import { useAuth } from './auth-context'
import SecureAxios from '../common/services/SecureAxios'
import { getUserSettingEndpoint } from '../common/services/UserSettingsService'
import { useUser } from './user-context'
import { organizations } from '../common/services/ConnectAuthService'

const OrgContext = React.createContext(),
  ORG_NAME_KEY = 'organization',
  USER_SETTINGS_ORG_KEY = 'organization'

function OrgProvider(props) {
  const { org, setOrg } = useAuth()
  const user = useUser()
  const newUserSettingsId = useToggle('new_user_settings_id')
  const [isOrgSwitched, setIsOrgSwitched] = useState(false)
  const { data: orgs } = useQuery({
    queryKey: [organizations],
    queryFn: async () => {
      try {
        const res = await SecureAxios.get(organizations)
        return res.data
      } catch (error) {
        console.log('Error fetching organizations data:', error)
        throw error
      }
    },
  })
  const userOrgs = useMemo(() => {
    return user?.orgs?.map((o) => {
      return {
        code: o.code,
        name: orgs?.find((org) => org.code === o.code)?.name,
      }
    })
  }, [orgs, user?.orgs])
  const saveOrganization = useCallback(
    (organization) => {
      setOrg(organization)
      SecureAxios.put(
        getUserSettingEndpoint(USER_SETTINGS_ORG_KEY, newUserSettingsId)
          .endpoint,
        {
          organization,
        },
      ).catch(() => {
        // console.log('Error saving organization')
      })
      localStorage.setItem(ORG_NAME_KEY, JSON.stringify(organization))
    },
    [setOrg, newUserSettingsId],
  )

  const getOrganization = useCallback(() => {
    const current_org = localStorage.getItem(ORG_NAME_KEY)
    if (notEmpty(current_org)) {
      saveOrganization(JSON.parse(current_org))
      return
    }
    SecureAxios.get(
      getUserSettingEndpoint(USER_SETTINGS_ORG_KEY, newUserSettingsId).endpoint,
    )
      .then((result) => {
        if (notEmpty(result.data.organization)) {
          // Verifies if the user is still a member of the organization stored in their user settings table.
          const existingUserOrg = user.orgs.find(
            (o) => o.code === result.data.organization.code,
          )
          if (existingUserOrg) {
            saveOrganization(result.data.organization)
            return
          }
          saveOrganization(
            userOrgs.find((o) => o.code === 'pattern') || userOrgs[0],
          )
        } else {
          saveOrganization(
            userOrgs.find((o) => o.code === 'pattern') || userOrgs[0],
          )
        }
      })
      .catch(() => {
        saveOrganization(
          userOrgs.find((o) => o.code === 'pattern') || userOrgs[0],
        )
      })
  }, [newUserSettingsId, saveOrganization, user.orgs, userOrgs])

  return (
    <OrgContext.Provider
      value={{
        org: org,
        setOrg: saveOrganization,
        setupOrg: getOrganization,
        userOrgs,
        isOrgSwitched,
        setIsOrgSwitched,
      }}
      {...props}
    />
  )
}

function useOrg() {
  const context = React.useContext(OrgContext)
  if (context === undefined) {
    throw new Error(`useOrg must be used within an OrgProvider`)
  }
  if (localStorage.getItem(ORG_NAME_KEY) !== null) {
    return {
      ...context,
      org: JSON.parse(localStorage.getItem(ORG_NAME_KEY)),
    }
  }
  return context
}

export { OrgProvider, useOrg, ORG_NAME_KEY, USER_SETTINGS_ORG_KEY, OrgContext }
