import React, {
  createContext,
  type ReactNode,
  useCallback,
  useContext,
  useMemo,
} from 'react'
import { useToggle } from '@patterninc/react-ui'
import { useQuery } from '@tanstack/react-query'
import { useParams } from 'react-router-dom'

import {
  connectionSettings,
  connectionSettingsNew,
  destinationSettings,
  locationSettings,
} from '../common/services/ConnectAuthService'
import {
  type DetailsData,
  type RawCredentialSettingsType,
  type RawSettingsType,
} from '../modules/Connections/types'
import { PRIVILEGES, useHasPrivilege } from './auth-context'
import SecureAxios from '../common/services/SecureAxios'

// Define the context type
interface SettingsContextType {
  formatSettingsData: (
    rawSettingData?: RawCredentialSettingsType,
  ) => DetailsData
  getSettingsEndpoint: (key: string, settingName?: string) => string
  settingsAtLevel: RawSettingsType | undefined
  key: string
  metadata: (rawSettingData?: RawCredentialSettingsType) => {
    api_key_pattern: string
    algorithm: string
  }
  paths: (rawSettingData?: RawCredentialSettingsType) => Record<string, string>
  canEditConnections: boolean
  canEditNotifications: boolean
}

// Create the context with a default value
const SettingsContext = createContext<SettingsContextType | undefined>(
  undefined,
)

interface SettingsProviderProps {
  children: ReactNode
  id?: string
  settingLevel?: 'credential' | 'credential_location' | 'destination'
}

export const SettingsProvider = ({
  children,
  id,
  settingLevel = 'credential',
}: SettingsProviderProps) => {
  const enableThreeTieredSettings = useToggle('three_tiered_settings'),
    canEditConnections = useHasPrivilege(PRIVILEGES.EDIT_CONNECTION),
    canEditNotifications = useHasPrivilege(
      PRIVILEGES.EDIT_CONNECTION_NOTIFICATIONS,
    ),
    settingsEndpoint = useMemo(() => {
      if (enableThreeTieredSettings) {
        if (settingLevel === 'credential') {
          return connectionSettingsNew
        } else if (settingLevel === 'credential_location') {
          return locationSettings
        } else {
          return destinationSettings
        }
      }
      return connectionSettings
    }, [enableThreeTieredSettings, settingLevel]),
    { id: idParam } = useParams<{ id: string }>(),
    key = useMemo(() => id || idParam || '', [id, idParam]),
    { data: settingsAtLevel } = useQuery({
      queryKey: [settingsEndpoint, key],
      queryFn: async () => {
        const result = await SecureAxios.get<RawSettingsType>(
          settingsEndpoint(key),
        )
        return result.data
      },
      enabled:
        !!settingsEndpoint &&
        enableThreeTieredSettings &&
        settingLevel !== 'destination',
    })

  // Format notifications data
  const formatNotifications = (settings: RawSettingsType) => {
    return settings?.notifications
      ? Object.entries(settings.notifications).map((entry) => {
          return {
            job_type: entry[0],
            notify_people: entry[1]?.notify_people,
            notify_channels: entry[1]?.notify_channels,
            job_success: entry[1]?.job_success,
            job_failure: entry[1]?.job_failure,
            expiration: entry[1]?.expiration,
          }
        })
      : []
  }

  // Format call limits data
  const formatCallLimits = (settings: RawSettingsType) => {
    return typeof settings?.throttling === 'object' &&
      settings.throttling.operations
      ? Object.entries(settings.throttling.operations).map((entry) => {
          return {
            call_limit: entry[0],
            tokens_per_time_unit: entry[1].tokens_per_time_unit,
            time_unit: entry[1].time_unit,
            max: Number(entry[1].max),
          }
        })
      : []
  }

  // Format other settings data
  const formatOtherSettings = (settings: RawSettingsType) => {
    if (!settings) return []
    return Object.entries(settings)
      .filter(([key]) => key !== 'notifications' && key !== 'throttling')
      .map(([key, value]) => ({
        name: key,
        value: JSON.stringify(value),
      }))
  }

  // Process the raw data into formatted table data
  const formatSettingsData = useCallback(
    (connectionSettingsData?: RawCredentialSettingsType) => {
      if (settingLevel === 'credential') {
        return connectionSettingsData
          ? {
              call_limits: formatCallLimits(connectionSettingsData.settings),
              notifications: formatNotifications(
                connectionSettingsData.settings,
              ),
              other_settings: formatOtherSettings(
                connectionSettingsData.settings,
              ),
            }
          : {
              call_limits: [],
              notifications: [],
              other_settings: [],
            }
      } else {
        return settingsAtLevel
          ? {
              call_limits: formatCallLimits(settingsAtLevel),
              notifications: formatNotifications(settingsAtLevel),
              other_settings: formatOtherSettings(settingsAtLevel),
            }
          : {
              call_limits: [],
              notifications: [],
              other_settings: [],
            }
      }
    },
    [settingLevel, settingsAtLevel],
  )

  // Get metadata for throttling
  const metadata = useCallback(
    (connectionSettingsData?: RawCredentialSettingsType) => {
      if (!connectionSettingsData?.settings?.throttling) {
        return {
          api_key_pattern: '',
          algorithm: '',
        }
      }
      return connectionSettingsData.settings.throttling.metadata
    },
    [],
  )

  // Get paths for throttling
  const paths = useCallback(
    (connectionSettingsData?: RawCredentialSettingsType) => {
      if (!connectionSettingsData?.settings?.throttling) {
        return {}
      }
      return connectionSettingsData.settings.throttling.paths || {}
    },
    [],
  )

  return (
    <SettingsContext.Provider
      value={{
        getSettingsEndpoint: settingsEndpoint,
        settingsAtLevel,
        key,
        metadata,
        paths,
        canEditConnections,
        canEditNotifications,
        formatSettingsData,
      }}
    >
      {children}
    </SettingsContext.Provider>
  )
}

// Custom hook to use the settings context
export const useSettings = () => {
  const context = useContext(SettingsContext)
  if (context === undefined) {
    throw new Error('useSettings must be used within a SettingsProvider')
  }
  return context
}
