import React from 'react'
import { createRoot } from 'react-dom/client'
import { QueryClient, QueryClientProvider } from '@tanstack/react-query'
import { ReactQueryDevtools } from '@tanstack/react-query-devtools'
import { TranslationProvider } from '@patterninc/react-ui'

import AppProviders from './context/app-providers'
import App from './App'
import * as serviceWorker from './serviceWorker'
import {
  i18nConnectInstance,
  initConnectI18nInstance,
} from './common/helpers/TranslationHelpers'

initConnectI18nInstance()

const queryClient = new QueryClient({
    defaultOptions: {
      queries: {
        refetchOnWindowFocus: false,
      },
    },
  }),
  container = document.getElementById('root') as HTMLElement,
  root = createRoot(container),
  enableReactQueryDevTools =
    localStorage.getItem('react-query-devtools') === 'true'

root.render(
  <React.StrictMode>
    <TranslationProvider i18nInstance={i18nConnectInstance}>
      <QueryClientProvider client={queryClient}>
        <AppProviders>
          <App />
        </AppProviders>
        {enableReactQueryDevTools ? <ReactQueryDevtools /> : null}
      </QueryClientProvider>
    </TranslationProvider>
  </React.StrictMode>,
)

// If you want your app to work offline and load faster, you can change
// unregister() to register() below. Note this comes with some pitfalls.
// Learn more about service workers: http://bit.ly/CRA-PWA
serviceWorker.unregister()
