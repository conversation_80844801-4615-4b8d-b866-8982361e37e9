import React, {
  useCallback,
  useContext,
  useEffect,
  useMemo,
  useRef,
  useState,
} from 'react'
import {
  SearchBar,
  type SortByProps,
  type SortColumnProps,
  StandardTable,
  Tabs,
} from '@patterninc/react-ui'
import { useQuery } from '@tanstack/react-query'
import moment from 'moment'
import { useLocation } from 'react-router-dom'

import { ThemeContext } from '../../Context'
import { apiTrafficTableConfig } from './api-traffic-table-config'
import { apiTrafficBottomTabs, apiTrafficTopTabs } from './api-traffic-tabs'
import {
  type ApiType,
  type BreakdownDataRow,
  type BreakdownDataType,
  type ChartDataRow,
  type HeaderSummaryData,
  type MarketplaceType,
  type SortObjType,
} from './types'
import SecureAxios from '../../common/services/SecureAxios'
import { marketplacesApi } from '../../common/services/ConnectAuthService'
import { job_types, jobChart } from '../../common/services/ConnectEngineService'
import { defaultFilterState } from './ApiTrafficFilter'
import { type OptionType } from '../../common/types'
import {
  jobBreakdown,
  jobSummary,
} from '../../common/services/ConnectEngineService'
import { getUserSettingEndpoint } from '../../common/services/UserSettingsService'
import { useOrg } from '../../context/org-context'
import { PRIVILEGES, useHasPrivilege } from '../../context/auth-context'
import { filterEndDate } from '../../common/helpers/filterEndDate'
import { c, useTranslate } from '../../common/helpers/TranslationHelpers'
import useResetOnOrgSwitch from '../../common/helpers/useResetOnOrgSwitch'

const ApiTraffic = (): React.JSX.Element => {
  const { t } = useTranslate('apiTraffic'),
    { updateBreadcrumbs } = useContext(ThemeContext),
    [topActive, setTopActive] = useState(0),
    [bottomActive, setBottomActive] = useState(0),
    [search, setSearch] = useState<string>(''),
    [count, setCount] = useState<number>(0),
    { org } = useOrg(),
    canViewMetrics = useHasPrivilege(PRIVILEGES.VIEW_METRICS, true),
    tableKeys = [c('marketplace'), c('connection')],
    tableIds = [
      'api_traffic_marketplace_table',
      'api_traffic_connection_table',
    ],
    { endpoint: sortBottomTableApi } = getUserSettingEndpoint(
      `apitraffic:table:${tableIds[bottomActive]}`,
    ),
    { data: marketplaceOptions } = useQuery({
      queryKey: ['api_traffic_filter_by_marketplace', marketplacesApi],
      queryFn: async ({ signal }) => {
        const result = await SecureAxios.get<MarketplaceType[]>(
          marketplacesApi,
          { signal },
        )
        return result.data.map((entry, index) => ({
          id: index,
          text: entry.display_name,
          value: entry.name,
        }))
      },
    }),
    marketplaceNames: Record<string, string> = useMemo(() => {
      const mdata = marketplaceOptions ? marketplaceOptions : []
      return Object.fromEntries(mdata.map((entry) => [entry.text, entry.value]))
    }, [marketplaceOptions]),
    [filterState, setFilterState] = useState(defaultFilterState),
    { pathname } = useLocation(),
    previousEndpoints = useRef<string[]>([]),
    prevTopActive = useRef<number>(0),
    { data: endpointOptionsData } = useQuery<OptionType[]>({
      queryKey: ['api_traffic_filter_by_job_types', job_types],
      queryFn: async ({ signal }) => {
        const result = await SecureAxios.get<string[]>(job_types, { signal })
        return result.data.map((elt, index) => ({
          id: index,
          text: elt,
          value: elt,
        }))
      },
    }),
    endpointOptions = useMemo(() => {
      const endpointTypes = {
        inventories: ['Inventories.create', 'Inventories.index'],
        orders: [
          'Orders.accept',
          'Orders.fulfill',
          'Orders.index',
          'Orders.show',
        ],
        advertising: [],
      }
      switch (topActive) {
        case 0:
          return endpointOptionsData
        case 1:
          return endpointOptionsData?.filter((endpoint) => {
            return (
              endpointTypes.inventories.findIndex((type) =>
                endpoint.text.includes(type),
              ) !== -1
            )
          })
        case 2:
          return endpointOptionsData?.filter((endpoint) => {
            return (
              endpointTypes.orders.findIndex((type) =>
                endpoint.text.includes(type),
              ) !== -1
            )
          })
        case 3:
          return endpointOptionsData?.filter((endpoint) => {
            return (
              endpointTypes.advertising.findIndex((type) =>
                endpoint.text.includes(type),
              ) !== -1
            )
          })
      }
    }, [endpointOptionsData, topActive]),
    filterOptions = useMemo(() => {
      return {
        marketplaces: marketplaceOptions ?? [],
        connections: [],
        endpoints: endpointOptions ?? [],
      }
    }, [marketplaceOptions, endpointOptions]),
    apiParams = useMemo(() => {
      const rawStartDate = filterState?.dateRange?.start_date as string
      const rawEndDate = filterState?.dateRange?.end_date as string
      const startDate = rawStartDate
        ? moment(new Date(rawStartDate))
        : moment().subtract(7, 'days')
      const endDate = rawEndDate ? moment(new Date(rawEndDate)) : moment()

      const jobTypes =
        filterState.endpoints.length > 0
          ? filterState.endpoints
          : topActive !== 0
            ? filterOptions.endpoints.map((e) => e.value)
            : []
      return {
        created_at_start: startDate.format('YYYY-MM-DD'),
        created_at_end: filterEndDate(endDate).format('YYYY-MM-DD'),
        marketplace: filterState.marketplaces.map((m) => marketplaceNames[m]),
        account: filterState.connections,
        request_name: jobTypes,
        organization: org.code,
      }
    }, [
      filterState,
      marketplaceNames,
      org,
      topActive,
      filterOptions.endpoints,
    ]),
    { status: summaryStatus, data: summaryData } = useQuery({
      queryKey: [jobSummary, apiParams, canViewMetrics],
      queryFn: async () => {
        const result = await SecureAxios.get<HeaderSummaryData>(jobSummary, {
          params: apiParams,
        })
        return result.data
      },
      enabled: canViewMetrics,
    }),
    summaryHeaderData: HeaderSummaryData = useMemo(() => {
      return (
        summaryData || {
          total: 0,
          successful: 0,
          failed: 0,
          in_progress: 0,
          uploads: 0,
          downloads: 0,
        }
      )
    }, [summaryData]),
    { status: breakdownStatus, data: breakdownData } =
      useQuery<BreakdownDataType>({
        queryKey: [jobBreakdown, apiParams, canViewMetrics],
        queryFn: async ({ signal }) => {
          const result = await SecureAxios.get<BreakdownDataType>(
            jobBreakdown,
            {
              params: apiParams,
              signal,
            },
          )
          return result.data
        },
        enabled: canViewMetrics,
      }),
    [sortBy, setSortBy] = useState<SortByProps>({
      prop: 'failed_count',
      flip: false,
    }),
    saveSort = (sortObj: SortObjType) => {
      SecureAxios.put(sortBottomTableApi, {
        sort: {
          prop: sortObj.activeColumn,
          flip: sortObj.direction,
        },
      }).catch((error) => {
        //TODO: Handle error in future prs and remove the following log
        console.log('Error saving sort data:', error)
      })
    },
    sort: SortColumnProps['sorter'] = (sortObj: SortObjType) => {
      setSortBy({
        prop: sortObj.activeColumn,
        flip: sortObj.direction,
      })
      saveSort(sortObj)
    },
    sortData = useCallback(
      (unsorted: ApiType[]) => {
        const sortedData: ApiType[] = [...unsorted].sort(
          (a: ApiType, b: ApiType) => {
            let first: string | number, second: string | number
            switch (sortBy.prop) {
              case 'main':
                first = a.main?.toUpperCase()
                second = b.main?.toUpperCase()
                break
              case 'api_calls':
                first = a.api_calls
                second = b.api_calls
                break
              case 'successful_count':
                first = a.successful
                second = b.successful
                break
              case 'failed_count':
                first = a.failed
                second = b.failed
                break
              case 'in_progress_count':
                first = a.in_progress
                second = b.in_progress
                break
              case 'successful_percentage':
                first = a.successful / a.api_calls
                second = b.successful / b.api_calls
                break
              case 'failed_percentage':
                first = a.failed / a.api_calls
                second = b.failed / b.api_calls
                break
              case 'in_progress_percentage':
                first = a.in_progress / a.api_calls
                second = b.in_progress / b.api_calls
                break
              default:
                return 0
            }
            if (
              (first < second && sortBy.flip) ||
              (first > second && !sortBy.flip)
            ) {
              return -1
            }
            if (
              (first > second && sortBy.flip) ||
              (first < second && !sortBy.flip)
            ) {
              return 1
            }
            return 0
          },
        )
        return sortedData
      },
      [sortBy],
    ),
    filterBySearchString = (
      breakdownDataObject: Record<string, BreakdownDataRow>,
      searchString: string,
    ) => {
      const filteredObj: Record<string, BreakdownDataRow> = {}
      for (const entry in breakdownDataObject) {
        if (entry.includes(searchString.toLocaleLowerCase())) {
          filteredObj[entry] =
            breakdownDataObject[entry as keyof BreakdownDataType]
        }
      }
      return filteredObj
    },
    breakdownTableData: ApiType[] = useMemo(() => {
      // Added topActive === 3 condition to handle and set Advertising tab empty state.
      if (breakdownData === undefined || topActive === 3) {
        setCount(0)
        return []
      }
      const data =
        bottomActive === 0
          ? search === ''
            ? breakdownData.by_marketplace
            : filterBySearchString(breakdownData.by_marketplace, search)
          : search === ''
            ? breakdownData.by_connection
            : filterBySearchString(breakdownData.by_connection, search)

      const unsorted = Object.entries(data).map((entry) => {
        const totalCalls: number =
          entry[1].success + entry[1].fail + entry[1].in_progress
        return {
          main: entry[0],
          api_calls: totalCalls,
          successful: entry[1].success,
          failed: entry[1].fail,
          in_progress: entry[1].in_progress,
        }
      })
      return sortData(unsorted)
    }, [breakdownData, topActive, bottomActive, search, sortData]),
    filterProps = useMemo(() => {
      return {
        filterOptions: filterOptions,
        filterState: filterState,
        setFilterState: setFilterState,
      }
    }, [filterOptions, filterState, setFilterState]),
    { data: chartData } = useQuery<ChartDataRow[]>({
      queryKey: [jobChart, apiParams, canViewMetrics],
      queryFn: async () => {
        const result = await SecureAxios.get<ChartDataRow[]>(jobChart, {
          params: apiParams,
        })
        return result.data
      },
      enabled: canViewMetrics,
    }),
    [activeMetricsState, setActiveMetricsState] = useState<string[]>([
      c('successful'),
      c('failed'),
    ])

  useEffect(() => {
    setCount(breakdownTableData.length)
  }, [breakdownTableData.length])

  useEffect(() => {
    setActiveMetricsState([c('successful'), c('failed')])
  }, [topActive])

  useEffect(() => {
    if (topActive !== 0 && prevTopActive.current === 0) {
      previousEndpoints.current = filterState.endpoints
      setFilterState({
        ...filterState,
        endpoints: [],
      })
    } else if (topActive === 0 && prevTopActive.current !== 0) {
      setFilterState({ ...filterState, endpoints: previousEndpoints.current })
    } else if (topActive !== prevTopActive.current) {
      setFilterState({ ...filterState, endpoints: [] })
    }
    prevTopActive.current = topActive
  }, [topActive, filterState])

  useEffect(() => {
    updateBreadcrumbs({
      name: t('apiTraffic'),
      link: pathname,
      changeType: 'rootLevel',
    })
  }, [pathname, updateBreadcrumbs, t])

  useResetOnOrgSwitch(setSearch)

  return (
    <>
      {/* Top Tabs */}
      <Tabs
        active={topActive}
        tabs={apiTrafficTopTabs(
          summaryHeaderData,
          chartData ?? [],
          summaryStatus === 'pending',
          filterProps,
          breakdownData,
          activeMetricsState,
          setActiveMetricsState,
        )}
        callout={(tabId: number) => {
          setTopActive(tabId)
        }}
      />

      {/* Bottom Tabs */}
      <Tabs
        active={bottomActive}
        tabs={apiTrafficBottomTabs(tableKeys)}
        callout={(tabId: number) => setBottomActive(tabId)}
      />
      <>
        <span>
          {count} {tableKeys[bottomActive]}s
        </span>
        <div className='flex pat-mt-4 pat-mb-4'>
          <SearchBar
            value={search}
            onChange={setSearch}
            placeholder={
              tableKeys[bottomActive] === c('marketplace')
                ? t('searchMarketplaces')
                : t('searchConnections')
            }
            minWidth={350}
          />
        </div>
      </>
      {/* Table */}
      <StandardTable
        config={apiTrafficTableConfig(tableKeys[bottomActive])}
        data={breakdownTableData}
        dataKey='main'
        getData={() => {
          return []
        }}
        hasData={breakdownStatus === 'success' && breakdownTableData.length > 0}
        sort={sort}
        sortBy={sortBy}
        successStatus={breakdownStatus === 'success'}
        tableId={tableIds[bottomActive]}
        hasMore={false}
        loading={breakdownStatus === 'pending'}
        noDataFields={{
          primaryText: t('noTabDataAvailable', {
            tabName: tableKeys[bottomActive],
          }),
        }}
      />
    </>
  )
}

export default ApiTraffic
