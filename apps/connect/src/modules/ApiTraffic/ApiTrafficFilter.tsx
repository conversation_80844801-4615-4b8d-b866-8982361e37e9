import { Filter, type FilterProps } from '@patterninc/react-ui'
import React, { useCallback, useEffect, useMemo, useState } from 'react'
import moment, { type Moment } from 'moment'

import sortOptions from '../../common/services/SortOptions'
import useFilteredConnections from '../../common/helpers/useFilteredConnections'
import { type OptionType } from '../../common/types'
import useAreOptionsLoaded from '../../common/services/UseAreOptionsLoaded'
import { c, useTranslate } from '../../common/helpers/TranslationHelpers'
import useResetOnOrgSwitch from '../../common/helpers/useResetOnOrgSwitch'

type FilterOptionsType = {
  marketplaces: OptionType[]
  connections: OptionType[]
  endpoints: OptionType[]
}

type ApiTrafficFilterState = {
  marketplaces: string[]
  connections: string[]
  endpoints: string[]
  dateRange: {
    start_date: string | Moment
    end_date: string | Moment
  }
}

export type ApiTrafficFilterProps = {
  filterOptions: FilterOptionsType
  filterState: ApiTrafficFilterState
  setFilterState: React.Dispatch<React.SetStateAction<ApiTrafficFilterState>>
}

export const defaultFilterState: ApiTrafficFilterState = {
  marketplaces: [],
  connections: [],
  endpoints: [],
  dateRange: {
    start_date: '',
    end_date: '',
  },
}

export const defaultFilterOptions: FilterOptionsType = {
  marketplaces: [],
  connections: [],
  endpoints: [],
}

export const ApiTrafficFilter = ({
  filterOptions,
  filterState,
  setFilterState,
}: ApiTrafficFilterProps): React.JSX.Element => {
  const { t } = useTranslate('apiTraffic'),
    [filterOptionCopy, setFilterOptionCopy] = useState(filterOptions),
    [filterStateCopy, setFilterStateCopy] = useState(filterState),
    marketplaceNames: string[] = useMemo(() => {
      return filterOptionCopy.marketplaces
        .filter((o) => filterStateCopy.marketplaces.includes(o.text))
        .map((m) => m.value)
    }, [filterStateCopy.marketplaces, filterOptionCopy.marketplaces]),
    connectionOptions = useFilteredConnections(marketplaceNames),
    [filtersCount, setFiltersCount] = useState(0),
    handleOutsideRange = (startDate: Moment) => {
      return (
        startDate.isBefore(moment().subtract(90, 'days')) ||
        startDate.isAfter(moment())
      )
    },
    calculateFiltersCount = useCallback(
      (
        marketplaces: string[],
        connections: string[],
        endpoints: string[],
        defaultFilterState: ApiTrafficFilterState,
      ) => {
        let count = 0
        if (connections.length > 0) count += 1
        if (filterState.dateRange !== defaultFilterState.dateRange) count += 1
        if (endpoints.length > 0) count += 1
        if (marketplaces.length > 0) count += 1
        return count
      },
      [filterState.dateRange],
    ),
    updateFilter = useCallback(() => {
      setFilterState(filterStateCopy)
      const count = calculateFiltersCount(
        filterStateCopy.marketplaces,
        filterStateCopy.connections,
        filterStateCopy.endpoints,
        defaultFilterState,
      )
      setFiltersCount(count)
    }, [calculateFiltersCount, filterStateCopy, setFilterState]),
    resetFilters = useCallback(() => {
      setFilterStateCopy(defaultFilterState)
      setFilterState(defaultFilterState)
      setFiltersCount(0)
    }, [setFilterState, setFilterStateCopy, setFiltersCount])

  useResetOnOrgSwitch(undefined, resetFilters)

  useEffect(() => {
    setFilterOptionCopy({
      marketplaces: sortOptions(filterOptions.marketplaces, 'text', true),
      connections: sortOptions(connectionOptions, 'text', true),
      endpoints: sortOptions(filterOptions.endpoints, 'text', true),
    })
  }, [filterOptions, connectionOptions])

  useEffect(() => {
    setFilterStateCopy(filterState)
    setFiltersCount(
      calculateFiltersCount(
        filterState.marketplaces,
        filterState.connections,
        filterState.endpoints,
        defaultFilterState,
      ),
    )
  }, [calculateFiltersCount, filterState])

  const allFilters = useMemo((): FilterProps<OptionType>['filterStates'] => {
    return {
      marketplace: {
        type: 'multi-select',
        selectedOptions: filterStateCopy.marketplaces.map((m) => ({
          text: m,
        })),
        options: filterOptionCopy.marketplaces,
        stateName: 'marketplaces',
        labelKey: 'text',
        formLabelProps: {
          label: c('marketplaces'),
        },
        selectPlaceholder: `-- ${c('selectMarketplaces')} --`,
      },
      connections: {
        type: 'multi-select',
        selectedOptions: filterStateCopy.connections.map((m) => ({
          text: m,
        })),
        options: filterOptionCopy.connections,
        stateName: 'connections',
        labelKey: 'text',
        formLabelProps: {
          label: c('connections'),
        },
        selectPlaceholder: `-- ${c('selectConnections')} --`,
      },
      endpoints: {
        type: 'multi-select',
        selectedOptions: filterStateCopy.endpoints.map((m) => ({
          text: m,
        })),
        options: filterOptionCopy.endpoints,
        stateName: 'endpoints',
        labelKey: 'text',
        formLabelProps: {
          label: c('endpoints'),
        },
        selectPlaceholder: `-- ${c('selectEndpoints')} --`,
      },

      dateRange: {
        type: 'dates',
        defaultValue: filterStateCopy.dateRange,
        labelText: t('selectDateRange'),
        stateName: 'dateRange',
        ...{ isOutsideRangeHandler: handleOutsideRange },
      },
    }
  }, [filterStateCopy, filterOptionCopy, t])

  const updateSelect = (...params: unknown[]) => {
    const text = params[0] as string,
      value = params[1] as string[]
    if (value) {
      setFilterStateCopy({
        ...filterStateCopy,
        [text]: value,
      })
    }
    if (text === 'marketplaces') {
      setFilterOptionCopy({
        marketplaces: filterOptionCopy.marketplaces,
        connections: filterOptionCopy.connections,
        endpoints: filterOptionCopy.endpoints,
      })
    }
  }

  const cancelCallout = () => {
    setFilterStateCopy(filterState)
  }

  return (
    <Filter
      loading={!useAreOptionsLoaded(allFilters)}
      filterStates={allFilters}
      filterCallout={updateFilter}
      resetCallout={resetFilters}
      onChangeCallout={updateSelect}
      cancelCallout={cancelCallout}
      appliedFilters={filtersCount}
    />
  )
}
