import React from 'react'
import {
  CartesianGrid,
  Line,
  LineChart,
  ResponsiveContainer,
  Tooltip,
  XAxis,
  YAxis,
} from 'recharts'

import GraphHelperService from '../../common/services/GraphHelperService'
import { type GraphData } from './types'
import { ApiTrafficGraphTooltip } from './ApiTrafficGraphTooltip'
import { c } from '../../common/helpers/TranslationHelpers'

type ApiTrafficGraphProps = {
  data: GraphData[]
  activeMetricsState: string[]
}

export const ApiTrafficGraph = ({
  data,
  activeMetricsState,
}: ApiTrafficGraphProps): React.JSX.Element => {
  return (
    <ResponsiveContainer>
      <LineChart data={data}>
        <CartesianGrid vertical={false} />
        <XAxis
          dataKey='date'
          axisLine={false}
          tickLine={false}
          type={'category'}
          tickFormatter={GraphHelperService.formatTick}
          tickMargin={5}
          style={{
            fontSize: '12px',
          }}
          interval={
            data.some((item) => item.date.split(' ')[1] !== '00:00:00')
              ? 24
              : 'equidistantPreserveStart'
          }
          height={45}
        />
        <YAxis
          axisLine={false}
          tickLine={false}
          tickCount={5}
          tickMargin={5}
          width={56}
          orientation='left'
          yAxisId='left'
          style={{
            fontSize: '12px',
          }}
          type='number'
          tickFormatter={(tick: number) => {
            return `${GraphHelperService.abbreviateNumber(Math.abs(tick))}`
          }}
        />
        <Tooltip
          content={(payloadData) => {
            return <ApiTrafficGraphTooltip payloadData={payloadData?.payload} />
          }}
        />
        <Line
          dot={false}
          yAxisId='left'
          type='linear'
          dataKey='success'
          stroke={'var(--chart-dark-2-green)'}
          strokeWidth={4}
          hide={!activeMetricsState.includes(c('successful'))}
        />
        <Line
          dot={false}
          yAxisId='left'
          type='linear'
          dataKey='fail'
          stroke={'var(--chart-light-2-red)'}
          strokeWidth={4}
          hide={!activeMetricsState.includes(c('failed'))}
        />
      </LineChart>
    </ResponsiveContainer>
  )
}
