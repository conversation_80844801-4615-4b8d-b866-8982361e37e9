import React from 'react'
import {
  type Payload,
  type ValueType,
} from 'recharts/types/component/DefaultTooltipContent'

import GraphHelperService from '../../common/services/GraphHelperService'
import styles from './tooltip.module.scss'
import { apiTrafficGraphTooltipLegends } from './api-traffic-tabs'

interface ApiTrafficGraphTooltipType {
  payloadData: Payload<ValueType, string | number>[] | undefined
}

export const ApiTrafficGraphTooltip = ({
  payloadData,
}: ApiTrafficGraphTooltipType): React.JSX.Element => {
  return (
    <div className={`multiline-tooltip ${styles.tooltipContainer}`}>
      <div className={'flex'}>
        <div className={`${styles.section} pat-p-2`}>
          <div className='top-section pat-mb-2'>
            <div className='fs-12 fw-bold'>
              {GraphHelperService.formatLabel(payloadData?.[0]?.payload?.date)}
            </div>
          </div>
          {Object.entries(apiTrafficGraphTooltipLegends()).map(
            ([key, { legendName, color }]) => {
              return (
                <>
                  <div className={`flex pat-p-2 ${styles.metricsContainer}`}>
                    <div className={`${color} ${styles.legends}`} />

                    <div
                      className={`fs-12 pat-ml-2 fw-regular ${styles.legendName}`}
                    >
                      {legendName}
                    </div>
                    <div className={`fs-12 pat-ml-2 ${styles.legendValue}`}>
                      {key === 'total_api_calls'
                        ? payloadData?.[0]?.payload['success'] +
                          payloadData?.[0]?.payload['fail']
                        : payloadData?.[0]?.payload[key]}
                    </div>
                  </div>
                </>
              )
            },
          )}
        </div>
      </div>
    </div>
  )
}
