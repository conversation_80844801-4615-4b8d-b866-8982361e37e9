import React from 'react'
import { type ConfigItemType, MdashCheck, notEmpty } from '@patterninc/react-ui'

import { type ApiType } from './types'
import { c, t } from '../../common/helpers/TranslationHelpers'

export const apiTrafficTableConfig = (
  tableKey: string,
): ConfigItemType<ApiType, Record<string, unknown>>[] => {
  function getPercentage(fraction: number, decimal: number) {
    return (fraction * 100.0).toFixed(decimal) + '%'
  }

  return [
    {
      cell: {
        children: (d: ApiType) => {
          return (
            <MdashCheck check={notEmpty(d.main)}>
              <span className={d.sortProp === 'main' ? 'fw-semi-bold' : ''}>
                {d.main}
              </span>
              <div className='fc-gray fs-10'>marketplace key</div>
            </MdashCheck>
          )
        },
      },
      label: tableKey,
      mainColumn: true,
      name: 'main',
    },
    {
      cell: {
        children: (d: ApiType) => {
          return (
            <MdashCheck check={notEmpty(d.api_calls)}>
              <span
                className={d.sortProp === 'api_calls' ? 'fw-semi-bold' : ''}
              >
                {d.api_calls}
              </span>
            </MdashCheck>
          )
        },
      },
      label: t('apiTraffic:apiCalls'),
      name: 'api_calls',
    },
    {
      cell: {
        children: (d: ApiType) => {
          return (
            <MdashCheck check={notEmpty(d.successful)}>
              <span
                className={
                  d.sortProp === 'successful_count' ||
                  d.sortProp === 'successful_percentage'
                    ? 'fw-semi-bold'
                    : ''
                }
              >
                {d.sortProp === 'successful_percentage'
                  ? getPercentage(d.successful / d.api_calls, 2)
                  : d.successful}
              </span>
              <div className='fc-gray fs-10'>
                {d.sortProp === 'successful_percentage'
                  ? d.successful
                  : getPercentage(d.successful / d.api_calls, 2)}
              </div>
            </MdashCheck>
          )
        },
      },
      label: c('successful'),
      name: 'successful',
      options: [
        { name: 'successful_count', label: c('count') },
        { name: 'successful_percentage', label: c('percentage') },
      ],
    },
    {
      cell: {
        children: (d: ApiType) => {
          return (
            <MdashCheck check={notEmpty(d.failed)}>
              <span
                className={
                  d.sortProp === 'failed_count' ||
                  d.sortProp === 'failed_percentage'
                    ? 'fw-semi-bold'
                    : ''
                }
              >
                {d.sortProp === 'failed_percentage'
                  ? getPercentage(d.failed / d.api_calls, 2)
                  : d.failed}
              </span>
              <div className='fc-gray fs-10'>
                {d.sortProp === 'failed_percentage'
                  ? d.failed
                  : getPercentage(d.failed / d.api_calls, 2)}
              </div>
            </MdashCheck>
          )
        },
      },
      label: c('failed'),
      name: 'failed',
      options: [
        { name: 'failed_count', label: c('count') },
        { name: 'failed_percentage', label: c('percentage') },
      ],
    },
    {
      cell: {
        children: (d: ApiType) => {
          return (
            <MdashCheck check={notEmpty(d.in_progress)}>
              <span
                className={
                  d.sortProp === 'in_progress_count' ||
                  d.sortProp === 'in_progress_percentage'
                    ? 'fw-semi-bold'
                    : ''
                }
              >
                {d.sortProp === 'in_progress_percentage'
                  ? getPercentage(d.in_progress / d.api_calls, 2)
                  : d.in_progress}
              </span>
              <div className='fc-gray fs-10'>
                {d.sortProp === 'in_progress_percentage'
                  ? d.in_progress
                  : getPercentage(d.in_progress / d.api_calls, 2)}
              </div>
            </MdashCheck>
          )
        },
      },
      label: t('apiTraffic:inProgress'),
      name: 'in_progress',
      options: [
        { name: 'in_progress_count', label: c('count') },
        { name: 'in_progress_percentage', label: c('percentage') },
      ],
    },
  ]
}
