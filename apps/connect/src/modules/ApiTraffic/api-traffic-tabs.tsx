import React from 'react'
import {
  CsvExport,
  HeaderMetric,
  HeaderMetricGroup,
  Icon,
} from '@patterninc/react-ui'
import { NavLink } from 'react-router-dom'

import {
  type BreakdownDataType,
  type ChartDataRow,
  type CsvDataType,
  type HeaderSummaryData,
  type Legends,
  type TabType,
} from './types'
import styles from './apiTraffic.module.scss'
import { ApiTrafficGraph } from './ApiTrafficGraph'
import {
  ApiTrafficFilter,
  type ApiTrafficFilterProps,
} from './ApiTrafficFilter'
import { c, t } from '../../common/helpers/TranslationHelpers'

export const apiTrafficTopTabs = (
  summaryHeaderData: HeaderSummaryData,
  chartData: ChartDataRow[],
  isLoading: boolean,
  filterProps: ApiTrafficFilterProps,
  breakdownData?: BreakdownDataType,
  activeMetricsState?: string[],
  setActiveMetricsState?: React.Dispatch<React.SetStateAction<string[]>>,
): TabType[] => {
  return [
    {
      content: getContent(
        summaryHeaderData,
        chartData,
        isLoading,
        filterProps,
        breakdownData,
        activeMetricsState,
        setActiveMetricsState,
      ),
      id: 0,
      tabName: t('apiTraffic:atAGlance'),
    },
    {
      content: getContent(
        summaryHeaderData,
        chartData,
        isLoading,
        filterProps,
        breakdownData,
        activeMetricsState,
        setActiveMetricsState,
      ),
      id: 1,
      tabName: c('inventory'),
    },
    {
      content: getContent(
        summaryHeaderData,
        chartData,
        isLoading,
        filterProps,
        breakdownData,
        activeMetricsState,
        setActiveMetricsState,
      ),
      id: 2,
      tabName: c('orders'),
    },
    {
      content: getContent(
        //Sending empty values as Advertising Tab is supposed to be empty for now.
        {
          total: 0,
          successful: 0,
          failed: 0,
          in_progress: 0,
          uploads: 0,
          downloads: 0,
        },
        [],
        isLoading,
        filterProps,
        {
          by_connection: {},
          by_marketplace: {},
        },
        activeMetricsState,
        setActiveMetricsState,
      ),
      id: 3,
      tabName: c('advertising'),
    },
  ]
}

const getContent = (
  summaryHeaderData: HeaderSummaryData,
  chartData: ChartDataRow[],
  isLoading: boolean,
  filterProps: ApiTrafficFilterProps,
  breakdownData?: BreakdownDataType,
  activeMetricsState?: string[],
  setActiveMetricsState?: React.Dispatch<React.SetStateAction<string[]>>,
) => {
  const getPercentage = (fraction: number, decimal: number) => {
      return (fraction * 100.0).toFixed(decimal) + '%'
    },
    formatCsvData = (type: keyof BreakdownDataType) => {
      const csvDataArray: CsvDataType[] = Object.entries({
        ...breakdownData?.[type],
      }).map(([marketplace, data]) => {
        const totalApiCalls = data.success + data.fail + data.in_progress
        const successPercent = getPercentage(data.success / totalApiCalls, 2)
        const failPercent = getPercentage(data.fail / totalApiCalls, 2)
        return {
          marketplace,
          total_api_calls: totalApiCalls,
          success_count: data.success,
          success_percent: successPercent,
          fail_count: data.fail,
          fail_percent: failPercent,
          in_progress: data.in_progress,
        }
      })
      return csvDataArray
    },
    getCsvName = (type: string): string => {
      return `${type}_${chartData[0]?.date.split(' ')[0]}_to_${
        chartData[chartData.length - 2]?.date.split(' ')[0]
      }`
    }

  return (
    <section className={`${styles.mainBoxMetric} box`}>
      <div className={`flex ${styles.topHeaderMetric}`}>
        <HeaderMetric
          metricValueClassName='fc-blue fc-bold'
          loading={isLoading}
          value={summaryHeaderData?.total || 0}
          title={t('apiTraffic:totalAPICalls')}
          fontSize='fs-22'
        />
        <div className='flex align-items-center pat-pr-4'>
          <div className='pat-mr-4'>
            <NavLink to='/support'>
              <Icon icon='info' color='dark-blue' />
            </NavLink>
          </div>
          <div className='pat-mr-4'>
            <CsvExport
              csvDownloadOptions={[
                {
                  callout: (element) => {
                    if (element instanceof HTMLAnchorElement) element.click()
                  },
                  csvData: formatCsvData('by_marketplace'),
                  csvName: getCsvName('jobs_by_marketplace_from'),
                  linkName: t('apiTraffic:byMarketplace'),
                },
                {
                  callout: (element) => {
                    if (element instanceof HTMLAnchorElement) element?.click()
                  },
                  csvData: formatCsvData('by_connection'),
                  csvName: getCsvName('jobs_by_connection_from'),
                  linkName: t('apiTraffic:byConnection'),
                },
              ]}
              initialDisplay={!isLoading}
              show
            />
          </div>
          <div>
            <div className='flex'>
              <div className={`pat-mx-4 ${styles.divider}`} />
              <ApiTrafficFilter {...filterProps} />
            </div>
          </div>
        </div>
      </div>
      <HeaderMetricGroup
        loading={isLoading}
        mainCallOut={(metrics) => {
          const { name, checked } = metrics
          if ([c('successful'), c('failed')].includes(name)) {
            setActiveMetricsState?.((prevState: string[]) => {
              const selectedMetrics = new Set(prevState)
              if (checked) {
                selectedMetrics.add(name)
              } else {
                selectedMetrics.delete(name)
              }
              if (selectedMetrics.size === 0) {
                selectedMetrics.add(c('successful'))
                selectedMetrics.add(c('failed'))
              }
              return Array.from(selectedMetrics)
            })
          }
        }}
        activeMetrics={activeMetricsState}
        colors={['chart-dark-2-green', 'chart-light-2-red']}
        data={[
          {
            formatType: 'number',
            title: c('successful'),
            value: summaryHeaderData.successful,
          },
          {
            formatType: 'number',
            title: c('failed'),
            value: summaryHeaderData.failed,
          },
          {
            formatType: 'number',
            title: t('apiTraffic:currentlyInProgress'),
            value: summaryHeaderData.in_progress,
          },
          {
            formatType: 'number',
            title: t('apiTraffic:totalUploads'),
            value: summaryHeaderData.uploads,
          },
          {
            formatType: 'number',
            title: t('apiTraffic:totalDownloads'),
            value: summaryHeaderData.downloads,
          },
        ]}
        showRadio
      />
      <div className={styles.graphStyle}>
        <ApiTrafficGraph
          data={chartData}
          activeMetricsState={activeMetricsState || []}
        />
      </div>
    </section>
  )
}

export const apiTrafficBottomTabs = (tableKeys: string[]): TabType[] => {
  return [
    {
      content: '',
      id: 0,
      tabName: tableKeys[0],
    },
    {
      content: '',
      id: 1,
      tabName: tableKeys[1],
    },
  ]
}

export const apiTrafficGraphTooltipLegends = (): Legends => {
  return {
    total_api_calls: {
      legendName: t('apiTraffic:totalAPICalls'),
      color: 'bgc-blue',
    },
    upload: { legendName: t('apiTraffic:totalUploads'), color: 'bgc-lavender' },
    download: { legendName: t('apiTraffic:totalDownloads'), color: 'bgc-pink' },
    success: { legendName: c('successful'), color: 'bgc-chart-dark-2-green' },
    fail: { legendName: c('failed'), color: 'bgc-chart-dark-2-red' },
  }
}
