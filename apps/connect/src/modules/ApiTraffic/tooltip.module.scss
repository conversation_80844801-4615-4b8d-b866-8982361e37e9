.tooltipContainer {
  box-sizing: border-box;
  background-color: white;
  height: auto;
  border-radius: 4px;
  border: 1px solid var(--light-gray);
  box-shadow: 0 3px 6px var(--light-gray); //values for box shadow is same as mentioned in the ui prototype.

  .section {
    min-width: 200px;
    .metricsContainer {
      align-items: center;
      width: 100%;
      .legends {
        height: 16px;
        border-radius: 4px;
        width: 4px;
      }
      .legendName {
        width: 55%;
      }
      .legendValue {
        width: 40%;
      }
    }
  }
}
