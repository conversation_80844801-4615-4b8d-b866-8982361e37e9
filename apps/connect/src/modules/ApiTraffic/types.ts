export type ApiType = {
  main: string
  api_calls: number
  successful: number
  failed: number
  in_progress: number
  sortProp?: string
}

export type TabType = {
  content: string | React.JSX.Element
  id: number
  tabName: string
}

export type HeaderSummaryData = {
  total: number
  successful: number
  failed: number
  in_progress: number
  uploads: number
  downloads: number
}

export type BreakdownDataRow = {
  success: number
  fail: number
  in_progress: number
}

export type BreakdownDataType = {
  by_marketplace: Record<string, BreakdownDataRow>
  by_connection: Record<string, BreakdownDataRow>
}

export type GraphData = {
  date: string
  success: number
  fail: number
  upload: number
  download: number
}

export type MarketConnectionType = {
  connection: string
  marketplace: string
  endpoint: string
}

export type Legend = {
  legendName: string
  color: string
}

export type Legends = {
  total_api_calls: Legend
  success: Legend
  fail: Legend
  download: Legend
  upload: Legend
}

export type MarketplaceType = {
  id: number
  name: string
  display_name: string
  destination_group: string
  destination_state_id: number
  destination_type_id: number
  image_url: string
  updated_at: string
  created_at: string
}

export type ChartDataRow = {
  date: string
  success: number
  fail: number
  upload: number
  download: number
}

export type SortObjType = {
  activeColumn: string
  direction: boolean
  lowerCaseParam?: boolean | undefined
}

export type CsvDataType = {
  marketplace: string
  total_api_calls: number
  success_count: number
  success_percent: string
  fail_count: number
  fail_percent: string
  in_progress: number
}
