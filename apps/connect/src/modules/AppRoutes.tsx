import React from 'react'
import { Navigate, Route, Routes } from 'react-router-dom'

import LoginRedirect from '../LoginRedirect'
import ApiTraffic from './ApiTraffic/ApiTraffic'
import ConnectionsRoutes from './Connections/ConnectionsRoutes'
import DestinationsRoutes from './Destinations/DestinationsRoutes'
import JobsRoutes from './Jobs/JobsRoutes'
import MappingsRoutes from './Mappings/MappingsRoutes'
import SupportRoutes from './Support/SupportRoutes'

const AppRoutes = (): React.JSX.Element => {
  return (
    <Routes>
      <Route path='/'>
        <Route path='connections/*' element={<ConnectionsRoutes />} />
        <Route path='support/*' element={<SupportRoutes />} />
        <Route path='jobs/*' element={<JobsRoutes />} />
        <Route path='mappings/*' element={<MappingsRoutes />} />
        <Route path='destinations/*' element={<DestinationsRoutes />} />
        <Route path='apitraffic/*' element={<ApiTraffic />} />
        <Route index element={<Navigate to='/connections' replace />} />
      </Route>
      <Route path='authenticate' element={<LoginRedirect />} />
      {/* Redirect to the home page when a route does not exist. */}
      <Route path='*' element={<Navigate to='/connections' replace />} />
    </Routes>
  )
}

export default AppRoutes
