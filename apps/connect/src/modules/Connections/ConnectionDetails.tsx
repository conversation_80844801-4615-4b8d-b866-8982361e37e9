import {
  <PERSON><PERSON><PERSON><PERSON>,
  not<PERSON><PERSON><PERSON>,
  <PERSON><PERSON>ooter,
  SideDrawer,
  type SortByProps,
  type SortColumnProps,
  StandardTable,
  toast,
} from '@patterninc/react-ui'
import React, {
  useCallback,
  useContext,
  useEffect,
  useMemo,
  useState,
} from 'react'
import { useLocation, useParams, useSearchParams } from 'react-router-dom'
import { useMutation, useQuery } from '@tanstack/react-query'
import { useNavigate } from 'react-router-dom'

import {
  connectionAccess,
  connectionDetails,
} from '../../common/services/ConnectAuthService'
import SecureAxios from '../../common/services/SecureAxios'
import { ThemeContext } from '../../Context'
import { ConnectionDetailsTableConfig } from './connection-details-table-config'
import {
  type ConnectionDetailType,
  type CredentialDefinition,
  type CredentialFieldValues,
  type FieldValue,
  type PatchConnectionParams,
} from './types'
import ConnectionDetailsForm from './ConnectionFormSteps/ConnectionDetailsForm'
import { PRIVILEGES, useHasPrivilege } from '../../context/auth-context'
import { type ApiErrorType } from '../../common/types'
import { c, useTranslate } from '../../common/helpers/TranslationHelpers'
import { useConnectionDetails } from '../../context/connection-details'
import { useConnectionForm } from '../../context/connection-form-context'
import { useSupportId } from './hooks/UseSupportId'

const ConnectionDetails = (): React.JSX.Element => {
  const { pathname, state } = useLocation(),
    { t } = useTranslate('connections'),
    { id } = useParams<{ id: string }>(),
    navigate = useNavigate(),
    tableId = 'connection_details_table',
    canEditConnections = useHasPrivilege(PRIVILEGES.EDIT_CONNECTION),
    canViewConnectionDetails = useHasPrivilege(
      PRIVILEGES.VIEW_CONNECTION_DETAILS,
    ),
    { updateBreadcrumbs } = useContext(ThemeContext),
    [sortBy, setSortBy] = useState<SortByProps>({ prop: 'key', flip: false }),
    // Table Params
    fetchNextPage = () => {
      return []
    },
    sort: SortColumnProps['sorter'] = (sortObj: {
      activeColumn: string
      direction: boolean
      lowerCaseParam?: boolean | undefined
    }) => {
      setSortBy({
        prop: sortObj.activeColumn,
        flip: sortObj.direction,
      })
    },
    [countries, setCountries] = useState<FieldValue>([]),
    [refreshOauth, setRefreshOauth] = useState(false),
    [edit, setEdit] = useState(false), // sidedrawer open and close
    [reset, setReset] = useState(false),
    // pulling table data from the backend
    {
      data: rawTableData,
      status,
      refetch,
    } = useQuery({
      queryKey: ['connection_details_table_data', id, canViewConnectionDetails],
      queryFn: async () => {
        if (typeof id === 'undefined') {
          toast({ type: 'error', message: c('missingCredentialIdInPath') })
          return
        }
        try {
          const result = await SecureAxios.get<Record<string, string | number>>(
            connectionAccess(id),
          )
          return result.data
        } catch (e: unknown) {
          const status = (e as { status?: number }).status
          const message =
            (e as { data?: { message?: string } }).data?.message ||
            (e as { data?: { error?: string } }).data?.error ||
            'Something went wrong. Please try again.'

          if ((status === 403 || status === 404) && state?.job_id) {
            toast({
              type: 'error',
              message: message,
            })
            setTimeout(() => {
              toast({
                type: 'info',
                message: c('redirectingToPreviousPage'),
              })
              navigate(`/jobs/${state.job_id}`)
            }, 3000)
          }
        }
      },
      enabled: canViewConnectionDetails,
    }),
    capitalizeKey = (key: string) => {
      return key
        .split('_')
        .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
        .join(' ')
    },
    // formatting table data
    tableData = useMemo(() => {
      const details: ConnectionDetailType[] = Object.entries(
        rawTableData ?? [],
      ).map((entry) => {
        return {
          key: capitalizeKey(entry[0]),
          value: `${entry[1]}`,
        }
      })
      return details
    }, [rawTableData]),
    // get marketplace/destination name (i.e. walmart-ads)
    {
      connectionDetails: connectionDetailsData,
      refetchConnectionDetails,
      locations,
    } = useConnectionDetails(),
    // Params for sidedrawer form
    [credentialFields, setCredentialFields] = useState<CredentialFieldValues>(
      {},
    ),
    [connectionName, setConnectionName] = useState<string>(''),
    [whiteGloveStatus, setWhiteGloveStatus] = useState<string>(),
    { supportId, setSupportId, resetSupportId, errorMessage } = useSupportId({
      destinationId: connectionDetailsData?.destination_id.toString() || '',
      initialSupportId: connectionDetailsData?.destination_key,
    }),
    [defaultConnectionName, setDefaultConnectionName] = useState(''),
    [credentialDefinition, setCredentialDefinition] =
      useState<CredentialDefinition>(),
    [isFilledOut, setIsFilledOut] = useState(false),
    { mutate: submitForm } = useMutation({
      mutationKey: ['edit connection', refreshOauth, setRefreshOauth, refetch],
      mutationFn: async (params: PatchConnectionParams) => {
        const result = await SecureAxios.patch(
          connectionDetails(id ?? ''),
          params,
        )
        return result
      },
      onSuccess: (result, params) => {
        setEdit(false)
        if (refreshOauth) {
          setRefreshOauth(false)
          toast({
            type: 'success',
            message: t('refreshingOauthForConnection', { connectionName }),
          })
          window.location.href = result.data.location
        } else {
          refetchConnectionDetails()
          navigate(`/connections/${params.name}/details`)
          refetch()
          toast({
            type: 'success',
            message: t('successfullyUpdatedConnection', { connectionName }),
          })
        }
      },
      onError: (e: ApiErrorType) => {
        toast({
          message: c('errorMessage', {
            message: `${e.status}, ${e.statusText}: ${e.data.error}`,
          }),
          type: 'error',
        })
      },
    }),
    {
      supportedCountries,
      marketplaceDisplayName: destinationName,
      setMarketplaceName: setDestinationName,
      rawCredentialDefinition,
    } = useConnectionForm(),
    handleSave = useCallback(() => {
      setReset(true)
      setDefaultConnectionName(connectionName)
      if (typeof credentialFields === 'undefined') {
        return
      }
      const contentParams = Object.keys(credentialFields).reduce(
        (obj, fieldName) => {
          if (
            credentialDefinition?.fields[fieldName]?.sensitive &&
            !notEmpty(credentialFields[fieldName])
          ) {
            return obj
          } else {
            return {
              ...obj,
              [fieldName]: credentialFields[fieldName],
            }
          }
        },
        {},
      )
      submitForm({
        display_name: connectionName,
        name: supportId,
        content: contentParams,
        white_glove_status: whiteGloveStatus,
        countries: refreshOauth
          ? undefined
          : Array.isArray(countries)
            ? supportedCountries
                .filter((c) => countries.findIndex((i) => i === c.name) >= 0)
                .map((c) => {
                  return { country_code: c.alpha_2_code }
                })
            : [],
        refresh: refreshOauth,
      })
    }, [
      supportId,
      credentialFields,
      connectionName,
      countries,
      supportedCountries,
      credentialDefinition?.fields,
      whiteGloveStatus,
      refreshOauth,
      submitForm,
    ]),
    [pageParams, setPageParams] = useSearchParams(),
    handleRefreshOauth = () => {
      setRefreshOauth(true)
    },
    handleCancel = () => {
      setCountries(
        locations.map((location) => {
          return (
            supportedCountries.find(
              (c) => c.alpha_2_code === location.country_code,
            )?.name || ''
          )
        }),
      )
      setConnectionName(defaultConnectionName)
      resetSupportId()
      setEdit(false)
    },
    footerButtons = useMemo(() => {
      const buttons: React.ComponentProps<typeof PageFooter>['rightSection'] =
        []
      if (credentialDefinition?.creation_method === 'OauthRedirect') {
        buttons.push({
          type: 'button',
          onClick: handleRefreshOauth,
          children: t('refreshOath'),
          styleType: 'secondary',
        })
      }
      buttons.push({
        type: 'button',
        onClick: () => {
          setEdit(true)
          setReset(!edit)
        },
        children: t('editConnection'),
        styleType: 'primary-green',
        disabled: !canEditConnections,
      })
      return buttons
    }, [canEditConnections, credentialDefinition?.creation_method, edit, t]),
    isValidRedirectUri = useCallback((uri: string) => {
      // validation for local, staging, and production environments
      // Allowed URIs:
      // - https://amplifi.io* (production)
      // - https://pattern.staging.amplifi.io* (staging)
      // - https://*.ngrok.io* (local)
      const validation =
        /^https:\/\/(((pattern\.staging\.)?amplifi\.io)|([a-zA-Z0-9-.]*?ngrok\.io))/
      return validation.test(uri)
    }, [])

  useEffect(() => {
    if (rawCredentialDefinition) {
      setCredentialDefinition(rawCredentialDefinition)
    }
  }, [rawCredentialDefinition])

  useEffect(() => {
    const isFromOauth = pageParams.get('from_oauth')
    if (notEmpty(isFromOauth) && connectionDetailsData?.credential_state_id) {
      if (isFromOauth === 'true') {
        if (connectionDetailsData?.credential_state_id === 1) {
          toast({
            type: 'success',
            message: t('connectionSuccessfulMessage', { destinationName }),
            config: { autoClose: 3000, toastId: 'credential_state' },
          })
          setPageParams((prev) => {
            prev.delete('from_oauth')
            return prev
          })
        } else {
          toast({
            type: 'error',
            message: t('yourAccountHasNotBeenAuthentiated', {
              destinationName,
            }),
            config: { autoClose: 3000, toastId: 'credential_state' },
          })
          setPageParams((prev) => {
            prev.delete('from_oauth')
            return prev
          })
        }
      }
    }
    const redirectUri = pageParams.get('redirect_uri')
    if (!notEmpty(redirectUri)) return
    const isRedirectValid = isValidRedirectUri(`${redirectUri}`)
    if (isRedirectValid) {
      toast({
        type: 'success',
        message: t('accountReadyForSyndication', { destinationName }),
        config: { autoClose: 3000, toastId: 'redirect_uri' },
      })
      setTimeout(() => {
        window.location.href = `${redirectUri}`
      }, 3000)
    } else {
      toast({
        type: 'error',
        message: t('invalidRedirectURI', { redirectUri }),
        config: { autoClose: 3000, toastId: 'invalid_redirect_uri' },
      })
      setPageParams({})
    }
  }, [
    pageParams,
    destinationName,
    connectionDetailsData?.credential_state_id,
    isValidRedirectUri,
    setPageParams,
    t,
  ])

  useEffect(() => {
    if (defaultConnectionName === '') {
      setDefaultConnectionName(connectionName)
    }
  }, [connectionName, defaultConnectionName, edit])

  useEffect(() => {
    updateBreadcrumbs({
      name: c('details'),
      link: pathname,
      changeType: 'tab',
    })
  }, [updateBreadcrumbs, pathname])

  // make the request to get the redirect uri
  useEffect(() => {
    if (refreshOauth) handleSave()
  }, [refreshOauth, handleSave])

  // isolate destination name and locations from connection details
  useEffect(() => {
    setDestinationName(connectionDetailsData?.destination_name ?? '')
    setSupportId(connectionDetailsData?.destination_key ?? '')
    setCountries(
      (connectionDetailsData?.locations || []).map((location) => {
        return (
          supportedCountries.find(
            (c) => c.alpha_2_code === location.country_code,
          )?.name || ''
        )
      }),
    )
    setWhiteGloveStatus(connectionDetailsData?.white_glove_status)
  }, [
    connectionDetailsData,
    supportedCountries,
    setSupportId,
    setDestinationName,
  ])

  useEffect(() => {
    setDefaultConnectionName(connectionDetailsData?.display_name ?? '')
    setConnectionName(connectionDetailsData?.display_name ?? '')
  }, [connectionDetailsData?.display_name])

  // update credential fields
  useEffect(() => {
    if (typeof credentialDefinition === 'undefined') {
      setCredentialFields({})
      return
    }
    const newData = Object.entries(rawTableData ?? {}).reduce(
      (obj, item) => {
        const displayName = item[0],
          fieldValue = item[1],
          credentialFields = credentialDefinition.fields,
          fieldName =
            Object.keys(credentialFields).find((fieldName) => {
              return credentialFields[fieldName].display_name === displayName
            }) || '',
          isSensitive = credentialFields[fieldName]?.sensitive
        return {
          ...obj,
          [fieldName]: isSensitive ? '' : fieldValue,
        }
      },
      Object.entries(credentialDefinition.fields).reduce((obj, item) => {
        const fieldName = item[0],
          type = item[1].field_type,
          value = item[1].value || ''

        return {
          ...obj,
          [fieldName]: type === 'hidden' ? value : undefined,
        }
      }, {}),
    )
    setCredentialFields(newData)
  }, [rawTableData, credentialDefinition, setCredentialFields, reset, id])
  return (
    <>
      <StandardTable
        data={tableData}
        customWidth='auto'
        dataKey='key'
        hasData={tableData.length > 0}
        loading={status === 'pending'}
        successStatus={status === 'success'}
        hasMore={false}
        getData={fetchNextPage}
        tableId={tableId}
        config={ConnectionDetailsTableConfig()}
        sort={sort}
        sortBy={sortBy}
        noDataFields={{
          primaryText: t('noConnectionDetailsAvailable'),
        }}
      />
      {canEditConnections && (
        <>
          <PageFooter rightSection={footerButtons} />
          <SideDrawer
            isOpen={edit}
            closeCallout={(isOutsideClick) => {
              if (isOutsideClick === true) {
                return setEdit(false)
              }
              handleCancel()
            }}
            headerContent={t('editConnection')}
            footerContent={
              <FormFooter
                cancelButtonProps={{ onClick: handleCancel }}
                saveButtonProps={{
                  children: c('saveChanges'),
                  disabled: !isFilledOut || status !== 'success',
                  onClick: handleSave,
                }}
              />
            }
          >
            <ConnectionDetailsForm
              credentialDefinition={credentialDefinition}
              setCredentialDefinition={setCredentialDefinition}
              credentialFields={credentialFields ?? {}}
              setCredentialFields={setCredentialFields}
              whiteGloveStatus={whiteGloveStatus}
              setWhiteGloveStatus={setWhiteGloveStatus}
              connectionName={connectionName}
              setConnectionName={setConnectionName}
              setIsComplete={setIsFilledOut}
              countries={countries}
              setCountries={setCountries}
              supportId={supportId}
              setSupportId={setSupportId}
              isMultiStep={false}
              supportIdErrorText={errorMessage}
            />
          </SideDrawer>
        </>
      )}
    </>
  )
}

export { ConnectionDetails }
