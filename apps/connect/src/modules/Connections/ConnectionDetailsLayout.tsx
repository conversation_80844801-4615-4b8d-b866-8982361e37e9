import {
  InformationPane,
  ListLoading,
  notEmpty,
  RouterTabs,
  Tag,
} from '@patterninc/react-ui'
import moment from 'moment'
import React, { useEffect, useState } from 'react'
import {
  Navigate,
  Route,
  Routes,
  useLocation,
  useNavigate,
  useParams,
} from 'react-router-dom'
import { Outlet } from 'react-router-dom'
import { NavLink } from 'react-router-dom'

import { getLocationString } from './ConnectionHelpers'
import { PRIVILEGES, useHasPrivilege } from '../../context/auth-context'
import { c, useTranslate } from '../../common/helpers/TranslationHelpers'
import { useConnectionDetails } from '../../context/connection-details'

const ConnectionDetailsLayout = (): React.JSX.Element => {
  const navigate = useNavigate(),
    { t } = useTranslate('connections'),
    { pathname } = useLocation(),
    { id } = useParams<{ id: string }>(),
    [location, setLocation] = useState('Region/Country'),
    [status, setStatus] = useState<'active' | 'inactive'>('active'),
    [marketplaceName, setMarketplaceName] = useState<string>(''),
    [connectedOn, setConnectedOn] = useState<string>(''),
    [connectedBy, setConnectedBy] = useState<string>(''),
    [marketplaceImageUrl, setmarketplaceImageUrl] = useState<string>(''),
    [whiteGloveContactEmail, setWhiteGloveContactEmail] = useState(''),
    [whiteGloveStatus, setWhiteGloveStatus] = useState(''),
    [supportId, setSupportId] = useState(''),
    [displayName, setDisplayName] = useState(''),
    canViewConnectionDetails = useHasPrivilege(
      PRIVILEGES.VIEW_CONNECTION_DETAILS,
    ),
    { connectionDetails, fetchingConnectionDetails } = useConnectionDetails()

  useEffect(() => {
    if (connectionDetails?.name && connectionDetails.name !== id) {
      return navigate(`/connections/${connectionDetails.name}/details`)
    }
    setSupportId(connectionDetails?.name || '')
    setDisplayName(connectionDetails?.display_name || '')
    setLocation(getLocationString(connectionDetails?.locations || [], 0))
    setStatus(
      connectionDetails?.credential_state_id === 1 ? 'active' : 'inactive',
    )
    setMarketplaceName(connectionDetails?.destination_display_name || '')
    setConnectedOn(connectionDetails?.created_at || '')
    setConnectedBy(`${connectionDetails?.user_full_name}`)
    setmarketplaceImageUrl(connectionDetails?.marketplace_image_url || '')
    setWhiteGloveContactEmail(
      connectionDetails?.white_glove_contact_email || '',
    )
    setWhiteGloveStatus(
      connectionDetails?.white_glove_contact_email
        ? connectionDetails?.white_glove_status
            .split('_')
            .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
            .join(' ')
        : '',
    )
  }, [connectionDetails, id, navigate])

  return (
    <div className='has-info-pane'>
      <div>
        {fetchingConnectionDetails ? (
          <ListLoading />
        ) : (
          <InformationPane
            header={{
              labelAndData: {
                label: <span className='fw-semi-bold'>{supportId}</span>,
                data: '',
                check: true,
              },
            }}
          >
            <InformationPane.ImageAndName
              product={{ name: marketplaceName }}
              imgUrl={marketplaceImageUrl}
            />
            <InformationPane.Divider />
            <InformationPane.Section
              data={[
                {
                  label: c('status'),
                  data: (
                    <div className='flex flex-row justify-content-around pat-pt-2'>
                      <Tag color={status === 'active' ? 'blue' : 'gray'}>
                        {status}
                      </Tag>
                    </div>
                  ),
                  check: true,
                },
                { label: t('displayName'), data: displayName, check: true },
                { label: t('accountRegion'), data: location, check: true },
                {
                  label: c('connectedOn'),
                  data: moment(connectedOn).format('MMM DD, YYYY HH:mm'),
                  check: true,
                },
                { label: t('connectedBy'), data: connectedBy, check: true },
              ]}
            />
            {whiteGloveContactEmail && (
              <>
                <InformationPane.Divider />
                <InformationPane.Section
                  data={[
                    {
                      check: notEmpty(whiteGloveContactEmail),
                      data: whiteGloveContactEmail,
                      label: t('whiteGloveContact'),
                    },
                    {
                      check: notEmpty(whiteGloveStatus),
                      data: whiteGloveStatus,
                      label: t('whiteGloveStatus'),
                    },
                  ]}
                />
              </>
            )}
          </InformationPane>
        )}
      </div>
      <div>
        <RouterTabs
          mobileConfig={[
            { label: c('details'), link: 'details' },
            { label: c('history'), link: 'history' },
            { label: c('settings'), link: 'settings' },
            { label: c('locations'), link: 'locations' },
          ]}
          navigate={navigate}
          currentPath={pathname}
        >
          {canViewConnectionDetails && (
            <NavLink to={`/connections/${id}/details`}>{c('details')}</NavLink>
          )}
          {canViewConnectionDetails && (
            <NavLink to={`/connections/${id}/settings`}>
              {c('settings')}
            </NavLink>
          )}
          {canViewConnectionDetails && (
            <NavLink to={`/connections/${id}/history`}>{c('history')}</NavLink>
          )}
          {canViewConnectionDetails && (
            <NavLink to={`/connections/${id}/locations`}>
              {c('locations')}
            </NavLink>
          )}
        </RouterTabs>
        <Outlet />
      </div>
      <Routes>
        <Route path='/' element={<Navigate to='details' replace />} />
      </Routes>
    </div>
  )
}

export default ConnectionDetailsLayout
