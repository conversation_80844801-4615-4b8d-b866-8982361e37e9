import { Filter } from '@patterninc/react-ui'
import React, {
  useCallback,
  useContext,
  useEffect,
  useMemo,
  useState,
} from 'react'

import { ConnectionsFilterContext } from '../../context/connections-filter-context'
import { c, useTranslate } from '../../common/helpers/TranslationHelpers'
import useResetOnOrgSwitch from '../../common/helpers/useResetOnOrgSwitch'

type ConnectionFilters = {
  status: {
    id: number
    status_name: 'Active' | 'Inactive' | 'All'
    value: 'active' | 'inactive' | 'all'
  }
  account_region: string[]
  account_country: string[]
  marketplace: string[]
  created_by: string[]
}

type ConnectionFilterProps = {
  filtersCount: number
  setFiltersCount: React.Dispatch<React.SetStateAction<number>>
  filterState: ConnectionFilters
  filterOptions: FilterOptions
  update: (newFilterState: ConnectionFilters) => void
  areFilterOptionsLoading: boolean
}

export type FilterOptions = {
  country: { id: number; country_name: string; value: string }[]
  region: { id: number; region_name: string; value: string }[]
  status: { id: number; status_name: string; value: string }[]
  marketplace: { id: number; marketplace_name: string; value: string }[]
  created_by: { id: number; value: string; text: string }[]
}

const emptyStatusFilter: ConnectionFilters['status'] = {
  id: 0,
  status_name: 'All',
  value: 'all',
}

type FilterStatesType = React.ComponentProps<typeof Filter>['filterStates']

const ConnectionFilter = ({
  filtersCount,
  setFiltersCount,
  filterState,
  filterOptions,
  update,
  areFilterOptionsLoading,
}: ConnectionFilterProps): React.JSX.Element => {
  const { setAppliedFilters } = useContext(ConnectionsFilterContext),
    { t } = useTranslate('connections'),
    [filterStateCopy, setFilterStateCopy] = useState(filterState),
    [marketplaceOptions, setMarketplaceOptions] = useState(
      filterOptions.marketplace,
    ),
    [regionOptions, setRegionOptions] = useState(filterOptions.region),
    [countryOptions, setCountryOptions] = useState(filterOptions.country),
    [connectedByOptions, setConnectedByOptions] = useState(
      filterOptions.created_by,
    ),
    initialFilters = filterState,
    filterCallout = () => {
      setFiltersCount(filtersCount)
      update(filterStateCopy)
      setAppliedFilters(filterStateCopy)
    },
    resetCallout = useCallback(() => {
      setFiltersCount(0)
      setFilterStateCopy({
        status: { id: 0, status_name: 'All', value: 'all' },
        account_region: [],
        account_country: [],
        marketplace: [],
        created_by: [],
      })
      update({
        status: emptyStatusFilter,
        account_region: [],
        account_country: [],
        marketplace: [],
        created_by: [],
      })
      setAppliedFilters(null)
    }, [setAppliedFilters, setFiltersCount, update]),
    onChangeCallout = (...params: unknown[]) => {
      const key = params[0] as string
      const value = params[1]
      setFilterStateCopy({
        ...filterStateCopy,
        [key]: value,
      })
    },
    cancelCallout = () => {
      setFilterStateCopy(initialFilters)
    },
    allFilters = useMemo((): FilterStatesType => {
      return {
        marketplace: {
          formLabelProps: {
            label: c('marketplace'),
          },
          labelKey: 'marketplace_name',
          options: marketplaceOptions,
          selectedOptions: filterStateCopy.marketplace.map((m) => ({
            marketplace_name: m,
          })),
          selectPlaceholder: t('selectMarketplaces'),
          stateName: 'marketplace',
          type: 'multi-select',
        },
        status: {
          defaultValue: filterStateCopy.status,
          labelText: c('status'),
          optionKeyName: 'status_name',
          options: filterOptions.status,
          stateName: 'status',
          type: 'select',
        },
        account_region: {
          formLabelProps: {
            label: t('accountRegion'),
          },
          labelKey: 'region_name',
          options: regionOptions,
          selectedOptions: filterStateCopy.account_region.map((m) => ({
            region_name: m,
          })),
          selectPlaceholder: t('selectAccountRegions'),
          stateName: 'account_region',
          type: 'multi-select',
          disabled: regionOptions.length === 0,
        },
        account_country: {
          formLabelProps: {
            label: t('accountCountry'),
          },
          labelKey: 'country_name',
          options: countryOptions,
          selectedOptions: filterStateCopy.account_country.map((m) => ({
            country_name: m,
          })),
          selectPlaceholder: t('selectAccountCountries'),
          stateName: 'account_country',
          type: 'multi-select',
        },
        created_by: {
          type: 'multi-select',
          selectedOptions: filterStateCopy.created_by.map((c) => ({
            text: c,
          })),
          options: connectedByOptions,
          stateName: 'created_by',
          labelKey: 'text',
          formLabelProps: {
            label: c('createdBy'),
          },
          selectPlaceholder: t('selectUsers'),
        },
      }
    }, [
      countryOptions,
      filterOptions.status,
      filterStateCopy,
      marketplaceOptions,
      regionOptions,
      connectedByOptions,
      t,
    ])

  useResetOnOrgSwitch(undefined, resetCallout)

  useEffect(() => {
    setRegionOptions(filterOptions.region)
    setCountryOptions(filterOptions.country)
    setMarketplaceOptions(filterOptions.marketplace)
    setConnectedByOptions(filterOptions.created_by)
  }, [filterOptions])

  return (
    <>
      <Filter
        loading={areFilterOptionsLoading}
        filterStates={allFilters}
        filterCallout={filterCallout}
        resetCallout={resetCallout}
        onChangeCallout={onChangeCallout}
        cancelCallout={cancelCallout}
        appliedFilters={filtersCount}
      />
    </>
  )
}

export { ConnectionFilter, ConnectionFilters, emptyStatusFilter }
