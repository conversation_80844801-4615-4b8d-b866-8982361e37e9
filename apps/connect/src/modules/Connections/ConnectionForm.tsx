import {
  <PERSON>Footer,
  notE<PERSON><PERSON>,
  <PERSON>Footer,
  SideDrawer,
  toast,
} from '@patterninc/react-ui'
import React, { useCallback, useMemo, useState } from 'react'
import { useMutation, useQuery } from '@tanstack/react-query'
import {
  createSearchParams,
  useNavigate,
  useSearchParams,
} from 'react-router-dom'

import MarketplaceForm from './ConnectionFormSteps/MarketplaceForm'
import ConnectionDetailsForm from './ConnectionFormSteps/ConnectionDetailsForm'
import {
  type CountryInfoType,
  type CredentialDefinition,
  type CredentialFieldValues,
  type FieldValue,
  type MarketplaceFormDetailType,
  type MarketplaceFormOptionType,
  type NewConnectionPostParams,
  type RegionInfoType,
  type SelectedMarketplaceFormOptionsType,
} from './types'
import SecureAxios from '../../common/services/SecureAxios'
import {
  connections,
  countriesApi,
  regionsApi,
} from '../../common/services/ConnectAuthService'
import { useOrg } from '../../context/org-context'
import { type ApiErrorType } from '../../common/types'
import { c, useTranslate } from '../../common/helpers/TranslationHelpers'
import { useConnectionForm } from '../../context/connection-form-context'
import { useSupportId } from './hooks/UseSupportId'

const ConnectionForm = (): React.JSX.Element => {
  const { org } = useOrg(),
    { t } = useTranslate('connections'),
    navigate = useNavigate(),
    [pageParams] = useSearchParams(),
    [credentialDefinition, setCredentialDefinition] =
      useState<CredentialDefinition>(),
    [credentialFields, setCredentialFields] = useState<CredentialFieldValues>(
      {},
    ),
    [whiteGloveStatus, setWhiteGloveStatus] = useState<string>(),
    [selectedCountries, setSelectedCountries] = useState<FieldValue>([]),
    [connectionName, setConnectionName] = useState(''),
    [credentialState, setCredentialState] = useState<string | undefined>('1'),
    [marketplaceFormOptions, setMarketplaceFormOptions] =
      useState<MarketplaceFormOptionType>({
        search: '',
        marketplaces: [],
        regions: [],
        countries: [],
        categories: [],
      }),
    defaultMarketplaceFormDetails: MarketplaceFormDetailType = {
      search: '',
      region_ids: [],
      country_ids: [],
      category_ids: [],
    },
    [marketplaceFormDetails, setMarketplaceFormDetails] =
      useState<MarketplaceFormDetailType>(defaultMarketplaceFormDetails),
    defaultSelectedMarketplaceFormOptions: SelectedMarketplaceFormOptionsType =
      {
        regions: [],
        countries: [],
        categories: [],
      },
    [selectedMarketplaceFormOptions, setSelectedMarketplaceFormOptions] =
      useState<SelectedMarketplaceFormOptionsType>(
        defaultSelectedMarketplaceFormOptions,
      ),
    [searchParams, setSearchParams] = useSearchParams(),
    {
      resetDestinationType,
      marketplaceId,
      setMarketplaceId,
      setMarketplaceName,
      setMarketplaceDisplayName,
      currentStep,
      isFormOpen,
      setIsFormOpen,
      setCurrentStep,
    } = useConnectionForm(),
    {
      supportId,
      setSupportId,
      resetSupportId,
      errorMessage: supportIdErrorMessage,
    } = useSupportId({
      destinationId: marketplaceId || '',
      needSuggestion: true,
    }),
    [isStepTwoComplete, setIsStepTwoComplete] = useState(false),
    [isPreSales, setIsPreSales] = useState(false),
    resetForm = () => {
      setCurrentStep(1)
      setMarketplaceFormDetails(defaultMarketplaceFormDetails)
      setSelectedMarketplaceFormOptions(defaultSelectedMarketplaceFormOptions)
      setMarketplaceId(null)
      setMarketplaceDisplayName('')
      setMarketplaceName('')
      setConnectionName('')
      setCredentialFields({})
      resetSupportId()
      setSelectedCountries([])
      setCredentialState('1')
      resetDestinationType()
    },
    submitForm = useMutation({
      mutationFn: (params: NewConnectionPostParams) =>
        SecureAxios.post(connections, params),
      onSuccess: (result) => {
        toast({
          message: t('connectionCreatedSuccessfully'),
          type: 'success',
          config: {
            autoClose: 5000,
            toastId: 'connection-created',
          },
        })
        setIsFormOpen(!isFormOpen)
        const navigateTo = supportId || result.data.name
        resetForm()
        if (notEmpty(result.data.location)) {
          window.location.href = result.data.location
        } else if (notEmpty(result.data.link)) {
          navigator.clipboard
            .writeText(result.data.link)
            .then(() => {
              toast({
                message: t('linkCopied', { link: result.data.link }),
                type: 'success',
                config: {
                  autoClose: 5000,
                  toastId: 'link-copied',
                },
              })
            })
            .catch(() => {
              const textArea = document.createElement('textarea')
              textArea.value = result.data.link
              document.body.appendChild(textArea)
              textArea.select()
              document.execCommand('copy')
              document.body.removeChild(textArea)
              toast({
                message: t('linkCopied', { link: result.data.link }),
                type: 'success',
                config: {
                  autoClose: 5000,
                  toastId: 'link-copied',
                },
              })
            })
        } else {
          navigateToDetails(navigateTo)
        }
      },
      onError: (error: ApiErrorType) => {
        toast({
          message: c('errorMessage', {
            message: `${error.status}, ${error.statusText}: ${error.data.error}`,
          }),
          type: 'error',
        })
      },
    }),
    navigateToDetails = useCallback(
      (connectionName: string) => {
        const navigateTo = `/connections/${connectionName}/details`
        const search = createSearchParams(
          pageParams.get('redirect_uri')
            ? { redirect_uri: `${pageParams.get('redirect_uri')}` }
            : undefined,
        ).toString()
        navigate({
          pathname: navigateTo,
          search: search,
        })
      },
      [pageParams, navigate],
    ),
    formSteps = useMemo(() => {
      return [t('selectAMarketplace'), t('connectionSettings')]
    }, [t]),
    handleCancel = () => {
      searchParams.delete('create_connection')
      searchParams.delete('marketplace_code')
      setSearchParams(searchParams)
      setIsFormOpen(!isFormOpen)
      resetForm()
    },
    { data: regionsData, status: regionsDataStatus } = useQuery({
      queryKey: ['regionsKey'],
      queryFn: async ({ signal }) => {
        const response = await SecureAxios.get(regionsApi, { signal })
        return response.data
      },
    }),
    regions: RegionInfoType[] = useMemo(
      () => (regionsDataStatus === 'success' ? regionsData : []),
      [regionsData, regionsDataStatus],
    ),
    { data: countriesData, status: countriesDataStatus } = useQuery({
      queryKey: ['countriesKey', selectedMarketplaceFormOptions.regions],
      queryFn: async ({ signal }) => {
        const response = await SecureAxios.get(countriesApi, {
          signal,
          params: {
            region: selectedMarketplaceFormOptions.regions.map(
              (r) => r.abbreviation,
            ),
          },
        })
        return response.data
      },
    }),
    countries: CountryInfoType[] = useMemo(
      () => (countriesDataStatus === 'success' ? countriesData : []),
      [countriesData, countriesDataStatus],
    )
  return (
    <>
      <PageFooter
        rightSection={[
          {
            children: t('connections:addNewConnection'),
            onClick: () => {
              setIsFormOpen(!isFormOpen)
            },
            type: 'button',
            styleType: 'primary-green',
          },
        ]}
      />
      <SideDrawer
        isOpen={isFormOpen}
        closeCallout={(isOutsideClick) => {
          if (isOutsideClick === true) {
            return setIsFormOpen(!isFormOpen)
          }
          handleCancel()
        }}
        footerContent={
          <FormFooter
            cancelButtonProps={{ onClick: handleCancel }}
            resetButtonProps={{
              onClick: resetForm,
            }}
            saveButtonProps={{
              children:
                currentStep !== formSteps.length
                  ? 'next'
                  : isPreSales
                    ? t('generateLink')
                    : t('connect'),
              disabled:
                (currentStep === 1 && marketplaceId === null) ||
                (currentStep === 2 && !isStepTwoComplete),
              onClick: () => {
                if (currentStep !== formSteps.length) {
                  setCurrentStep(currentStep + 1)
                } else {
                  searchParams.delete('create_connection')
                  searchParams.delete('marketplace_code')
                  setSearchParams(searchParams)
                  submitForm.mutate({
                    display_name: connectionName,
                    name: supportId,
                    content_version: 1,
                    redirect_uri: pageParams.get('redirect_uri') || undefined,
                    organization: org.code,
                    credential_state_id: Number(credentialState),
                    white_glove_status: whiteGloveStatus,
                    destination_id: Number(marketplaceId),
                    countries: Array.isArray(selectedCountries)
                      ? countries
                          .filter(
                            (c) =>
                              selectedCountries.findIndex(
                                (i) => i === c.name,
                              ) >= 0,
                          )
                          .map((c) => {
                            return { country_code: c.alpha_2_code }
                          })
                      : [{ country_code: 'US' }],
                    regions: [],
                    content: credentialFields,
                    pre_sales_onboarding: isPreSales ? true : undefined,
                  })
                }
              },
            }}
          />
        }
        headerContent={t('addNewConnection')}
        stepperProps={{
          callout: () => {
            if (currentStep === 2) {
              setConnectionName('')
              setSelectedCountries([])
            }
            setCurrentStep(currentStep - 1)
          },
          currentStep: currentStep,
          steps: formSteps,
        }}
      >
        <div>
          {currentStep === 1 && (
            <MarketplaceForm
              marketplaceFormOptions={marketplaceFormOptions}
              setMarketplaceFormOptions={setMarketplaceFormOptions}
              marketplaceFormDetails={marketplaceFormDetails}
              setMarketplaceFormDetails={setMarketplaceFormDetails}
              setSelectedMarketplaceFormOptions={
                setSelectedMarketplaceFormOptions
              }
              regions={regions}
              countries={countries}
            />
          )}
          {currentStep === 2 && (
            <ConnectionDetailsForm
              isPreSales={isPreSales}
              setIsPreSales={setIsPreSales}
              supportId={supportId}
              setSupportId={setSupportId}
              credentialDefinition={credentialDefinition}
              setCredentialDefinition={setCredentialDefinition}
              credentialFields={credentialFields}
              setCredentialFields={setCredentialFields}
              connectionName={connectionName}
              setConnectionName={setConnectionName}
              whiteGloveStatus={whiteGloveStatus}
              setWhiteGloveStatus={setWhiteGloveStatus}
              setIsComplete={setIsStepTwoComplete}
              countries={selectedCountries}
              setCountries={setSelectedCountries}
              isMultiStep={true}
              setCredentialState={setCredentialState}
              credentialState={credentialState}
              supportIdErrorText={supportIdErrorMessage}
            />
          )}
        </div>
      </SideDrawer>
    </>
  )
}

export default ConnectionForm
