import React, { useCallback, useEffect, useRef } from 'react'
import { ListLoading, notEmpty, TextInput } from '@patterninc/react-ui'

import { MAX_SUPPORT_ID_LENGTH } from '../../../common/constants'
import {
  type CredentialDefinition,
  type CredentialFieldValues,
  type FieldValue,
} from '../types'
import ConnectionFieldInput from './ConnectionFieldInput'
import { c, useTranslate } from '../../../common/helpers/TranslationHelpers'
import { useConnectionForm } from '../../../context/connection-form-context'

type ConnectionDetailsProps = {
  credentialDefinition?: CredentialDefinition
  setCredentialDefinition: (
    definition: CredentialDefinition | undefined,
  ) => void
  credentialFields: CredentialFieldValues
  setCredentialFields: React.Dispatch<
    React.SetStateAction<CredentialFieldValues>
  >
  whiteGloveStatus?: string
  setWhiteGloveStatus: React.Dispatch<React.SetStateAction<string | undefined>>
  connectionName: string
  setConnectionName: (name: string) => void
  countries: FieldValue
  setCountries: (selected: FieldValue) => void
  supportId: string
  setSupportId: (supportId: string) => void
  supportIdErrorText?: string
  setIsComplete: (val: boolean) => void
  isMultiStep: boolean
  setCredentialState?: React.Dispatch<React.SetStateAction<string | undefined>>
  credentialState?: string | undefined
  isPreSales?: boolean
  setIsPreSales?: React.Dispatch<React.SetStateAction<boolean>>
}

const ConnectionDetailsForm = ({
  credentialDefinition,
  setCredentialDefinition,
  credentialFields,
  setCredentialFields,
  whiteGloveStatus: whiteGloveStatus,
  setWhiteGloveStatus: setWhiteGloveStatus,
  connectionName,
  setConnectionName,
  countries,
  setCountries,
  supportId,
  setSupportId,
  supportIdErrorText,
  setIsComplete,
  isMultiStep,
  isPreSales,
  setIsPreSales,
}: ConnectionDetailsProps): React.JSX.Element => {
  const { t } = useTranslate('connections'),
    // Use the connection form context
    {
      destinationIsWhiteGloveSupported,
      isWhiteGloveLoading,
      rawCredentialDefinition,
      credentialDefinitionStatus,
      supportedCountries,
      hasWhiteGloveAccess,
      marketplaceDisplayName,
    } = useConnectionForm(),
    initialConnectionWhiteGloveStatus = useRef<string | undefined>(
      whiteGloveStatus,
    ),
    setCredentialField = useCallback(
      (fieldName: string, value: FieldValue) => {
        setCredentialFields((prev) => ({ ...prev, [fieldName]: value }))
      },
      [setCredentialFields],
    ),
    fieldInputs = useCallback(
      (
        credentialDefinition: CredentialDefinition | undefined,
      ): React.JSX.Element[] => {
        if (credentialDefinitionStatus === 'pending' || isWhiteGloveLoading) {
          return [<ListLoading key='loading' />]
        }
        if (credentialDefinition === undefined) {
          return [
            <span key='error' className='fw-semi-bold fc-red'>
              {t('errorCredentialDefinitionNotImplmementedForMarketPlace', {
                marketplaceName: marketplaceDisplayName,
              })}
            </span>,
          ]
        }

        const inputs: React.JSX.Element[] = []

        inputs.push(
          <ConnectionFieldInput
            connectionField={{
              field_type: 'text',
              size: 30,
              validation: 'string',
              display_name: t('nameYourConnection'),
              display_description: t('aCustomizedNameForThisAccount'),
            }}
            fieldName='name'
            key='name'
            setValue={(name) => {
              setConnectionName(`${name}`)
            }}
            value={connectionName}
          />,
        )

        inputs.push(
          <TextInput
            labelText={c('supportId')}
            labelTooltip={{
              tooltipContent: t('aUniqueIdentifierForThisAccount'),
            }}
            value={supportId}
            type='text'
            callout={(_, value) => {
              setSupportId(value.toString())
            }}
            hasError={notEmpty(supportIdErrorText)}
            errorText={supportIdErrorText}
            classType={notEmpty(supportIdErrorText) ? 'error' : ''}
            debounce={250}
            key='support id'
            maxLength={MAX_SUPPORT_ID_LENGTH}
          />,
        )

        if (!credentialDefinition.custom_regions)
          inputs.push(
            <ConnectionFieldInput
              connectionField={{
                field_type: 'multiselect',
                size: 30,
                validation: 'string',
                display_name: t('accountRegions'),
                display_description: t(
                  'aListOfTheCountriesThatThisAccountWillBeUsedToSellTo',
                ),
                multiOptions: supportedCountries.map((c) => c.name),
              }}
              fieldName='countries'
              value={countries}
              setValue={(selected) => {
                setCountries(selected)
              }}
              key='countries'
            />,
          )

        Object.keys(credentialDefinition['fields']).forEach((key) => {
          const field = credentialDefinition['fields'][key]

          // calculate visibility criteria for conditional credential definitions
          const visibleConditions = Object.entries(field.visible || {})
          const isVisible = visibleConditions.every(([key, value]) => {
            return credentialFields[key] === value
          })

          // show inputs fields only if
          //   - field is not hidden
          //   - field is not readonly
          //   - field is create_only and form is create connection(not edit connection)
          //   - field meets visibility criteria
          if (
            field.field_type !== 'hidden' &&
            field.field_type !== 'readonly' &&
            isVisible &&
            (!field.create_only || isMultiStep)
          ) {
            inputs.push(
              <ConnectionFieldInput
                connectionField={field}
                fieldName={key}
                value={credentialFields[key]}
                setValue={(value) => {
                  setCredentialField(key, value)
                }}
                key={key}
                credentialFields={credentialFields}
                setCredentialField={setCredentialField}
              />,
            )
          }
        })

        if (hasWhiteGloveAccess && destinationIsWhiteGloveSupported) {
          const setWgActive = ['active', 'active_pending'].includes(
            whiteGloveStatus || '',
          )
          const isWgActive = ['active', 'active_pending'].includes(
            initialConnectionWhiteGloveStatus.current || '',
          )
          inputs.push(
            <ConnectionFieldInput
              connectionField={{
                field_type: 'boolean',
                size: 30,
                validation: 'boolean',
                display_name: t('whiteGlove'),
                display_description: t('whiteGloveServiceDescription'),
                message_on_selection: t(
                  'enablingThisWillEnrollYouInPatternsWhiteGloveService',
                ),
              }}
              fieldName='white_glove'
              value={setWgActive}
              disabled={['active_pending', 'inactive_pending'].includes(
                initialConnectionWhiteGloveStatus.current || '',
              )}
              setValue={(value) => {
                if (value && whiteGloveStatus === undefined) {
                  setWhiteGloveStatus('active_pending')
                } else if (!value && isWgActive) {
                  setWhiteGloveStatus('inactive_pending')
                } else if (value && whiteGloveStatus === 'inactive_pending') {
                  setWhiteGloveStatus('active')
                } else {
                  setWhiteGloveStatus(
                    setWgActive ? 'inactive_pending' : 'active_pending',
                  )
                }
              }}
              key='white_glove'
              confirmation={
                isWgActive && !isMultiStep
                  ? {
                      body: t(
                        'disablingThisWillDeleteYourWhiteGloveCredentials',
                      ),
                      header: t('areYouSure'),
                      type: 'red',
                    }
                  : undefined
              }
            />,
          )
        }

        if (
          credentialDefinition.creation_method === 'OauthRedirect' &&
          setIsPreSales &&
          isPreSales !== undefined
        ) {
          inputs.push(
            <ConnectionFieldInput
              connectionField={{
                field_type: 'boolean',
                size: 30,
                validation: 'boolean',
                display_name: t('generateLink'),
                display_description: t('generateLinkDescription'),
              }}
              fieldName='generate_link'
              value={isPreSales}
              setValue={(value) => {
                setIsPreSales(!!value)
              }}
              key='generate_link'
            />,
          )
        }

        return inputs
      },
      [
        isMultiStep,
        credentialDefinitionStatus,
        isWhiteGloveLoading,
        t,
        connectionName,
        supportedCountries,
        countries,
        hasWhiteGloveAccess,
        destinationIsWhiteGloveSupported,
        marketplaceDisplayName,
        setConnectionName,
        supportId,
        setSupportId,
        supportIdErrorText,
        setCountries,
        credentialFields,
        setCredentialField,
        whiteGloveStatus,
        setWhiteGloveStatus,
        isPreSales,
        setIsPreSales,
      ],
    )

  useEffect(() => {
    const invisibleFieldWithValue = Object.keys(credentialFields).find(
      (field) => {
        const fieldDef = credentialDefinition?.fields[field]
        return (
          fieldDef?.visible &&
          credentialFields[field] &&
          !Object.entries(fieldDef.visible).every(([key, value]) => {
            return credentialFields[key] === value
          })
        )
      },
    )

    if (invisibleFieldWithValue) {
      setCredentialField(invisibleFieldWithValue, undefined)
    }
  }, [credentialFields, setCredentialField, credentialDefinition])

  useEffect(() => {
    const formFields = credentialFields ?? {},
      fieldDefintions = credentialDefinition?.fields ?? {},
      foundMissingField = Object.keys(credentialDefinition?.fields || {}).find(
        (field) =>
          !notEmpty(formFields[field]) && // empty
          !(fieldDefintions[field]?.sensitive && !isMultiStep) && // not sensitive
          fieldDefintions[field].field_type !== 'readonly' && // not readonly
          !fieldDefintions[field]?.optional && // not optional
          Object.entries(fieldDefintions[field]?.visible || {}).every(
            ([key, value]) => {
              return formFields[key] === value
            },
          ), // meets visibility criteria for conditional credential definitions
      )
    setIsComplete(
      !foundMissingField &&
        notEmpty(connectionName) &&
        notEmpty(supportId) &&
        !notEmpty(supportIdErrorText),
    )
  }, [
    credentialDefinition?.fields,
    isMultiStep,
    credentialFields,
    connectionName,
    supportId,
    setIsComplete,
    supportIdErrorText,
  ])

  useEffect(() => {
    setCredentialDefinition(rawCredentialDefinition || undefined)
    if (rawCredentialDefinition !== undefined) {
      const field_names = Object.keys(rawCredentialDefinition.fields)
      field_names.forEach((field_name) => {
        if (
          rawCredentialDefinition.fields[field_name].field_type === 'hidden'
        ) {
          setCredentialField(
            field_name,
            rawCredentialDefinition.fields[field_name].value,
          )
        }
      })
    }
  }, [rawCredentialDefinition, setCredentialDefinition, setCredentialField])

  return (
    <div className='flex flex-direction-column pat-gap-4'>
      {fieldInputs(credentialDefinition)}
    </div>
  )
}

export default ConnectionDetailsForm
