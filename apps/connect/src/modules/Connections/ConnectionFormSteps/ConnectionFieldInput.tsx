import React, { type ComponentProps, useEffect, useState } from 'react'
import { Picker, Select, Switch, TextInput } from '@patterninc/react-ui'

import {
  type CredentialField,
  type CredentialFieldOption,
  type CredentialFieldValues,
  type FieldValue,
} from '../types'
import MultiSelectWrapper from './MultiSelectWrapper'
import { c } from '../../../common/helpers/TranslationHelpers'

type ConnectionFieldInputProps = {
  connectionField: CredentialField
  fieldName: string
  setValue: (value: FieldValue) => void
  value: FieldValue
  confirmation?: {
    header: string
    type: 'blue' | 'green' | 'red'
    body: string
  }
  disabled?: boolean
  credentialFields?: CredentialFieldValues
  setCredentialField?: (fieldName: string, value: FieldValue) => void
}

const ConnectionFieldInput = ({
  connectionField,
  fieldName,
  value,
  setValue,
  confirmation,
  disabled,
  credentialFields,
  setCredentialField,
}: ConnectionFieldInputProps): React.JSX.Element => {
  const getTooltipContent = () => {
      return connectionField.display_description
        ? {
            tooltipContent: connectionField.display_description,
          }
        : undefined
    },
    [filteredOptions, setFilteredOptions] = useState(connectionField.options),
    filterOptionsByVisibility = (
      options: CredentialFieldOption[],
      credentialFields?: CredentialFieldValues,
    ): CredentialFieldOption[] => {
      return options.filter((option) => {
        // If no visibility condition is specified, include the option
        if (option.visible === undefined) {
          return true
        }

        // Check if the visibility condition matches the current credential fields
        return Object.entries(option.visible).every(([key, value]) => {
          return credentialFields?.[key] === value
        })
      })
    }

  useEffect(() => {
    if (connectionField.field_type !== 'picker') return
    const options =
        connectionField.options?.map((o, i) => {
          return {
            id: i,
            label: o.label,
            text: o.label,
            value: o.value,
            visible: o.visible,
          }
        }) || [],
      newFilteredOptions = filterOptionsByVisibility(options, credentialFields)

    setFilteredOptions(newFilteredOptions)
    if (!value) {
      setValue(connectionField.options?.[0].value)
      return
    }
    if (!value && newFilteredOptions.length > 0) {
      setValue(newFilteredOptions[0].value)
    } else if (value && newFilteredOptions.length === 1) {
      setCredentialField?.(fieldName, newFilteredOptions[0].value)
    }
  }, [
    connectionField.field_type,
    connectionField.options,
    credentialFields,
    fieldName,
    setCredentialField,
    setValue,
    value,
  ])

  useEffect(() => {
    if (connectionField.field_type === 'multiselect') {
      const multiOptions = connectionField.multiOptions || []
      if (multiOptions.length === 1) {
        if (JSON.stringify(value) !== JSON.stringify(multiOptions)) {
          setValue(
            multiOptions.map((o) => (typeof o === 'string' ? o : o.value)),
          )
        }
      }
    } else if (connectionField.field_type === 'select') {
      const options = connectionField.options || []
      if (options.length === 1) {
        if (value !== options[0].value) {
          setValue(options[0].value)
        }
      }
    } else if (connectionField.field_type === 'picker') {
      if (!value) setValue(connectionField.options?.[0]?.value)
    }
  }, [connectionField, value, setValue])

  switch (connectionField.field_type) {
    case 'text':
      return (
        <TextInput
          callout={(_, text) => {
            setValue(text)
          }}
          id={fieldName}
          labelText={connectionField.display_name}
          type={connectionField.sensitive ? 'password' : 'text'}
          value={`${value ? value : ''}`}
          labelTooltip={getTooltipContent()}
          autoComplete='off'
        />
      )
    case 'select': {
      if (connectionField.options?.length === 1) {
        return <></>
      }
      const filteredOptions = filterOptionsByVisibility(
        connectionField.options || [],
        credentialFields,
      )
      return (
        <Select
          options={filteredOptions}
          selectedItem={
            filteredOptions.find((elt) => elt.value === value) || {
              value: '',
              label: '',
            }
          }
          optionKeyName='value'
          labelKeyName='label'
          onChange={(selected) => {
            if (typeof selected === 'undefined') {
              return
            }
            setValue(selected.value)
          }}
          labelProps={{
            label: connectionField.display_name,
            tooltip: getTooltipContent(),
          }}
        />
      )
    }

    case 'multiselect': {
      const multiOptions = connectionField.multiOptions || []
      if (multiOptions.length === 1) {
        return <></>
      }
      return (
        <MultiSelectWrapper
          labelText={connectionField.display_name}
          labelTooltip={getTooltipContent()}
          options={multiOptions}
          selectedOptions={(value || []) as string[]}
          callout={(selectedOptions) => setValue(selectedOptions)}
          placeholder={`-- ${c('selectOptions')} --`}
        />
      )
    }

    case 'boolean':
      // eslint-disable-next-line no-case-declarations
      let toggleProps: ComponentProps<typeof Switch> = {
        checked: !!value,
        disabled: disabled,
        formLabelProps: {
          label: connectionField.display_name,
          tooltip: getTooltipContent(),
        },
        callout: (checked) => setValue(checked),
      }
      if (confirmation) {
        toggleProps = {
          ...toggleProps,
          callout: undefined,
          confirmation: {
            ...confirmation,
            confirmCallout: () => setValue(!value),
          },
        }
      }
      return (
        <div>
          <div className='flex flex-direction-row pat-gap-4 pat-pb-2'>
            <Switch {...toggleProps} />
          </div>
          {connectionField.message_on_selection && value && (
            <span className='fs-10'>
              {connectionField.message_on_selection}
            </span>
          )}
        </div>
      )
    case 'integer':
      return (
        <TextInput
          callout={(_, text) => {
            setValue(text)
          }}
          id={fieldName}
          labelText={connectionField.display_name}
          type='number'
          value={`${value ? value : ''}`}
          labelTooltip={getTooltipContent()}
          autoComplete='off'
          max={connectionField.max}
          min={0}
        />
      )
    case 'picker':
      return (
        <Picker
          options={filteredOptions || []}
          selected={value as string}
          callout={(selected) => setValue(selected)}
          labelText={connectionField.display_name}
          labelTooltip={getTooltipContent()}
        />
      )
    default:
      return (
        <div>Field type '{connectionField.field_type}' not yet supported</div>
      )
  }
}

export default ConnectionFieldInput
