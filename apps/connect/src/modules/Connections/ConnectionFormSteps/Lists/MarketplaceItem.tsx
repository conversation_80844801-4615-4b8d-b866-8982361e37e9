import React from 'react'
import { But<PERSON>, TrimText } from '@patterninc/react-ui'
import { trimText } from '@patterninc/react-ui'

import { type MarketplaceInfoType } from '../../types'
import styles from './../_create_connection.module.scss'
import { useConnectionForm } from '../../../../context/connection-form-context'

interface inputProps {
  marketplace: MarketplaceInfoType
}

const MarketplaceItem = ({ marketplace }: inputProps): React.JSX.Element => {
  const {
    marketplaceId,
    setMarketplaceId,
    setMarketplaceDisplayName,
    setMarketplaceName,
  } = useConnectionForm()

  const handleClick = () => {
    if (marketplace.id !== Number(marketplaceId)) {
      setMarketplaceId(marketplace.id.toString())
      setMarketplaceDisplayName(marketplace.display_name)
      setMarketplaceName(marketplace.name)
    } else {
      setMarketplaceId(null)
      setMarketplaceDisplayName('')
      setMarketplaceName('')
    }
  }

  return (
    <Button
      as='unstyled'
      className={styles.marketplaceSection}
      id={`${marketplace.id}`}
      onClick={handleClick}
    >
      <div
        className={
          marketplaceId !== null && marketplace.id === Number(marketplaceId)
            ? styles.selectedMarketplaceLogoDiv
            : styles.ordinaryMarketplaceLogoDiv
        }
      >
        <img
          className={styles.marketplaceLogo}
          src={marketplace.image_url}
          alt={marketplace.display_name}
        />
      </div>
      {marketplaceId !== null && marketplace.id === Number(marketplaceId) ? (
        <div className={styles.selectedMarketplaceName}>
          <div className='fs-12 fw-bold'>
            {trimText(marketplace.display_name, 9)}
          </div>
        </div>
      ) : (
        <span className={styles.marketplaceName}>
          <TrimText text={marketplace.display_name} limit={11} />
        </span>
      )}
    </Button>
  )
}

export default MarketplaceItem
