import React from 'react'

import MarketplaceItem from './MarketplaceItem'
import styles from './../_create_connection.module.scss'
import {
  type MarketplaceFormDetailType,
  type MarketplaceInfoType,
} from '../../types'

interface inputProps {
  marketplaces: MarketplaceInfoType[]
  setMarketplaceFormDetails: (arg: MarketplaceFormDetailType) => void
  marketplaceFormDetails: MarketplaceFormDetailType
}

const MarketPlaceList = ({ marketplaces }: inputProps): React.JSX.Element => {
  return (
    <div className={styles.marketplaceList}>
      {marketplaces.map((marketplace) => {
        return (
          <MarketplaceItem key={marketplace.id} marketplace={marketplace} />
        )
      })}
    </div>
  )
}

export default MarketPlaceList
