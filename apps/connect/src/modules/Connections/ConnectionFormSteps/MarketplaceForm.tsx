import React, { useEffect, useMemo } from 'react'
import { <PERSON>Loa<PERSON>, Picker, SearchBar } from '@patterninc/react-ui'
import { useQuery } from '@tanstack/react-query'

import {
  type CountryInfoType,
  type MarketplaceFormDetailType,
  type MarketplaceFormOptionType,
  type RegionInfoType,
  type SelectedMarketplaceFormOptionsType,
} from '../types'
import MarketPlaceList from './Lists/MarketplaceList'
import {
  categoriesApi,
  destinationTypesApi,
  marketplacesApi,
} from '../../../common/services/ConnectAuthService'
import SecureAxios from '../../../common/services/SecureAxios'
import sortOptions from '../../../common/services/SortOptions'
import { useTranslate } from '../../../common/helpers/TranslationHelpers'
import { useConnectionForm } from '../../../context/connection-form-context'

type MarketplaceProps = {
  marketplaceFormOptions: MarketplaceFormOptionType
  setMarketplaceFormOptions: (arg: MarketplaceFormOptionType) => void
  marketplaceFormDetails: MarketplaceFormDetailType
  setMarketplaceFormDetails: (arg: MarketplaceFormDetailType) => void
  setSelectedMarketplaceFormOptions: (
    arg: SelectedMarketplaceFormOptionsType,
  ) => void
  regions: RegionInfoType[]
  countries: CountryInfoType[]
}

const MarketplaceForm = ({
  marketplaceFormOptions,
  setMarketplaceFormOptions,
  marketplaceFormDetails,
  setMarketplaceFormDetails,
  regions,
  countries,
}: MarketplaceProps): React.JSX.Element => {
  const { t } = useTranslate('connections'),
    {
      destinationType,
      setDestinationType,
      marketplaceId,
      setMarketplaceId,
      setMarketplaceDisplayName,
      setMarketplaceName,
    } = useConnectionForm(),
    { data: marketplacesData, status: marketplacesStatus } = useQuery({
      queryKey: [
        'marketplaces',
        marketplaceFormDetails.search,
        destinationType,
      ],
      queryFn: async ({ signal }) => {
        const queryParams = {
          region: marketplaceFormDetails.region_ids,
          country: marketplaceFormDetails.country_ids,
          category: marketplaceFormDetails.category_ids,
          search: marketplaceFormDetails.search,
          destination_type: destinationType,
        }
        const response = await SecureAxios.get(marketplacesApi, {
          params: queryParams,
          signal,
        })
        return response.data
      },
    }),
    marketplaces = useMemo(
      () => (marketplacesStatus === 'success' ? marketplacesData : []),
      [marketplacesData, marketplacesStatus],
    ),
    { data: destinationTypesData } = useQuery({
      queryKey: ['destinationTypes'],
      queryFn: async ({ signal }) => {
        const response = await SecureAxios.get<{ name: string; id: number }[]>(
          destinationTypesApi,
          { signal },
        )
        return response.data
      },
    }),
    destinationTypeOptions = useMemo(() => {
      const destinationTypes = destinationTypesData ? destinationTypesData : []
      return destinationTypes.map((destinationType) => {
        return {
          id: destinationType.id,
          text: destinationType.name.toUpperCase(),
          value: destinationType.id.toString(),
        }
      })
    }, [destinationTypesData]),
    { data: categoriesData, status: categoriesDataStatus } = useQuery({
      queryKey: ['categoriesKey'],
      queryFn: async ({ signal }) => {
        const response = await SecureAxios.get(categoriesApi, { signal })
        return response.data
      },
    }),
    categories = useMemo(
      () => (categoriesDataStatus === 'success' ? categoriesData : []),
      [categoriesData, categoriesDataStatus],
    )

  // If the selected marketplace is not in the list of marketplaces returned
  // for our search, reset the marketplaceId and marketplaceName
  useEffect(() => {
    if (
      marketplaces.findIndex(
        (m: { id: number }) => m.id.toString() === marketplaceId,
      ) === -1 &&
      marketplacesStatus === 'success'
    ) {
      setMarketplaceDisplayName('')
      setMarketplaceName('')
      setMarketplaceId(null)
    }
  }, [
    marketplaceId,
    marketplaces,
    setMarketplaceId,
    setMarketplaceName,
    setMarketplaceDisplayName,
    marketplacesStatus,
  ])

  useEffect(() => {
    setMarketplaceFormOptions({
      search: marketplaceFormDetails.search,
      marketplaces: sortOptions(marketplaces, 'display_name', true),
      regions: sortOptions(regions, 'name', true),
      countries: sortOptions(countries, 'name', true),
      categories: categories,
    })
  }, [
    setMarketplaceFormOptions,
    marketplaceFormDetails.search,
    marketplaces,
    regions,
    countries,
    categories,
  ])

  return (
    <>
      <div className='flex flex-direction-column pat-gap-4'>
        <SearchBar
          value={marketplaceFormDetails.search}
          onChange={(value) => {
            setMarketplaceFormDetails({
              ...marketplaceFormDetails,
              search: value,
            })
          }}
          placeholder={t('searchByNameEGAmazon')}
        />
        <Picker
          options={destinationTypeOptions}
          callout={setDestinationType}
          selected={destinationType}
        />
      </div>
      <div className='pat-mt-4'>
        {marketplacesStatus === 'pending' ? (
          <ListLoading />
        ) : marketplaceFormOptions.marketplaces.length ? (
          <MarketPlaceList
            marketplaces={marketplaceFormOptions.marketplaces}
            setMarketplaceFormDetails={setMarketplaceFormDetails}
            marketplaceFormDetails={marketplaceFormDetails}
          />
        ) : (
          <span>
            No{' '}
            {destinationTypeOptions
              ?.find((d) => d.value === destinationType)
              ?.text.toLowerCase()}{' '}
            destinations found
          </span>
        )}
      </div>
    </>
  )
}

export default MarketplaceForm
