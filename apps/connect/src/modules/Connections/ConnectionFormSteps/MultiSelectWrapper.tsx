import { MultiSelect, type TooltipProps } from '@patterninc/react-ui'
import React, { useCallback, useMemo } from 'react'

import { type CredentialFieldOption } from '../types'

type MultiSelectWrapperProps = {
  options: CredentialFieldOption[] | string[]
  selectedOptions: string[] // the value of the selected options
  callout: (selectedOptions: string[]) => void
  placeholder: string
  labelText: string
  labelTooltip?: Omit<TooltipProps, 'children'>
}

const MultiSelectWrapper = ({
  options,
  selectedOptions,
  callout,
  placeholder,
  labelText,
  labelTooltip,
}: MultiSelectWrapperProps): React.JSX.Element => {
  const formattedOptions: string[] = useMemo(() => {
      return options.map((option) => {
        // get the list of labels from the options
        return typeof option === 'string' ? option : option.label
      })
    }, [options]),
    formattedSelectedOptions: string[] = useMemo(() => {
      return selectedOptions.map((value) => {
        // map the list of selected values to their corresponding labels
        const option = options.find((o) =>
          typeof o === 'string' ? o === value : o.value === value,
        )
        if (!option) return value
        return typeof option === 'string' ? option : option.label
      })
    }, [selectedOptions, options]),
    onChange = useCallback(
      (newSelectedOptions: string[]) => {
        if (typeof options[0] === 'string') {
          return callout(newSelectedOptions)
        }
        const fullOptions = (options as CredentialFieldOption[]).filter((o) =>
          newSelectedOptions.includes(o.label),
        )
        callout(fullOptions.map((o) => o.value))
      },
      [options, callout],
    )
  const formattedOptionsArray = useMemo(() => {
    return formattedOptions.map((option) => ({ label: option }))
  }, [formattedOptions])

  const formattedSelectedOptionsArray = useMemo(() => {
    return formattedSelectedOptions.map((option) => ({ label: option }))
  }, [formattedSelectedOptions])
  return (
    <MultiSelect
      formLabelProps={{ label: labelText, tooltip: labelTooltip }}
      options={formattedOptionsArray}
      selectedOptions={formattedSelectedOptionsArray}
      callout={(selectedList) =>
        onChange(selectedList.map((option) => option.label))
      }
      selectPlaceholder={placeholder}
      labelKey='label'
    />
  )
}

export default MultiSelectWrapper
