// Marketplace form

.marketplaceList {
  display: flex;
  flex-wrap: wrap;
  row-gap: 16px; // Need this gap to keep marketplace names attached to upper logo & distant from next row of logo
}

.marketplaceSectionWidth {
  // Need this to keep the width of logo image fix.
  // TODO: We can try to remove this if we get all the logo images in this same size i.e. 80px X 80px
  width: 80px;
}

.marketplaceSection {
  @extend .marketplaceSectionWidth;
  flex: 0 1 25%; // Need this to show four icons per row
  justify-content: space-between;
  cursor: pointer;
}

.marketplaceLogoDiv {
  // width and height to keep logos square with varying aspect ratios
  @extend .marketplaceSectionWidth;
  margin: auto;
  height: 80px;
  border-radius: 4px;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.marketplaceLogo {
  @extend .marketplaceSectionWidth;
  // Need this to keep the width of logo image fixed
  // 64px + 8px padding either side = required 80 px from mockup
  width: 64px;
  padding: 8px;
}

.ordinaryMarketplaceLogoDiv {
  @extend .marketplaceLogoDiv;
  border: 2px solid var(--light-gray);
}

.selectedMarketplaceLogoDiv {
  @extend .marketplaceLogoDiv;
  border: 2px solid var(--medium-blue);
}

.flexCenter {
  display: flex;
  justify-content: center;
}

.marketplaceName {
  @extend .marketplaceSectionWidth;
  @extend .flexCenter;
  text-align: center;
  font-size: var(--font-size-12);
  color: var(--dark-blue);
  margin: auto;
  white-space: nowrap;
}

.selectedMarketplaceName {
  @extend .marketplaceSectionWidth;
  @extend .flexCenter;
  margin: auto;
}
