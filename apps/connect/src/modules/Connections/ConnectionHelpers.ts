import { type Location } from './types'

const CHARACTER_LIMIT = 25

/**
 * Get a string containing all region and country codes for given locations
 * @param locations A list of location objects to include
 * @param limit The maximum number of characters for the returned value, defaults to 25. Passing 0 means no character limit
 * @returns A string with all region codes and country codes from the locations list, separated by commas, and truncated at the character limit
 */
export const getLocationString = (
  locations: Location[],
  limit: number = CHARACTER_LIMIT,
): string => {
  let locationString = ''
  locations.forEach((location, i) => {
    if (!(location.country_code || location.region)) return
    const currentLocationString =
      location.region && location.country_code
        ? `${location.region}: ${location.country_code}`
        : `${location.country_code || location.region}`
    locationString += `${i !== 0 ? ', ' : ''}${currentLocationString}`
  })
  if (locationString.length > limit && limit !== 0) {
    locationString = locationString.substring(0, limit - 3)
    locationString += '...'
  }
  return locationString
}
