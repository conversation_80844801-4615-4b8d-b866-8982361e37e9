import {
  type SortByProps,
  type SortColumnProps,
  StandardTable,
  useIsMounted,
} from '@patterninc/react-ui'
import React, { useContext, useEffect, useMemo, useState } from 'react'
import { useInfiniteQuery } from '@tanstack/react-query'
import { useLocation, useParams } from 'react-router-dom'

import { ThemeContext } from '../../Context'
import { type ConnectionHistoryType } from './types'
import { connectionHistories } from '../../common/services/ConnectAuthService'
import SecureAxios from '../../common/services/SecureAxios'
import { ConnectionHistoryTableConfig } from './connection-history-table-config'
import { PRIVILEGES, useHasPrivilege } from '../../context/auth-context'
import { c, useTranslate } from '../../common/helpers/TranslationHelpers'

const ConnectionHistory = (): React.JSX.Element => {
  const { pathname } = useLocation(),
    { t } = useTranslate('connections'),
    canViewConnectionDetails = useHasPrivilege(
      PRIVILEGES.VIEW_CONNECTION_DETAILS,
    ),
    { updateBreadcrumbs } = useContext(ThemeContext),
    tableId = 'connection_histories_table',
    isMounted = useIsMounted(),
    [sortBy, setSortBy] = useState<SortByProps>({
      prop: 'timestamp',
      flip: false,
    }),
    { id } = useParams<{ id: string }>(),
    getSortColumn = (prop: string) => {
      if (prop === 'timestamp') return 'created_at'
      if (prop === 'events') return 'description'
      return 'email'
    },
    { status, data, fetchNextPage, hasNextPage } = useInfiniteQuery({
      queryKey: [id, sortBy],
      queryFn: async ({ pageParam = 1 }) => {
        const response = await SecureAxios.get(connectionHistories(id || ''), {
          params: {
            page: pageParam,
            per_page: 20,
            sort_by: getSortColumn(sortBy.prop),
            order: sortBy.flip ? 'asc' : 'desc',
          },
        })
        return response
      },
      initialPageParam: 1,
      enabled: canViewConnectionDetails,
      gcTime: 1000 * 60 * 60 * 8,
      getNextPageParam: (previousPage) => {
        if (
          previousPage.headers['current-page'] !==
          previousPage.headers['total-pages']
        ) {
          return Number(previousPage.headers['current-page']) + 1
        }
        return undefined
      },
    }),
    isLoading = useMemo(() => {
      return status === 'pending'
    }, [status]),
    sort: SortColumnProps['sorter'] = (sortObj: {
      activeColumn: string
      direction: boolean
      lowerCaseParam?: boolean | undefined
    }) => {
      if (isMounted()) {
        setSortBy({
          prop: sortObj.activeColumn,
          flip: sortObj.direction,
        })
      }
    },
    tableData: ConnectionHistoryType[] = useMemo(() => {
      return data ? data.pages?.flatMap((page) => page?.data) : []
    }, [data])

  useEffect(() => {
    updateBreadcrumbs({
      name: c('details'),
      link: pathname,
      changeType: 'tab',
    })
  }, [updateBreadcrumbs, pathname])

  return (
    <>
      <StandardTable
        data={tableData}
        customWidth='auto'
        dataKey='id'
        hasData={status === 'success' && tableData.length > 0}
        loading={isLoading}
        successStatus={status === 'success'}
        hasMore={!!hasNextPage}
        getData={fetchNextPage}
        tableId={tableId}
        sort={sort}
        sortBy={sortBy}
        config={ConnectionHistoryTableConfig()}
        noDataFields={{
          primaryText: t('noHistoryForThisConnectionYet'),
        }}
      />
    </>
  )
}

export { ConnectionHistory }
