import React, { useMemo, useState } from 'react'
import {
  <PERSON><PERSON>ooter,
  SideDrawer,
  Tabs,
  TextInput,
  toast,
} from '@patterninc/react-ui'
import { useMutation } from '@tanstack/react-query'

import { LocationTab } from './LocationTab'
import { useTranslate } from '../../common/helpers/TranslationHelpers'
import { useConnectionDetails } from '../../context/connection-details'
import { type Location } from './types'
import SecureAxios from '../../common/services/SecureAxios'
import { updateLocationKey } from '../../common/services/ConnectAuthService'

const ConnectionLocations = (): React.JSX.Element => {
  const { t } = useTranslate('connections')
  const { locations, refetchConnectionDetails } = useConnectionDetails()
  const [isEditDrawerOpen, setIsEditDrawerOpen] = useState(false)
  const [newLocationKey, setNewLocationKey] = useState('')
  const [selectedLocation, setSelectedLocation] = useState<Location | null>(
    null,
  )

  const handleEdit = (location: Location) => {
    setSelectedLocation(location)
    setNewLocationKey(location.key)
    setIsEditDrawerOpen(true)
  }

  const { mutate: callUpdateLocation } = useMutation({
      mutationFn: (newKey: string) => {
        return SecureAxios.put(updateLocationKey(selectedLocation?.key || ''), {
          new_key: newKey,
        })
      },
      mutationKey: ['updateLocation', selectedLocation?.key],
      onSuccess: () => {
        refetchConnectionDetails()
        setIsEditDrawerOpen(false)
      },
      onError: () => {
        toast({
          type: 'error',
          message: t('saveLocationKeyError', {
            oldKey: selectedLocation?.key,
            newKey: newLocationKey,
          }),
        })
        setIsEditDrawerOpen(false)
      },
    }),
    tabs = useMemo(() => {
      return locations.map((location, i) => {
        return {
          content: <LocationTab location={location} onEditClick={handleEdit} />,
          id: i,
          tabName: location.country_code || location.region || '',
          location: location,
        }
      })
    }, [locations])

  const isNewKeyValid: boolean = useMemo(() => {
    return !!newLocationKey.match(/^[a-zA-Z0-9-_]+$/)
  }, [newLocationKey])

  return (
    <>
      {tabs.length > 0 && <Tabs subtabs tabs={tabs} />}
      <SideDrawer
        headerContent={t('editLocation', {
          countryCode:
            selectedLocation?.country_code || selectedLocation?.region,
        })}
        isOpen={isEditDrawerOpen}
        closeCallout={() => setIsEditDrawerOpen(false)}
        footerContent={
          <FormFooter
            saveButtonProps={{
              onClick: () => callUpdateLocation(newLocationKey),
              disabled:
                newLocationKey === selectedLocation?.key || !isNewKeyValid,
            }}
            cancelButtonProps={{
              onClick: () => {
                setIsEditDrawerOpen(false)
                if (selectedLocation) {
                  setNewLocationKey(selectedLocation.key)
                }
              },
            }}
          />
        }
      >
        <TextInput
          errorText={t('invalidLocationKey')}
          classType={`${!isNewKeyValid ? 'error' : ''}`}
          hasError={!isNewKeyValid}
          required
          type='text'
          labelText={t('credentialLocationKey')}
          value={newLocationKey}
          callout={(_: unknown, text: string | number) => {
            setNewLocationKey(text.toString())
          }}
        />
      </SideDrawer>
    </>
  )
}

export default ConnectionLocations
