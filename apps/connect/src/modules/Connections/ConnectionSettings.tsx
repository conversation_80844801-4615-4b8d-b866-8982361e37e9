import React, { useContext, useEffect, useMemo, useState } from 'react'
import { useLocation, useParams } from 'react-router-dom'
import {
  type SortByProps,
  type SortColumnProps,
  Tabs,
  useToggle,
} from '@patterninc/react-ui'

import { ThemeContext } from '../../Context'
import { connectionSettingsSubtabs } from './connection-settings-subtabs'
import { type SettingType } from './types'
import CallLimitsForm from './SettingForms/CallLimitsForm'
import NotificationsForm from './SettingForms/NotificationsForm'
import { useHasPatternAdminRole } from '../../context/auth-context'
import OtherSettingForm from './SettingForms/OtherSettingForm'
import { c } from '../../common/helpers/TranslationHelpers'
import { useSettings } from '../../context/settings-context'
import { useConnectionDetails } from '../../context/connection-details'

const ConnectionSettings = (): React.JSX.Element => {
  const replaceDelimiterToggle = useToggle('replacesymbol_to_replacedelimeter')
  const { pathname } = useLocation(),
    { updateBreadcrumbs } = useContext(ThemeContext),
    { id } = useParams<{ id: string }>(),
    {
      formatSettingsData,
      canEditConnections,
      canEditNotifications,
      getSettingsEndpoint,
      metadata: getMetadata,
      paths: getPaths,
    } = useSettings(),
    {
      isSlackConnected,
      isSlackLoading,
      connectionSettings: rawSettingData,
      refetchConnectionSettings: refetch,
    } = useConnectionDetails(),
    tableData = useMemo(() => {
      return formatSettingsData(rawSettingData)
    }, [rawSettingData, formatSettingsData]),
    paths = useMemo(() => {
      return getPaths(rawSettingData)
    }, [rawSettingData, getPaths]),
    metadata = useMemo(() => {
      return getMetadata(rawSettingData)
    }, [rawSettingData, getMetadata]),
    // tab states
    [activeTab, setActiveTab] = useState(0),
    // table sorting params
    [sortBy, setSortBy] = useState<SortByProps>({
      prop: 'job_type',
      flip: false,
    }),
    sort: SortColumnProps['sorter'] = (sortObj: {
      activeColumn: string
      direction: boolean
      lowerCaseParam?: boolean | undefined
    }) => {
      setSortBy({
        prop: sortObj.activeColumn,
        flip: sortObj.direction,
      })
    },
    [isFormOpen, setIsFormOpen] = useState(false),
    [currentStep, setCurrentStep] = useState(1),
    [isSingleNotificationEdit, setIsSingleNotificationEdit] = useState(false),
    [isSettingFormOpen, setIsSettingFormOpen] = useState(false),
    [selectedSetting, setSelectedSetting] = useState<SettingType>({
      name: '',
      value: '',
    }),
    isPatternAdmin = useHasPatternAdminRole(),
    [tempSettingState, setTempSettingState] = useState<
      Record<string, string>[]
    >([])

  useEffect(() => {
    if (tableData?.other_settings) {
      setTempSettingState(
        tableData.other_settings.map((setting) => ({
          [setting.name]: '',
        })),
      )
    }
  }, [tableData?.other_settings])

  useEffect(() => {
    updateBreadcrumbs({
      name: c('details'),
      link: pathname,
      changeType: 'tab',
    })
  }, [updateBreadcrumbs, pathname])

  const otherTableData = tableData.other_settings.map((data) => data.name)

  return (
    <>
      <div>
        {/* Subtabs */}
        <Tabs
          subtabs
          tabs={connectionSettingsSubtabs({
            tableData,
            setIsSingleNotificationEdit,
            setIsFormOpen,
            setCurrentStep,
            sort,
            sortBy,
            status,
            isSlackConnected,
            isSlackLoading,
            canEditNotifications: canEditNotifications,
            setIsSettingFormOpen,
            credential_name: id || '',
            refetch,
            setSelectedSetting,
            tempSettingState,
            connectionSettings: getSettingsEndpoint,
            replaceDelimiterToggle,
          }).filter((tab) => (isPatternAdmin ? true : tab.id !== 2))}
          callout={(tabId: number) => setActiveTab(tabId)}
        />

        {/* side-drawers and forms */}
        {activeTab === 1 ? (
          canEditNotifications && (
            <NotificationsForm
              refetch={refetch}
              notifications={rawSettingData?.settings?.notifications}
              isSingleNotificationEdit={isSingleNotificationEdit}
              setIsSingleNotificationEdit={setIsSingleNotificationEdit}
              isFormOpen={isFormOpen}
              setIsFormOpen={setIsFormOpen}
              currentStep={currentStep}
              setCurrentStep={setCurrentStep}
            />
          )
        ) : activeTab === 0 ? (
          canEditConnections && (
            <CallLimitsForm
              callLimits={tableData.call_limits}
              sort={sort}
              sortBy={sortBy}
              status={status}
              refetch={refetch}
              paths={paths}
              metadata={metadata}
            />
          )
        ) : (
          <OtherSettingForm
            otherTableData={otherTableData}
            isSettingFormOpen={isSettingFormOpen}
            setIsSettingFormOpen={setIsSettingFormOpen}
            selectedSetting={selectedSetting}
            refetch={refetch}
            tempSettingState={tempSettingState}
            setTempSettingState={setTempSettingState}
          />
        )}
      </div>
    </>
  )
}

export { ConnectionSettings }
