import {
  SearchBar,
  type SortByProps,
  type SortColumnProps,
  StandardTable,
  toast,
} from '@patterninc/react-ui'
import moment from 'moment'
import React, {
  useCallback,
  useContext,
  useEffect,
  useMemo,
  useState,
} from 'react'
import { useQuery } from '@tanstack/react-query'
import { useLocation, useSearchParams } from 'react-router-dom'

import {
  connectionDetails,
  connections,
  credentialStates,
} from '../../common/services/ConnectAuthService'
import SecureAxios from '../../common/services/SecureAxios'
import { getUserSettingEndpoint } from '../../common/services/UserSettingsService'
import { ThemeContext } from '../../Context'
import {
  ConnectionFilter,
  type ConnectionFilters,
  emptyStatusFilter,
  type FilterOptions,
} from './ConnectionFilter'
import { getLocationString } from './ConnectionHelpers'
import { ConnectionsTableConfig } from './connections-table-config'
import { type ConnectionType, type RawConnectionFromApi } from './types'
import ConnectionForm from './ConnectionForm'
import sortOptions from '../../common/services/SortOptions'
import { PRIVILEGES, useHasPrivilege } from '../../context/auth-context'
import { useOrg } from '../../context/org-context'
import { ConnectionsFilterContext } from '../../context/connections-filter-context'
import { c, useTranslate } from '../../common/helpers/TranslationHelpers'
import useResetOnOrgSwitch from '../../common/helpers/useResetOnOrgSwitch'
import { ConnectionFormProvider } from '../../context/connection-form-context'

const Connections = (): React.JSX.Element => {
  const { updateBreadcrumbs } = useContext(ThemeContext),
    { t } = useTranslate('connections'),
    [searchParams] = useSearchParams(),
    {
      appliedFilters,
      searchQuery: query,
      setSearchQuery,
    } = useContext(ConnectionsFilterContext),
    canViewConnections = useHasPrivilege(PRIVILEGES.VIEW_CONNECTIONS, true),
    canDeleteConnections = useHasPrivilege(PRIVILEGES.DELETE_CONNECTION),
    canDeactivateConnections = useHasPrivilege(
      PRIVILEGES.DEACTIVATE_CONNECTION,
    ),
    canViewConnectionDetails = useHasPrivilege(
      PRIVILEGES.VIEW_CONNECTION_DETAILS,
    ),
    canCreateConnections = useHasPrivilege(
      PRIVILEGES.CREATE_CONNECTION,
      searchParams.get('create_connection') === 'true',
    ),
    { org } = useOrg(),
    [sortBy, setSortBy] = useState<SortByProps>({
      prop: 'marketplace_name',
      flip: false,
    }),
    [isFormOpen, setIsFormOpen] = useState<boolean>(false),
    [filtersCount, setFiltersCount] = useState(0),
    initialActiveFilters = useMemo(() => {
      return appliedFilters !== null
        ? appliedFilters
        : {
            status: emptyStatusFilter,
            account_region: [],
            account_country: [],
            marketplace: [],
            created_by: [],
          }
    }, [appliedFilters]),
    [activeFilters, setActiveFilters] =
      useState<ConnectionFilters>(initialActiveFilters),
    { pathname } = useLocation(),
    [needsRefresh, setNeedsRefresh] = useState(true),
    lastPage = true,
    tableId = 'Connections',
    { endpoint: sortConnectionsApi } = getUserSettingEndpoint(
      `connections:table:${tableId}`,
    ),
    stickyTableConfig = { right: 1 },
    saveSort = async (sortObj: {
      activeColumn: string
      direction: boolean
      lowerCaseParam?: boolean | undefined
    }) => {
      try {
        await SecureAxios.put(sortConnectionsApi, {
          sort: {
            prop: sortObj.activeColumn,
            flip: sortObj.direction,
          },
        })
      } catch (error) {
        //TODO: Handle error in future prs and remove the following log
        console.log('Error saving sort data:', error)
      }
    },
    sort: SortColumnProps['sorter'] = (sortObj: {
      activeColumn: string
      direction: boolean
      lowerCaseParam?: boolean | undefined
    }) => {
      setSortBy({
        prop: sortObj.activeColumn,
        flip: sortObj.direction,
      })
      saveSort(sortObj)
    },
    fetchNextPage = () => {
      return []
    },
    { data: credentialStatesData } = useQuery<{ id: number; name: string }[]>({
      queryKey: ['credentialStates'],
      queryFn: () =>
        SecureAxios.get<{ states: { id: number; name: string }[] }>(
          credentialStates,
        ).then((result) => result.data.states),
    }),
    deleteCallback = useCallback(
      (id: string) => {
        toast({ type: 'success', message: t('connectionDeleted') })
        SecureAxios.delete(connectionDetails(id))
          .then(() => {
            setNeedsRefresh(true)
          })
          .catch((error) => {
            toast({
              type: 'error',
              message: c('errorMessage', { message: error.data.error }),
            })
          })
      },
      [t, setNeedsRefresh],
    ),
    statusToggleCallback = useCallback(
      (id: string, newStatus: number) => {
        SecureAxios.patch(connectionDetails(id), {
          credential_state_id: newStatus,
        })
          .then(() => {
            setNeedsRefresh(true)
            toast({
              type: 'success',
              message: t('connections:statusUpdated', {
                status: (credentialStatesData || []).find(
                  (cs) => cs.id === newStatus,
                )?.name,
              }),
            })
          })
          .catch((error) => {
            toast({ type: 'error', message: `Error: ${error.data.error}` })
          })
      },
      [t, setNeedsRefresh, credentialStatesData],
    ),
    [filterOptions, setFilterOptions] = useState<FilterOptions>({
      status: [
        { id: 0, status_name: c('all'), value: 'all' },
        { id: 1, status_name: c('inactive'), value: 'inactive' },
        { id: 2, status_name: c('active'), value: 'active' },
      ],
      region: [],
      country: [],
      marketplace: [],
      created_by: [],
    }),
    // Helper functions for formatting raw API data (c&p from useEffects)
    // parseData -> updateFilter -> sortData
    parseData = useCallback(
      (rawData: RawConnectionFromApi[]) => {
        const parsedData: ConnectionType[] = []
        for (const row of rawData) {
          parsedData.push({
            marketplace_id: row.destination_id,
            marketplace_name: row.destination_display_name,
            status: row.credential_state_id,
            created_at: moment(row.created_at).valueOf(),
            expires_on: row.expires_on || 'N/A',
            credential_name: row.destination_key,
            country_code: getLocationString(row.locations, 0),
            sortProp: sortBy.prop,
            locations: row.locations,
            marketplace_image_url: row.marketplace_image_url,
            display_name: row.display_name,
            created_by: row.user_full_name || '',
          })
        }
        return parsedData
      },
      [sortBy.prop],
    ),
    updateFilter = useCallback(
      (parsedData: ConnectionType[]) => {
        const regions: FilterOptions['region'] = [],
          countries: FilterOptions['country'] = [],
          marketplaces: FilterOptions['marketplace'] = [],
          created_by: FilterOptions['created_by'] = []

        parsedData.forEach((item, i) => {
          item.locations.forEach((location, j) => {
            // populate regions for filter
            if (location.region) {
              if (!regions.some((r) => r.region_name === location.region)) {
                regions.push({
                  id: i * 100 + j,
                  region_name: location.region,
                  value: location.region.toLowerCase(),
                })
              }
            }
            // populate countries for filter
            if (location.country_code) {
              if (
                !countries.some((r) => r.country_name === location.country_code)
              ) {
                countries.push({
                  id: i * 100 + j,
                  country_name: location.country_code,
                  value: location.country_code.toLowerCase(),
                })
              }
            }
          })
          if (
            !marketplaces.some(
              (m) => m.marketplace_name === item.marketplace_name,
            )
          ) {
            marketplaces.push({
              id: i,
              marketplace_name: item.marketplace_name,
              value: item.marketplace_name.toLowerCase().replace(' ', ''),
            })
          }
          if (!created_by.some((m) => m.value === item.created_by)) {
            created_by.push({
              id: i,
              value: item.created_by,
              text: item.created_by.toLowerCase(),
            })
          }
        })
        let filtersCounter = 0
        if (activeFilters.status.status_name !== 'All') {
          filtersCounter += 1
        }
        if (activeFilters.account_region.length > 0) {
          filtersCounter += 1
        }
        if (activeFilters.account_country.length > 0) {
          filtersCounter += 1
        }
        if (activeFilters.marketplace.length > 0) {
          filtersCounter += 1
        }
        if (activeFilters.created_by.length > 0) {
          filtersCounter += 1
        }
        setFiltersCount(filtersCounter)
        setFilterOptions({
          status: filterOptions.status,
          marketplace: sortOptions(marketplaces, 'marketplace_name', true),
          region: sortOptions(regions, 'region_name', true),
          country: countries,
          created_by: sortOptions(created_by, 'value', true),
        })
      },
      [activeFilters, filterOptions.status],
    ),
    sortData = useCallback(
      (parsedData: ConnectionType[]) => {
        const filteredData = parsedData.filter((row) => {
          if (
            query !== '' &&
            !row.country_code.toLowerCase().includes(query) &&
            !row.credential_name.toLowerCase().includes(query) &&
            !row.marketplace_name.toLowerCase().includes(query) &&
            !row.created_by.toLowerCase().includes(query)
          ) {
            return false
          }
          if (
            activeFilters.status.status_name === 'All' &&
            activeFilters.account_region.length === 0 &&
            activeFilters.account_country.length === 0 &&
            activeFilters.marketplace.length === 0 &&
            activeFilters.created_by.length === 0
          ) {
            return true
          }
          if (activeFilters.status.status_name !== 'All') {
            if (
              (activeFilters.status.status_name === 'Active' &&
                row.status !== 1) ||
              (row.status === 1 &&
                activeFilters.status.status_name === 'Inactive')
            ) {
              return false
            }
          }
          if (activeFilters.account_region.length > 0) {
            let exclude = true
            for (const region of activeFilters.account_region) {
              const reg = new RegExp(`${region}(?!,)`, 'gi')
              if (row.country_code.match(reg)) {
                exclude = false
              }
            }
            if (exclude) return false
          }
          if (activeFilters.account_country.length > 0) {
            let exclude = true
            for (const country of activeFilters.account_country) {
              const reg = new RegExp(`${country}(?!:)`, 'gi')
              if (row.country_code.match(reg)) {
                exclude = false
              }
            }
            if (exclude) return false
          }
          if (activeFilters.marketplace.length > 0) {
            if (!activeFilters.marketplace.includes(row.marketplace_name)) {
              return false
            }
          }
          if (activeFilters.created_by.length > 0) {
            if (
              !activeFilters.created_by.includes(row.created_by.toLowerCase())
            ) {
              return false
            }
          }
          return true
        })
        const sortedData: Array<ConnectionType> = [...filteredData].sort(
          (a: ConnectionType, b: ConnectionType) => {
            let first: string | number, second: string | number
            switch (sortBy.prop) {
              case 'marketplace_name':
                first = a.marketplace_name?.toUpperCase()
                second = b.marketplace_name?.toUpperCase()
                break
              case 'credential_name':
                first = a.credential_name?.toUpperCase()
                second = b.credential_name?.toUpperCase()
                break
              case 'country_code':
                first = a.country_code?.toUpperCase()
                second = b.country_code?.toUpperCase()
                break
              case 'created_at':
                first = a.created_at
                second = b.created_at
                break
              case 'expires_on':
                first =
                  typeof a.expires_on === 'string'
                    ? Number.MAX_SAFE_INTEGER
                    : a.expires_on
                second =
                  typeof b.expires_on === 'string'
                    ? Number.MAX_SAFE_INTEGER
                    : b.expires_on
                break
              default:
                return 0
            }
            if (
              (first < second && sortBy.flip) ||
              (first > second && !sortBy.flip)
            ) {
              return -1
            }
            if (
              (first > second && sortBy.flip) ||
              (first < second && !sortBy.flip)
            ) {
              return 1
            }
            return 0
          },
        )
        return sortedData
      },
      [activeFilters, query, sortBy],
    ),
    { status, data, isFetching } = useQuery<ConnectionType[]>({
      queryKey: [
        sortBy.prop,
        filterOptions.status,
        sortBy,
        activeFilters,
        query,
        needsRefresh,
        org,
        canViewConnections,
      ],
      queryFn: ({ signal }) =>
        SecureAxios.get<RawConnectionFromApi[]>(connections, {
          signal,
          params: {
            organization_code: org.code,
          },
        }).then((result) => {
          setNeedsRefresh(false)
          const parsedData = parseData(result.data)
          updateFilter(parsedData)
          return sortData(parsedData)
        }),
      enabled: canViewConnections,
    }),
    tableData = data ? data : [],
    tableConfig = useMemo(() => {
      return ConnectionsTableConfig(
        statusToggleCallback,
        deleteCallback,
        credentialStatesData || [],
        canDeleteConnections,
        canDeactivateConnections,
        canViewConnectionDetails,
      )
    }, [
      canDeactivateConnections,
      canDeleteConnections,
      canViewConnectionDetails,
      credentialStatesData,
      deleteCallback,
      statusToggleCallback,
    ])

  useEffect(() => {
    updateBreadcrumbs({
      name: 'Connections',
      link: pathname,
      changeType: 'rootLevel',
    })
  }, [pathname, updateBreadcrumbs])

  useResetOnOrgSwitch(setSearchQuery)
  useEffect(() => {
    if (appliedFilters !== null) {
      setActiveFilters(appliedFilters)
    }
  }, [appliedFilters])

  return (
    <>
      <div className='flex justify-content-between pat-mb-4 pat-gap-2'>
        <SearchBar
          value={query}
          onChange={(value: string) => {
            setSearchQuery(value.toLowerCase())
          }}
          placeholder={t('marketPlaceAccountRegionOrSupportID')}
          minWidth={350}
        />
        <ConnectionFilter
          filtersCount={filtersCount}
          setFiltersCount={setFiltersCount}
          filterState={activeFilters}
          filterOptions={filterOptions}
          update={setActiveFilters}
          areFilterOptionsLoading={isFetching}
        />
      </div>
      <StandardTable
        data={tableData}
        stickyTableConfig={stickyTableConfig}
        dataKey='marketplace_id'
        hasData={tableData.length > 0}
        loading={status === 'pending' || (needsRefresh && isFetching)}
        successStatus={status === 'success'}
        hasMore={!lastPage}
        getData={fetchNextPage}
        tableId={tableId}
        sort={sort}
        sortBy={sortBy}
        config={tableConfig}
        noDataFields={{
          primaryText: t('noConnectionsAvailable'),
          secondaryText: canCreateConnections
            ? t('allYourConnectionsWillBeListedHere')
            : '',
          icon: 'sellers',
          buttonProps: canCreateConnections
            ? {
                children: t('addNewConnection'),
                onClick: () => {
                  setIsFormOpen(!isFormOpen)
                },
              }
            : undefined,
        }}
      />
      {canCreateConnections && (
        <ConnectionFormProvider>
          <ConnectionForm />
        </ConnectionFormProvider>
      )}
    </>
  )
}

export default Connections
