import React from 'react'
import { Route, Routes } from 'react-router-dom'

import PrivateRoute from '../../common/components/PrivateRoute/PrivateRoute'
import { ConnectionDetails } from './ConnectionDetails'
import ConnectionDetailsLayout from './ConnectionDetailsLayout'
import { ConnectionHistory } from './ConnectionHistory'
import Connections from './Connections'
import { ConnectionSettings } from './ConnectionSettings'
import { ConnectionsFilterProvider } from '../../context/connections-filter-context'
import ConnectionLocations from './ConnectionLocations'
import { ConnectionDetailsProvider } from '../../context/connection-details'
import { SettingsProvider } from '../../context/settings-context'
import { ConnectionFormProvider } from '../../context/connection-form-context'

const ConnectionsRoutes = (): React.JSX.Element => {
  return (
    <ConnectionsFilterProvider>
      <Routes>
        <Route
          index
          element={
            <PrivateRoute>
              <Connections />
            </PrivateRoute>
          }
        />
        <Route
          path=':id/*'
          element={
            <ConnectionDetailsProvider>
              <ConnectionDetailsLayout />
            </ConnectionDetailsProvider>
          }
        >
          <Route
            path='details'
            element={
              <ConnectionFormProvider>
                <ConnectionDetails />
              </ConnectionFormProvider>
            }
          />
          <Route path='history' element={<ConnectionHistory />} />
          <Route
            path='settings'
            element={
              <SettingsProvider>
                <ConnectionSettings />
              </SettingsProvider>
            }
          />
          <Route path='locations' element={<ConnectionLocations />} />
        </Route>
      </Routes>
    </ConnectionsFilterProvider>
  )
}

export default ConnectionsRoutes
