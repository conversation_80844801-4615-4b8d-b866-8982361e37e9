import { Button, useToggle } from '@patterninc/react-ui'

import { type Location } from './types'
import { useTranslate } from '../../common/helpers/TranslationHelpers'
import { ConnectionSettings } from './ConnectionSettings'
import { SettingsProvider } from '../../context/settings-context'

type LocationTabContentProps = {
  location: Location
  onEditClick: (location: Location) => void
}

export const LocationTab = ({
  location,
  onEditClick,
}: LocationTabContentProps): React.JSX.Element => {
  const { t } = useTranslate('connections'),
    enableThreeTieredSettings = useToggle('three_tiered_settings')

  return (
    <>
      <div className='flex justify-content-between bdrt bdrb bdrc-medium-purple align-items-center pat-pt-1 pat-pb-1 pat-mb-4'>
        <div>
          <div className='fw-bold'>{t('credentialLocationKey')}</div>
          <div>{location.key}</div>
        </div>
        <Button onClick={() => onEditClick(location)}>Edit</Button>
      </div>
      {enableThreeTieredSettings && (
        <SettingsProvider id={location.key} settingLevel='credential_location'>
          <ConnectionSettings />
        </SettingsProvider>
      )}
    </>
  )
}
