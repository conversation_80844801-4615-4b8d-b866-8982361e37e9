import {
  <PERSON><PERSON><PERSON><PERSON>,
  PageFooter,
  SideDrawer,
  type SortByProps,
  type SortColumnProps,
  StandardTable,
  toast,
  useToggle,
} from '@patterninc/react-ui'
import React, { useCallback, useEffect, useMemo, useState } from 'react'
import { useMutation } from '@tanstack/react-query'
import { useParams } from 'react-router-dom'

import { editCallLimitsConfig } from '../connection-edit-call-limits-table-config'
import {
  type ConnectionSettingsPutParams,
  type DetailsData,
  type FormStateType,
} from '../types'
import SecureAxios from '../../../common/services/SecureAxios'
import { type ApiErrorType } from '../../../common/types'
import { c, useTranslate } from '../../../common/helpers/TranslationHelpers'
import { useSettings } from '../../../context/settings-context'

const CallLimitsForm = ({
  callLimits,
  sort,
  sortBy,
  status,
  refetch,
  metadata,
  paths,
}: {
  callLimits: DetailsData['call_limits']
  sort: SortColumnProps['sorter']
  sortBy: SortByProps
  status: string
  refetch: () => void
  metadata: {
    api_key_pattern: string
    algorithm: string
  }
  paths: Record<string, string>
}): React.JSX.Element => {
  const replaceDelimiterToggle = useToggle('replacesymbol_to_replacedelimeter')
  const { t } = useTranslate('connections')
  const initialFormState = useMemo(() => {
      return callLimits.reduce(
        (obj, item) => ({
          ...obj,
          [item.call_limit]: item.tokens_per_time_unit,
        }),
        {},
      )
    }, [callLimits]),
    [edit, setEdit] = useState(false),
    [saveForm, setSaveForm] = useState(false),
    [formState, setFormState] = useState<FormStateType>(initialFormState),
    { id } = useParams<{ id: string }>(),
    handleClose = useCallback(() => {
      setEdit(false)
    }, [setEdit]),
    handleCancel = () => {
      setEdit(false)
      setFormState(initialFormState)
    },
    operations = useMemo(() => {
      return callLimits.reduce(
        (a, v) => ({
          ...a,
          [v.call_limit]: {
            bucket_size: '',
            tokens_per_time_unit: formState[v.call_limit],
            time_unit: v.time_unit,
          },
        }),
        {},
      )
    }, [callLimits, formState]),
    { getSettingsEndpoint, key } = useSettings(),
    // put request
    submitForm = useMutation({
      mutationFn: (params: ConnectionSettingsPutParams) =>
        SecureAxios.put(getSettingsEndpoint(key, 'throttling'), params),
      onSuccess: () => {
        setEdit(false)
        refetch()
        toast({
          type: 'success',
          message: t('successfullyUpdatedConnectionSettings', {
            id,
          }),
        })
      },
      onError: (error: ApiErrorType) => {
        toast({
          type: 'error',
          message: c('errorMessage', {
            message: `${error.status}, ${error.statusText}: ${error.data.error}`,
          }),
        })
      },
    }),
    handleSave = useCallback(() => {
      setSaveForm(true)
      handleClose()
      const params = {
        metadata: {
          metadata: metadata,
          operations: operations,
          paths: paths,
        },
      }
      submitForm.mutate(params)
    }, [metadata, operations, paths, setSaveForm, handleClose, submitForm]),
    // call limit max throttling object
    maxThrottling = useMemo(() => {
      return callLimits.reduce(
        (obj, item) => ({
          ...obj,
          [item.call_limit]: item.max,
        }),
        {},
      )
    }, [callLimits])

  useEffect(() => {
    setFormState(initialFormState)
  }, [initialFormState])

  return (
    <>
      <SideDrawer
        isOpen={edit}
        closeCallout={handleClose}
        headerContent={t('editCallLimits')}
        footerContent={
          <FormFooter
            cancelButtonProps={{ onClick: handleCancel }}
            saveButtonProps={{ onClick: handleSave }}
          />
        }
      >
        <StandardTable
          data={callLimits}
          config={editCallLimitsConfig(
            formState,
            setFormState,
            initialFormState,
            saveForm,
            maxThrottling,
            replaceDelimiterToggle,
          )}
          dataKey='call_limit'
          getData={() => {
            return []
          }}
          hasData={callLimits.length > 0}
          sort={sort}
          sortBy={sortBy}
          successStatus={status === 'success'}
          tableId='edit_call_limit_sidedrawer'
          hasMore={false}
          loading={status === 'loading'}
          customWidth='auto'
          noDataFields={{
            primaryText: t('noConnectionNotificationSettingsAvailable'),
          }}
        />
      </SideDrawer>
      <PageFooter
        rightSection={[
          {
            children: t('editCallLimits'),
            onClick: () => {
              setEdit(true)
            },
            type: 'button',
            styleType: 'primary-green',
          },
        ]}
      />
    </>
  )
}

export default CallLimitsForm
