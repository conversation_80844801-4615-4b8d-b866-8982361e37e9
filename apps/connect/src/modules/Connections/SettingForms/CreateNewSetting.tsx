import {
  FormFooter,
  notEmpty,
  SideDrawer,
  TextInput,
  toast,
} from '@patterninc/react-ui'
import React, { useState } from 'react'
import { useMutation } from '@tanstack/react-query'

import SecureAxios from '../../../common/services/SecureAxios'
import { settings } from '../../../common/services/ConnectAuthService'
import { type SettingType } from '../types'
import { type ApiErrorType } from '../../../common/types'
import { c, useTranslate } from '../../../common/helpers/TranslationHelpers'

const CreateNewSetting = ({
  isCreateNewSettingFormOpen,
  setIsCreateNewSettingFormOpen,
  selectedSettingState,
  setSelectedSettingState,
}: {
  isCreateNewSettingFormOpen: boolean
  setIsCreateNewSettingFormOpen: React.Dispatch<React.SetStateAction<boolean>>
  selectedSettingState: SettingType
  setSelectedSettingState: React.Dispatch<React.SetStateAction<SettingType>>
}) => {
  const { t } = useTranslate('connections'),
    [settingName, setSettingName] = useState(''),
    [settingDescription, setSettingDescription] = useState(''),
    submitForm = useMutation({
      mutationFn: (params: { name: string; description: string }) => {
        return SecureAxios.post(settings, params)
      },
      onSuccess: (result) => {
        setSelectedSettingState({
          ...selectedSettingState,
          name: result.data.name,
          id: result.data.id,
        })
        setIsCreateNewSettingFormOpen(false)
        toast({
          type: 'success',
          message: t('successfullyCreatedNewSetting', { settingName }),
        })
      },

      onError: (error: ApiErrorType) => {
        toast({
          message: c('errorMessage', {
            message: `${error.status}, ${error.statusText}: ${error.data.message}`,
          }),
          type: 'error',
        })
      },
    }),
    [isSettingNameValid, setIsSettingNameValid] = useState(true),
    handleSave = () => {
      submitForm.mutate({
        name: settingName,
        description: settingDescription,
      })
    },
    validateSettingName = (value: string): void => {
      const regex = /^[a-z_]+$/
      setIsSettingNameValid(regex.test(value))
    },
    handleCancel = () => {
      setIsCreateNewSettingFormOpen(false)
      setSettingName('')
      setSettingDescription('')
    }

  return (
    <SideDrawer
      layerPosition={2}
      footerContent={
        <FormFooter
          cancelButtonProps={{ onClick: handleCancel }}
          saveButtonProps={{
            children: t('createSetting'),
            disabled:
              !notEmpty(settingName) ||
              !isSettingNameValid ||
              !notEmpty(settingDescription),
            onClick: handleSave,
          }}
        />
      }
      isOpen={isCreateNewSettingFormOpen}
      closeCallout={handleCancel}
      headerContent={t('createANewSetting')}
    >
      <div className='flex flex-direction-column pat-gap-4'>
        <TextInput
          required
          callout={(_, value) => {
            validateSettingName(value as string)
            setSettingName(value as string)
          }}
          id='text'
          labelText={c('name')}
          type='text'
          value={settingName}
          hasError={!isSettingNameValid && notEmpty(settingName)}
          errorText={t(
            'invalidInputTheSettingNameMustContainOnlyLowercaseLetters',
          )}
          classType={
            !isSettingNameValid && notEmpty(settingName) ? 'error' : ''
          }
        />
        <TextInput
          required
          callout={(_, value) => {
            setSettingDescription(value as string)
          }}
          id='textarea'
          labelText={c('description')}
          type='textarea'
          value={settingDescription}
        />
      </div>
    </SideDrawer>
  )
}

export default CreateNewSetting
