import {
  FormFooter,
  notEmpty,
  PageFooter,
  Select,
  SideDrawer,
  TextInput,
  toast,
} from '@patterninc/react-ui'
import React, { useEffect, useState } from 'react'
import { useMutation, useQuery } from '@tanstack/react-query'
import { useParams } from 'react-router-dom'

import { useSettings } from '../../../context/settings-context'
import SecureAxios from '../../../common/services/SecureAxios'
import { settings } from '../../../common/services/ConnectAuthService'
import { type SettingTemp, type SettingType } from '../types'
import CreateNewSetting from './CreateNewSetting'
import { type ApiErrorType } from '../../../common/types'
import { c, useTranslate } from '../../../common/helpers/TranslationHelpers'

const OtherSettingForm = ({
  otherTableData,
  isSettingFormOpen,
  setIsSettingFormOpen,
  selectedSetting,
  refetch,
  tempSettingState,
  setTempSettingState,
}: {
  otherTableData: string[]
  isSettingFormOpen: boolean
  setIsSettingFormOpen: React.Dispatch<React.SetStateAction<boolean>>
  selectedSetting: SettingType
  refetch: () => void
  tempSettingState: SettingTemp[]
  setTempSettingState: React.Dispatch<
    React.SetStateAction<Record<string, string>[]>
  >
}): React.JSX.Element => {
  const { id } = useParams<{ id: string }>(),
    { t } = useTranslate('connections'),
    initialSettingState: SettingType = {
      name: '',
      value: '',
      id: 0,
    },
    [selectedSettingState, setSelectedSettingState] =
      useState<SettingType>(selectedSetting),
    [addNewSettingTemp, setAddNewSettingTemp] = useState<
      SettingType | undefined
    >(undefined),
    [isInCreateNewMode, setIsInCreateNewMode] = useState(false),
    [isCreateNewSettingFormOpen, setIsCreateNewSettingFormOpen] =
      useState(false),
    isDestinationDetailsPage = location.pathname.includes('/destinations'),
    { getSettingsEndpoint, key } = useSettings(),
    { mutate: submitForm } = useMutation({
      mutationKey: ['edit_or_create_setting'],
      mutationFn: (params: { metadata: JSON }) => {
        return SecureAxios.put(
          isDestinationDetailsPage
            ? getSettingsEndpoint(
                id || '',
                (selectedSettingState.id || 0).toString(),
              )
            : getSettingsEndpoint(key, selectedSettingState.name),
          params,
        )
      },
      onSuccess: () => {
        setIsSettingFormOpen(false)
        setIsInCreateNewMode(false)
        toast({ message: c('settingUpdatedSuccessfully'), type: 'success' })
        refetch()
        setSelectedSettingState(initialSettingState)
      },
      onError: (error: ApiErrorType) => {
        toast({
          message: c('errorMessage', {
            message: `${error.status}, ${error.statusText}: ${error.data.message || error.data.error}`,
          }),
          type: 'error',
        })
      },
    }),
    { data: settingsList, isLoading } = useQuery({
      queryKey: ['settings_list_key', isCreateNewSettingFormOpen],
      queryFn: ({ signal }) =>
        SecureAxios.get(settings, { signal }).then((response) => response.data),
    }),
    handleSave = () => {
      submitForm({
        metadata: JSON.parse(selectedSettingState.value),
      })
    },
    isValidJSON = (str: string): boolean => {
      try {
        const result = JSON.parse(str)
        if (typeof result !== 'object' || Array.isArray(result)) {
          return false
        }
        return true
        // eslint-disable-next-line @typescript-eslint/no-unused-vars
      } catch (e) {
        return false
      }
    },
    resetFormState = (isCancel: boolean) => {
      setIsSettingFormOpen(false)
      setIsInCreateNewMode(false)
      if (!isInCreateNewMode) {
        const updatedTempState = tempSettingState.map((obj) => {
          if (selectedSetting.name in obj) {
            return {
              ...obj,
              [selectedSetting.name]: isCancel
                ? ''
                : selectedSettingState.value,
            }
          }
          return obj
        })
        setTempSettingState(updatedTempState)
      }
      if (isInCreateNewMode && !isCancel) {
        setAddNewSettingTemp({
          name: selectedSettingState.name,
          value: selectedSettingState.value,
          id: selectedSettingState.id || 0,
        })
      } else if (isCancel) {
        setAddNewSettingTemp(undefined)
      }
    },
    handleCancel = () => {
      setSelectedSettingState(initialSettingState)
      resetFormState(true)
    },
    handleClose = () => {
      resetFormState(false)
    }

  useEffect(() => {
    setSelectedSettingState(selectedSetting)
  }, [selectedSetting])
  return (
    <>
      <SideDrawer
        noGradient
        isOpen={isSettingFormOpen}
        closeCallout={handleClose}
        headerContent={c('editSetting')}
        footerContent={
          <FormFooter
            cancelButtonProps={{ onClick: handleCancel }}
            saveButtonProps={{
              children: c('saveChanges'),
              disabled:
                selectedSettingState.name == '' ||
                !isValidJSON(selectedSettingState.value),
              onClick: handleSave,
            }}
          />
        }
      >
        <div className='flex flex-direction-column pat-gap-4'>
          <Select
            required
            loading={isLoading}
            noMenuPortal
            footerButtonProps={{
              text: c('createNewSetting'),
              callout: () => {
                setIsCreateNewSettingFormOpen(true)
              },
            }}
            options={settingsList?.filter(
              (setting: SettingType) =>
                setting.name !== 'notifications' &&
                setting.name !== 'throttling' &&
                !otherTableData.includes(setting.name),
            )}
            selectedItem={{
              name: selectedSettingState.name,
              id: selectedSettingState.id || 0,
            }}
            onChange={(d: { name: string; id: number }) => {
              setSelectedSettingState((prevState) => ({
                ...prevState,
                name: d.name,
                id: d.id,
                ...(isInCreateNewMode && { value: '' }),
              }))
            }}
            optionKeyName='name'
            labelKeyName='name'
            labelProps={{ label: c('settingName') }}
            disabled={!isInCreateNewMode}
          />
          <CreateNewSetting
            isCreateNewSettingFormOpen={isCreateNewSettingFormOpen}
            setIsCreateNewSettingFormOpen={setIsCreateNewSettingFormOpen}
            selectedSettingState={selectedSettingState}
            setSelectedSettingState={setSelectedSettingState}
          />
          <TextInput
            required
            callout={(_: unknown, value: string | number) => {
              setSelectedSettingState({
                ...selectedSettingState,
                value: value as string,
              })
            }}
            id='textarea'
            labelText={c('settingValue')}
            type='textarea'
            value={selectedSettingState.value}
            hasError={
              !isValidJSON(selectedSettingState.value) &&
              notEmpty(selectedSettingState.value)
            }
            errorText={t('invalidInputPleaseMakeSureEntryIsValidJSON')}
            placeholder={t('enterValidJSONAsASettingValue')}
            classType={
              !isValidJSON(selectedSettingState.value) &&
              notEmpty(selectedSettingState.value)
                ? 'error'
                : ''
            }
          />
        </div>
      </SideDrawer>
      <PageFooter
        rightSection={[
          {
            children: t('addNewSettings'),
            onClick: () => {
              setSelectedSettingState(addNewSettingTemp || initialSettingState)
              setIsInCreateNewMode(true)
              setIsSettingFormOpen(true)
            },
            disabled: false,
            type: 'button',
            styleType: 'primary-green',
          },
        ]}
      />
    </>
  )
}

export default OtherSettingForm
