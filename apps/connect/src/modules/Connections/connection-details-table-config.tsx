import { type ConfigItemType, Tooltip } from '@patterninc/react-ui'
import React from 'react'

import { type ConnectionDetailType } from './types'
import { c, t } from '../../common/helpers/TranslationHelpers'

export const ConnectionDetailsTableConfig = (): ConfigItemType<
  ConnectionDetailType,
  Record<string, unknown>
>[] => {
  return [
    {
      label: c('setting'),
      name: 'setting',
      noSort: true,
      cell: {
        children: (d: ConnectionDetailType): React.JSX.Element => {
          return <span>{d.key}</span>
        },
      },
    },
    {
      label: c('value'),
      name: 'value',
      noSort: true,
      cell: {
        children: (d: ConnectionDetailType): React.JSX.Element => {
          if (d.key === 'credential_state') {
            return (
              <Tooltip
                tooltipContent={t(
                  'connections:thisFieldRepresentsTheLiveStatusOfYourConnection',
                )}
              >
                <span>{d.value}</span>
              </Tooltip>
            )
          }
          return <span>{d.value}</span>
        },
      },
    },
  ]
}
