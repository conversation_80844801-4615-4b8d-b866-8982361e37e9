import React from 'react'
import {
  capitalize,
  type ConfigItemType,
  hasValue,
  MdashCheck,
  notEmpty,
  replaceDelimiter,
  replaceSymbol,
  TextInput,
} from '@patterninc/react-ui'

import { type JobCallLimitType } from './types'
import { t } from '../../common/helpers/TranslationHelpers'

export const editCallLimitsConfig = (
  state: Record<string, number>,
  setState: (state: Record<string, number>) => void,
  initialFormState: Record<string, number>,
  saveForm: boolean,
  maxThrottling: Record<string, number>,
  replaceDelimiterToggle?: boolean,
): ConfigItemType<JobCallLimitType, Record<string, unknown>>[] => {
  const inputHandler = (...params: unknown[]) => {
    const name = params[0] as string,
      value = params[1] as number
    if (hasValue(value)) {
      setState({
        ...state,
        [name]: value,
      })
    }
  }

  return [
    {
      cell: {
        children: (d: JobCallLimitType) => {
          return (
            <MdashCheck check={notEmpty(d.call_limit) && notEmpty(d.time_unit)}>
              {replaceDelimiterToggle
                ? replaceDelimiter({
                    text: d.call_limit,
                    joinOption: ' ',
                    splitSymbol: '-',
                    keepCasing: false,
                  })
                : replaceSymbol(d.call_limit, ' ', '-', false) +
                  ' / ' +
                  capitalize(d.time_unit)}
            </MdashCheck>
          )
        },
      },
      label: t('connections:callLimits'),
      mainColumn: true,
      name: 'call_limits',
      noSort: true,
    },
    {
      cell: {
        children: (d: JobCallLimitType) => {
          return (
            <TextInput
              callout={inputHandler}
              stateName={d.call_limit}
              type='number'
              value={
                notEmpty(d.tokens_per_time_unit) ? `${state[d.call_limit]}` : ''
              }
              max={maxThrottling[d.call_limit]}
            />
          )
        },
      },
      label: t('connections:callsHour'),
      mainColumn: true,
      name: 'tokens_per_time_unit',
      noSort: true,
    },
  ]
}
