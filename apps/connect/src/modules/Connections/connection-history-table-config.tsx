import { type ConfigItemType } from '@patterninc/react-ui'
import React from 'react'
import moment from 'moment'

import { type ConnectionHistoryType } from './types'
import { c, t } from '../../common/helpers/TranslationHelpers'

export const ConnectionHistoryTableConfig = (): ConfigItemType<
  ConnectionHistoryType,
  Record<string, unknown>
>[] => {
  return [
    {
      label: t('connections:events'),
      name: 'events',
      cell: {
        children: (d: ConnectionHistoryType): React.JSX.Element => {
          return (
            <span className={d.sortProp === 'events' ? 'fw-semi-bold' : ''}>
              {d.description}
            </span>
          )
        },
      },
    },
    {
      label: c('updatedBy'),
      name: 'updated_by',
      cell: {
        children: (d: ConnectionHistoryType): React.JSX.Element => {
          return (
            <span className={d.sortProp === 'updated_by' ? 'fw-semi-bold' : ''}>
              {d.user_email}
            </span>
          )
        },
      },
    },
    {
      label: c('timestamp'),
      name: 'timestamp',
      cell: {
        children: (d: ConnectionHistoryType): React.JSX.Element => {
          return (
            <span className={d.sortProp === 'timestamp' ? 'fw-semi-bold' : ''}>
              {moment(d.timestamp).format('MMM DD, YYYY HH:mm:ss A')}
            </span>
          )
        },
      },
    },
  ]
}
