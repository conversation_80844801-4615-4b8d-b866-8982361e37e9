import React from 'react'
import {
  Button,
  type ConfigItemType,
  MdashCheck,
  notEmpty,
} from '@patterninc/react-ui'

import { t } from '../../common/helpers/TranslationHelpers'
import { type Location } from './types'

export const connectionLocationsTableConfig = (
  onEdit: (location: Location) => void,
): ConfigItemType<Location, Record<string, unknown>>[] => {
  return [
    {
      cell: {
        children: (d: Location) => {
          return (
            <MdashCheck check={notEmpty(d.country_code || d.region)}>
              {d.country_code || d.region}
            </MdashCheck>
          )
        },
      },
      label: t('connections:location'),
      mainColumn: true,
      name: 'location',
      noSort: true,
    },
    {
      cell: {
        children: (d: Location) => {
          return <MdashCheck check={notEmpty(d.key)}>{d.key}</MdashCheck>
        },
      },
      label: t('connections:credentialLocationKey'),
      name: 'key',
      noSort: true,
    },
    {
      isButton: true,
      cell: {
        children: (d: Location) => {
          return (
            <Button onClick={() => onEdit(d)} as='button'>
              {t('common:edit')}
            </Button>
          )
        },
      },
      label: '',
      name: 'edit_location',
      noSort: true,
    },
  ]
}
