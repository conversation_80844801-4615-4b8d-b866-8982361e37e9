import React from 'react'
import {
  Alert,
  type SortByProps,
  type SortColumnProps,
  StandardTable,
  type TabsProps,
} from '@patterninc/react-ui'

import { type DetailsData, type SettingTemp, type SettingType } from './types'
import {
  connectionCallLimitTableConfig,
  connectionNotificationsTableConfig,
  connectionOthersTableConfig,
} from './connection-settings-table-config'
import { t } from '../../common/helpers/TranslationHelpers'

export const connectionSettingsSubtabs = ({
  tableData,
  setIsSingleNotificationEdit,
  setIsFormOpen,
  setCurrentStep,
  sort,
  sortBy,
  status,
  isSlackConnected,
  isSlackLoading,
  canEditNotifications,
  setIsSettingFormOpen,
  credential_name,
  refetch,
  setSelectedSetting,
  tempSettingState,
  connectionSettings,
  replaceDelimiterToggle,
}: {
  tableData: DetailsData
  setIsSingleNotificationEdit: React.Dispatch<React.SetStateAction<boolean>>
  setIsFormOpen: React.Dispatch<React.SetStateAction<boolean>>
  setCurrentStep: React.Dispatch<React.SetStateAction<number>>
  sort: SortColumnProps['sorter']
  sortBy: SortByProps
  status: string
  isSlackConnected: boolean
  isSlackLoading: boolean
  canEditNotifications: boolean
  setIsSettingFormOpen: React.Dispatch<React.SetStateAction<boolean>>
  credential_name: string
  refetch: () => void
  setSelectedSetting: React.Dispatch<React.SetStateAction<SettingType>>
  tempSettingState: SettingTemp[]
  connectionSettings: (id: string, type: string) => string
  replaceDelimiterToggle?: boolean
}): TabsProps['tabs'] => {
  return [
    {
      content: (
        <StandardTable
          data={tableData.call_limits}
          config={connectionCallLimitTableConfig(replaceDelimiterToggle)}
          dataKey='call_limit'
          getData={() => []}
          hasData={tableData.call_limits.length > 0}
          sort={sort}
          sortBy={sortBy}
          successStatus={status === 'success'}
          tableId='connection_call_limit_table'
          hasMore={false}
          loading={status === 'loading'}
          customHeight='auto'
          customWidth='auto'
          noDataFields={{
            primaryText: t('connections:noCallLimitsAvailable'),
          }}
        />
      ),
      id: 0,
      tabName: t('connections:callLimits'),
    },
    {
      content: (
        <>
          {!isSlackConnected && !isSlackLoading && canEditNotifications && (
            <Alert
              type='info'
              text={t('connections:youreNotConnectedToSlack')}
            />
          )}
          <StandardTable
            data={tableData.notifications}
            config={connectionNotificationsTableConfig(
              setIsSingleNotificationEdit,
              setIsFormOpen,
              setCurrentStep,
              canEditNotifications,
            )}
            dataKey='job_type'
            getData={() => []}
            hasData={tableData.notifications.length > 0}
            sort={sort}
            sortBy={sortBy}
            successStatus={status === 'success'}
            tableId='connection_notifications_table'
            hasMore={false}
            loading={status === 'loading'}
            customWidth='auto'
            customHeight='auto'
            noDataFields={{
              primaryText: t(
                'connections:noConnectionNotificationSettingsAvailable',
              ),
            }}
          />
        </>
      ),
      id: 1,
      tabName: t('connections:notifications'),
    },
    {
      content: (
        <StandardTable
          data={tableData.other_settings}
          config={connectionOthersTableConfig(
            setIsSettingFormOpen,
            credential_name,
            refetch,
            setSelectedSetting,
            tempSettingState,
            connectionSettings,
          )}
          dataKey='name'
          getData={() => []}
          hasData={tableData.other_settings.length > 0}
          sort={sort}
          sortBy={sortBy}
          successStatus={status === 'success'}
          tableId='connection_other_settings_table'
          hasMore={false}
          loading={status === 'loading'}
          customHeight='auto'
          customWidth='auto'
          noDataFields={{
            primaryText: t('connections:noOtherSettingAvailable'),
          }}
        />
      ),
      id: 2,
      tabName: t('connections:others'),
    },
  ]
}
