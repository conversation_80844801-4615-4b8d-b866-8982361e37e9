import {
  Button,
  ButtonGroup,
  capitalize,
  type ConfigItemType,
  type MenuProps,
  Tag,
} from '@patterninc/react-ui'
import moment from 'moment'
import React from 'react'
import { Link } from 'react-router-dom'

import { getLocationString } from './ConnectionHelpers'
import { type ConnectionType } from './types'
import styles from './_connections.module.scss'
import { c, t } from '../../common/helpers/TranslationHelpers'

export const ConnectionsTableConfig = (
  statusChangeCallback: (id: string, newStatus: number) => void,
  deleteCallback: (id: string) => void,
  statuses: { id: number; name: string }[],
  canDelete: boolean,
  canDeactivate: boolean,
  canViewDetails: boolean,
): ConfigItemType<ConnectionType, Record<string, unknown>>[] => {
  const columns: ConfigItemType<ConnectionType, Record<string, unknown>>[] = [
    {
      label: c('marketplace'),
      name: 'marketplace_name',
      cell: {
        children: (d: ConnectionType): React.JSX.Element => {
          return (
            <span
              className={`${
                d.sortProp === 'marketplace_name' ? 'fw-semi-bold' : ''
              } ${styles.marketplace}`}
            >
              <img src={d.marketplace_image_url} alt={d.marketplace_name} />
              {d.display_name}
            </span>
          )
        },
      },
    },
    {
      label: c('status'),
      name: 'status',
      noSort: true,
      cell: {
        children: (d: ConnectionType): React.JSX.Element => {
          return (
            <Tag color={d.status === 1 ? 'blue' : 'gray'}>
              <span className={d.sortProp === 'status' ? 'fw-semi-bold' : ''}>
                {statuses.find((s) => s.id === d.status)?.name}
              </span>
            </Tag>
          )
        },
      },
    },
    {
      label: c('supportId'),
      name: 'credential_name',
      cell: {
        children: (d: ConnectionType): React.JSX.Element => {
          return (
            <span
              className={d.sortProp === 'credential_name' ? 'fw-semi-bold' : ''}
            >
              {d.credential_name}
            </span>
          )
        },
      },
    },
    {
      label: t('connections:accountRegion'),
      name: 'country_code',
      cell: {
        children: (d: ConnectionType): React.JSX.Element => {
          return (
            <span
              className={d.sortProp === 'country_code' ? 'fw-semi-bold' : ''}
            >
              {getLocationString(d.locations)}
            </span>
          )
        },
      },
    },
    {
      label: c('connectedOn'),
      name: 'created_at',
      cell: {
        children: (d: ConnectionType): React.JSX.Element => {
          return (
            <span className={d.sortProp === 'created_at' ? 'fw-semi-bold' : ''}>
              {moment(d.created_at).format('MMM DD, YYYY HH:mm')}
            </span>
          )
        },
      },
    },
    {
      label: t('connections:expiresOn'),
      name: 'expires_on',
      cell: {
        children: (d: ConnectionType): React.JSX.Element => {
          return (
            <span className={d.sortProp === 'expires_on' ? 'fw-semi-bold' : ''}>
              {d.expires_on === 'N/A'
                ? d.expires_on
                : moment(d.expires_on).format('MMM DD, YYYY HH:mm')}
            </span>
          )
        },
      },
    },
  ]

  if (canViewDetails) {
    columns.push({
      label: '',
      name: 'link',
      noSort: true,
      isButton: true,
      cell: {
        children: (d: ConnectionType): React.JSX.Element => {
          const actions: MenuProps['actions'] = []
          if (canDeactivate) {
            actions.push(
              ...statuses.map((status) => {
                return {
                  text: t('connections:setStatusPrompt', {
                    status: capitalize(status.name),
                  }),
                  confirmation: {
                    body: t('connections:connectionStatusChangeBody', {
                      status: capitalize(status.name),
                    }),
                    type: 'blue' as const,
                    header: t('connections:connectionStatusChangeHeader', {
                      status: capitalize(status.name),
                    }),
                    confirmCallout: () => {
                      statusChangeCallback(d.credential_name, status.id)
                    },
                    confirmButtonText: t('connections:iUnderstandChangeStatus'),
                  },
                }
              }),
            )
          }
          if (canDelete)
            actions.push({
              text: c('delete'),
              destructive: true,
              hasDivider: true,
              confirmation: {
                body: t('connections:deleteConnectionConfirmationBody'),
                type: 'red',
                header: t('connections:deleteConnection?'),
                confirmCallout: () => {
                  deleteCallback(d.credential_name)
                },
                confirmButtonText: c('iUnderstandDelete'),
              },
            })
          return actions.length > 0 ? (
            <ButtonGroup
              buttons={[
                {
                  actions: actions,
                },
                {
                  as: 'link',
                  children: c('details'),
                  to: `/connections/${d.credential_name}`,
                  routerComponent: Link,
                },
              ]}
            />
          ) : (
            <Button
              as='link'
              to={`/connections/${d.credential_name}`}
              routerComponent={Link}
            >
              {c('details')}
            </Button>
          )
        },
      },
    })
  }

  return columns
}
