import { notEmpty } from '@patterninc/react-ui'
import { useQuery } from '@tanstack/react-query'
import { useCallback, useEffect, useMemo, useState } from 'react'

import { MAX_SUPPORT_ID_LENGTH } from '../../../common/constants'
import { useTranslate } from '../../../common/helpers/TranslationHelpers'
import { supportIdSuggestion } from '../../../common/services/ConnectAuthService'
import SecureAxios from '../../../common/services/SecureAxios'
import { useOrg } from '../../../context/org-context'

type UseSupportIdProps = {
  destinationId: string
  initialSupportId?: string
  needSuggestion?: boolean
}

type UseSupportIdResult = {
  isValid: boolean
  supportId: string
  setSupportId: (supportId: string) => void
  resetSupportId: () => void
  errorMessage?: string
}

type SupportIdSuggestionParams = {
  destination_id: string
  organization: string
  credential_name?: string
}

/**
 * A custom hook that manages support ID validation, loading states, and uniqueness checking.
 *
 * This hook handles:
 * - Support ID validation using regex pattern for allowed characters
 * - Checking if the support ID is unique via API
 * - Auto-suggesting a unique support ID if requested
 * - Tracking loading states during validation
 * - Providing appropriate error messages
 *
 * @param {Object} props - The hook props
 * @param {string} props.destinationId - The ID of the destination to check against
 * @param {string} [props.initialSupportId=''] - Initial support ID value
 * @param {boolean} [props.needSuggestion=false] - Whether to auto-populate with a suggested ID
 *
 * @returns {UseSupportIdResult} Result object containing:
 *   - isValid: Whether the current support ID is valid and unique
 *   - supportId: The current support ID value
 *   - setSupportId: Function to update the support ID
 *   - resetSupportId: Function to reset the support ID to its initial value
 *   - errorMessage: Current validation error message, if any
 */
export const useSupportId = ({
  destinationId,
  initialSupportId = '',
  needSuggestion = false,
}: UseSupportIdProps): UseSupportIdResult => {
  const { t } = useTranslate('connections')

  const [supportId, setSupportId] = useState<string>(initialSupportId)

  const passesRegex = useMemo(() => {
    return /^[a-z0-9-]*$/.test(supportId)
  }, [supportId])

  const { org } = useOrg()

  const supportIdSuggestionParams: SupportIdSuggestionParams = useMemo(() => {
    return {
      organization: org.code,
      destination_id: destinationId,
    }
  }, [org.code, destinationId])

  const { data: uniqueSuggestion, status: suggestionStatus } = useQuery<string>(
    {
      queryKey: [
        'supportIdSuggestion',
        supportIdSuggestionParams.destination_id,
        supportIdSuggestionParams.organization,
      ],
      queryFn: async () => {
        const result = await SecureAxios.get(supportIdSuggestion, {
          params: supportIdSuggestionParams,
        })
        return result.data.name
      },
      enabled:
        notEmpty(supportIdSuggestionParams.destination_id) &&
        notEmpty(supportIdSuggestionParams.organization),
    },
  )

  const supportIdUniquenessParams: SupportIdSuggestionParams = useMemo(() => {
    return {
      organization: org.code,
      destination_id: destinationId,
      credential_name: supportId,
    }
  }, [org.code, destinationId, supportId])

  const { data: isUnique, status: uniquenessStatus } = useQuery<boolean>({
    queryKey: [
      'supportIdUniqueness',
      supportIdUniquenessParams.destination_id,
      supportIdUniquenessParams.organization,
      supportIdUniquenessParams.credential_name,
      passesRegex,
      uniqueSuggestion,
    ],
    queryFn: async () => {
      const result = await SecureAxios.get(supportIdSuggestion, {
        params: supportIdUniquenessParams,
      })
      return result.data.name === supportIdUniquenessParams.credential_name
    },
    enabled:
      notEmpty(supportIdUniquenessParams.destination_id) &&
      notEmpty(supportIdUniquenessParams.credential_name) &&
      notEmpty(supportIdUniquenessParams.organization) &&
      supportIdUniquenessParams.credential_name !== uniqueSuggestion &&
      passesRegex,
  })

  const resetSupportId = useCallback(() => {
    setSupportId(initialSupportId)
  }, [initialSupportId, setSupportId])

  const isValid = useMemo(() => {
    return (
      passesRegex &&
      ((isUnique === true && uniquenessStatus === 'success') ||
        (notEmpty(initialSupportId) && initialSupportId === supportId) ||
        (notEmpty(uniqueSuggestion) &&
          uniqueSuggestion === supportId &&
          suggestionStatus === 'success'))
    )
  }, [
    initialSupportId,
    isUnique,
    passesRegex,
    suggestionStatus,
    supportId,
    uniqueSuggestion,
    uniquenessStatus,
  ])

  const errorMessage = useMemo(() => {
    if (!notEmpty(supportId)) {
      return t('supportIDRequired')
    }

    if (supportId.length >= MAX_SUPPORT_ID_LENGTH) {
      return t('supportIDTooLong')
    }

    if (!passesRegex) {
      return t('yourSupportIDMustContainOnlyNumbersLowercaseLettersAndHyphens')
    }

    if (notEmpty(initialSupportId) && initialSupportId === supportId) {
      return undefined
    }

    if (isUnique === false) {
      return t('thisSupportIDisNotUniqueTrySuggestion', {
        uniqueSupportIdSuggestion: uniqueSuggestion,
      })
    }
  }, [supportId, passesRegex, initialSupportId, isUnique, t, uniqueSuggestion])

  // handle the result of the suggestion request
  useEffect(() => {
    if (
      notEmpty(uniqueSuggestion) &&
      uniqueSuggestion !== undefined && // extra check for typescript
      needSuggestion
    ) {
      setSupportId(uniqueSuggestion)
    }
  }, [uniqueSuggestion, setSupportId, needSuggestion])

  return {
    isValid,
    supportId,
    setSupportId,
    resetSupportId,
    errorMessage,
  }
}
