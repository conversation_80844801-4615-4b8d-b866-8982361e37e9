export type ConnectionType = {
  locations: Location[]
  marketplace_id: number
  marketplace_name: string
  status: number
  created_at: number
  expires_on: number | string
  credential_name: string
  country_code: string
  sortProp: string
  marketplace_image_url: string
  display_name: string
  created_by: string
}

export type Location = {
  key: string
  country_code?: string | undefined
  region?: string | undefined
}

export type SettingTemp = {
  [key: string]: string
}
export type RawConnectionFromApi = {
  locations: Location[]
  content_version: string
  created_at: string
  credential_state_id: number
  customer_id: number
  destination_display_name: string
  destination_id: number
  destination_key: string
  destination_name: string
  display_name: string
  id: number
  name: string
  updated_at: string
  user_id: number
  expires_on: string | undefined
  marketplace_image_url: string
  user_full_name: string
}

export type ConnectionDetailType = {
  key: string
  value: string
}

export type RawConnectionDetails = {
  locations: Location[]
  user_full_name?: string
  content_version: number
  created_at: string
  credential_state_id: number
  customer_id: number
  destination_display_name: string
  destination_id: number
  destination_key: string
  destination_name: string
  display_name: string
  white_glove_status: string
  white_glove_contact_email: string
  id: number
  name: string
  updated_at: string
  user_id: number
  marketplace_image_url: string
}

export type ConnectionHistoryType = {
  id: number
  user_id: number
  timestamp: string
  user_full_name: string
  user_email: string
  description: string
  sortProp: string
}

export type CredentialDefinition = {
  version: number
  creation_method: 'FieldCollection' | 'OauthRedirect'
  runtime_action: string
  fields: Record<string, CredentialField>
  custom_regions?: boolean
}

export type CredentialField = {
  field_type: string
  size: number
  validation: string
  display_name: string
  display_description: string
  default?: number | string
  max?: number
  sensitive?: boolean
  options?: CredentialFieldOption[]
  multiOptions?: string[] | CredentialFieldOption[]
  value?: number | string
  create_only?: boolean
  optional?: boolean
  visible?: Record<string, string>
  message_on_selection?: string
}

export type CredentialFieldOption = {
  label: string
  value: string
  text?: string
  visible?: Record<string, string>
}

export type FieldValue =
  | string
  | number
  | boolean
  | undefined
  | CredentialFieldOption
  | string[]

export type CredentialFieldValues = Record<string, FieldValue>

export type MarketplaceFormOptionType = {
  search: string
  marketplaces: MarketplaceInfoType[]
  regions: RegionInfoType[]
  countries: CountryInfoType[]
  categories: CategoryInfoType[]
}

export type RegionInfoType = {
  id: number
  name: string
  abbreviation: string
}

export type CountryInfoType = {
  id: number
  name: string
  alpha_2_code: string
}
export type CategoryInfoType = {
  id: number
  name: string
}

export type MarketplaceInfoType = {
  id: number
  name: string
  display_name: string
  logo: string
  image_url: string
}

export type MarketplaceFormDetailType = {
  search: string
  region_ids: number[]
  country_ids: number[]
  category_ids: number[]
}

export type SelectedMarketplaceFormOptionsType = {
  regions: RegionInfoType[]
  countries: CountryInfoType[]
  categories: CategoryInfoType[]
}

export type JobNotificationType = {
  job_type: string
  notify_people: string
  notify_channels?: SlackChannel[]
  expiration?: string
  job_success: string
  job_failure: string
}

export type JobCallLimitType = {
  call_limit: string
  tokens_per_time_unit: string
  time_unit: string
  max: number
}

export type TabType = {
  content: string
  id: number
  tabName: string
}

export type NotificationDetailsType = {
  expiration?: string
  job_failure: string
  job_success: string
  notify_people: string
  notify_channels: SlackChannel[]
}

export type SlackChannel = {
  id: string
  name: string
}

export type RawNotificationType = {
  all: NotificationDetailsType
  inventory: NotificationDetailsType
  listings: NotificationDetailsType
  order: NotificationDetailsType
  syndication: NotificationDetailsType
}

export type NotificationSettingsPutParams = {
  metadata: RawNotificationType
}

export type ThrottlingDetailsType = Record<
  string,
  {
    api_key?: string
    bucket_size: string
    time_unit: string
    tokens_per_time_unit: string
    max: string
  }
>

export type RawThrottlingType = {
  metadata: {
    api_key_pattern: string
    algorithm: string
  }
  operations: ThrottlingDetailsType
  paths: Record<string, string>
}

export type RawSettingsType = {
  invoices: string
  notifications: RawNotificationType
  orders: string
  throttling: RawThrottlingType
}

export type RawCredentialSettingsType = {
  country: string
  destination_name: string
  key: string
  region: string
  settings: RawSettingsType
}

export type NewConnectionPostParams = {
  name?: string
  display_name?: string
  content_version: number
  customer_id?: number
  organization?: string
  redirect_uri?: string
  credential_state_id: number
  destination_id: number
  countries: { country_code: string }[]
  white_glove_contact_email?: string
  white_glove_status?: string
  regions: []
  content: CredentialFieldValues
  pre_sales_onboarding?: boolean
}

export type PatchConnectionParams = Partial<
  Pick<
    NewConnectionPostParams,
    | 'display_name'
    | 'name'
    | 'content'
    | 'countries'
    | 'white_glove_contact_email'
    | 'white_glove_status'
  > & { refresh?: boolean }
>

export type ConnectionSettingsPutParams = {
  metadata: RawThrottlingType
}
export type OtherSettingsType = {
  name: string
  value: string
}

export type DetailsData = {
  call_limits: {
    call_limit: string
    tokens_per_time_unit: string
    time_unit: string
    max: number
  }[]
  notifications: {
    job_type: string
    notify_people: string
    job_success: string
    job_failure: string
    expiration: string | undefined
  }[]
  other_settings: OtherSettingsType[]
}
export type FormStateType = Record<string, number>

export type RawDestinationDetails = {
  id: number
  name: string
  display_name: string
  destination_group: string
  destination_type_id: number
  destination_type_name: string
  destination_state_id: number
  image_url: string
  created_at: string
  updated_at: string
}

export type DestinationSettingType = {
  id: number
  setting_name: string
  metadata: string | JSON
  created_at: string
  updated_at: string
  destination_id: number
  setting_id: number
}

export type DestinationSettingsType = DestinationSettingType[]

export type SettingType = {
  name: string
  id?: number
  value: string
}

export type AppsOrgsResponseObject = {
  id: string
  name: string
  code: string
  created_at: string
  updated_at: string
}
