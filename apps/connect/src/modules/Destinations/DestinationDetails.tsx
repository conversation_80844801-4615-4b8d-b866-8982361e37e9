import React, { useContext, useEffect, useState } from 'react'
import { useLocation, useParams } from 'react-router-dom'
import {
  InformationPane,
  ListLoading,
  notEmpty,
  StandardTable,
  toast,
} from '@patterninc/react-ui'
import { useQuery } from '@tanstack/react-query'
import moment from 'moment'

import { ThemeContext } from '../../Context'
import SecureAxios from '../../common/services/SecureAxios'
import {
  destinationDetails,
  destinationSettings,
} from '../../common/services/ConnectAuthService'
import {
  type DestinationSettingsType,
  type RawDestinationDetails,
  type SettingType,
} from '../Connections/types'
import { destinationDetailsConfig } from './destination-details-table-config'
import OtherSettingForm from '../Connections/SettingForms/OtherSettingForm'
import { type ApiErrorType } from '../../common/types'
import { c, useTranslate } from '../../common/helpers/TranslationHelpers'
import { SettingsProvider } from '../../context/settings-context'

const DestinationDetails = (): React.JSX.Element => {
  const { updateBreadcrumbs } = useContext(ThemeContext),
    { t } = useTranslate('destinations'),
    [isSettingFormOpen, setIsSettingFormOpen] = useState(false),
    { pathname } = useLocation(),
    { id } = useParams<{ id: string }>(),
    { isFetching, data } = useQuery({
      queryKey: [id, 'layout_data'],
      queryFn: async () => {
        try {
          const result = await SecureAxios.get<RawDestinationDetails>(
            destinationDetails(id || ''),
          )
          return result.data
        } catch (error: unknown) {
          const errorMsg = error as ApiErrorType
          toast({ type: 'error', message: errorMsg.data.error })
          throw error
        }
      },
      enabled: notEmpty(id),
    }),
    {
      data: tableData,
      status,
      refetch,
    } = useQuery({
      queryKey: [id, 'table_data'],
      queryFn: async () => {
        try {
          const result = await SecureAxios.get<DestinationSettingsType>(
            destinationSettings(id || ''),
          )
          return result.data || []
        } catch (error: unknown) {
          const errorMsg = error as ApiErrorType
          toast({ type: 'error', message: errorMsg.data.error })
          return []
        }
      },
      enabled: notEmpty(id),
    }),
    [selectedSetting, setSelectedSetting] = useState<SettingType>({
      name: '',
      value: '',
      id: 0,
    }),
    [tempSettingState, setTempSettingState] = useState<
      Record<string, string>[]
    >([])

  useEffect(() => {
    if (tableData) {
      setTempSettingState(
        tableData.map((setting) => ({ [setting.setting_name]: '' })),
      )
    }
  }, [tableData])

  useEffect(() => {
    updateBreadcrumbs({
      name: t('destinationDetails'),
      link: pathname,
      changeType: 'tab',
    })
  }, [data?.display_name, pathname, t, updateBreadcrumbs])

  const tableSettingNames = tableData
    ? tableData?.map((data) => data.setting_name)
    : []

  return (
    <div className='has-info-pane'>
      <div>
        {isFetching ? (
          <ListLoading />
        ) : (
          <InformationPane
            header={{
              labelAndData: {
                label: data?.display_name,
                data: '',
                check: true,
              },
            }}
          >
            <InformationPane.ImageAndName
              product={{ name: data?.display_name || '' }}
              imgUrl={data?.image_url}
            />
            <InformationPane.Divider />
            <InformationPane.Section
              data={[
                {
                  label: t('destinationType'),
                  data: data?.destination_type_name,
                  check: true,
                },
                {
                  label: c('connectedOn'),
                  data: moment(data?.created_at).format('MMM DD, YYYY HH:mm'),
                  check: true,
                },
                {
                  label: c('updatedOn'),
                  data: moment(data?.updated_at).format('MMM DD, YYYY HH:mm'),
                  check: true,
                },
              ]}
            />
          </InformationPane>
        )}
      </div>
      <div>
        <StandardTable
          data={tableData || []}
          config={destinationDetailsConfig(
            refetch,
            setIsSettingFormOpen,
            setSelectedSetting,
            tempSettingState,
          )}
          dataKey='setting_name'
          getData={() => []}
          hasData={!!tableData?.length}
          successStatus={status === 'success'}
          tableId='destination_settings_table'
          hasMore={false}
          loading={status === 'pending'}
          customHeight='auto'
          customWidth='auto'
          noDataFields={{
            primaryText: t('noSettingsAvailable'),
          }}
          sort={() => {
            // TODO: Implement sorting
          }}
          sortBy={{
            prop: '',
            flip: false,
          }}
        />
      </div>
      <SettingsProvider id={id?.toString() || ''} settingLevel='destination'>
        <OtherSettingForm
          otherTableData={tableSettingNames}
          isSettingFormOpen={isSettingFormOpen}
          setIsSettingFormOpen={setIsSettingFormOpen}
          selectedSetting={selectedSetting}
          refetch={refetch}
          //Todo: Implement form persistence for Destination details
          tempSettingState={tempSettingState}
          setTempSettingState={setTempSettingState}
        />
      </SettingsProvider>
    </div>
  )
}

export { DestinationDetails }
