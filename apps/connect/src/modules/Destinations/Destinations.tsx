import React, { useContext, useEffect, useMemo, useState } from 'react'
import { useLocation } from 'react-router-dom'
import {
  SearchBar,
  type SortByProps,
  type SortColumnProps,
  StandardTable,
} from '@patterninc/react-ui'
import { useInfiniteQuery } from '@tanstack/react-query'

import destinationsTableConfig from './destinations-table-config'
import { ThemeContext } from '../../Context'
import { type DestinationType } from './types'
import { marketplacesApi } from '../../common/services/ConnectAuthService'
import SecureAxios from '../../common/services/SecureAxios'
import sortOptions from '../../common/services/SortOptions'
import { c, useTranslate } from '../../common/helpers/TranslationHelpers'
import { useOrg } from '../../context/org-context'
import useResetOnOrgSwitch from '../../common/helpers/useResetOnOrgSwitch'

const Destinations = (): React.JSX.Element => {
  const { updateBreadcrumbs } = useContext(ThemeContext),
    { t } = useTranslate('destinations'),
    { pathname } = useLocation(),
    [searchValue, setSearchValue] = useState(''),
    [sortBy, setSortBy] = useState<SortByProps>({
      prop: 'marketplace_name',
      flip: false,
    }),
    sort: SortColumnProps['sorter'] = (sortObj: {
      activeColumn: string
      direction: boolean
      lowerCaseParam?: boolean | undefined
    }) => {
      setSortBy({
        prop: sortObj.activeColumn,
        flip: sortObj.direction,
      })
    },
    { org } = useOrg(),
    { data, status, fetchNextPage, hasNextPage } = useInfiniteQuery({
      queryKey: [org, marketplacesApi, searchValue, 'destinations_table'],
      queryFn: ({ pageParam = 1, signal }) => {
        return SecureAxios.get<DestinationType[]>(marketplacesApi, {
          params: {
            search: searchValue,
            pageParam: pageParam > 1 ? pageParam : undefined,
          },
          signal,
        })
      },
      initialPageParam: 1,
      getNextPageParam: (previousPage) => {
        if (
          previousPage.headers['current-page'] !==
          previousPage.headers['total-pages']
        ) {
          return Number(previousPage.headers['current-page']) + 1
        }
        return undefined
      },
    }),
    tableData: (DestinationType & { sortProp: string })[] = useMemo(() => {
      const destinations = data
        ? data?.pages?.flatMap((page) => page?.data)
        : []
      return sortOptions(
        destinations.map((d) => {
          return { ...d, sortProp: sortBy.prop }
        }),
        'display_name',
        true,
      )
    }, [data, sortBy.prop])

  useEffect(() => {
    updateBreadcrumbs({
      name: c('destinations'),
      link: pathname,
      changeType: 'rootLevel',
    })
  }, [pathname, updateBreadcrumbs])

  useResetOnOrgSwitch(setSearchValue)

  return (
    <>
      <SearchBar value={searchValue} onChange={setSearchValue} />
      <StandardTable
        data={tableData}
        hasData={status === 'success' && tableData.length > 0}
        config={destinationsTableConfig()}
        successStatus={status === 'success'}
        loading={status === 'pending'}
        hasMore={hasNextPage || false}
        tableId='destinations-table'
        noDataFields={{
          primaryText: t('noDestinationsFound'),
          secondaryText: t('trySearchingForADifferentDestination'),
        }}
        sortBy={sortBy}
        sort={sort}
        dataKey={'id'}
        getData={fetchNextPage}
      />
    </>
  )
}

export { Destinations }
