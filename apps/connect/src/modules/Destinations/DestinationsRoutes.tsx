import React from 'react'
import { Route, Routes } from 'react-router-dom'

import PrivateRoute from '../../common/components/PrivateRoute/PrivateRoute'
import { DestinationDetails } from './DestinationDetails'
import { Destinations } from './Destinations'
import { useHasPatternAdminRole } from '../../context/auth-context'

const DestinationsRoutes = (): React.JSX.Element => {
  const isPatternAdmin = useHasPatternAdminRole()
  return (
    <Routes>
      {isPatternAdmin && (
        <>
          <Route
            index
            element={
              <PrivateRoute>
                <Destinations />
              </PrivateRoute>
            }
          />
          <Route path=':id/*' element={<DestinationDetails />} />
        </>
      )}
    </Routes>
  )
}

export default DestinationsRoutes
