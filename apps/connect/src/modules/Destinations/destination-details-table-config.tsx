import React from 'react'
import {
  ButtonGroup,
  type ConfigItemType,
  MdashCheck,
  notEmpty,
  toast,
} from '@patterninc/react-ui'

import {
  type DestinationSettingType,
  type SettingType,
} from '../Connections/types'
import { destinationSettings } from '../../common/services/ConnectAuthService'
import SecureAxios from '../../common/services/SecureAxios'
import { c } from '../../common/helpers/TranslationHelpers'

export const destinationDetailsConfig = (
  refetch: () => void,
  setIsSettingFormOpen: (value: boolean) => void,
  setSelectedSetting: React.Dispatch<React.SetStateAction<SettingType>>,
  tempSettingState: Record<string, string>[],
): ConfigItemType<DestinationSettingType, Record<string, unknown>>[] => {
  const deleteCallback = (id: number, setting_id: number) => {
    SecureAxios.delete(
      destinationSettings(id.toString(), setting_id.toString()),
    )
      .then(() => {
        toast({ type: 'success', message: c('settingDeleted') })
        refetch()
      })
      .catch((error) => {
        toast({
          type: 'error',
          message: c('errorMessage', { message: error.data.error }),
        })
      })
  }

  return [
    {
      cell: {
        children: (d: DestinationSettingType) => {
          return <span>{d.setting_name}</span>
        },
      },
      label: c('settingName'),
      mainColumn: true,
      name: 'setting_name',
      noSort: true,
    },
    {
      cell: {
        children: (d: DestinationSettingType) => {
          return (
            <MdashCheck check={notEmpty(d.metadata)}>
              {JSON.stringify(d.metadata, null, 2)}
            </MdashCheck>
          )
        },
      },
      label: c('settingValue'),
      mainColumn: true,
      name: 'setting_value',
      noSort: true,
    },
    {
      isButton: true,
      label: '',
      name: 'edit',
      noSort: true,
      cell: {
        children: (d: DestinationSettingType): React.JSX.Element => {
          return (
            <ButtonGroup
              buttons={[
                {
                  actions: [
                    {
                      text: c('delete'),
                      destructive: true,
                      hasDivider: true,
                      confirmation: {
                        body: c('areYouSureYouWantToDeleteThisSetting'),
                        type: 'red',
                        header: c('deleteSetting?'),
                        confirmCallout: () => {
                          deleteCallback(d.destination_id, d.setting_id)
                        },
                        confirmButtonText: c('iUnderstandDelete'),
                      },
                    },
                  ],
                },
                {
                  as: 'button',
                  children: c('edit'),
                  onClick: () => {
                    setIsSettingFormOpen(true)
                    const foundValue = tempSettingState?.find(
                      (obj) => d.setting_name in obj,
                    )?.[d.setting_name]
                    setSelectedSetting({
                      name: d.setting_name,
                      id: d.setting_id,
                      value:
                        foundValue != undefined && notEmpty(foundValue)
                          ? foundValue
                          : JSON.stringify(d.metadata, null, 2),
                    })
                  },
                },
              ]}
            />
          )
        },
      },
    },
  ]
}
