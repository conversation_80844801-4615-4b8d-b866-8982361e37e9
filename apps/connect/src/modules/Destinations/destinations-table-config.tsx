import { Button, type ConfigItemType } from '@patterninc/react-ui'
import React from 'react'
import { Link } from 'react-router-dom'

import { type DestinationType } from './types'
import styles from './_destinations.module.scss'
import { c, t } from '../../common/helpers/TranslationHelpers'

type DestinationRowType = DestinationType & { sortProp: string }

const destinationsTableConfig = (): ConfigItemType<
  DestinationType,
  Record<string, unknown>
>[] => {
  return [
    {
      name: 'logo',
      label: t('destinations:logo'),
      noSort: true,
      cell: {
        children: (d): React.JSX.Element => {
          return (
            <div className={styles.marketplace}>
              <img src={d.image_url} alt={d.name} />
            </div>
          )
        },
      },
    },
    {
      name: 'name',
      label: c('name'),
      noSort: true,
      cell: {
        children: (d): React.JSX.Element => {
          return (
            <span className={d.sortProp === 'name' ? 'fw-semi-bold' : ''}>
              {d.display_name}
            </span>
          )
        },
      },
    },
    {
      name: 'type',
      label: c('type'),
      noSort: true,
      cell: {
        children: (d): React.JSX.Element => {
          return (
            <span className={d.sortProp === 'type' ? 'fw-semi-bold' : ''}>
              {d.destination_type_id}
            </span>
          )
        },
      },
    },
    {
      name: 'details',
      noSort: true,
      label: '',
      isButton: true,
      cell: {
        children: (d: DestinationRowType): React.JSX.Element => {
          return (
            <Button
              as='link'
              to={`/destinations/${d.id}`}
              routerComponent={Link}
            >
              {c('details')}
            </Button>
          )
        },
      },
    },
  ]
}

export default destinationsTableConfig
