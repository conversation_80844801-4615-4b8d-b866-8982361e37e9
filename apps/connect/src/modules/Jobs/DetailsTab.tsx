import React from 'react'
import JsonView from '@uiw/react-json-view'
import { Button, notEmpty } from '@patterninc/react-ui'

import styles from './_jobs.module.scss'
import { type JobType } from './types'
import { useTranslate } from '../../common/helpers/TranslationHelpers'

const DetailsTab = ({
  jobData,
  setFetchFullRequestBody,
  fullRequestBody,
  fullResponseBody,
  setFetchFullResponseBody,
  fetchFullResponseBody,
  isRequestBodyToggleOn,
  setIsRequestBodyToggleOn,
}: {
  jobData: JobType | undefined
  setFetchFullRequestBody: React.Dispatch<React.SetStateAction<boolean>>
  fullRequestBody: { request_body: string } | undefined
  fullResponseBody: { response_body: string } | undefined
  setFetchFullResponseBody: React.Dispatch<React.SetStateAction<boolean>>
  fetchFullResponseBody: boolean
  isRequestBodyToggleOn: boolean
  setIsRequestBodyToggleOn: React.Dispatch<React.SetStateAction<boolean>>
}) => {
  const { t } = useTranslate('jobs')

  // decoding the request path
  const decodedRequestPath = jobData?.request_path
    ? decodeURIComponent(jobData.request_path)
    : ''

  return (
    <>
      <div className={styles.headerContainer}>
        <p className='fs-16 pat-pb-4 fw-bold'>{t('requestPath')}</p>
      </div>
      <div
        className={`bdr bdrc-medium-purple pat-p-2 ${styles.metadataBodyContainer} ${
          decodedRequestPath !== 'null' && decodedRequestPath
            ? styles.alignStart
            : styles.alignCenter
        }`}
      >
        {decodedRequestPath !== 'null' && decodedRequestPath ? (
          <p className='pat-p-1 fs-14'>{decodedRequestPath}</p>
        ) : (
          <p className='pat-p-5 fs-16 pat-pb-4'>
            {t('noRequestPathAvailable')}
          </p>
        )}
      </div>
      <div className='pat-pb-8'></div>
      <div className={styles.headerContainer}>
        <p className='fs-16 pat-pb-4 fw-bold'>{t('requestBody')}</p>
        <div className='pat-pb-4'>
          {(jobData?.is_oversized_request ||
            jobData?.job_type === 'Syndication') &&
            !notEmpty(fullRequestBody?.request_body) && (
              <Button
                styleType='text-blue'
                onClick={() => {
                  setIsRequestBodyToggleOn(true)
                  setFetchFullRequestBody(true)
                }}
              >
                {t('jobs:fullRequestBody')}
              </Button>
            )}
        </div>
      </div>
      <div
        className={`bdr bdrc-medium-purple pat-p-2 ${styles.metadataBodyContainer} ${
          jobData?.request_body !== 'null' && jobData?.request_body
            ? styles.alignStart
            : styles.alignCenter
        }`}
      >
        {jobData?.request_body !== 'null' && jobData?.request_body ? (
          (() => {
            try {
              const parsedData = JSON.parse(
                (isRequestBodyToggleOn
                  ? fullRequestBody?.request_body
                  : jobData.request_body) || '',
              )
              return (
                <JsonView
                  displayObjectSize={false}
                  displayDataTypes={false}
                  value={parsedData}
                  shortenTextAfterLength={0}
                />
              )
            } catch {
              return jobData.job_type === 'Syndication' ? (
                <JsonView
                  displayObjectSize={false}
                  displayDataTypes={false}
                  value={JSON.parse(jobData?.request_body) || ''}
                  shortenTextAfterLength={0}
                />
              ) : (
                <p
                  onClick={() => {
                    setIsRequestBodyToggleOn(true)
                    setFetchFullRequestBody(true)
                  }}
                  className='pat-p-1 fs-14 cursor-pointer'
                >
                  {jobData.request_body}
                </p>
              )
            }
          })()
        ) : (
          <p className='pat-p-5 fs-16 pat-pb-4'>
            {t('noRequestBodyAvailable')}
          </p>
        )}
      </div>
      <div className='pat-pb-8'></div>
      <div className={styles.headerContainer}>
        <p className='fs-16 pat-pb-4 fw-bold'>{t('responseBody')}</p>
        <div className='pat-pb-4'>
          {jobData?.status_reason?.endsWith('...') &&
            !notEmpty(fullResponseBody?.response_body) && (
              <Button
                styleType='text-blue'
                onClick={() => {
                  setFetchFullResponseBody(true)
                }}
              >
                {t('jobs:fullResponseBody')}
              </Button>
            )}
        </div>
      </div>
      <div
        className={`bdr bdrc-medium-purple pat-p-2 ${styles.metadataBodyContainer} ${
          jobData?.status_reason !== 'null' && jobData?.status_reason
            ? styles.alignStart
            : styles.alignCenter
        }`}
      >
        {jobData?.status_reason !== 'null' && jobData?.status_reason ? (
          (() => {
            try {
              const parsedData = JSON.parse(
                (fetchFullResponseBody
                  ? fullResponseBody?.response_body
                  : jobData.status_reason) || '',
              )

              return (
                <JsonView
                  displayObjectSize={false}
                  displayDataTypes={false}
                  value={parsedData}
                  shortenTextAfterLength={0}
                />
              )
            } catch {
              return (
                <p
                  onClick={() => {
                    setFetchFullResponseBody(true)
                  }}
                  className='pat-p-1 fs-14 cursor-pointer'
                >
                  {jobData.status_reason}
                </p>
              )
            }
          })()
        ) : (
          <p className='pat-p-5 fs-16 pat-pb-4'>
            {t('noResponseBodyAvailable')}
          </p>
        )}
      </div>
    </>
  )
}

export default DetailsTab
