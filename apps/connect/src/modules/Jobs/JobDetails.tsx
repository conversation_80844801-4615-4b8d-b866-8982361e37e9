import {
  <PERSON><PERSON>,
  InformationPane,
  <PERSON>Loa<PERSON>,
  notE<PERSON>y,
  <PERSON>Footer,
  Ta<PERSON>,
  toast,
  useToggle,
} from '@patterninc/react-ui'
import React, {
  useCallback,
  useContext,
  useEffect,
  useMemo,
  useState,
} from 'react'
import { useLocation, useNavigate, useParams } from 'react-router-dom'
import { useInfiniteQuery, useMutation, useQuery } from '@tanstack/react-query'
import _ from 'lodash'
import moment from 'moment'
import { Link } from 'react-router-dom'

import { ThemeContext } from '../../Context'
import SecureAxios from '../../common/services/SecureAxios'
import {
  type JobDetailsProps,
  type JobType,
  type RerunJobParamType,
} from './types'
import {
  getFullBody,
  jobDetails,
  rerunJob,
  subjobs as subjobsApi,
} from '../../common/services/ConnectEngineService'
import { PRIVILEGES, useHasPrivilege } from '../../context/auth-context'
import { useOrg } from '../../context/org-context'
import JobSideDrawer from './JobSideDrawer'
import { c, useTranslate } from '../../common/helpers/TranslationHelpers'
import SubJobsTab from './SubJobsTab'
import DetailsTab from './DetailsTab'
import ResendCallbackSideDrawer from './ResendCallbackSideDrawer'
import { type ApiErrorType } from '../../common/types'
import { useUser } from '../../context/user-context'

const JobDetails = ({
  isOpen,
  setIsOpen,
  formFields,
  setFormFields,
  handleCancel,
  handleIsDisabled,
  setIsScheduledInPast,
  scheduleAt,
  setScheduleAt,
  isCallbackFormOpen,
  setIsCallbackFormOpen,
}: JobDetailsProps): React.JSX.Element => {
  const spinnerToggle = useToggle('replace_downloadloader_with_spinner')

  const { t } = useTranslate('jobs')
  const { pathname } = useLocation(),
    canViewJobDetails = useHasPrivilege(PRIVILEGES.VIEW_JOB_DETAILS),
    { id } = useParams<{ id: string }>(),
    { updateBreadcrumbs } = useContext(ThemeContext),
    [downloadingFile, setDownloadingFile] = useState(''),
    {
      data: subjobs,
      fetchNextPage,
      hasNextPage: hasMoreSubJobs,
      status: subjobsStatus,
    } = useInfiniteQuery({
      queryKey: ['get_subjob', id, subjobsApi],
      queryFn: ({ pageParam = 1, signal }) => {
        return SecureAxios.get<JobType[]>(subjobsApi(id || ''), {
          params: { page: pageParam, per_page: 20 },
          signal,
        })
      },
      initialPageParam: 1,
      getNextPageParam: (previousPage) => {
        return previousPage &&
          previousPage.headers['current-page'] !==
            previousPage.headers['total-pages']
          ? Number(previousPage.headers['current-page']) + 1
          : undefined
      },
      enabled: canViewJobDetails,
    }),
    subjobRows = useMemo(() => {
      return subjobs?.pages.flatMap((page) => page?.data)
    }, [subjobs]),
    subjobCount = useMemo(() => {
      if (subjobs?.pages === undefined) {
        return 0
      }
      return subjobs.pages.length > 0
        ? Number(subjobs.pages[0].headers['total-count'])
        : 0
    }, [subjobs]),
    convertObjectToStrings = useCallback((row: JobType, header: string) => {
      const value = row[header as keyof typeof row]?.toString() || ''
      // Escape double quotes and commas for the CSV in case of stringified JSON
      return notEmpty(value) ? `"${value.replace(/"/g, '""')}"` : 'N/A'
    }, []),
    convertJsonToCsv = useCallback(
      (data: JobType) => {
        const subjobs = data?.subjobs,
          headers = _.reject(Object.keys(data), (key) => key === 'subjobs'),
          csv = [
            'Parent Job:',
            headers.join(','),
            headers
              .map((header) => convertObjectToStrings(data, header))
              .join(','),
            '',
            'Sub Jobs:',
            headers.join(','),
            (subjobs || [])
              .map((subjob) => {
                return headers
                  .map((header) => convertObjectToStrings(subjob, header))
                  .join(',')
              })
              .join('\r\n'),
          ].join('\r\n')
        return csv
      },
      [convertObjectToStrings],
    ),
    { org } = useOrg(),
    { data: jobData, status } = useQuery({
      queryKey: ['jobDetails', canViewJobDetails, id, org],
      queryFn: ({ signal }) => {
        if (typeof id === 'undefined') {
          toast({ type: 'error', message: c('missingCredentialIdInPath') })
          return
        }
        return SecureAxios.get<JobType>(jobDetails(id), {
          signal,
          params: { org_code: org },
        }).then((result) => {
          return result.data
        })
      },
      enabled: canViewJobDetails,
    }),
    downloadFile = useCallback(
      async (file_format: string) => {
        if (jobData === undefined) {
          return
        }
        const jobJsonData = { ...jobData, subjobs: subjobRows }
        const file_data =
          file_format === 'csv'
            ? convertJsonToCsv(jobJsonData)
            : JSON.stringify(jobJsonData)
        const url = window.URL.createObjectURL(
          new Blob([file_data], { type: `text/${file_format}` }),
        )
        const link = document.createElement('a')
        link.href = url
        link.setAttribute(
          'download',
          `JobDetails${jobData.job_id}.${file_format}`,
        )
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link)
      },
      [subjobRows, jobData, convertJsonToCsv],
    ),
    startDownloadingFile = (type: string) => {
      setDownloadingFile(type)
    },
    formatTime = useCallback((dateTimeString: string) => {
      return moment(dateTimeString).format('MMM DD, YYYY HH:mm')
    }, []),
    hasRerunAsNewJobPrivilege = useHasPrivilege(PRIVILEGES.RERUN_AS_NEW_JOB),
    [isRequestBodyToggleOn, setIsRequestBodyToggleOn] = useState(false),
    [activeTab, setActiveTab] = useState(0)

  useEffect(() => {
    updateBreadcrumbs({
      name: `${t('jobDetails')} - ${id}`,
      link: pathname,
      changeType: 'tab',
    })
    setActiveTab(0)
    setFetchFullResponseBody(false)
    setFetchFullRequestBody(false)
  }, [updateBreadcrumbs, pathname, id, t])

  useEffect(() => {
    if (!downloadingFile) {
      return
    }
    if (hasMoreSubJobs && subjobCount > 0) {
      fetchNextPage()
    } else {
      downloadFile(downloadingFile)
      setDownloadingFile('')
    }
  }, [
    downloadingFile,
    hasMoreSubJobs,
    subjobRows,
    subjobCount,
    setDownloadingFile,
    downloadFile,
    fetchNextPage,
  ])

  const infoPaneData = useMemo(() => {
    return [
      {
        label: t('jobName'),
        data: jobData?.job_name || '',
        check: notEmpty(jobData?.job_name),
      },
      {
        label: t('jobType'),
        data: jobData?.job_type || '',
        check: notEmpty(jobData?.job_type),
      },
      {
        label: c('supportId'),
        data: (
          <Button
            as='link'
            state={{ job_id: jobData?.job_id }}
            styleType='text-blue'
            to={`/connections/${jobData?.support_id}/details`}
            routerComponent={Link}
          >
            {jobData?.support_id || ''}
          </Button>
        ),
        check: notEmpty(jobData?.support_id),
      },
      {
        label: t('description'),
        data: jobData?.notes || '',
        check: notEmpty(jobData?.notes),
      },
      {
        label: c('createdBy'),
        data: jobData?.created_by || '',
        check: notEmpty(jobData?.created_by),
      },
      {
        label: c('marketplace'),
        data: jobData?.marketplace || '',
        check: notEmpty(jobData?.marketplace),
      },
      {
        label: c('createdAt'),
        data: formatTime(jobData?.created_on || ''),
        check: notEmpty(jobData?.created_on),
      },
      {
        label: t('scheduledFor'),
        data: formatTime(jobData?.scheduled_to_run_at || ''),
        check: notEmpty(jobData?.scheduled_to_run_at),
      },
      {
        label: c('startedAt'),
        data: formatTime(jobData?.started_on || ''),
        check: notEmpty(jobData?.started_on),
      },
      {
        label: c('completedAt'),
        data: formatTime(jobData?.ended_on || ''),
        check: notEmpty(jobData?.ended_on),
      },
      {
        label: t('jobs:retryAttempt'),
        data: (jobData?.retry_count as number) || 0,
        check: notEmpty(jobData?.retry_count),
      },
    ]
  }, [jobData, formatTime, t])

  const navigate = useNavigate(),
    navigateToNewJob = useCallback(
      (jobId: string) => {
        const navigateTo = `/jobs/${jobId}`
        navigate({
          pathname: navigateTo,
        })
      },
      [navigate],
    ),
    submitForm = useMutation({
      mutationFn: (params: RerunJobParamType) => {
        return SecureAxios.post(rerunJob(id || ''), params)
      },
      onSuccess: (result) => {
        setIsOpen(false)
        navigateToNewJob(result.data.job_id)
        toast({
          type: 'success',
          message: `${t('successfullyCreatedJob')} '${result.data.job_id}'${
            notEmpty(scheduleAt)
              ? `. ${c('scheduledFor', { scheduleAt: scheduleAt })}`
              : ''
          }`,
        })
      },
      onError: (e: ApiErrorType) => {
        toast({
          type: 'error',
          message: `Error: ${e.status}, ${e.statusText}: ${e.data.error}`,
        })
      },
    }),
    { email } = useUser(),
    getQueryParamsString = (urlString: string) => {
      const queryString = urlString?.split('?')[1]
      return queryString
    },
    isJobIncomplete =
      jobData?.status !== 'completed' &&
      jobData?.status !== 'failed' &&
      jobData?.status !== 'partially_failed',
    isSyndicationJob = jobData?.job_type === 'Syndication',
    [fetchFullRequestBody, setFetchFullRequestBody] = useState(false),
    [fetchFullResponseBody, setFetchFullResponseBody] = useState(false),
    { data: fullRequestBody, status: bodyStatus } = useQuery({
      queryKey: [
        'getFullBody',
        fetchFullRequestBody,
        jobData?.is_oversized_request,
        id,
      ],
      queryFn: async ({ signal }) => {
        const response = await SecureAxios.get(
          getFullBody('full_request_body', id || ''),
          {
            signal,
          },
        )
        return response.data
      },
      enabled:
        fetchFullRequestBody &&
        ((jobData?.is_oversized_request ?? false) ||
          jobData?.job_type === 'Syndication'),
    }),
    { data: fullResponseBody } = useQuery({
      queryKey: ['getFullResponseBody', fetchFullResponseBody, id],
      queryFn: async ({ signal }) => {
        const response = await SecureAxios.get(
          getFullBody('full_response_body', id || ''),
          {
            signal,
          },
        )
        return response.data
      },
      enabled: fetchFullResponseBody,
    }),
    handleCreate = useCallback(() => {
      const currentUtcTime = moment().utc()
      const scheduleAtTime = moment.utc(scheduleAt, 'YYYY-MM-DD HH:mm:ss [UTC]')
      if (scheduleAtTime.isSameOrBefore(currentUtcTime)) {
        toast({
          type: 'error',
          message: t('errorCannotScheduleJobInThePast'),
        })
        return
      }
      submitForm.mutate({
        schedule_at: notEmpty(scheduleAt) ? scheduleAt : undefined,
        query_string: formFields.query_string,
        job: {
          notes: notEmpty(formFields.description)
            ? formFields.description
            : undefined,
          requester: email,
          request_body: notEmpty(formFields.request_body)
            ? formFields.is_oversized_request && fullRequestBody?.request_body
              ? JSON.parse(fullRequestBody?.request_body)
              : JSON.parse(formFields.request_body)
            : undefined,
          callback_url: notEmpty(formFields.callback_url)
            ? formFields.callback_url
            : undefined,
          account: notEmpty(formFields.connection.text)
            ? formFields.connection.text
            : undefined,
        },
      })
    }, [
      scheduleAt,
      submitForm,
      formFields.query_string,
      formFields.description,
      formFields.request_body,
      formFields.is_oversized_request,
      formFields.callback_url,
      formFields.connection.text,
      email,
      fullRequestBody?.request_body,
      t,
    ])

  return (
    <>
      <div className='has-info-pane'>
        <div>
          <InformationPane
            header={{
              labelAndData: {
                check: true,
                data: id,
                label: t('jobId'),
              },
              tag: {
                children: jobData?.status,
                color:
                  status === 'pending'
                    ? 'light-gray'
                    : jobData?.status === 'queued'
                      ? 'blue'
                      : jobData?.status === 'completed'
                        ? 'green'
                        : jobData?.status === 'running'
                          ? 'yellow'
                          : 'red',
              },
            }}
          >
            {status === 'pending' ? (
              <ListLoading />
            ) : (
              <InformationPane.Section data={infoPaneData} />
            )}
          </InformationPane>
        </div>
        <Tabs
          active={activeTab}
          callout={(tabId: number) => {
            setActiveTab(tabId)
          }}
          tabs={[
            {
              content:
                status === 'pending' ? (
                  <ListLoading />
                ) : (
                  <DetailsTab
                    jobData={jobData}
                    setFetchFullRequestBody={setFetchFullRequestBody}
                    fullRequestBody={fullRequestBody}
                    fullResponseBody={fullResponseBody}
                    setFetchFullResponseBody={setFetchFullResponseBody}
                    fetchFullResponseBody={fetchFullResponseBody}
                    isRequestBodyToggleOn={isRequestBodyToggleOn}
                    setIsRequestBodyToggleOn={setIsRequestBodyToggleOn}
                  />
                ),
              id: 0,
              tabName: t('details'),
            },
            {
              content: (
                <SubJobsTab
                  subjobCount={subjobCount}
                  canViewJobDetails={canViewJobDetails}
                  startDownloadingFile={startDownloadingFile}
                  downloadingFile={downloadingFile}
                  spinnerToggle={spinnerToggle}
                  jobData={jobData}
                  subjobRows={subjobRows}
                  fetchNextPage={fetchNextPage}
                  hasMoreSubJobs={hasMoreSubJobs}
                  subjobsStatus={subjobsStatus}
                  setActiveTab={setActiveTab}
                />
              ),
              id: 1,
              tabName: t('subJobs'),
            },
          ]}
        />
      </div>
      {
        <PageFooter
          rightSection={
            hasRerunAsNewJobPrivilege
              ? [
                  {
                    children: t('jobs:resendCallback'),
                    onClick: () => {
                      setIsCallbackFormOpen(true)
                    },
                    type: 'button',
                    disabled: isJobIncomplete,
                  },
                  {
                    type: 'button',
                    disabled:
                      status === 'pending' ||
                      isJobIncomplete ||
                      isSyndicationJob,
                    children: t('rerunAsNewJob'),
                    onClick: () => {
                      setFetchFullRequestBody(true)
                      setFormFields({
                        callback_url: jobData?.callback_url || '',
                        job_id: jobData?.job_id || '',
                        is_oversized_request:
                          jobData?.is_oversized_request || false,
                        job_name: jobData?.job_name || '',
                        connection: {
                          id: 0,
                          text: jobData?.support_id || '',
                          value: jobData?.support_id || '',
                        },
                        query_string: getQueryParamsString(
                          jobData?.request_path || '',
                        ),
                        job_purpose:
                          jobData?.request_method === 'GET'
                            ? 'Download'
                            : 'Upload',
                        job_type: {
                          id: 0,
                          text: jobData?.job_type || '',
                          value: jobData?.job_type || '',
                        },
                        description: jobData?.notes || '',
                        request_body: jobData?.request_body || '',
                        csv: undefined,
                        schedule_job: false,
                        date: undefined,
                        hour: undefined,
                        minute: undefined,
                        timezone: '',
                      })
                      setIsOpen(true)
                    },
                    styleType: 'primary-green',
                  },
                ]
              : [
                  {
                    children: t('jobs:resendCallback'),
                    onClick: () => {
                      setIsCallbackFormOpen(true)
                    },
                    type: 'button',
                  },
                ]
          }
        />
      }
      {(jobData?.status == 'completed' ||
        jobData?.status == 'failed' ||
        jobData?.status == 'partially_failed') && (
        <ResendCallbackSideDrawer
          isOpen={isCallbackFormOpen}
          setIsOpen={setIsCallbackFormOpen}
          callbackUrl={jobData?.callback_url || ''}
          jobId={jobData?.job_id || ''}
        />
      )}
      <JobSideDrawer
        isOpen={isOpen}
        setIsOpen={setIsOpen}
        handleCancel={handleCancel}
        handleIsDisabled={handleIsDisabled}
        handleSave={handleCreate}
        saveButtonText={t('rerunJob')}
        headerContent={t('rerunAJob')}
        formFields={formFields}
        setFormFields={setFormFields}
        setIsScheduledInPast={setIsScheduledInPast}
        setScheduleAt={setScheduleAt}
        fullRequestBody={fullRequestBody}
        status={bodyStatus}
      />
    </>
  )
}

export { JobDetails }
