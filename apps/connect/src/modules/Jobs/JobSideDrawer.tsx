import { FormFooter, SideDrawer } from '@patterninc/react-ui'
import React, { useState } from 'react'

import JobsCreateForm from './JobsCreateForm'
import { type NewJobFieldsType } from './types'

type JobSideDrawerProps = {
  isOpen: boolean
  setIsOpen: React.Dispatch<React.SetStateAction<boolean>>
  handleCancel: () => void
  handleIsDisabled: () => boolean
  handleSave: () => void
  saveButtonText: string
  headerContent: string
  formFields: NewJobFieldsType
  setFormFields: React.Dispatch<React.SetStateAction<NewJobFieldsType>>
  setIsScheduledInPast: React.Dispatch<React.SetStateAction<boolean>>
  setScheduleAt: React.Dispatch<React.SetStateAction<string>>
  fullRequestBody?:
    | {
        request_body: string
      }
    | undefined
  status?: string | undefined
  setFetchFullRequestBody?: React.Dispatch<React.SetStateAction<boolean>>
}

const JobSideDrawer: React.FC<JobSideDrawerProps> = ({
  isOpen,
  setIsOpen,
  handleCancel,
  handleIsDisabled,
  handleSave,
  saveButtonText,
  headerContent,
  formFields,
  setFormFields,
  setIsScheduledInPast,
  setScheduleAt,
  fullRequestBody,
  status,
}) => {
  const [isRequestBodyValid, setIsRequestBodyValid] = useState(true)
  return (
    <SideDrawer
      isOpen={isOpen}
      closeCallout={() => setIsOpen(!isOpen)}
      headerContent={headerContent}
      footerContent={
        <FormFooter
          cancelButtonProps={{ onClick: handleCancel }}
          saveButtonProps={{
            disabled: !isRequestBodyValid || handleIsDisabled(),
            onClick: handleSave,
            children: saveButtonText,
          }}
        />
      }
    >
      <JobsCreateForm
        setScheduleAt={setScheduleAt}
        setIsScheduledInPast={setIsScheduledInPast}
        formFields={formFields}
        setFormFields={setFormFields}
        isRequestBodyValid={isRequestBodyValid}
        setIsRequestBodyValid={setIsRequestBodyValid}
        fullRequestBody={fullRequestBody}
        status={status}
      />
    </SideDrawer>
  )
}

export default JobSideDrawer
