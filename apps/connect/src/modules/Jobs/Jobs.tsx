import {
  notEmpty,
  SearchBar,
  type SortByProps,
  type SortColumnProps,
  standardSortParams,
  StandardTable,
  Switch,
  toastify,
} from '@patterninc/react-ui'
import React, { useContext, useEffect, useMemo, useRef, useState } from 'react'
import { useInfiniteQuery, useQuery } from '@tanstack/react-query'
import moment from 'moment'
import { useSearchParams } from 'react-router-dom'

import { ThemeContext } from '../../Context'
import { type JobsProps, type JobType } from './types'
import { job_types, jobs_url } from '../../common/services/ConnectEngineService'
import SecureAxios from '../../common/services/SecureAxios'
import { JobsTableConfig } from './jobs-table-config'
import JobsFilter from './JobsFilter'
import {
  marketplacesApi,
  usersApi,
} from '../../common/services/ConnectAuthService'
import {
  getUserSettingEndpoint,
  saveCustomization,
} from '../../common/services/UserSettingsService'
import { useOrg } from '../../context/org-context'
import { PRIVILEGES, useHasPrivilege } from '../../context/auth-context'
import {
  initialFilters,
  JobsFilterContext,
} from '../../context/jobs-filter-context'
import { type JobFilterType, type OptionType } from '../../common/types'
import { filterEndDate } from '../../common/helpers/filterEndDate'
import useFilteredConnections from '../../common/helpers/useFilteredConnections'
import { c, useTranslate } from '../../common/helpers/TranslationHelpers'
import ResendCallbackSideDrawer from './ResendCallbackSideDrawer'
import useResetOnOrgSwitch from '../../common/helpers/useResetOnOrgSwitch'
import { convertToTitleCase } from '../../common/helpers/utils'

const Jobs = ({
  isCallbackFormOpen,
  setIsCallbackFormOpen,
}: JobsProps): React.JSX.Element => {
  const { t } = useTranslate('jobs'),
    canViewJobDetails = useHasPrivilege(PRIVILEGES.VIEW_JOB_DETAILS),
    {
      appliedFilters: {
        statuses,
        marketplaces,
        sellerAccounts,
        createdBy,
        creationDateRange,
        completionDateRange,
        runTimeRange,
        jobTypes,
      },
      setAppliedFilters,
      searchQuery: search,
      setSearchQuery,
      exactJobSearch,
      setExactJobSearch,
    } = useContext(JobsFilterContext),
    [areFilterOptionsLoading, setAreFilterOptionsLoading] = useState(true)
  // IDs
  const { updateBreadcrumbs } = useContext(ThemeContext),
    tableId = 'jobs_table'

  // PERMISSIONS
  const { org } = useOrg(),
    canViewUsersAndJobs = useHasPrivilege(PRIVILEGES.VIEW_JOBS, true)

  // SORTING
  const { endpoint: sortApi, decoded } = getUserSettingEndpoint(
      `connect:table:${tableId}`,
    ),
    {
      status: sortStatus,
      data: sortResponse,
      isFetching: sortFetching,
    } = useQuery({
      queryKey: [decoded, sortApi],
      queryFn: ({ signal }) => SecureAxios.get(sortApi, { signal }),
    }),
    sortData = sortStatus === 'success' && sortResponse?.data?.sort

  // SORTING TABLE PARAMS
  const [sortBy, setSortBy] = useState<SortByProps>({
      prop: 'created_on',
      flip: false,
    }),
    sort: SortColumnProps['sorter'] = (sortObj: {
      activeColumn: string
      direction: boolean
      lowerCaseParam?: boolean | undefined
    }) => {
      setSortBy({
        prop: sortObj.activeColumn,
        flip: sortObj.direction,
      })
      saveSort(sortObj)
    },
    saveSort = (sortObj: {
      activeColumn: string
      direction: boolean
      lowerCaseParam?: boolean | undefined
    }) => {
      SecureAxios.put(sortApi, {
        sort: {
          prop: sortObj.activeColumn,
          flip: sortObj.direction,
        },
      }).catch((error) => {
        //TODO: Handle error in future prs and remove the following log
        console.log('Error saving sort data:', error)
      })
    },
    sortParam = standardSortParams(sortBy, [
      'created_on',
      'started_on',
      'ended_on',
      'run_time',
      'retry_count',
    ]).replace('_on', '_at')

  const [copiedJobId, setCopiedJobId] = useState<string>('')
  const { endpoint: customColumnsApi, decoded: customColumnsDecoded } =
      getUserSettingEndpoint(`customColumns:table:${tableId}`),
    totalConfigItems = JobsTableConfig().slice(1, -1),
    totalList = totalConfigItems.map((item) => item.label),
    defaultSelectionList = totalList.filter(
      (item) => item !== 'Request Body' && item !== 'Description',
    ),
    [selectionList, setSelectionList] = useState(defaultSelectionList),
    { status: customColumnsStatus, data: customColumnsResponse } = useQuery({
      queryKey: [customColumnsDecoded, customColumnsApi],
      queryFn: ({ signal }) =>
        SecureAxios.get(customColumnsApi, { signal }).catch((error) => {
          //TODO: Handle error in future prs and remove the following log
          console.log('Error fetching sort data:', error)
        }),
    }),
    customColumnsData =
      customColumnsStatus === 'success' &&
      customColumnsResponse?.data?.selectedColumns,
    customSelectionCallout = (
      selectedList: string[],
      setToDefault?: boolean,
    ) => {
      setSelectionList(selectedList)
      saveCustomization({
        api: customColumnsApi,
        selected: selectedList,
        setToDefault: setToDefault,
        type: 'table',
      })
    },
    [callbackUrl, setCallbackUrl] = useState(''),
    [jobId, setJobId] = useState(''),
    filteredColumnConfig = useMemo(() => {
      return JobsTableConfig(
        selectionList,
        canViewJobDetails,
        copiedJobId,
        setIsCallbackFormOpen,
        setCallbackUrl,
        setJobId,
        setCopiedJobId,
      )
    }, [
      copiedJobId,
      selectionList,
      canViewJobDetails,
      setJobId,
      setIsCallbackFormOpen,
    ])

  // FILTERS
  const [queryParams] = useSearchParams(),
    sellerAccountOptions = useFilteredConnections([]),
    params: Record<string, string[] | number[] | string | number | undefined> =
      useMemo(
        () => ({
          ...(marketplaces && marketplaces.length > 0
            ? {
                marketplace: marketplaces.map((marketplace) =>
                  marketplace.toLowerCase(),
                ),
              }
            : {}),
          ...(statuses && statuses.length > 0
            ? { status: statuses.map((s) => s.toLowerCase().replace(' ', '_')) }
            : {}),
          ...(createdBy && createdBy.length > 0
            ? { created_by: createdBy.map((c) => c.toLowerCase()) }
            : {}),
          ...(jobTypes && jobTypes.length > 0
            ? { request_name: jobTypes }
            : {}),
          ...(sellerAccounts && sellerAccounts.length > 0
            ? { account: sellerAccounts }
            : {}),
          ...(creationDateRange.start_date && creationDateRange.end_date
            ? {
                created_at_start: moment(creationDateRange.start_date)
                  ?.utc()
                  ?.format('YYYY-MM-DDTHH:mm'),
                created_at_end: moment(creationDateRange.end_date)
                  ?.utc()
                  ?.format('YYYY-MM-DDTHH:mm'),
              }
            : {}),
          ...(completionDateRange?.start_date && completionDateRange?.end_date
            ? {
                ended_at_start:
                  completionDateRange.start_date.format('YYYY-MM-DDTHH:mm'),
                ended_at_end: filterEndDate(
                  completionDateRange.end_date,
                ).format('YYYY-MM-DDTHH:mm'),
              }
            : {}),
          ...(runTimeRange.min !== ''
            ? { run_time_min: runTimeRange.min }
            : {}),
          ...(runTimeRange.max !== ''
            ? { run_time_max: runTimeRange.max }
            : {}),
        }),
        [
          marketplaces,
          statuses,
          createdBy,
          sellerAccounts,
          jobTypes,
          completionDateRange?.start_date,
          completionDateRange?.end_date,
          creationDateRange.start_date,
          creationDateRange.end_date,
          runTimeRange,
        ],
      ),
    {
      data: marketplacesData,
      status: marketplacesStatus,
      isLoading: isMarketplacesOptionsLoading,
    } = useQuery({
      queryKey: ['jobs_filter_by_marketplace', marketplacesApi],
      queryFn: ({ signal }) =>
        SecureAxios.get(marketplacesApi, { signal }).then((response) => {
          return response.data
        }),
    }),
    {
      data: usersData,
      status: usersStatus,
      isLoading: isCreatedByOptionsLoading,
    } = useQuery<{ id: number; email: string; name: string }[]>({
      queryKey: [usersApi, org],
      queryFn: ({ signal }) => {
        return SecureAxios.get(usersApi, {
          signal,
          params: {
            organization_code: org.code,
          },
        }).then((response) => {
          return response.data
        })
      },
      enabled: canViewUsersAndJobs,
    }),
    marketplaceOptions: OptionType[] = useMemo(
      () =>
        marketplacesStatus === 'success'
          ? marketplacesData.map(
              (marketplace: {
                id: string
                name: string
                display_name: string
              }) => {
                return {
                  id: marketplace.id,
                  text: marketplace.display_name,
                  value: marketplace.name.toLowerCase(),
                }
              },
            )
          : [],
      [marketplacesData, marketplacesStatus],
    ),
    createdByOptions: OptionType[] = useMemo(() => {
      if (usersStatus === 'success') {
        const uniqueEmails: string[] = [
          ...new Set<string>(
            usersData.map(
              (user: { id: number; name: string; email: string }) => user.email,
            ),
          ),
        ]
        return uniqueEmails
          .map(
            (email) =>
              usersData.find((u) => u.email === email) || {
                id: 0,
                name: '',
                email: '',
              },
          )
          .map((user) => {
            return {
              id: user.id,
              text: user.name,
              value: user.email,
            }
          })
      } else {
        return []
      }
    }, [usersData, usersStatus]),
    statusOptions: OptionType[] = useMemo(
      () => [
        {
          id: 1,
          text: c('completed'),
          value: 'COMPLETED',
        },
        {
          id: 2,
          text: c('queued'),
          value: 'QUEUED',
        },
        {
          id: 3,
          text: c('running'),
          value: 'RUNNING',
        },
        {
          id: 4,
          text: c('failed'),
          value: 'FAILED',
        },
        {
          id: 5,
          text: c('partiallyFailed'),
          value: 'PARTIALLY_FAILED',
        },
      ],
      [],
    )

  // TABLE DATA
  const { status, data, fetchNextPage, hasNextPage } = useInfiniteQuery({
      queryKey: [sortBy, params, search, org, exactJobSearch],
      queryFn: ({ pageParam = 1, signal }) => {
        return SecureAxios.get<JobType[]>(jobs_url, {
          params: {
            ...params,
            page: pageParam ?? 1,
            per_page: 20,
            sort: sortParam,
            organization: org.code,
            ...(notEmpty(search) ? { search } : {}),
            ...(notEmpty(search) ? { exact_search: exactJobSearch } : {}),
          },
          signal,
        })
      },
      initialPageParam: 1,
      enabled: canViewUsersAndJobs,
      refetchInterval: 1000 * 60, // refetch every minute
      getNextPageParam: (previousPage) => {
        return previousPage &&
          previousPage.headers['current-page'] !==
            previousPage.headers['total-pages']
          ? Number(previousPage.headers['current-page']) + 1
          : undefined
      },
    }),
    isLoading = useMemo(() => {
      return status === 'pending' || sortStatus === 'pending'
    }, [status, sortStatus]),
    tableData: JobType[] = useMemo(() => {
      return data ? data?.pages?.flatMap((page) => page?.data) : []
    }, [data])

  const { data: endpointsResult, isLoading: isJobTypesLoading } = useQuery({
      queryKey: ['jobs_filter_by_job_types', job_types],
      queryFn: async ({ signal }) => {
        try {
          const result = await SecureAxios.get<string[]>(job_types, { signal })
          return result.data
        } catch (e) {
          console.log(e)
          return []
        }
      },
    }),
    endpointOptions: OptionType[] = useMemo(() => {
      if (endpointsResult === undefined) return []
      return endpointsResult.map((elt, index) => {
        return { id: index, text: elt, value: elt }
      })
    }, [endpointsResult]),
    queryFilters = useMemo((): JobFilterType => {
      const completionDateRange:
        | JobFilterType['completionDateRange']
        | undefined =
        queryParams.get('ended_at_start') !== null ||
        queryParams.get('ended_at_end') !== null
          ? {
              start_date:
                queryParams.get('ended_at_start') !== null
                  ? moment(queryParams.get('ended_at_start'))
                  : '',
              end_date:
                queryParams.get('ended_at_end') !== null
                  ? moment(queryParams.get('ended_at_end'))
                  : '',
            }
          : undefined
      return {
        ...initialFilters,
        sellerAccounts: queryParams.getAll('account[]'),
        marketplaces: queryParams
          .getAll('marketplace[]')
          .map((m) => convertToTitleCase(m)),
        statuses: queryParams
          .getAll('status[]')
          .map((s) => convertToTitleCase(s)),
        createdBy: queryParams.getAll('created_by[]'),
        jobTypes: queryParams.getAll('request_name[]'),
        completionDateRange:
          completionDateRange || initialFilters.completionDateRange,
        runTimeRange: {
          min: Number(queryParams.get('run_time_min')) || '',
          max: Number(queryParams.get('run_time_max')) || '',
        },
      }
    }, [queryParams]),
    queryString = useRef('')

  useResetOnOrgSwitch(setSearchQuery)

  useEffect(() => {
    if (sortData) {
      setSortBy(sortData)
    }
  }, [setSortBy, sortData])

  useEffect(() => {
    if (customColumnsData) {
      setSelectionList(customColumnsData)
    }
  }, [setSelectionList, customColumnsData])

  useEffect(() => {
    if (queryParams.toString() !== queryString.current) {
      if (
        (!queryFilters.sellerAccounts.every((account) =>
          sellerAccountOptions.map((o) => o.value).includes(account),
        ) ||
          !queryFilters.marketplaces.every((marketplace) =>
            marketplaceOptions.map((o) => o.value).includes(marketplace),
          ) ||
          !queryFilters.statuses.every((status) =>
            statusOptions.map((o) => o.value).includes(status),
          ) ||
          !queryFilters.createdBy.every((user) =>
            createdByOptions.map((o) => o.value).includes(user),
          ) ||
          !queryFilters.jobTypes.every((endpoint) =>
            endpointOptions.map((o) => o.value).includes(endpoint),
          )) &&
        sellerAccountOptions.length &&
        marketplaceOptions.length &&
        createdByOptions.length &&
        endpointOptions.length
      ) {
        toastify.error(t('unrecognizedURLQueryParamFilterOptions'), {
          toastId: 'wrongUrlParams',
        })
      }
      queryString.current = queryParams.toString()
      setAppliedFilters(queryFilters)
    }
  }, [
    queryParams,
    queryFilters,
    setAppliedFilters,
    sellerAccountOptions,
    marketplaceOptions,
    statusOptions,
    createdByOptions,
    endpointOptions,
    t,
  ])

  useEffect(() => {
    updateBreadcrumbs({
      name: c('jobs'),
      link: `/jobs${window.location.search}`,
      changeType: 'rootLevel',
    })
  }, [updateBreadcrumbs])

  useEffect(() => {
    setAreFilterOptionsLoading(
      isMarketplacesOptionsLoading ||
        isCreatedByOptionsLoading ||
        isJobTypesLoading,
    )
  }, [
    isCreatedByOptionsLoading,
    isJobTypesLoading,
    isMarketplacesOptionsLoading,
  ])

  return (
    <>
      <div className='flex flex-wrap justify-content-between align-items-center pat-gap-2 pat-mb-4'>
        <div className='flex align-items-center pat-gap-2'>
          <SearchBar
            value={search}
            onChange={(value) => {
              setSearchQuery(value)
            }}
            placeholder={t('searchJobs')}
          />
          <Switch
            disabled={search.length === 0}
            callout={() => {
              setExactJobSearch(!exactJobSearch)
            }}
            checked={exactJobSearch}
            formLabelProps={{
              label: t('exactSearch'),
            }}
          />
        </div>
        <JobsFilter
          marketplaces={marketplaceOptions}
          statuses={statusOptions}
          createdBy={createdByOptions}
          sellerAccounts={sellerAccountOptions}
          jobTypes={endpointOptions}
          areFilterOptionsLoading={areFilterOptionsLoading}
        />
      </div>
      <StandardTable
        data={tableData}
        dataKey='job_id'
        hasData={status === 'success' && tableData.length > 0}
        loading={isLoading || sortFetching}
        successStatus={status === 'success'}
        hasMore={!!(status === 'success' && hasNextPage)}
        getData={fetchNextPage}
        tableId={tableId}
        sort={sort}
        sortBy={sortBy}
        config={filteredColumnConfig}
        noDataFields={{
          primaryText: t('noJobsAvailable'),
        }}
        customColumnProps={{
          selected: selectionList,
          list: totalList,
          callout: customSelectionCallout,
          setToDefaultCallout: () =>
            customSelectionCallout(defaultSelectionList, true),
        }}
        stickyTableConfig={{ right: 1 }}
      />
      <ResendCallbackSideDrawer
        isOpen={isCallbackFormOpen}
        setIsOpen={setIsCallbackFormOpen}
        callbackUrl={callbackUrl}
        jobId={jobId}
      />
    </>
  )
}

export default Jobs
