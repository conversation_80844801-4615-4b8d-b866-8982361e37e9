import React, {
  type Dispatch,
  type SetStateAction,
  useEffect,
  useState,
} from 'react'
import {
  DatePicker,
  DatepickerNew,
  FormLabel,
  hasValue,
  ListLoading,
  notEmpty,
  Select,
  Switch,
  TextInput,
  useToggle,
} from '@patterninc/react-ui'
import { useQuery } from '@tanstack/react-query'
import momentTz from 'moment-timezone'
import moment from 'moment'

import { type JobFieldValue, type NewJobFieldsType } from './types'
import styles from './_jobs.module.scss'
import { c, useTranslate } from '../../common/helpers/TranslationHelpers'
import { isValidJson } from '../../common/helpers/utils'

type JobsCreateFormProps = {
  setScheduleAt: React.Dispatch<React.SetStateAction<string>>
  setIsScheduledInPast: React.Dispatch<React.SetStateAction<boolean>>
  formFields: NewJobFieldsType
  setFormFields: Dispatch<SetStateAction<NewJobFieldsType>>
  isRequestBodyValid: boolean
  setIsRequestBodyValid: React.Dispatch<React.SetStateAction<boolean>>
  fullRequestBody:
    | {
        request_body: string
      }
    | undefined
  status: string | undefined
  setFetchFullRequestBody?: React.Dispatch<React.SetStateAction<boolean>>
}

const JobsCreateForm = ({
  setScheduleAt,
  setIsScheduledInPast,
  formFields,
  setFormFields,
  isRequestBodyValid,
  setIsRequestBodyValid,
  fullRequestBody,
  status,
}: JobsCreateFormProps): React.JSX.Element => {
  const { t } = useTranslate('jobs')
  const replaceOldDatepicker = useToggle('replace_old_datepicker_in_connect')

  // FIELD INPUT HANDLERS
  const setValue = (key: string, value: JobFieldValue) => {
      setFormFields((prevState) => ({
        ...prevState,
        [key]: value,
      }))
    },
    [errorText, setErrorText] = useState(''),
    inputHandler = (...params: unknown[]) => {
      const name = params[0] as string,
        value = params[1] as JobFieldValue
      if (name === 'request_body') {
        if (isValidJson(value as string) || !notEmpty(value as string)) {
          setErrorText('')
          setIsRequestBodyValid(true)
        } else {
          setErrorText(t('jobs:invalidRequestBody'))
          setIsRequestBodyValid(false)
        }
      }
      if (hasValue(value) && typeof name === 'string') {
        setValue(name, value)
      }
    },
    formatTimeDigits = (timeUnit: number) => {
      return Number(timeUnit) < 10 ? `0${Number(timeUnit)}` : Number(timeUnit)
    }

  useEffect(() => {
    const { date, hour, minute, timezone } = formFields
    if ([date, hour, minute, timezone].includes('')) {
      setIsScheduledInPast(false)
      return setScheduleAt('')
    }
    const userTimezone = timezone.split(',')[0]
    const formattedMinute = minute && formatTimeDigits(minute)
    const formattedHour = hour && formatTimeDigits(hour)
    const userDateTimeUtc = momentTz
      .tz(
        `${date?.format('YYYY-MM-DD')} ${formattedHour}:${formattedMinute}`,
        userTimezone,
      )
      .utc()
    const currentUtcTime = momentTz().utc()
    if (userDateTimeUtc.isBefore(currentUtcTime)) {
      setIsScheduledInPast(true)
    } else {
      setIsScheduledInPast(false)
      setScheduleAt(`${userDateTimeUtc.format('YYYY-MM-DD HH:mm:ss')} UTC`)
    }
  }, [setIsScheduledInPast, setScheduleAt, formFields])

  // FIELD OPTIONS
  const { data: timezoneOptions } = useQuery({
    queryKey: ['timezone_options'],
    queryFn: () => {
      return momentTz.tz.names().map((zone, idx) => {
        const offset = momentTz.tz(zone).format('Z')
        return {
          id: idx,
          value: `${zone}, ${offset}`,
        }
      })
    },
  })

  const prettifyJson = (jsonString: string) => {
    try {
      const json = JSON.parse(jsonString)
      return JSON.stringify(json, null, 16)
    } catch {
      return jsonString // Return original string if it's not valid JSON
    }
  }

  return (
    <>
      {/* Connection Dropdown */}
      <div className='pat-pb-4'>
        <TextInput
          value={formFields['connection']?.value}
          disabled={true}
          type='text'
          labelText={t('connection')}
          callout={inputHandler}
          stateName='connection'
        />
      </div>

      {/* URL Postfix Text Field  */}
      <div className='pat-pb-4'>
        <TextInput
          value={formFields.query_string}
          type='text'
          labelText={c('queryString')}
          callout={inputHandler}
          stateName='query_string'
        />
      </div>

      {/* Description Text Field */}
      <div className='pat-pb-4'>
        <TextInput
          value={formFields.description}
          labelText={c('description')}
          callout={inputHandler}
          stateName='description'
          type='textarea'
        />
      </div>
      {/* Callback URL  */}
      <div className='pat-pb-4'>
        <TextInput
          value={formFields.callback_url || ''}
          labelText={c('callbackURL')}
          callout={inputHandler}
          stateName='callback_url'
          type='text'
        />
      </div>

      {/* CSV Upload */}
      {formFields['job_purpose'] === 'Upload' && (
        <div className='pat-pb-4'>
          {formFields.is_oversized_request &&
          (status === undefined || status !== 'success') ? (
            <ListLoading />
          ) : (
            <div className={styles.requestBodyInput}>
              <TextInput
                value={
                  formFields.is_oversized_request &&
                  fullRequestBody?.request_body
                    ? prettifyJson(fullRequestBody?.request_body)
                    : prettifyJson(formFields.request_body)
                }
                labelText={t('requestBody')}
                callout={inputHandler}
                stateName='request_body'
                type='textarea'
                errorText={errorText}
                hasError={!isRequestBodyValid}
                classType={`${notEmpty(errorText) ? 'error' : ''}`}
              />
            </div>
          )}
        </div>
      )}

      {/* Schedule Job Toggle */}
      <div className={`flex justify-content-between pat-pb-2.5`}>
        <FormLabel label={t('scheduleJob')} />
        <Switch
          checked={formFields.schedule_job}
          callout={() => {
            inputHandler('schedule_job', !formFields.schedule_job)
          }}
        />
      </div>

      {formFields.schedule_job && (
        <>
          {/* Date Time Picker */}
          <div className={`flex pat-pb-4`}>
            {/* Date Picker */}
            <div className={`${styles.date} pat-pr-4`}>
              <FormLabel label={t('selectDateAndTime')} required />
              {replaceOldDatepicker ? (
                <DatePicker
                  type='single'
                  selectedDate={
                    formFields['date']
                      ? new Date(formFields['date'].valueOf())
                      : undefined
                  }
                  onDateChange={(start) => {
                    inputHandler('date', moment(start?.valueOf()))
                  }}
                  placeholder={t('selectADate')}
                  dateRestrictions={{
                    beforeDate: moment().toDate(),
                    afterDate: moment().add(1, 'month').toDate(),
                  }}
                />
              ) : (
                <DatepickerNew
                  isSingle
                  startDate={formFields['date'] || null}
                  endDate={undefined}
                  onDateChange={(start) => {
                    inputHandler('date', moment(start))
                  }}
                  placeholder={t('selectADate')}
                  hasFutureDates
                  isOutsideRangeHandler={(date) => {
                    return date.isAfter(moment().add(1, 'month'))
                  }}
                />
              )}
            </div>
            {/* Hour Picker */}
            <div className='pat-pr-2'>
              <TextInput
                required
                type='number'
                callout={inputHandler}
                labelText={c('hr')}
                stateName='hour'
                value={formFields.hour || ''}
                max={23}
                onlyPositiveNumbers
              />
            </div>
            {/* Minute Picker */}
            <div>
              <TextInput
                required
                type='number'
                callout={inputHandler}
                labelText={c('min')}
                stateName='minute'
                value={formFields.minute || ''}
                max={59}
                onlyPositiveNumbers
              />
            </div>
          </div>

          {/* Timezone Dropdown */}
          <div className='pat-pb-4'>
            {
              <Select
                options={timezoneOptions || []}
                selectedItem={
                  timezoneOptions?.find(
                    (tz) => tz.value === formFields.timezone,
                  ) || { value: '' }
                }
                optionKeyName='value'
                labelKeyName='value'
                onChange={(selected) => {
                  inputHandler('timezone', selected?.value)
                }}
                required
                labelProps={{ label: t('selectATimezone') }}
              />
            }
          </div>
        </>
      )}
    </>
  )
}

export default JobsCreateForm
