import { use<PERSON><PERSON>back, useContext, useEffect, useMemo, useState } from 'react'
import {
  DatePicker,
  DatepickerNew,
  Filter,
  FormLabel,
  TextInput,
  useToggle,
} from '@patterninc/react-ui'
import moment, { type Moment } from 'moment'
import React from 'react'
import { useSearchParams } from 'react-router-dom'

import NumberRangeInput from '../../common/components/NumberRangeInput/NumberRangeInput'
import sortOptions from '../../common/services/SortOptions'
import {
  initialFilters,
  JobsFilterContext,
} from '../../context/jobs-filter-context'
import { type JobFilterType } from '../../common/types'
import useFilteredConnections from '../../common/helpers/useFilteredConnections'
import { type OptionType } from '../../common/types'
import { c, useTranslate } from '../../common/helpers/TranslationHelpers'
import styles from './_jobs.module.scss'
import useResetOnOrgSwitch from '../../common/helpers/useResetOnOrgSwitch'

type JobsFilterProps = {
  marketplaces: OptionType[]
  statuses: OptionType[]
  createdBy: OptionType[]
  sellerAccounts: OptionType[]
  jobTypes: OptionType[]
  areFilterOptionsLoading: boolean
}

export type ApiParamsType = {
  marketplaces?: string[]
  statuses?: string[]
  createdBy?: number[]
  sellerAccounts?: number[]
  creationDateStart?: string
  creationDateEnd?: string
  runTimeMin?: number | string
  runTimeMax?: number | string
  sort_by?: string
  sort_order?: string
  search?: string
}

type FilterStatesType = React.ComponentProps<typeof Filter>['filterStates']

const JobsFilter = ({
  marketplaces,
  statuses,
  createdBy,
  jobTypes,
  areFilterOptionsLoading,
}: JobsFilterProps): React.JSX.Element => {
  const replaceOldDatepicker = useToggle('replace_old_datepicker_in_connect'),
    { setAppliedFilters: update, appliedFilters: filterState } =
      useContext(JobsFilterContext),
    { t } = useTranslate('jobs'),
    [filterStateCopy, setFilterStateCopy] = useState(filterState),
    [filtersCount, setFiltersCount] = useState(0),
    handleOutsideStartDateRange = (startDate: Moment): boolean => {
      const endDate = filterStateCopy.creationDateRange.end_date
      if (!endDate) {
        return startDate.isBefore(moment().subtract(90, 'days'))
      }
      return (
        startDate.isBefore(moment().subtract(90, 'days')) ||
        startDate.isAfter(moment(endDate))
      )
    },
    handleOutsideEndDateRange = (endDate: Moment): boolean => {
      const startDate = filterStateCopy.creationDateRange.start_date
      const today = moment().startOf('day')
      if (!startDate) {
        return (
          endDate.startOf('day').isAfter(today) ||
          endDate.isBefore(moment().subtract(90, 'days'))
        )
      }
      return (
        endDate.startOf('day').isAfter(today) ||
        endDate.isBefore(moment().subtract(90, 'days')) ||
        endDate.startOf('day').isBefore(moment(startDate).startOf('day'))
      )
    },
    connectionOptions = useFilteredConnections(
      filterStateCopy.marketplaces.map((display_name) => {
        return marketplaces.find((m) => m.text === display_name)?.value || ''
      }),
    ),
    setSearchParams = useSearchParams()[1]

  useEffect(() => {
    setFilterStateCopy(filterState)
  }, [filterState])

  const allFilters = useMemo((): FilterStatesType => {
      return {
        marketplaces: {
          type: 'multi-select',
          selectedOptions: filterStateCopy.marketplaces.map((m) => ({
            text: m,
          })),
          options: sortOptions(marketplaces, 'text'),
          stateName: 'marketplaces',
          labelKey: 'text',
          formLabelProps: {
            label: c('marketplaces'),
          },
          selectPlaceholder: `-- ${t('selectMarketplaces')} --`,
        },
        statuses: {
          type: 'multi-select',
          selectedOptions: filterStateCopy.statuses.map((s) => ({
            text: s,
          })),
          options: sortOptions(statuses, 'text'),
          stateName: 'statuses',
          labelKey: 'text',
          formLabelProps: {
            label: c('statuses'),
          },
          selectPlaceholder: `-- ${t('selectStatuses')} --`,
        },
        createdBy: {
          type: 'multi-select',
          selectedOptions: filterStateCopy.createdBy.map((c) => {
            const index = createdBy
              .map((option) => option?.value?.toLowerCase())
              .indexOf(c?.toLowerCase())
            if (index !== -1) {
              return {
                text: createdBy[index]?.text,
              }
            }
            return { text: c }
          }),
          options: sortOptions(createdBy, 'text'),
          stateName: 'createdBy',
          labelKey: 'text',
          formLabelProps: {
            label: c('createdBy'),
          },
          selectPlaceholder: `-- ${t('selectUsers')} --`,
        },
        sellerAccounts: {
          type: 'multi-select',
          selectedOptions: filterStateCopy.sellerAccounts.map((s) => ({
            text: s,
          })),
          options: connectionOptions,
          stateName: 'sellerAccounts',
          labelKey: 'text',
          formLabelProps: {
            label: t('sellerAccounts'),
          },
          selectPlaceholder: `-- ${t('selectSellerAccounts')} --`,
        },
        jobTypes: {
          type: 'multi-select',
          selectedOptions: filterStateCopy.jobTypes.map((j) => ({
            text: j,
          })),
          options: sortOptions(jobTypes, 'text'),
          stateName: 'jobTypes',
          labelKey: 'text',
          formLabelProps: {
            label: t('jobTypes'),
          },
          selectPlaceholder: `-- ${t('selectJobTypes')} --`,
        },
      }
    }, [
      marketplaces,
      statuses,
      createdBy,
      jobTypes,
      connectionOptions,
      filterStateCopy.marketplaces,
      filterStateCopy.statuses,
      filterStateCopy.createdBy,
      filterStateCopy.sellerAccounts,
      filterStateCopy.jobTypes,
      t,
    ]),
    runTimeRangeState = useMemo(() => {
      return {
        labelText: t('runTimeRanges'),
        minimum: {
          defaultValue: filterStateCopy.runTimeRange.min,
          placeholder: c('min'),
          stateName: 'runTimeRange.min',
          onlyPositiveNumbers: true,
          fullWidth: true,
        },
        maximum: {
          defaultValue: filterStateCopy.runTimeRange.max,
          placeholder: c('max'),
          stateName: 'runTimeRange.max',
          onlyPositiveNumbers: true,
          fullWidth: true,
        },
      }
    }, [filterStateCopy.runTimeRange, t])

  const updateForm = (...params: unknown[]) => {
    // NOTE: the Filter component in react-ui has the type definitions set up incorrectly which is causing these workarounds
    const name = params[0] as string,
      value = params[1]
    if (typeof name == 'string' && name.includes('.')) {
      const keyNames = name.split('.'),
        parentKey = keyNames[0],
        childKey = keyNames[1]
      if (parentKey === 'creationDateRange') {
        setFilterStateCopy((prevState) => ({
          ...prevState,
          [parentKey]: {
            ...prevState.creationDateRange,
            [childKey]: value,
          },
        }))
      } else if (parentKey === 'runTimeRange') {
        setFilterStateCopy((prevState) => ({
          ...prevState,
          [parentKey]: {
            ...prevState.runTimeRange,
            [childKey]: value,
          },
        }))
      }
    } else {
      setFilterStateCopy((prevState) => ({
        ...prevState,
        [name]: value,
      }))
    }
  }

  const updateDateRange = (
    field: 'start_date' | 'end_date',
    value: Moment | null,
  ) => {
    setFilterStateCopy((prevState) => ({
      ...prevState,
      creationDateRange: {
        ...prevState.creationDateRange,
        [field]: value
          ? field === 'start_date'
            ? value.startOf('day')
            : value.endOf('day')
          : null,
      },
    }))
  }

  const resetFilters = useCallback(() => {
    setFiltersCount(0)
    setFilterStateCopy(initialFilters)
    update(initialFilters, true)
    setSearchParams()
  }, [setSearchParams, update])

  useResetOnOrgSwitch(undefined, resetFilters)
  const difference = (filter: JobFilterType) => {
    let count = 0
    for (const key in filter) {
      if (Object.prototype.hasOwnProperty.call(filter, key)) {
        if (
          key === 'creationDateRange' &&
          filter['creationDateRange']['start_date'] !== '' &&
          filter['creationDateRange']['end_date'] !== ''
        ) {
          count++
        }
        if (key === 'runTimeRange' && filter['runTimeRange']['min'] !== '') {
          count++
        }
        if (key === 'runTimeRange' && filter['runTimeRange']['max'] !== '') {
          count++
        }
        if (
          key === 'marketplaces' ||
          key === 'statuses' ||
          key === 'createdBy' ||
          key === 'sellerAccounts' ||
          key === 'jobTypes'
        ) {
          if (filter[key].length > 0) {
            count++
          }
        }
      }
    }
    return count
  }

  const updateFilter = () => {
    const count = difference(filterStateCopy)
    setFiltersCount(count)
    update({ ...filterStateCopy }, true)
  }

  const cancelCallout = () => {
    setFilterStateCopy(filterState)
  }

  useEffect(() => {
    setFilterStateCopy(filterState)
    const count = difference(filterState)
    setFiltersCount(count)
  }, [filterState])

  const isFilterButtonDisabled = () => {
    const hasStartDate = filterStateCopy.creationDateRange.start_date !== ''
    const hasEndDate = filterStateCopy.creationDateRange.end_date !== ''

    return (hasStartDate && !hasEndDate) || (!hasStartDate && hasEndDate)
  }

  return (
    <Filter
      disabled={isFilterButtonDisabled()}
      loading={areFilterOptionsLoading}
      filterStates={allFilters}
      filterCallout={updateFilter}
      resetCallout={resetFilters}
      onChangeCallout={updateForm}
      cancelCallout={cancelCallout}
      appliedFilters={filtersCount}
    >
      {() => (
        <>
          <div className={`flex`}>
            <div className={`${styles.date} pat-pr-4`}>
              <FormLabel
                tooltip={{
                  tooltipContent: t('filterJobsStartDateTooltip'),
                }}
                label={t('startDateAndTime')}
              />
              {replaceOldDatepicker ? (
                <DatePicker
                  dateRestrictions={{
                    beforeDate: moment().subtract(90, 'days').toDate(),
                    afterDate: filterStateCopy.creationDateRange.end_date
                      ? moment(
                          filterStateCopy.creationDateRange.end_date,
                        ).toDate()
                      : moment().toDate(),
                  }}
                  type='single'
                  selectedDate={
                    filterStateCopy.creationDateRange.start_date
                      ? new Date(
                          filterStateCopy.creationDateRange.start_date.valueOf(),
                        )
                      : undefined
                  }
                  onDateChange={(startDate) => {
                    updateDateRange('start_date', moment(startDate?.valueOf()))
                  }}
                  placeholder={t('startDateAndTime')}
                />
              ) : (
                <DatepickerNew
                  manualInput={false}
                  isOutsideRangeHandler={handleOutsideStartDateRange}
                  isSingle
                  startDate={
                    moment.isMoment(
                      filterStateCopy.creationDateRange.start_date,
                    )
                      ? filterStateCopy.creationDateRange.start_date
                      : null
                  }
                  endDate={undefined}
                  onDateChange={(startDate) => {
                    updateDateRange('start_date', startDate)
                  }}
                  placeholder={t('startDateAndTime')}
                />
              )}
            </div>
            <div className='pat-pr-2'>
              <TextInput
                required={filterStateCopy.creationDateRange.start_date !== ''}
                disabled={filterStateCopy.creationDateRange.start_date === ''}
                type='number'
                callout={(_, val) => {
                  const hour = parseInt(val as string, 10)
                  setFilterStateCopy((prevState) => ({
                    ...prevState,
                    creationDateRange: {
                      ...prevState.creationDateRange,
                      start_date: moment(
                        prevState.creationDateRange.start_date,
                      ).set('hour', hour),
                    },
                  }))
                }}
                labelText={c('hr')}
                stateName='hour'
                value={
                  filterStateCopy.creationDateRange.start_date
                    ? moment(
                        filterStateCopy.creationDateRange.start_date,
                      ).hour()
                    : '00'
                }
                max={23}
                onlyPositiveNumbers
              />
            </div>
            <TextInput
              required={filterStateCopy.creationDateRange.start_date !== ''}
              disabled={filterStateCopy.creationDateRange.start_date === ''}
              type='number'
              callout={(_, val) => {
                setFilterStateCopy((prevState) => ({
                  ...prevState,
                  creationDateRange: {
                    ...prevState.creationDateRange,
                    start_date: moment(
                      prevState.creationDateRange.start_date,
                    ).set('minute', val as number),
                  },
                }))
              }}
              labelText={c('min')}
              stateName='minute'
              value={
                filterStateCopy.creationDateRange.start_date
                  ? moment(
                      filterStateCopy.creationDateRange.start_date,
                    ).minute()
                  : '00'
              }
              max={59}
              onlyPositiveNumbers
            />
          </div>
          <div className={`flex`}>
            <div className={`${styles.date} pat-pr-4`}>
              <FormLabel
                tooltip={{
                  tooltipContent: t('filterJobsEndDateTooltip'),
                }}
                label={t('endDateAndTime')}
              />
              {replaceOldDatepicker ? (
                <DatePicker
                  dateRestrictions={{
                    beforeDate: filterStateCopy.creationDateRange.start_date
                      ? moment(
                          filterStateCopy.creationDateRange.start_date,
                        ).toDate()
                      : moment().subtract(90, 'days').toDate(),
                    afterDate: moment().toDate(),
                  }}
                  type='single'
                  selectedDate={
                    filterStateCopy.creationDateRange.end_date
                      ? new Date(
                          filterStateCopy.creationDateRange.end_date.valueOf(),
                        )
                      : undefined
                  }
                  onDateChange={(endDate) => {
                    updateDateRange('end_date', moment(endDate?.valueOf()))
                  }}
                  placeholder={t('endDateAndTime')}
                />
              ) : (
                <DatepickerNew
                  manualInput={false}
                  isOutsideRangeHandler={handleOutsideEndDateRange}
                  isSingle
                  startDate={
                    moment.isMoment(filterStateCopy.creationDateRange.end_date)
                      ? filterStateCopy.creationDateRange.end_date
                      : null
                  }
                  onDateChange={(endDate) => {
                    updateDateRange('end_date', endDate)
                  }}
                  placeholder={t('endDateAndTime')}
                />
              )}
            </div>
            <div className='pat-pr-2'>
              <TextInput
                required={filterStateCopy.creationDateRange.end_date !== ''}
                disabled={filterStateCopy.creationDateRange.end_date === ''}
                type='number'
                callout={(_, val) => {
                  const hour = parseInt(val as string, 10)
                  setFilterStateCopy((prevState) => ({
                    ...prevState,
                    creationDateRange: {
                      ...prevState.creationDateRange,
                      end_date: moment(
                        prevState.creationDateRange.end_date,
                      ).set('hour', hour),
                    },
                  }))
                }}
                labelText={c('hr')}
                stateName='hour'
                value={
                  filterStateCopy.creationDateRange.end_date
                    ? moment(filterStateCopy.creationDateRange.end_date).hour()
                    : 23
                }
                max={23}
                onlyPositiveNumbers
              />
            </div>
            <div>
              <TextInput
                required={filterStateCopy.creationDateRange.end_date !== ''}
                disabled={filterStateCopy.creationDateRange.end_date === ''}
                type='number'
                callout={(_, val) => {
                  setFilterStateCopy((prevState) => ({
                    ...prevState,
                    creationDateRange: {
                      ...prevState.creationDateRange,
                      end_date: moment(
                        prevState.creationDateRange.end_date,
                      ).set('minute', val as number),
                    },
                  }))
                }}
                labelText={c('min')}
                stateName='minute'
                value={
                  filterStateCopy.creationDateRange.end_date
                    ? moment(
                        filterStateCopy.creationDateRange.end_date,
                      ).minute()
                    : 59
                }
                max={59}
                onlyPositiveNumbers
              />
            </div>
          </div>
          <NumberRangeInput
            filterStates={runTimeRangeState}
            onChangeCallout={updateForm}
          />
        </>
      )}
    </Filter>
  )
}

export default JobsFilter
