import React, { useState } from 'react'
import { Route, Routes } from 'react-router-dom'

import { JobDetails } from './JobDetails'
import Jobs from './Jobs'
import { JobsFilterProvider } from '../../context/jobs-filter-context'
import { type NewJobFieldsType } from './types'
import { c } from '../../common/helpers/TranslationHelpers'

const JobsRoutes = (): React.JSX.Element => {
  const [isOpen, setIsOpen] = useState(false),
    defaultFormFields = {
      job_name: '',
      connection: { id: 0, text: c('selectOption'), value: 'Select Option' },
      query_string: '',
      job_purpose: 'Upload',
      job_type: { id: 0, text: c('selectOption'), value: 'Select Option' },
      description: '',
      request_body: '',
      csv: undefined,
      schedule_job: false,
      date: undefined,
      hour: undefined,
      minute: undefined,
      timezone: '',
    },
    [formFields, setFormFields] = useState<NewJobFieldsType>(defaultFormFields),
    [isCallbackFormOpen, setIsCallbackFormOpen] = useState(false),
    handleCancel = () => {
      setIsOpen(false)
      setFormFields(defaultFormFields)
    },
    handleIsDisabled = (): boolean => {
      return (
        isScheduledInPast ||
        formFields.job_type.value === 'Select Option' ||
        formFields.connection.value === 'Select Option' ||
        (formFields.schedule_job &&
          !(
            formFields.date?.isValid() &&
            formFields.hour &&
            formFields.minute &&
            formFields.timezone
          ))
      )
    },
    [isScheduledInPast, setIsScheduledInPast] = useState(false),
    [scheduleAt, setScheduleAt] = useState(''),
    jobsProps = {
      isOpen: isOpen,
      setIsOpen,
      formFields,
      setFormFields,
      handleCancel,
      handleIsDisabled,
      isScheduledInPast,
      setIsScheduledInPast,
      scheduleAt,
      setScheduleAt,
      isCallbackFormOpen,
      setIsCallbackFormOpen,
    }
  return (
    <JobsFilterProvider>
      <Routes>
        <Route index element={<Jobs {...jobsProps} />} />
        <Route path=':id' element={<JobDetails {...jobsProps} />} />
      </Routes>
    </JobsFilterProvider>
  )
}

export default JobsRoutes
