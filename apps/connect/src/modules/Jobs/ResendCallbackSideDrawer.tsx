import React, { useEffect, useMemo, useState } from 'react'
import { <PERSON><PERSON><PERSON><PERSON>, SideDrawer, TextInput, toast } from '@patterninc/react-ui'
import { useMutation } from '@tanstack/react-query'

import { c, t } from '../../common/helpers/TranslationHelpers'
import SecureAxios from '../../common/services/SecureAxios'
import { resendCallback } from '../../common/services/ConnectEngineService'

type ResendCallbackSideDrawerProps = {
  isOpen: boolean
  setIsOpen: React.Dispatch<React.SetStateAction<boolean>>
  callbackUrl: string
  jobId: string
}

const ResendCallbackSideDrawer: React.FC<ResendCallbackSideDrawerProps> = ({
  isOpen,
  setIsOpen,
  callbackUrl,
  jobId,
}) => {
  const [callbackUrlInput, setCallbackUrlInput] = useState(callbackUrl),
    submitResendCallback = useMutation({
      mutationKey: ['resendCallback', jobId, callbackUrlInput],
      mutationFn: () => {
        return SecureAxios.post(resendCallback(jobId), null, {
          params: { callback_url: callbackUrlInput },
        })
      },
      onSuccess: () => {
        toast({ type: 'success', message: t('jobs:resendCallbackSuccess') })
        setIsOpen(false)
        setCallbackUrlInput(callbackUrl)
      },
      onError: () => {
        toast({ type: 'error', message: t('jobs:resendCallbackFailed') })
        setIsOpen(false)
        setCallbackUrlInput(callbackUrl)
      },
    }),
    validCallbackUrl = useMemo(() => {
      try {
        new URL(callbackUrlInput)
        return true
      } catch {
        return false
      }
    }, [callbackUrlInput])

  useEffect(() => {
    setCallbackUrlInput(callbackUrl)
  }, [callbackUrl, setCallbackUrlInput])
  return (
    <SideDrawer
      isOpen={isOpen}
      closeCallout={() => setIsOpen(false)}
      headerContent={t('jobs:resendCallback')}
      footerContent={
        <FormFooter
          cancelButtonProps={{
            onClick: () => {
              setIsOpen(false)
            },
          }}
          saveButtonProps={{
            disabled: !validCallbackUrl,
            onClick: () => {
              submitResendCallback.mutate()
            },
            children: c('submit'),
          }}
        />
      }
    >
      <TextInput
        callout={(_, value) => {
          setCallbackUrlInput(value.toString())
        }}
        required
        id='callback_url'
        labelText={t('jobs:callbackUrl')}
        type='text'
        value={callbackUrl}
      />
    </SideDrawer>
  )
}

export default ResendCallbackSideDrawer
