import React from 'react'
import {
  Button,
  DownloadLoader,
  Menu,
  notEmpty,
  PopoverNew,
  SideDrawer,
  Spinner,
  StandardTable,
  useIsMobileView,
} from '@patterninc/react-ui'

import { JobDetailsTableConfig } from './job-details-table-config'
import { type JobType } from './types'
import { c, useTranslate } from '../../common/helpers/TranslationHelpers'
interface SubJobsTabProps {
  subjobCount: number
  canViewJobDetails: boolean
  startDownloadingFile: (fileType: string) => void
  downloadingFile: string
  spinnerToggle: boolean
  jobData: JobType | undefined
  subjobRows: JobType[] | undefined
  fetchNextPage: () => void
  hasMoreSubJobs: boolean
  subjobsStatus: 'pending' | 'success' | 'error'
  setActiveTab: (tab: number) => void
}
const SubJobsTab: React.FC<SubJobsTabProps> = ({
  subjobCount,
  canViewJobDetails,
  startDownloadingFile,
  downloadingFile,
  spinnerToggle,
  jobData,
  subjobRows,
  fetchNextPage,
  hasMoreSubJobs,
  subjobsStatus,
  setActiveTab,
}) => {
  const { t } = useTranslate('jobs')
  const isMobile = useIsMobileView(),
    [isDrawOpen, setIsDrawOpen] = React.useState(false)
  const popoverOrDrawerContent = () => (
    <div className='flex flex-direction-column pat-gap-2'>
      <Menu
        actions={[
          {
            callout: () => startDownloadingFile('csv'),
            text: c('downloadCSV'),
            disabled: {
              value: notEmpty(downloadingFile),
            },
          },
          {
            callout: () => startDownloadingFile('json'),
            text: c('downloadJSON'),
            disabled: {
              value: notEmpty(downloadingFile),
            },
          },
        ]}
      />
    </div>
  )
  return (
    <div>
      <div className={'box pat-mb-4'}>
        <div
          className={
            'flex justify-content-between align-items-center pat-gap-4'
          }
        >
          <div className={'flex flex-column'}>
            <div>
              <span className='fs-12 pat-mb-1'>{t('totalSubJobs')}</span>
              <div>
                <span className={`fs-18 ${subjobCount >= 500 ? 'fc-red' : ''}`}>
                  {subjobCount}
                </span>
              </div>
            </div>
          </div>
          {isMobile ? (
            <>
              {canViewJobDetails ? (
                <Button
                  disabled={!jobData || notEmpty(downloadingFile)}
                  className='flex align-items-center'
                  onClick={() => setIsDrawOpen(true)}
                >
                  {downloadingFile &&
                    (spinnerToggle ? (
                      <Spinner />
                    ) : (
                      <DownloadLoader customClass='pat-pr-2' />
                    ))}
                  {c('download')}
                </Button>
              ) : (
                <></>
              )}
              <SideDrawer
                isOpen={isDrawOpen}
                closeCallout={() => setIsDrawOpen(false)}
                headerContent=''
              >
                {popoverOrDrawerContent()}
              </SideDrawer>
            </>
          ) : (
            <PopoverNew
              position='top'
              popoverContent={popoverOrDrawerContent()}
              noPadding
            >
              {({ visible, setVisible }) => {
                return canViewJobDetails ? (
                  <Button
                    disabled={!jobData || notEmpty(downloadingFile)}
                    className='flex align-items-center'
                    onClick={() => setVisible(!visible)}
                  >
                    {downloadingFile &&
                      (spinnerToggle ? (
                        <Spinner />
                      ) : (
                        <DownloadLoader customClass='pat-pr-2' />
                      ))}
                    {c('download')}
                  </Button>
                ) : (
                  <></>
                )
              }}
            </PopoverNew>
          )}
        </div>
      </div>
      <StandardTable
        config={JobDetailsTableConfig(canViewJobDetails, setActiveTab)}
        data={subjobRows || []}
        dataKey='job_id'
        getData={fetchNextPage}
        hasMore={hasMoreSubJobs || false}
        hasData={(subjobRows || []).length > 0}
        noDataFields={{
          primaryText: t('noSubJobsAvailable'),
        }}
        sort={() => {
          //Todo: Implement sort logic
        }}
        sortBy={{
          flip: true,
          prop: 'name',
        }}
        successStatus={subjobsStatus === 'success'}
        tableId='job_details_table'
        loading={subjobsStatus === 'pending'}
        widthOffset={320}
        stickyTableConfig={{ right: 1 }}
      />
    </div>
  )
}

export default SubJobsTab
