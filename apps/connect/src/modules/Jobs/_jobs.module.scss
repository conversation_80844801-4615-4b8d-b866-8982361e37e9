.customClickText {
  display: flex;
  justify-content: space-between;
  padding: 12px;
}

.date {
  width: 750px;
}

.metadataBodyContainer {
  max-width: 70vw;
  max-height: 50vh;
  overflow-x: scroll;
  overflow-y: scroll;
  display: flex;
}

@media (min-width: 1200px) {
  .metadataBodyContainer {
    max-width: 70vw;
  }
}

@media (max-width: 800px) {
  .metadataBodyContainer {
    max-width: 90vw;
  }
}

.alignStart {
  align-items: flex-start;
  justify-content: flex-start;
}

.requestBodyInput textarea {
  min-height: 20vh;
}

.alignCenter {
  align-items: center;
  justify-content: center;
}

.headerContainer {
  display: flex;
  align-items: center;
  gap: 16px;
}
