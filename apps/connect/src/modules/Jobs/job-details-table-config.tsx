import React from 'react'
import {
  Button,
  type ConfigItemType,
  MdashCheck,
  notEmpty,
  Tag,
  TrimText,
} from '@patterninc/react-ui'
import moment from 'moment'
import { Link } from 'react-router-dom'

import { type JobType } from './types'
import { c, t } from '../../common/helpers/TranslationHelpers'

export const JobDetailsTableConfig = (
  canViewJobDetails: boolean,
  setActiveTab: (tab: number) => void,
): ConfigItemType<JobType, Record<string, unknown>>[] => {
  const timeFormat = 'MMM DD, YYYY HH:mm'

  return [
    {
      label: t('jobs:subJobId'),
      name: 'job_id',
      noSort: false,
      mainColumn: true,
      cell: {
        children: (d: JobType): React.JSX.Element => {
          return <span>{d.job_id}</span>
        },
      },
    },
    {
      label: c('status'),
      name: 'status',
      noSort: false,
      cell: {
        children: (d: JobType): React.JSX.Element => {
          return (
            <Tag
              color={
                d.status === 'completed'
                  ? 'green'
                  : d.status === 'queued'
                    ? 'blue'
                    : d.status === 'running'
                      ? 'yellow'
                      : 'red'
              }
            >
              {d.status}
            </Tag>
          )
        },
      },
    },
    {
      label: t('jobs:responseBody'),
      name: 'reason',
      noSort: true,
      cell: {
        children: (d: JobType): React.JSX.Element => {
          return (
            <MdashCheck check={notEmpty(d.status_reason)}>
              <TrimText limit={48} text={d.status_reason} />
            </MdashCheck>
          )
        },
      },
    },
    {
      label: c('createdOn'),
      name: 'created_on',
      cell: {
        children: (d: JobType): React.JSX.Element => {
          return (
            <MdashCheck check={!!d?.created_on}>
              {moment(d.created_on).format(timeFormat)}
            </MdashCheck>
          )
        },
      },
    },
    {
      label: c('completedOn'),
      name: 'ended_on',
      cell: {
        children: (d: JobType): React.JSX.Element => {
          return (
            <MdashCheck check={!!d?.ended_on}>
              {moment(d.ended_on).format(timeFormat)}
            </MdashCheck>
          )
        },
      },
    },
    {
      label: c('runTime'),
      name: 'run_time',
      cell: {
        children: (d: JobType): React.JSX.Element => {
          return (
            <MdashCheck check={d.run_time !== undefined && d.run_time !== null}>
              {`${d.run_time} ms`}
            </MdashCheck>
          )
        },
      },
    },
    {
      label: c('requestPath'),
      name: 'request_path',
      cell: {
        //Todo: Implement once request_path is sent from backend
        children: () => {
          return <></>
        },
      },
    },
    {
      label: '',
      name: 'details',
      noSort: true,
      cell: {
        children: (d: JobType): React.JSX.Element => {
          return canViewJobDetails ? (
            <Button
              onClick={() => setActiveTab(0)}
              as='link'
              to={`/jobs/${d.job_id}`}
              routerComponent={Link}
            >
              {c('viewDetails')}
            </Button>
          ) : (
            <></>
          )
        },
      },
    },
  ]
}
