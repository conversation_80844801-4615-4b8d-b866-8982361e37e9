import React from 'react'
import {
  Button,
  ButtonGroup,
  type ConfigItemType,
  Icon,
  MdashCheck,
  notEmpty,
  Tag,
  TrimText,
} from '@patterninc/react-ui'
import moment from 'moment'
import { Link } from 'react-router-dom'

import { type JobType } from './types'
import { c, t } from '../../common/helpers/TranslationHelpers'
import { copyToClipboard } from '../../common/helpers/utils'

export const JobsTableConfig = (
  selectionList?: string[],
  canViewJobDetails?: boolean,
  copiedJobId?: string,
  setIsCallbackFormOpen?: React.Dispatch<React.SetStateAction<boolean>>,
  setCallbackUrl?: React.Dispatch<React.SetStateAction<string>>,
  setJobId?: React.Dispatch<React.SetStateAction<string>>,
  setCopiedJobId?: React.Dispatch<React.SetStateAction<string>>,
): ConfigItemType<JobType, Record<string, unknown>>[] => {
  const timeFormat = 'MMM DD, YYYY HH:mm',
    handleCopyToClipboard = (jobId: string) => {
      copyToClipboard(jobId)
      if (setCopiedJobId) {
        setCopiedJobId(jobId)
        setTimeout(() => setCopiedJobId(''), 1000)
      }
    },
    firstColumnConfig = {
      label: t('jobs:jobType'),
      name: 'type',
      noSort: true,
      cell: {
        children: (d: JobType): React.JSX.Element => {
          return (
            <>
              <MdashCheck check={notEmpty(d.job_type)}>
                <span className={d.sortProp === 'type' ? 'fw-semi-bold' : ''}>
                  {d.job_type}
                </span>
              </MdashCheck>
              <span className='fs-10 fw-regular fc-gray'>{d.job_id}</span>
              <Button
                styleType='text-blue'
                onClick={() => handleCopyToClipboard(d.job_id)}
                className='align-items-center'
                style={{ marginLeft: '4px' }}
              >
                {copiedJobId === d.job_id ? (
                  <Icon icon='check' iconSize='12px' color='dark-green' />
                ) : (
                  <Icon icon='documents' iconSize='12px' color='dark-blue' />
                )}
              </Button>
            </>
          )
        },
      },
    },
    allColumnConfig = [
      {
        label: c('status'),
        name: 'status',
        noSort: true,
        cell: {
          children: (d: JobType): React.JSX.Element => {
            return (
              <>
                <Tag
                  color={
                    d.status === 'completed'
                      ? 'green'
                      : d.status === 'queued'
                        ? 'blue'
                        : d.status === 'running'
                          ? 'yellow'
                          : 'red'
                  }
                >
                  {d.status}
                </Tag>
                <MdashCheck check={notEmpty(d.status_reason)}>
                  <span className='pat-pt-2 fs-10 fw-regular fc-gray'>
                    <TrimText limit={20} text={d.status_reason} />
                  </span>
                </MdashCheck>
              </>
            )
          },
        },
      },
      {
        label: t('jobs:requestBody'),
        name: 'request_body',
        noSort: true,
        cell: {
          children: (d: JobType): React.JSX.Element => {
            // NOTE: This ternary, and the resulting cast to string[], can be removed once the backend bug
            // causing a string[] to be sent instead of a string is fixed.
            const request_body =
              typeof d.request_body === 'string'
                ? d.request_body
                : (d.request_body as string[]).reduce((res, curr) => {
                    return res + curr
                  }, '')
            return (
              <MdashCheck check={notEmpty(d.request_body)}>
                <span className='pat-pt-2 fs-10 fw-regular fc-gray'>
                  <TrimText
                    limit={20}
                    text={request_body.replace(/\s+/g, ' ')}
                  />
                </span>
              </MdashCheck>
            )
          },
        },
      },
      {
        label: c('marketplace'),
        name: 'marketplace',
        noSort: true,
        cell: {
          children: (d: JobType): React.JSX.Element => {
            return (
              <span
                className={d.sortProp === 'marketplace' ? 'fw-semi-bold' : ''}
              >
                {d.marketplace}
              </span>
            )
          },
        },
      },
      {
        label: c('supportId'),
        name: 'support_id',
        noSort: true,
        cell: {
          children: (d: JobType): React.JSX.Element => {
            return (
              <span
                className={d.sortProp === 'support_id' ? 'fw-semi-bold' : ''}
              >
                {d.support_id}
              </span>
            )
          },
        },
      },
      {
        label: t('jobs:retryAttempt'),
        name: 'retry_count',
        cell: {
          children: (d: JobType): React.JSX.Element => {
            return (
              <MdashCheck check={d.retry_count != null}>
                <span>{d.retry_count}</span>
              </MdashCheck>
            )
          },
        },
      },
      {
        label: c('description'),
        name: 'notes',
        noSort: true,
        cell: {
          children: (d: JobType): React.JSX.Element => {
            return (
              <MdashCheck check={notEmpty(d.notes)}>
                <span>{d.notes}</span>
              </MdashCheck>
            )
          },
        },
      },
      {
        label: c('created'),
        name: 'created_on',
        cell: {
          children: (d: JobType): React.JSX.Element => {
            return (
              <>
                <MdashCheck check={notEmpty(d.created_on)}>
                  <span
                    className={
                      d.sortProp === 'created_on' ? 'fw-semi-bold' : ''
                    }
                  >
                    {moment(d.created_on).format(timeFormat)}
                  </span>
                </MdashCheck>
                <span className='fs-10 fw-regular fc-gray'>
                  {d.created_by || 'Connect Admin User'}
                </span>
              </>
            )
          },
        },
      },
      {
        label: c('startedOn'),
        name: 'started_on',
        cell: {
          children: (d: JobType): React.JSX.Element => {
            return (
              <MdashCheck check={notEmpty(d.started_on)}>
                <span
                  className={d.sortProp === 'started_on' ? 'fw-semi-bold' : ''}
                >
                  {d.status === 'queued'
                    ? `Scheduled for: ${moment(d.scheduled_to_run_at).format(
                        timeFormat,
                      )}`
                    : moment(d.started_on).format(timeFormat)}
                </span>
              </MdashCheck>
            )
          },
        },
      },
      {
        label: t('jobs:runTime'),
        name: 'run_time',
        cell: {
          children: (d: JobType): React.JSX.Element => {
            return (
              <MdashCheck
                check={d.run_time !== undefined && d.run_time !== null}
              >
                <span
                  className={d.sortProp === 'run_time' ? 'fw-semi-bold' : ''}
                >
                  {`${d.run_time} ms`}
                </span>
              </MdashCheck>
            )
          },
        },
      },
      {
        label: c('completedOn'),
        name: 'ended_on',
        cell: {
          children: (d: JobType): React.JSX.Element => {
            return (
              <MdashCheck check={notEmpty(d.ended_on)}>
                <span
                  className={d.sortProp === 'ended_on' ? 'fw-semi-bold' : ''}
                >
                  {moment(d.ended_on).format(timeFormat)}
                </span>
              </MdashCheck>
            )
          },
        },
      },
      {
        isButton: true,
        label: '',
        name: 'details',
        noSort: true,
        cell: {
          children: (d: JobType): React.JSX.Element => {
            return canViewJobDetails ? (
              <ButtonGroup
                buttons={[
                  {
                    actions: [
                      {
                        text: t('jobs:resendCallback'),
                        callout: () => {
                          setIsCallbackFormOpen?.(true)
                          setCallbackUrl?.(d.callback_url)
                          setJobId?.(d.job_id)
                        },

                        disabled: {
                          value:
                            d.status !== 'completed' &&
                            d.status !== 'failed' &&
                            d.status !== 'partially_failed',
                        },
                      },
                    ],
                  },
                  {
                    as: 'link',
                    children: c('viewDetails'),
                    to: `/jobs/${d.job_id}`,
                    routerComponent: Link,
                  },
                ]}
              />
            ) : (
              <></>
            )
          },
        },
      },
    ]

  if (typeof selectionList !== 'undefined') {
    const orderedSelectionList = allColumnConfig
      ?.map((item) =>
        selectionList?.find((label) => item && item.label === label),
      )
      ?.filter((item) => item !== undefined)
    const allColumnConfigTemp = orderedSelectionList?.map((label) => {
      return allColumnConfig?.filter((item) => item.label === label && item)[0]
    })

    return [
      firstColumnConfig,
      ...allColumnConfigTemp,
      allColumnConfig[allColumnConfig.length - 1],
    ]
  } else {
    return [firstColumnConfig, ...allColumnConfig]
  }
}
