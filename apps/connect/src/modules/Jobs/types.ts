import { type Moment } from 'moment'

import { type OptionType } from '../../common/types'

export type JobType = {
  job_id: string
  is_oversized_request?: boolean
  job_name: string
  job_type: string
  status: string
  marketplace: string
  support_id: string
  status_reason: string
  notes: string
  created_by: string
  created_on: string
  started_on: string
  scheduled_to_run_at: string
  run_time: number
  ended_on: string
  request_body: string
  sortProp?: string
  subjobs?: JobType[]
  request_path: string
  request_method: 'POST' | 'GET' | 'PUT'
  callback_url: string
  retry_count?: number
}

export type JobsProps = {
  isCallbackFormOpen: boolean
  setIsCallbackFormOpen: React.Dispatch<React.SetStateAction<boolean>>
}

export type JobDetailsProps = {
  isOpen: boolean
  setIsOpen: React.Dispatch<React.SetStateAction<boolean>>
  formFields: NewJobFieldsType
  setFormFields: React.Dispatch<React.SetStateAction<NewJobFieldsType>>
  handleCancel: () => void
  handleIsDisabled: () => boolean
  setIsScheduledInPast: React.Dispatch<React.SetStateAction<boolean>>
  scheduleAt: string
  setScheduleAt: React.Dispatch<React.SetStateAction<string>>
  isCallbackFormOpen: boolean
  setIsCallbackFormOpen: React.Dispatch<React.SetStateAction<boolean>>
}

export type NewJobFieldsType = {
  callback_url?: string
  job_id?: string
  is_oversized_request?: boolean
  job_name: string
  connection: OptionType
  query_string: string
  request_body: string
  job_purpose: string
  job_type: OptionType
  description: string
  csv: File | undefined
  schedule_job: boolean
  date: Moment | undefined | null
  hour: number | undefined
  minute: number | undefined
  timezone: string
}

export type CreateJobParamType = {
  schedule_at?: string
  file?: File
  job: {
    name?: string
    query_string?: string
    request_body?: string
    request_path?: string
    type: string
    notes?: string
  } & ({ purpose: string } | { request_method: string })
}

export type RerunJobParamType = {
  schedule_at?: string
  query_string?: string
  job: {
    notes?: string
    requester: string
    request_body?: string
    callback_url?: string
    account?: string
  }
}

export type JobFieldValue =
  | string
  | boolean
  | Moment
  | File
  | OptionType
  | undefined
