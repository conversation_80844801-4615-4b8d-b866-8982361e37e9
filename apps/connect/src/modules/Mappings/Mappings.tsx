import React, { useContext, useEffect } from 'react'
import {
  Navigate,
  NavLink,
  Route,
  Routes,
  useLocation,
  useNavigate,
} from 'react-router-dom'
import { RouterTabs } from '@patterninc/react-ui'

import { ThemeContext } from '../../Context'
import MappingsChannelsTab from './MappingsChannels/MappingsChannelsTab'
import MappingsConnectFieldNamesTab from './MappingsConnectFieldNames/MappingsConnectFieldNamesTab'
import { useTranslate } from '../../common/helpers/TranslationHelpers'

const Mappings = (): React.JSX.Element => {
  const { t } = useTranslate('mappings'),
    { updateBreadcrumbs } = useContext(ThemeContext),
    { pathname } = useLocation(),
    navigate = useNavigate(),
    mobileTabs = [
      { label: t('channels'), link: 'channels' },
      { label: t('connectFieldNames'), link: 'connect_field_names' },
    ]

  useEffect(() => {
    updateBreadcrumbs({
      name: t('mappings'),
      link: pathname,
      changeType: 'rootLevel',
    })
  }, [pathname, updateBreadcrumbs, t])

  return (
    <>
      <RouterTabs
        mobileConfig={mobileTabs}
        navigate={navigate}
        currentPath={pathname}
      >
        <NavLink to='/mappings/channels'>{t('channels')}</NavLink>
        <NavLink to='/mappings/connect_field_names'>
          {t('connectFieldNames')}
        </NavLink>
      </RouterTabs>

      <Routes>
        <Route path='/' element={<Navigate to='channels' replace />} />
        <Route path='channels' element={<MappingsChannelsTab />} />
        <Route
          path='connect_field_names'
          element={<MappingsConnectFieldNamesTab />}
        />
      </Routes>
    </>
  )
}

export default Mappings
