import { Filter } from '@patterninc/react-ui'
import React, { useContext, useEffect, useMemo, useState } from 'react'

import sortOptions from '../../../common/services/SortOptions'
import { MappingsFilterContext } from '../../../context/mappings-filter-context'
import { c, useTranslate } from '../../../common/helpers/TranslationHelpers'

type ChannelsFilterProps = {
  marketplaceOptions?: { name: string; display_name: string }[] // subject to change, WIP
  areFilterOptionsLoading: boolean
}
export type ChannelsFilterStateType = {
  marketplaces: string[]
  status: string[]
}

export const initialChannelsFilters: ChannelsFilterStateType = {
  marketplaces: [],
  status: [],
}

export const ChannelsFilter = ({
  marketplaceOptions,
  areFilterOptionsLoading,
}: ChannelsFilterProps): React.JSX.Element => {
  const { t } = useTranslate('mappings'),
    { appliedFilters: filterState, setAppliedFilters: setFilterState } =
      useContext(MappingsFilterContext),
    [filterStateCopy, setFilterStateCopy] =
      useState<ChannelsFilterStateType>(filterState),
    marketplacesList = useMemo(() => {
      return sortOptions(marketplaceOptions ?? [], 'display_name')
    }, [marketplaceOptions]),
    filterStates = useMemo((): React.ComponentProps<
      typeof Filter
    >['filterStates'] => {
      return {
        marketplaces: {
          type: 'multi-select',
          labelKey: 'display_name',
          options: marketplacesList,
          formLabelProps: {
            label: c('marketplace(s)'),
          },
          selectedOptions: filterStateCopy.marketplaces.map((m) => ({
            display_name: m,
          })),
          stateName: 'marketplaces',
          selectPlaceholder: c('all'),
        },
        status: {
          type: 'multi-select',
          labelKey: 'status_name',
          options: [
            { status_name: t('mapped') },
            { status_name: t('notMapped') },
            { status_name: t('partiallyMapped') },
          ],
          selectedOptions: filterStateCopy.status.map((s) => ({
            status_name: s,
          })),
          formLabelProps: {
            label: c('status'),
          },
          stateName: 'status',
          selectPlaceholder: c('all'),
        },
      }
    }, [marketplacesList, filterStateCopy, t]),
    appliedFiltersCount = useMemo(() => {
      let count = 0
      const filters: (keyof ChannelsFilterStateType)[] = [
        'marketplaces',
        'status',
      ]
      filters.forEach((key) => {
        if (
          JSON.stringify(filterState[key]) !==
          JSON.stringify(initialChannelsFilters[key])
        ) {
          count++
        }
      })
      return count
    }, [filterState])

  useEffect(() => {
    setFilterStateCopy(filterState)
  }, [filterState])

  const cancelCallout = () => {
    setFilterStateCopy(filterState)
  }

  return (
    <>
      <Filter
        loading={areFilterOptionsLoading}
        resetCallout={() => {
          setFilterState(initialChannelsFilters)
          setFilterStateCopy(initialChannelsFilters)
        }}
        filterCallout={() => {
          setFilterState(filterStateCopy)
        }}
        onChangeCallout={(...params: unknown[]) => {
          const name = params[0] as keyof ChannelsFilterStateType,
            value = params[1]
          setFilterStateCopy({ ...filterStateCopy, [name]: value })
        }}
        cancelCallout={cancelCallout}
        appliedFilters={appliedFiltersCount}
        filterStates={filterStates}
      />
    </>
  )
}
