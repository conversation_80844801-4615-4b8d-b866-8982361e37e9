import { Filter } from '@patterninc/react-ui'
import React, { useEffect, useMemo, useState } from 'react'

import sortOptions from '../../../../common/services/SortOptions'
import { type OptionType } from '../../../../common/types'
import { c, useTranslate } from '../../../../common/helpers/TranslationHelpers'

export type ChannelDetailsFilterStateType = {
  updated_by: string[]
  connect_mapping_status: string[]
}

type ChannelDetailsFilterProps = {
  filterState: ChannelDetailsFilterStateType
  setFilterState: React.Dispatch<
    React.SetStateAction<ChannelDetailsFilterStateType>
  >
  userOptions?: OptionType[]
  setFilterParams: React.Dispatch<
    React.SetStateAction<ChannelDetailsFilterStateType>
  >
  areFilterOptionsLoading: boolean
}

export const initialChannelDetailsFilters: ChannelDetailsFilterStateType = {
  updated_by: [],
  connect_mapping_status: [],
}

export const ChannelDetailsFilter = ({
  filterState,
  setFilterState,
  userOptions,
  setFilterParams,
  areFilterOptionsLoading,
}: ChannelDetailsFilterProps): React.JSX.Element => {
  const { t } = useTranslate('mappings'),
    [filterStateCopy, setFilterStateCopy] = useState(filterState),
    updated_by = useMemo(() => {
      return sortOptions(userOptions ?? [], 'text')
    }, [userOptions]),
    filterStates = useMemo((): React.ComponentProps<
      typeof Filter
    >['filterStates'] => {
      return {
        updated_by: {
          type: 'multi-select',
          labelKey: 'text',
          options: updated_by,
          formLabelProps: {
            label: c('lastUpdatedBy'),
          },
          selectPlaceholder: c('all'),
          selectedOptions: filterStateCopy.updated_by.map((m) => ({
            text: m,
          })),
          stateName: 'updated_by',
        },
        connect_mapping_status: {
          type: 'multi-select',
          labelKey: 'text',
          options: [
            { text: t('mapped'), value: 'mapped', id: 1 },
            { text: t('notMapped'), value: 'not_mapped', id: 2 },
          ],
          selectedOptions: filterStateCopy.connect_mapping_status.map((c) => ({
            text: c,
          })),
          formLabelProps: {
            label: c('status'),
          },
          stateName: 'connect_mapping_status',
          selectPlaceholder: c('all'),
        },
      }
    }, [filterStateCopy, updated_by, t]),
    appliedFiltersCount = useMemo(() => {
      let count = 0
      const filters: (keyof ChannelDetailsFilterStateType)[] = [
        'updated_by',
        'connect_mapping_status',
      ]
      filters.forEach((key) => {
        if (
          JSON.stringify(filterState[key]) !==
          JSON.stringify(initialChannelDetailsFilters[key])
        ) {
          count++
        }
      })
      return count
    }, [filterState])

  useEffect(() => {
    setFilterStateCopy(filterState)
  }, [filterState])

  useEffect(() => {
    setFilterParams({
      connect_mapping_status: filterState['connect_mapping_status'].map((s) =>
        s.toLowerCase().replace(/ /g, '_'),
      ),
      updated_by: filterState['updated_by'],
    })
  }, [filterState, setFilterParams])

  const cancelCallout = () => {
    setFilterStateCopy(filterState)
  }

  return (
    <Filter
      loading={areFilterOptionsLoading}
      resetCallout={() => {
        setFilterState(initialChannelDetailsFilters)
        setFilterStateCopy(initialChannelDetailsFilters)
      }}
      filterCallout={() => {
        setFilterState(filterStateCopy)
      }}
      onChangeCallout={(...props: unknown[]) => {
        const key = props[0] as keyof ChannelDetailsFilterStateType,
          value = props[1]
        setFilterStateCopy({ ...filterStateCopy, [key]: value })
      }}
      cancelCallout={cancelCallout}
      filterStates={filterStates}
      appliedFilters={appliedFiltersCount}
    />
  )
}
