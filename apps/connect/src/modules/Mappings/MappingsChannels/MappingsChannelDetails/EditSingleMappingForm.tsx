import React, { useCallback } from 'react'
import { SideDrawer } from '@patterninc/react-ui'

import { type ChannelDetailType } from './types'
import { useTranslate } from '../../../../common/helpers/TranslationHelpers'

const EditSingleMappingForm = ({
  fieldMapping,
  isOpen,
  setIsOpen,
}: {
  fieldMapping: ChannelDetailType
  isOpen: boolean
  setIsOpen: React.Dispatch<React.SetStateAction<boolean>>
}): React.JSX.Element => {
  const { t } = useTranslate('mappings')

  const handleClose = useCallback(() => {
    setIsOpen(false)
  }, [setIsOpen])

  return (
    <SideDrawer
      isOpen={isOpen}
      closeCallout={handleClose}
      headerContent={`${t('youreEditing', { editingName: fieldMapping.display_name })}`}
    >
      {t('editMappingDetailsHere')}
    </SideDrawer>
  )
}

export default EditSingleMappingForm
