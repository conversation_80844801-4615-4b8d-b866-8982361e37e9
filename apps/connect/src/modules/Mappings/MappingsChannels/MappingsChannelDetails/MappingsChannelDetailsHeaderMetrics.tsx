import { HeaderMetric, HeaderMetricGroup } from '@patterninc/react-ui'
import React, { useMemo } from 'react'
import { useQuery } from '@tanstack/react-query'

import styles from '../mappingsChannels.module.scss'
import { type ChannelDetailsHeaderSummaryData } from '../../types'
import {
  ChannelDetailsFilter,
  type ChannelDetailsFilterStateType,
} from './ChannelDetailsFilter'
import { usersApi } from '../../../../common/services/ConnectAuthService'
import { useOrg } from '../../../../context/org-context'
import { PRIVILEGES, useHasPrivilege } from '../../../../context/auth-context'
import SecureAxios from '../../../../common/services/SecureAxios'
import { type OptionType } from '../../../../common/types'
import { useTranslate } from '../../../../common/helpers/TranslationHelpers'

const MappingsChannelDetailsHeaderMetrics = ({
  isLoading,
  summaryHeaderData,
  setFilterParams,
  filterState,
  setFilterState,
}: {
  isLoading: boolean
  summaryHeaderData: ChannelDetailsHeaderSummaryData
  setFilterParams: React.Dispatch<
    React.SetStateAction<ChannelDetailsFilterStateType>
  >
  filterState: ChannelDetailsFilterStateType
  setFilterState: React.Dispatch<
    React.SetStateAction<ChannelDetailsFilterStateType>
  >
}): React.JSX.Element => {
  const { t } = useTranslate('mappings'),
    { org } = useOrg(),
    canViewUsersAndJobs = useHasPrivilege(PRIVILEGES.VIEW_JOBS, true),
    {
      data: usersData,
      status: usersStatus,
      isLoading: isUserDataLoading,
    } = useQuery<{ id: number; email: string; name: string }[]>({
      queryKey: [usersApi, org],
      queryFn: ({ signal }) =>
        SecureAxios.get<{ id: number; email: string; name: string }[]>(
          usersApi,
          {
            signal,
            params: {
              organization_code: org.code,
            },
          },
        ).then((response) => response.data),
      enabled: canViewUsersAndJobs,
    }),
    updatedByOptions: OptionType[] = useMemo(() => {
      if (usersStatus === 'success') {
        const uniqueEmails: string[] = [
          ...new Set<string>(
            usersData.map(
              (user: { id: number; name: string; email: string }) => user.email,
            ),
          ),
        ]
        return uniqueEmails
          .map(
            (email) =>
              usersData.find((u) => u.email === email) || {
                id: 0,
                name: '',
                email: '',
              },
          )
          .map((user) => {
            return {
              id: user.id,
              text: user.name,
              value: user.email,
            }
          })
      } else {
        return []
      }
    }, [usersData, usersStatus])

  return (
    <section className={`${styles.mainBoxMetric} box`}>
      <div className='flex'>
        <HeaderMetric
          metricValueClassName='fc-black fw-medium'
          loading={isLoading}
          value={summaryHeaderData?.field_count}
          title={t('totalFields')}
          fontSize='fs-22'
        />
        <div className='flex align-items-center pat-pr-4'>
          <ChannelDetailsFilter
            filterState={filterState}
            setFilterState={setFilterState}
            userOptions={updatedByOptions}
            setFilterParams={setFilterParams}
            areFilterOptionsLoading={isUserDataLoading}
          />
        </div>
      </div>
      <HeaderMetricGroup
        loading={isLoading}
        data={[
          {
            formatType: 'number',
            title: t('notMappedFields'),
            value: summaryHeaderData?.connect_field_unmapped_count,
          },
          {
            formatType: 'number',
            title: t('mappedFields'),
            value: summaryHeaderData?.connect_field_mapped_count,
          },
        ]}
      />
    </section>
  )
}

export default MappingsChannelDetailsHeaderMetrics
