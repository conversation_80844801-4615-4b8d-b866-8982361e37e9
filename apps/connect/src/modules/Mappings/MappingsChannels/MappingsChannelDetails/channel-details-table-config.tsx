import React from 'react'
import {
  type ConfigItemType,
  MdashCheck,
  notEmpty,
  Select,
  Tag,
} from '@patterninc/react-ui'
import moment from 'moment'

import { type ChannelDetailType } from './types'
import { tableTimeFormat } from '../../../../common/constants'
import styles from '../mappingsChannels.module.scss'
import {
  type OtherOptionsType,
  type SuggestedParamsType,
} from './MappingsChannelDetails'
import { renderTooltip } from '../../SelectHelperFunctions'
import { c, t } from '../../../../common/helpers/TranslationHelpers'

export const ChannelDetailsTableConfig = ({
    isInBulkEditMode,
    otherOptions,
    suggestedOptions,
    setSuggestedParams,
    id,
    isOptionsLoading,
    setFetchTrigger,
    setSearchCFN,
    selected,
    setSelected,
    searchCFN,
    canManageConnectFields,
    hasNextOptionsPage,
    fetchNextOptionsPage,
  }: {
    isInBulkEditMode: boolean
    otherOptions: OtherOptionsType[] | undefined
    suggestedOptions: OtherOptionsType[] | undefined
    setSuggestedParams: React.Dispatch<
      React.SetStateAction<SuggestedParamsType | undefined>
    >
    id: string
    isOptionsLoading: boolean
    setFetchTrigger: React.Dispatch<React.SetStateAction<boolean>>
    searchCFN: string
    setSearchCFN: React.Dispatch<React.SetStateAction<string>>
    selected: Map<string, OtherOptionsType>
    setSelected: React.Dispatch<
      React.SetStateAction<Map<string, OtherOptionsType>>
    >
    canManageConnectFields: boolean
    hasNextOptionsPage: boolean
    fetchNextOptionsPage: () => void
  }): ConfigItemType<ChannelDetailType, Record<string, unknown>>[] => {
    return [
      {
        label: t('mappings:channelFieldName'),
        name: 'channel_field_display_name',
        cell: {
          children: (d: ChannelDetailType): React.JSX.Element => {
            return (
              <span
                className={
                  d.sortProp === 'channel_field_display_name'
                    ? 'fw-semi-bold'
                    : ''
                }
              >
                {notEmpty(d?.display_name)
                  ? d.display_name
                  : d.channel_field_key}
              </span>
            )
          },
        },
      },
      {
        label: t('mappings:connectFieldName'),
        name: 'connect_field_key',
        cell: {
          children: (d: ChannelDetailType): React.JSX.Element => {
            const selectedOption = getSelectedOption(
              selected,
              d.id,
              otherOptions,
            )
            return (
              <>
                {!isInBulkEditMode ? (
                  <MdashCheck check={notEmpty(d.connect_field_key)}>
                    <span
                      className={
                        d.sortProp === 'connect_field_key' ? 'fw-semi-bold' : ''
                      }
                    >
                      {d.connect_field_key}
                    </span>
                  </MdashCheck>
                ) : (
                  <div className={styles.cfnSelect}>
                    <Select
                      scrollProps={{
                        hasMore: hasNextOptionsPage,
                        getData: fetchNextOptionsPage,
                      }}
                      footerButtonProps={
                        canManageConnectFields
                          ? {
                              text: c('createNew'),
                              callout: d.onCreateCFN,
                            }
                          : undefined
                      }
                      loading={isOptionsLoading}
                      onFocus={() => {
                        setSuggestedParams({
                          channel_name: id,
                          channel_field_key: d.channel_field_key,
                        })
                        setFetchTrigger(true)
                      }}
                      options={[
                        {
                          key: 'null',
                          display_name: t('mappings:notMapped'),
                          secondaryContent: renderTooltip(
                            t('mappings:optionToUnmapTheField'),
                          ),
                        },
                        {
                          label: t('mappings:notApplicable'),
                          options:
                            otherOptions?.[0]?.display_name === 'Not Applicable'
                              ? [otherOptions?.[0] || []]
                              : [],
                        },
                        {
                          label: t('mappings:suggestConnectFieldName'),
                          options: suggestedOptions || [],
                        },
                        {
                          label: c('others'),
                          options:
                            d.connect_mapping_status === 'mapped'
                              ? [
                                  notEmpty(selectedOption.key)
                                    ? selectedOption
                                    : {
                                        key: d.connect_field_id,
                                        display_name: d.connect_field_key,
                                        secondaryContent: renderTooltip(
                                          d.description || '',
                                        ),
                                      },
                                  ...(
                                    (notEmpty(searchCFN)
                                      ? otherOptions
                                      : otherOptions?.slice(1)) || []
                                  ).filter(
                                    (item) => item.key !== d.connect_field_id,
                                  ),
                                ]
                              : notEmpty(selectedOption.key)
                                ? [
                                    selectedOption,
                                    ...((notEmpty(searchCFN)
                                      ? otherOptions
                                      : otherOptions?.slice(1)) || []),
                                  ]
                                : (notEmpty(searchCFN)
                                    ? otherOptions
                                    : otherOptions?.slice(1)) || [],
                        },
                      ]}
                      selectedItem={
                        selected.get(d.id) ||
                        (d.connect_field_id
                          ? {
                              key: d.connect_field_id,
                              display_name: d.connect_field_key,
                              secondaryContent: renderTooltip(
                                d.description || '',
                              ),
                            }
                          : {
                              key: 'null',
                              display_name: t('mappings:notMapped'),
                              secondaryContent: renderTooltip(
                                t('mappings:optionToUnmapTheField'),
                              ),
                            })
                      }
                      searchBarProps={{
                        showSearchBar: true,
                        onChange(value) {
                          setSearchCFN(value)
                        },
                      }}
                      optionKeyName='key'
                      labelKeyName='display_name'
                      onBlur={() => {
                        setSearchCFN('')
                      }}
                      onChange={(selectedOption) => {
                        setSelected((prevSelected) => {
                          const updatedMap = new Map(prevSelected)
                          updatedMap.set(d.id, {
                            key: selectedOption.key || '',
                            display_name: selectedOption?.display_name || '',
                            secondaryContent:
                              selectedOption.secondaryContent ||
                              renderTooltip(''),
                          })
                          setSearchCFN('')
                          return updatedMap
                        })
                      }}
                    />
                  </div>
                )}
              </>
            )
          },
        },
      },
      {
        label: c('status'),
        name: 'connect_mapping_status',
        noSort: true,
        cell: {
          children: (d: ChannelDetailType): React.JSX.Element => {
            return (
              <Tag
                color={d.connect_mapping_status === 'mapped' ? 'green' : 'red'}
              >
                {d.connect_mapping_status}
              </Tag>
            )
          },
        },
      },
      {
        label: c('updatedBy'),
        name: 'connect_mapping_updated_by_user_name',
        noSort: true,
        cell: {
          children: (d: ChannelDetailType): React.JSX.Element => {
            return (
              <>
                <MdashCheck
                  check={notEmpty(d.connect_mapping_updated_by_user_name)}
                >
                  <span
                    className={
                      d.sortProp === 'connect_mapping_updated_by_user_name'
                        ? 'fw-semi-bold'
                        : ''
                    }
                  >
                    {d.connect_mapping_updated_by_user_name}
                  </span>
                </MdashCheck>
              </>
            )
          },
        },
      },
      {
        label: c('updatedOn'),
        name: 'updated_at',
        cell: {
          children: (d: ChannelDetailType): React.JSX.Element => {
            return (
              <>
                <span>{moment(d.updated_at).format(tableTimeFormat)}</span>
              </>
            )
          },
        },
      },
    ]
  },
  getSelectedOption = (
    selected: Map<string, OtherOptionsType>,
    id: string,
    otherOptions: OtherOptionsType[] | undefined,
  ) => {
    return selected.get(id) &&
      selected.get(id)?.key !== 'null' &&
      selected.get(id)?.display_name !== 'Not Applicable' &&
      !otherOptions?.some((option) => option.key === selected.get(id)?.key)
      ? {
          key: selected.get(id)?.key || '',
          display_name: selected.get(id)?.display_name || '',
          secondaryContent: selected.get(id)?.secondaryContent || <></>,
        }
      : {}
  }
