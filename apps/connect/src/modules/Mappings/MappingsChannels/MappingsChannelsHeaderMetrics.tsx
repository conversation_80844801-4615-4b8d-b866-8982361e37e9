import { HeaderMetric } from '@patterninc/react-ui'
import React from 'react'
import { useQuery } from '@tanstack/react-query'

import styles from './mappingsChannels.module.scss'
import { type HeaderSummaryData } from '../types'
import { ChannelsFilter } from './ChannelsFilter'
import SecureAxios from '../../../common/services/SecureAxios'
import { marketplaces } from '../../../common/services/ConnectSyndicationService'
import { useTranslate } from '../../../common/helpers/TranslationHelpers'

const MappingsChannelsHeaderMetrics = ({
  summaryHeaderData,
  isLoading,
}: {
  summaryHeaderData: HeaderSummaryData
  isLoading: boolean
}): React.JSX.Element => {
  const { t } = useTranslate('mappings'),
    { data: marketplacesData, isLoading: isMarketplacesOptionsLoading } =
      useQuery({
        queryKey: ['marketplaces_list'],
        queryFn: () =>
          SecureAxios.get<{ name: string; display_name: string }[]>(
            marketplaces,
          ).then((result) => result.data),
      })

  return (
    <section className={`${styles.mainBoxMetric} box`}>
      <div className={`flex ${styles.topHeaderMetric}`}>
        <HeaderMetric
          key='unmapped_channels'
          title={t('unmappedChannels')}
          value={summaryHeaderData?.unmapped_channels}
          formatType='number'
          fontSize='fs-22'
          loading={isLoading}
        />
        <div className='flex pat-pr-4'>
          <div className={`pat-my-4 bgc-light-gray ${styles.divider}`} />
        </div>
        <HeaderMetric
          key='unmapped_fields'
          title={t('unmappedFields')}
          value={summaryHeaderData?.unmapped_fields}
          formatType='number'
          fontSize='fs-22'
          loading={isLoading}
        />
        <div className='flex align-items-center pat-pr-4'>
          <ChannelsFilter
            marketplaceOptions={marketplacesData}
            areFilterOptionsLoading={isMarketplacesOptionsLoading}
          />
        </div>
      </div>
    </section>
  )
}

export default MappingsChannelsHeaderMetrics
