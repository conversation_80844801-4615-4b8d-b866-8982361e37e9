import React, { useContext, useMemo, useState } from 'react'
import { SearchBar, StandardTable } from '@patterninc/react-ui'
import { useInfiniteQuery } from '@tanstack/react-query'

import MappingsChannelsHeaderMetrics from './MappingsChannelsHeaderMetrics'
import { type RawDataItem, type RawDataItemWithSummary } from './types'
import { mappingsChannelTableConfig } from './mappings-channel-table-config'
import SecureAxios from '../../../common/services/SecureAxios'
import { channels } from '../../../common/services/ConnectSyndicationService'
import { MappingsFilterContext } from '../../../context/mappings-filter-context'
import { initialChannelsFilters } from './ChannelsFilter'
import { useTranslate } from '../../../common/helpers/TranslationHelpers'

const MappingsChannelsTab = (): React.JSX.Element => {
  const { t } = useTranslate('mappings'),
    { appliedFilters, setAppliedFilters, searchQuery, setSearchQuery } =
      useContext(MappingsFilterContext),
    [sort, setSort] = useState<{ prop: string; flip: boolean }>({
      prop: 'channel',
      flip: false,
    }),
    params = useMemo(() => {
      const sortMap: Record<string, string> = {
        channel: 'name',
        unmapped_fields: 'connect_field_unmapped_count',
        total_fields: 'field_count',
      }
      const searchSortParams = {
        sort_by: sortMap[sort.prop],
        order: sort.flip ? 'asc' : 'desc',
        search: searchQuery,
      }
      const filterParams = {
        connect_mapping_status: appliedFilters['status'].map((s) =>
          s.toLowerCase().replace(/ /g, '_'),
        ),
        marketplace: appliedFilters['marketplaces'].map((m: string) => {
          return m.toLowerCase()
        }),
      }
      return { ...searchSortParams, ...filterParams }
    }, [sort, searchQuery, appliedFilters]),
    { status, data, isLoading, fetchNextPage, hasNextPage } = useInfiniteQuery({
      queryKey: ['channels_api', channels, params],
      queryFn: ({ pageParam = 1 }) => {
        return SecureAxios.get<RawDataItemWithSummary>(channels, {
          params: {
            ...params,
            summary: pageParam === 1,
            page: pageParam ?? 1,
          },
        })
      },
      initialPageParam: 1,
      getNextPageParam: (previousPage) => {
        return previousPage &&
          previousPage.headers['current-page'] !==
            previousPage.headers['total-pages']
          ? Number(previousPage.headers['current-page']) + 1
          : undefined
      },
    }),
    tableData: RawDataItem[] = useMemo(() => {
      return data ? data?.pages?.flatMap((page) => page?.data.channels) : []
    }, [data]),
    activeFilters = useMemo(() => {
      const active: Record<
        string,
        {
          value: number | number[] | string[]
          label: string
          comparison_value: string
        }
      > = {}
      Object.keys(appliedFilters).forEach((filter) => {
        const filterKey = filter as keyof typeof appliedFilters
        if (appliedFilters[filterKey].length > 0) {
          active[filterKey] = {
            value: appliedFilters[filterKey],
            label: filterKey,
            comparison_value: JSON.stringify(appliedFilters[filterKey]),
          }
        }
      })
      return active
    }, [appliedFilters])

  return (
    <>
      <MappingsChannelsHeaderMetrics
        summaryHeaderData={{
          unmapped_channels: data?.pages[0].data.total_not_mapped_channels ?? 0,
          unmapped_fields: data?.pages[0].data.total_unmapped_fields ?? 0,
        }}
        isLoading={isLoading}
      />
      <div className='flex pat-my-4'>
        <SearchBar
          value={searchQuery}
          onChange={setSearchQuery}
          placeholder={t('searchChannels')}
        />
      </div>
      <StandardTable
        config={mappingsChannelTableConfig()}
        data={tableData}
        dataKey='name'
        getData={fetchNextPage}
        hasData={status === 'success' && tableData.length > 0}
        noDataFields={{
          primaryText: t('noChannelsAvailable'),
        }}
        successStatus={status === 'success'}
        tableId='channels_table_id'
        hasMore={!!(status === 'success' && hasNextPage)}
        loading={isLoading}
        sort={({ activeColumn: prop, direction: flip }) => {
          setSort({ prop, flip })
        }}
        sortBy={sort}
        activeFilters={
          Object.keys(activeFilters).length ? activeFilters : undefined
        }
        removeFilters={(filter) => {
          if (filter === undefined) {
            setAppliedFilters(initialChannelsFilters)
            return
          }
          const filterKey = filter as keyof typeof appliedFilters
          setAppliedFilters({
            ...appliedFilters,
            [filterKey]: initialChannelsFilters[filterKey],
          })
        }}
      />
    </>
  )
}

export default MappingsChannelsTab
