import React from 'react'
import {
  Button,
  type ConfigItemType,
  notEmpty,
  Tag,
} from '@patterninc/react-ui'
import { startCase } from 'lodash'
import { Link } from 'react-router-dom'

import { type RawDataItem } from './types'
import { c, t } from '../../../common/helpers/TranslationHelpers'

export const mappingsChannelTableConfig = (): ConfigItemType<
  RawDataItem,
  Record<string, unknown>
>[] => {
  return [
    {
      label: t('mappings:channel'),
      name: 'channel',
      mainColumn: true,
      cell: {
        children: (d) => {
          return (
            <span className='fw-semi-bold'>
              {/* Remove the following conditional check once display_name is available in db */}
              {notEmpty(d.display_name) ? d.display_name : d.name}
            </span>
          )
        },
      },
    },
    {
      label: c('marketplace'),
      name: 'marketplace',
      cell: {
        children: (d) => {
          return <span>{startCase(d.marketplace_display_name)}</span>
        },
      },
      noSort: true,
    },
    {
      label: t('mappings:unmappedFields'),
      name: 'unmapped_fields',
      cell: {
        className: (d) =>
          d.connect_field_unmapped_count !== 0 ? `bgc-light-red` : '',
        children: (d) => {
          return (
            <span
              className={
                d.connect_field_unmapped_count !== 0 ? `fc-dark-red` : ''
              }
            >
              {d.connect_field_unmapped_count}
            </span>
          )
        },
      },
    },
    {
      label: t('mappings:totalFields'),
      name: 'total_fields',
      cell: {
        children: (d) => {
          return <span>{d.field_count}</span>
        },
      },
    },
    {
      label: c('status'),
      name: 'status',
      noSort: true,
      cell: {
        children: (d) => {
          const isMapped = d.connect_mapping_status === 'mapped'
          const isPartiallyMapped =
            d.connect_mapping_status === 'partially_mapped'
          return (
            <Tag
              color={isMapped ? 'green' : isPartiallyMapped ? 'yellow' : 'red'}
            >
              {isMapped
                ? t('mappings:mapped')
                : isPartiallyMapped
                  ? t('mappings:partiallyMapped')
                  : t('mappings:unmapped')}
            </Tag>
          )
        },
      },
    },
    {
      label: '',
      name: 'details',
      noSort: true,
      isButton: true,
      cell: {
        children: (d): React.JSX.Element => {
          return (
            <Button
              state={{
                summaryFromState: {
                  field_count: d.field_count,
                  connect_field_unmapped_count: d.connect_field_unmapped_count,
                  connect_field_mapped_count: d.connect_field_mapped_count,
                },
              }}
              as='link'
              to={`${d.name}`}
              routerComponent={Link}
            >
              {t('mappings:viewFields')}
            </Button>
          )
        },
      },
    },
  ]
}
