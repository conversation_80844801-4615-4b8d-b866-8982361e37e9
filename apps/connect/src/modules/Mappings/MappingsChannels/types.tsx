export type RawDataItem = {
  id: number
  name?: string
  display_name?: string
  connect_field_mapped_count: number
  connect_field_unmapped_count: number
  field_count: number
  marketplace?: string
  marketplace_display_name?: string
  created_at?: string
  updated_at?: string
  connect_mapping_status: 'mapped' | 'partially_mapped' | 'not_mapped'
}

export type RawDataItemWithSummary = {
  channels: RawDataItem[]
  total_channels: number
  total_fields: number
  total_mapped_fields: number
  total_unmapped_fields: number
  total_mapped_channels: number
  total_not_mapped_channels: number
  total_partially_mapped_channels: number
}
