import React, { useCallback, useState } from 'react'
import { useMutation } from '@tanstack/react-query'
import { <PERSON><PERSON>ooter, SideDrawer, TextInput, toast } from '@patterninc/react-ui'

import SecureAxios from '../../../common/services/SecureAxios'
import { connectFields } from '../../../common/services/ConnectSyndicationService'
import { c, useTranslate } from '../../../common/helpers/TranslationHelpers'

type CreateCFNFormProps = {
  isOpen: boolean
  setIsOpen: React.Dispatch<React.SetStateAction<boolean>>
  onItemCreated: (param: {
    id: string
    connect_field_key: string
    description: string
  }) => void
  layerPosition?: number
}

export const CreateCFNForm = ({
  isOpen,
  setIsOpen,
  onItemCreated,
  layerPosition,
}: CreateCFNFormProps): React.JSX.Element => {
  const { t } = useTranslate('mappings'),
    { mutate: saveNewCFN } = useMutation({
      mutationKey: ['createCFN', onItemCreated],
      mutationFn: (params: {
        connect_field_key: string
        description: string
      }) => {
        return SecureAxios.post(connectFields, params)
      },
      onSuccess: (result) => {
        onItemCreated({
          id: result.data.id,
          connect_field_key: result.data.connect_field_key,
          description: result.data.description,
        })
        setIsOpen(false)
        setDescription('')
        setName('')
      },
      onError: (error, variables) => {
        toast({
          type: 'error',
          message: t('failedToCreateConnectFieldNameWithName', {
            connectFieldName: variables.connect_field_key,
          }),
        })
      },
    }),
    [name, setName] = useState(''),
    [description, setDescription] = useState(''),
    cancelCallout = useCallback(() => {
      setName('')
      setDescription('')
      setIsOpen(false)
    }, [setName, setDescription, setIsOpen])

  return (
    <SideDrawer
      closeCallout={cancelCallout}
      isOpen={isOpen}
      headerContent={t('createNewConnectFieldName')}
      layerPosition={layerPosition}
      footerContent={
        <FormFooter
          cancelButtonProps={{ onClick: cancelCallout }}
          saveButtonProps={{
            children: c('saveChanges'),
            disabled: name === '' || description === '',
            onClick: () => {
              saveNewCFN({ connect_field_key: name, description })
            },
          }}
          resetButtonProps={{
            onClick: () => {
              setName('')
              setDescription('')
            },
          }}
        />
      }
    >
      <div className='flex flex-direction-column pat-gap-4'>
        <TextInput
          labelText={t('connectFieldName')}
          placeholder={t('egProductPrice')}
          value={name}
          callout={(_, value) => setName(`${value}`)}
          type='text'
          required
        />
        <TextInput
          labelText={c('description')}
          placeholder={t('max250Characters')}
          value={description}
          callout={(_, value) => setDescription(`${value}`)}
          type='textarea'
          required
          maxLength={250}
        />
      </div>
    </SideDrawer>
  )
}
