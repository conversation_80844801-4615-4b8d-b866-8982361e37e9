import { Icon, Tooltip, type TooltipProps } from '@patterninc/react-ui'
import React from 'react'

//This file had need to be created because for new Icon impletation we need to use new Icon Toggle.This renderTooltip helper func is using at many places
const CustomInfoTooltip = ({
  tooltipContent,
}: {
  tooltipContent: TooltipProps['tooltipContent']
}): React.JSX.Element => {
  return (
    <Tooltip tooltipContent={tooltipContent} position='right'>
      <Icon icon='info' color='dark-blue' iconSize='16px' />
    </Tooltip>
  )
}

export default CustomInfoTooltip
