import React, { useContext, useEffect, useMemo, useState } from 'react'
import { notEmpty, SearchBar, StandardTable } from '@patterninc/react-ui'
import { useLocation, useParams } from 'react-router-dom'
import { useInfiniteQuery } from '@tanstack/react-query'

import CFNDetailsHeaderMetrics from './CFNDetailsHeaderMetrics'
import { ThemeContext } from '../../../../Context'
import { cfnDetailsTableConfig } from './cfn-details-table-config'
import { type CFNDetailsType, type EditFormDetailsType } from '../../types'
import SecureAxios from '../../../../common/services/SecureAxios'
import { marketplaceFields } from '../../../../common/services/ConnectSyndicationService'
import { EditConnectFieldForm } from './EditConnectFieldForm'
import { renderTooltip } from '../../SelectHelperFunctions'
import { PRIVILEGES, useHasPrivilege } from '../../../../context/auth-context'
import { c, useTranslate } from '../../../../common/helpers/TranslationHelpers'

const CFNDetails = (): React.JSX.Element => {
  const { t } = useTranslate('mappings'),
    [summaryHeaderData, setSummaryHeaderData] = useState({
      mapped_marketplace_fields_count: 0,
    }),
    canManageConnectMappings = useHasPrivilege(
      PRIVILEGES.MANAGE_CONNECT_MAPPINGS,
    ),
    { updateBreadcrumbs } = useContext(ThemeContext),
    tableId = 'cfn_details_table',
    { id } = useParams<{ id: string }>(),
    { pathname, state } = useLocation(),
    [sort, setSort] = useState<{ prop: string; flip: boolean }>({
      prop: 'display_name',
      flip: true,
    }),
    [search, setSearch] = useState(''),
    [isEditOpen, setIsEditOpen] = useState(false),
    [editFormDetails, setEditFormDetails] = useState<EditFormDetailsType>(),
    [description, setDescription] = useState(''),
    params = useMemo(() => {
      const sortMap: Record<string, string> = {
        display_name: 'display_name',
        updated_at: 'updated_at',
        updated_by: 'updated_by',
      }
      return {
        sort_by: sortMap[sort.prop],
        order: sort.flip ? 'asc' : 'desc',
        search: search,
      }
    }, [sort, search]),
    { status, data, isLoading, fetchNextPage, hasNextPage, refetch } =
      useInfiniteQuery({
        queryKey: ['cfn_details_api', id, params, marketplaceFields(id || '')],
        queryFn: ({ pageParam = 1, signal }) => {
          return SecureAxios.get<CFNDetailsType>(marketplaceFields(id || ''), {
            signal,
            params: {
              ...params,
              page: pageParam ?? 1,
            },
          })
        },
        initialPageParam: 1,
        getNextPageParam: (previousPage) => {
          return previousPage &&
            previousPage.headers['current-page'] !==
              previousPage.headers['total-pages']
            ? Number(previousPage.headers['current-page']) + 1
            : undefined
        },
      }),
    tableData: CFNDetailsType[] = useMemo(() => {
      if (!notEmpty(search)) {
        setSummaryHeaderData({
          mapped_marketplace_fields_count:
            data?.pages?.flatMap((page) => page?.data).length || 0,
        })
      }
      return data ? data?.pages?.flatMap((page) => page?.data) : []
    }, [data, search]),
    selectedConnectFieldOption: {
      key: string | undefined
      display_name: string
      secondaryContent: React.JSX.Element
    } = {
      key: id,
      display_name: state?.connect_field_key || '',
      secondaryContent: renderTooltip(
        state?.description || c('noDescriptionAvailable'),
      ),
    }

  useEffect(() => {
    setDescription(state?.description || c('noDescriptionAvailable'))
    updateBreadcrumbs({
      name: t('fieldDetails-', { id: state?.connect_field_key || id }),
      link: pathname,
      changeType: 'tab',
    })
  }, [updateBreadcrumbs, pathname, state, id, t])

  return (
    <>
      <CFNDetailsHeaderMetrics
        isLoading={isLoading}
        summaryHeaderData={summaryHeaderData}
      />
      <div className='flex pat-my-4'>
        <SearchBar
          value={search}
          onChange={setSearch}
          placeholder={c('search')}
        />
      </div>
      <StandardTable
        config={cfnDetailsTableConfig(
          setIsEditOpen,
          setEditFormDetails,
          canManageConnectMappings,
        )}
        data={tableData}
        dataKey='display_name'
        hasData={status === 'success' && tableData.length > 0}
        hasMore={!!(status === 'success' && hasNextPage)}
        successStatus={status === 'success'}
        loading={isLoading}
        tableId={tableId}
        noDataFields={{
          primaryText: t('noFieldsAvailable'),
        }}
        sort={({ activeColumn: prop, direction: flip }) => {
          setSort({ prop, flip })
        }}
        sortBy={sort}
        getData={fetchNextPage}
      />
      <EditConnectFieldForm
        isEditOpen={isEditOpen}
        setIsEditOpen={setIsEditOpen}
        editFormDetails={editFormDetails}
        description={description}
        setDescription={setDescription}
        selectedConnectFieldOption={selectedConnectFieldOption}
        refetch={refetch}
      />
    </>
  )
}

export default CFNDetails
