import { HeaderMetric } from '@patterninc/react-ui'
import React from 'react'

import styles from '.././mappingsCFN.module.scss'
import { useTranslate } from '../../../../common/helpers/TranslationHelpers'

const CFNDetailsHeaderMetrics = ({
  isLoading,
  summaryHeaderData,
}: {
  isLoading: boolean
  summaryHeaderData: {
    mapped_marketplace_fields_count: number
  }
}): React.JSX.Element => {
  const { t } = useTranslate('mappings')

  return (
    <section className={`${styles.mainBoxMetric} box`}>
      <div className={`flex ${styles.topHeaderMetric}`}>
        <HeaderMetric
          key='mapped_marketplace_fields_count'
          title={t('totalChannelFields')}
          value={summaryHeaderData.mapped_marketplace_fields_count}
          fontSize='fs-22'
          loading={isLoading}
        />
      </div>
    </section>
  )
}

export default CFNDetailsHeaderMetrics
