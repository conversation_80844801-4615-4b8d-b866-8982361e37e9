import React, { use<PERSON><PERSON>back, useEffect, useMemo, useState } from 'react'
import {
  FormFooter,
  notEmpty,
  Select,
  SideDrawer,
  TextInput,
  toast,
} from '@patterninc/react-ui'
import { useMutation } from '@tanstack/react-query'

import { type EditFormDetailsType } from '../../types'
import {
  connectFields,
  connectMappings,
  suggestions,
} from '../../../../common/services/ConnectSyndicationService'
import {
  renderTooltip,
  useOtherOptionsQuery,
  useSuggestionsQuery,
} from '../../SelectHelperFunctions'
import { CreateCFNForm } from '../CreateCFNForm'
import SecureAxios from '../../../../common/services/SecureAxios'
import { PRIVILEGES, useHasPrivilege } from '../../../../context/auth-context'
import styles from '../mappingsCFN.module.scss'
import { c, useTranslate } from '../../../../common/helpers/TranslationHelpers'

type SelectOption = {
  key?: string
  display_name: string
  secondaryContent?: React.ReactNode
}

type EditConnectFieldFormProps = {
  isEditOpen: boolean
  setIsEditOpen: (isOpen: boolean) => void
  editFormDetails: EditFormDetailsType | undefined
  description: string
  setDescription: React.Dispatch<React.SetStateAction<string>>
  selectedConnectFieldOption: {
    key: string | undefined
    display_name: string
    secondaryContent: React.JSX.Element
  }
  refetch: () => void
}

export const EditConnectFieldForm = ({
  isEditOpen,
  setIsEditOpen,
  editFormDetails,
  description,
  setDescription,
  selectedConnectFieldOption,
  refetch,
}: EditConnectFieldFormProps) => {
  const { t } = useTranslate('mappings'),
    [suggestedParams, setSuggestedParams] = React.useState({
      channel_name: '',
      channel_field_key: '',
    }),
    canManageConnectFields = useHasPrivilege(PRIVILEGES.MANAGE_CONNECT_FIELDS),
    [selected, setSelected] = useState({
      key: '',
      display_name: '',
      secondaryContent: <></>,
    }),
    {
      isLoading: isSuggestedLoading,
      data: suggestedOptions,
      refetch: refetchSuggestedOptions,
    } = useSuggestionsQuery(suggestions, suggestedParams, {
      isEditOpen,
    }),
    [searchCFN, setSearchCFN] = useState(''),
    {
      data: otherOptions = [],
      isLoading: isOtherLoading,
      refetch: refetchOtherOptions,
      hasNextPage: hasNextOptionsPage,
      fetchNextPage: fetchNextOptionsPage,
    } = useOtherOptionsQuery(searchCFN, connectFields),
    [isCFNFormOpen, setIsCFNFormOpen] = useState(false),
    onNewCFNCreated = useCallback(
      (cfn: {
        id: string
        connect_field_key: string
        description?: string
      }) => {
        refetchSuggestedOptions()
        refetchOtherOptions()
        setSelected({
          key: cfn.id,
          display_name: cfn.connect_field_key,
          secondaryContent: renderTooltip(cfn.description || ''),
        })
        setDescription(cfn.description || '')
        setIsCFNFormOpen(false)
        toast({
          message: t('connectFieldCreatedSuccessfully', {
            connectFieldKey: cfn.connect_field_key,
          }),
          type: 'success',
          config: {},
        })
      },
      [
        setSelected,
        refetchOtherOptions,
        refetchSuggestedOptions,
        setIsCFNFormOpen,
        setDescription,
        t,
      ],
    ),
    submitForm = useMutation({
      mutationFn: (body: {
        connect_mappings: Record<string, string | null>
      }) => {
        return SecureAxios.post(
          connectMappings(editFormDetails?.channel_display_name || ''),
          body,
        )
      },
      onSuccess: () => {
        refetch()
        toast({
          type: 'success',
          message: t('connectMappingsUpdatedSuccessfully'),
        })
        setIsEditOpen(false)
      },
    }),
    requestBody = useMemo(() => {
      return {
        connect_mappings: {
          [editFormDetails?.id || '']:
            selected.key === 'null' ? null : selected.key,
        },
      }
    }, [editFormDetails, selected]),
    handleSave = useCallback(() => {
      if (selected.key !== '') {
        submitForm.mutate(requestBody)
      }
      setIsEditOpen(false)
    }, [selected.key, submitForm, requestBody, setIsEditOpen])

  useEffect(() => {
    if (isEditOpen) {
      setSuggestedParams({
        channel_name: editFormDetails?.channel_display_name || '',
        channel_field_key: editFormDetails?.channel_field_key || '',
      })
    }
  }, [
    editFormDetails?.channel_display_name,
    editFormDetails?.channel_field_key,
    isEditOpen,
  ])

  return (
    <SideDrawer
      isOpen={isEditOpen}
      closeCallout={() => {
        setIsEditOpen(false)
      }}
      headerContent={t('youreEditing', {
        editingName: editFormDetails?.channel_field_name,
      })}
      footerContent={
        <FormFooter
          cancelButtonProps={{
            onClick: () => {
              setIsEditOpen(false)
              setSelected({
                key: selectedConnectFieldOption?.key || '',
                display_name: selectedConnectFieldOption?.display_name || '',
                secondaryContent:
                  selectedConnectFieldOption.secondaryContent ||
                  renderTooltip(''),
              })
            },
          }}
          saveButtonProps={{
            disabled: false,
            onClick: () => {
              handleSave()
            },
            children: c('saveChanges'),
          }}
        />
      }
    >
      <div className='flex flex-direction-column pat-gap-4'>
        <div className={`box fs-12`}>
          <div className={`pat-mb-4`}>
            <div className='fc-purple pat-mb-1 uppercase'>
              {t('channelFieldName')}
            </div>
            {editFormDetails?.channel_field_name}
          </div>
          <div className='fc-purple pat-mb-1 uppercase'>{t('channel')}</div>
          {editFormDetails?.channel_display_name}
        </div>
        <div className={styles.select}>
          <Select
            scrollProps={{
              hasMore: hasNextOptionsPage,
              getData: fetchNextOptionsPage,
            }}
            labelProps={{ label: t('selectAConnectFieldName') }}
            footerButtonProps={
              canManageConnectFields
                ? {
                    text: c('createNew'),
                    callout: () => {
                      setIsCFNFormOpen(true)
                    },
                  }
                : undefined
            }
            options={[
              {
                key: 'null',
                display_name: t('notMapped'),
                secondaryContent: renderTooltip(t('optionToUnmapTheField')),
              },
              {
                label: t('notApplicable'),
                options:
                  otherOptions?.[0]?.display_name === 'Not Applicable'
                    ? [otherOptions?.[0] || []]
                    : [],
              },
              {
                label: t('suggestedConnectFieldName'),
                options: suggestedOptions || [],
              },
              {
                label: c('others'),
                options: [
                  {
                    key: selectedConnectFieldOption?.key || '',
                    display_name: selectedConnectFieldOption?.display_name,
                    secondaryContent:
                      selectedConnectFieldOption?.secondaryContent,
                  },
                  ...(
                    (notEmpty(searchCFN)
                      ? otherOptions
                      : otherOptions.slice(1)) || []
                  ).filter(
                    (item: SelectOption) =>
                      item.key !== selectedConnectFieldOption?.key,
                  ),
                ],
              },
            ]}
            optionKeyName='key'
            labelKeyName='display_name'
            onChange={(selectedOption) => {
              setDescription(
                selectedOption?.secondaryContent?.props?.tooltipContent,
              )
              setSelected({
                key: selectedOption?.key || '',
                display_name: selectedOption?.label || '',
                secondaryContent:
                  selectedOption.secondaryContent || renderTooltip(''),
              })
            }}
            selectedItem={
              notEmpty(selected.key)
                ? selected
                : {
                    key: selectedConnectFieldOption.key || '',
                    display_name: selectedConnectFieldOption?.display_name,
                    secondaryContent:
                      selectedConnectFieldOption.secondaryContent,
                  }
            }
            searchBarProps={{
              showSearchBar: true,
              onChange(value) {
                setSearchCFN(value)
              },
            }}
            loading={isOtherLoading || isSuggestedLoading}
          />
        </div>
        <CreateCFNForm
          isOpen={isCFNFormOpen}
          setIsOpen={setIsCFNFormOpen}
          onItemCreated={onNewCFNCreated}
          layerPosition={2}
        />

        <TextInput
          id='textarea'
          disabled={true}
          labelText={t('fieldDescriptionOptional')}
          type='textarea'
          value={description}
        />
      </div>
    </SideDrawer>
  )
}
