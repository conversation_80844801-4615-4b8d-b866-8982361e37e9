import React from 'react'
import {
  Button,
  type ConfigItemType,
  Mdash<PERSON>heck,
  notEmpty,
} from '@patterninc/react-ui'
import moment from 'moment'

import { tableTimeFormat } from '../../../../common/constants'
import { type CFNDetailsType, type EditFormDetailsType } from '../../types'
import { c, t } from '../../../../common/helpers/TranslationHelpers'

export const cfnDetailsTableConfig = (
  setIsEditOpen: React.Dispatch<React.SetStateAction<boolean>>,
  setEditFormDetails: React.Dispatch<
    React.SetStateAction<EditFormDetailsType | undefined>
  >,
  canManageConnectMappings: boolean,
): ConfigItemType<CFNDetailsType, Record<string, unknown>>[] => {
  return [
    {
      label: t('mappings:channelFieldName'),
      name: 'display_name',
      mainColumn: true,
      cell: {
        children: (d): React.JSX.Element => {
          return (
            <span
              className={d.sortProp === 'display_name' ? 'fw-semi-bold' : ''}
            >
              {notEmpty(d.display_name) ? d.display_name : d.channel_field_key}
            </span>
          )
        },
      },
    },
    {
      label: t('mappings:channel'),
      name: 'channel',
      cell: {
        children: (d): React.JSX.Element => {
          return (
            <span>
              {/* Remove the following conditional check once display_name is available in db */}
              {notEmpty(d.channel_display_name)
                ? d.channel_display_name
                : d.channel_name}
            </span>
          )
        },
      },
      noSort: true,
    },
    {
      label: c('updatedBy'),
      name: 'updated_by',
      cell: {
        children: (d): React.JSX.Element => {
          return (
            <>
              <MdashCheck check={notEmpty(d.updated_by)}>
                <span
                  className={d.sortProp === 'updated_by' ? 'fw-semi-bold' : ''}
                >
                  {d.updated_by}
                </span>
              </MdashCheck>
            </>
          )
        },
      },
    },
    {
      label: c('updatedOn'),
      name: 'updated_at',
      cell: {
        children: (d): React.JSX.Element => {
          return (
            <>
              <span
                className={d.sortProp === 'updated_at' ? 'fw-semi-bold' : ''}
              >
                {moment(d.updated_at).format(tableTimeFormat)}
              </span>
            </>
          )
        },
      },
    },
    {
      label: '',
      name: 'edit',
      noSort: true,
      isButton: true,
      cell: {
        children: (d): React.JSX.Element => {
          return canManageConnectMappings ? (
            <Button
              onClick={() => {
                setIsEditOpen(true)
                setEditFormDetails({
                  channel_field_key: d.channel_field_key,
                  channel_field_name: notEmpty(d.display_name)
                    ? d.display_name
                    : d.channel_field_key,
                  channel_display_name: notEmpty(d.channel_display_name)
                    ? d.channel_display_name
                    : d.channel_name,
                  id: d.id,
                })
              }}
            >
              {c('edit')}
            </Button>
          ) : (
            <></>
          )
        },
      },
    },
  ]
}
