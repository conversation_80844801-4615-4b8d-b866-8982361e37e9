import { HeaderMetric } from '@patterninc/react-ui'
import React from 'react'

import styles from './mappingsCFN.module.scss'
import { type CFNHeaderSummaryData } from '../types'
import { useTranslate } from '../../../common/helpers/TranslationHelpers'

const MappingsCFNHeaderMetrics = ({
  summaryHeaderData,
  isLoading,
}: {
  summaryHeaderData: CFNHeaderSummaryData
  isLoading: boolean
}): React.JSX.Element => {
  const { t } = useTranslate('mappings')

  return (
    <section className={`${styles.mainBoxMetric} box`}>
      <div className={`flex ${styles.topHeaderMetric}`}>
        <HeaderMetric
          key='unmapped_fields'
          title={t('unmappedFields')}
          value={summaryHeaderData.unmapped_fields}
          formatType='number'
          fontSize='fs-22'
          loading={isLoading}
        />
        <div className='flex pat-pr-4'>
          <div className={`pat-my-4 bgc-light-gray ${styles.divider}`} />
        </div>
        <HeaderMetric
          key='total_fields'
          title={t('totalFields')}
          value={summaryHeaderData.total_fields}
          formatType='number'
          fontSize='fs-22'
          loading={isLoading}
        />
      </div>
    </section>
  )
}

export default MappingsCFNHeaderMetrics
