import React, { useContext, useMemo, useState } from 'react'
import { SearchBar, StandardTable } from '@patterninc/react-ui'
import { useParams } from 'react-router-dom'
import { useInfiniteQuery } from '@tanstack/react-query'

import MappingsCFNHeaderMetrics from './MappingsCFNHeaderMetrics'
import { type CFNResponseType, type CFNType } from '../types'
import { mappingsCFNTableConfig } from './mappings-cfn-table-config'
import { connectFields } from '../../../common/services/ConnectSyndicationService'
import SecureAxios from '../../../common/services/SecureAxios'
import { MappingsFilterContext } from '../../../context/mappings-filter-context'
import { useTranslate } from '../../../common/helpers/TranslationHelpers'

const MappingsConnectFieldNamesTab = (): React.JSX.Element => {
  const { t } = useTranslate('mappings'),
    { id } = useParams<string>(),
    { cfnSearchQuery, setCfnSearchQuery } = useContext(MappingsFilterContext),
    [sort, setSort] = useState<{ prop: string; flip: boolean }>({
      prop: 'connect_field_key',
      flip: true,
    }),
    params = useMemo(() => {
      const sortMap: Record<string, string> = {
        connect_field_key: 'connect_field_key',
        mapped_marketplace_fields_count: 'mapped_marketplace_fields_count',
      }
      return {
        sort_by: sortMap[sort.prop],
        order: sort.flip ? 'asc' : 'desc',
        search: cfnSearchQuery,
      }
    }, [sort, cfnSearchQuery]),
    { status, data, isLoading, fetchNextPage, hasNextPage } = useInfiniteQuery({
      queryKey: ['cfn_api', connectFields, id, cfnSearchQuery, params],
      queryFn: ({ pageParam = 1, signal }) => {
        return SecureAxios.get<CFNResponseType>(connectFields, {
          signal,
          params: {
            ...params,
            details: true,
            summary: true,
            page: pageParam ?? 1,
          },
        })
      },
      initialPageParam: 1,
      getNextPageParam: (previousPage) => {
        return previousPage &&
          previousPage.headers['current-page'] !==
            previousPage.headers['total-pages']
          ? Number(previousPage.headers['current-page']) + 1
          : undefined
      },
    }),
    tableData: CFNType[] = useMemo(() => {
      return data
        ? data?.pages?.flatMap((page) => page?.data.connect_fields)
        : []
    }, [data])

  return (
    <>
      <MappingsCFNHeaderMetrics
        summaryHeaderData={{
          unmapped_fields: data?.pages[0].data.unmapped_connect_fields ?? 0,
          total_fields: data?.pages[0].data.total_connect_fields ?? 0,
        }}
        isLoading={isLoading}
      />
      <div className='flex pat-my-4'>
        <SearchBar
          value={cfnSearchQuery}
          onChange={setCfnSearchQuery}
          placeholder={t('searchFields')}
        />
      </div>
      <StandardTable
        config={mappingsCFNTableConfig()}
        data={tableData}
        dataKey='connect_field_key'
        getData={fetchNextPage}
        hasData={status === 'success' && tableData.length > 0}
        noDataFields={{
          primaryText: t('noConnectFieldNamesAvailable'),
        }}
        successStatus={status === 'success'}
        tableId='cfn_table_id'
        hasMore={!!(status === 'success' && hasNextPage)}
        loading={isLoading}
        sort={({ activeColumn: prop, direction: flip }) => {
          setSort({ prop, flip })
        }}
        sortBy={sort}
      />
    </>
  )
}

export default MappingsConnectFieldNamesTab
