import React from 'react'
import { Button, type ConfigItemType } from '@patterninc/react-ui'
import { Link } from 'react-router-dom'

import { type CFNType } from '../types'
import styles from './mappingsCFN.module.scss'
import { c, t } from '../../../common/helpers/TranslationHelpers'

export const mappingsCFNTableConfig = (): ConfigItemType<
    CFNType,
    Record<string, unknown>
  >[] => {
    return [
      {
        label: t('mappings:connectFieldName'),
        name: 'connect_field_key',
        mainColumn: true,
        cell: {
          className: getClassName,
          children: (d): React.JSX.Element => {
            return (
              <span
                className={
                  d.sortProp === 'connect_field_key' ? 'fw-semi-bold' : ''
                }
              >
                {d.connect_field_key}
              </span>
            )
          },
        },
      },
      {
        label: t('mappings:connectedChannelFields'),
        name: 'mapped_marketplace_fields_count',
        cell: {
          className: getClassName,
          children: (d): React.JSX.Element => {
            return (
              <span
                className={
                  d.sortProp === 'mapped_marketplace_fields_count'
                    ? 'fw-semi-bold'
                    : ''
                }
              >
                {d.mapped_marketplace_fields_count}
              </span>
            )
          },
        },
      },
      {
        label: '',
        name: 'view_details',
        noSort: true,
        isButton: true,
        cell: {
          className: getClassName,
          children: (d): React.JSX.Element => {
            return (
              <Button
                as='link'
                state={{
                  connect_field_key: d.connect_field_key,
                  description: d.description,
                }}
                to={d.id}
                routerComponent={Link}
              >
                {c('viewDetails')}
              </Button>
            )
          },
        },
      },
    ]
  },
  getClassName = (d: CFNType): string => {
    return d.connect_field_key === 'Not Applicable'
      ? styles.notApplicableBgc
      : ''
  }
