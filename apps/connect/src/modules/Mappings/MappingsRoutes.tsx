import React from 'react'
import { Route, Routes } from 'react-router-dom'

import PrivateRoute from '../../common/components/PrivateRoute/PrivateRoute'
import Mappings from './Mappings'
import MappingsChannelDetails from './MappingsChannels/MappingsChannelDetails/MappingsChannelDetails'
import CFNDetails from './MappingsConnectFieldNames/MappingsCFNDetails/CFNDetails'
import { MappingsFilterProvider } from '../../context/mappings-filter-context'

const MappingsRoutes = (): React.JSX.Element => {
  return (
    <MappingsFilterProvider>
      <Routes>
        <Route
          path='/*'
          element={
            <PrivateRoute>
              <Mappings />
            </PrivateRoute>
          }
        />
        <Route path='channels/:id' element={<MappingsChannelDetails />} />
        <Route path='connect_field_names/:id' element={<CFNDetails />} />
      </Routes>
    </MappingsFilterProvider>
  )
}

export default MappingsRoutes
