import { useInfiniteQuery, useQuery } from '@tanstack/react-query'
import { notEmpty } from '@patterninc/react-ui'
import React, { useMemo } from 'react'

import {
  type ConnectField,
  type OtherOptionsType,
  type SuggestedParamsType,
} from './MappingsChannels/MappingsChannelDetails/MappingsChannelDetails'
import SecureAxios from '../../common/services/SecureAxios'
import { c } from '../../common/helpers/TranslationHelpers'
import CustomInfoTooltip from './MappingsConnectFieldNames/CustomInfoTooltip'

const transformOptions = (data: ConnectField[]): OtherOptionsType[] =>
  data.map((item) => ({
    key: item.id,
    display_name: item.connect_field_key,
    secondaryContent: renderTooltip(
      item.description || c('noDescriptionAvailable'),
    ),
  }))

/**
 * A custom React hook that fetches suggested connect field names  based on the channel name and channel field key.
 */
export const useSuggestionsQuery = (
  suggestions: string,
  suggestedParams: SuggestedParamsType | undefined,
  options: {
    fetchTrigger?: boolean
    onSuccess?: () => void
    isEditOpen?: boolean
  } = {},
) => {
  const { fetchTrigger, isEditOpen } = options

  return useQuery({
    queryKey: ['suggestions_api', suggestions, suggestedParams, isEditOpen],
    queryFn: () =>
      SecureAxios.get(suggestions, {
        params: {
          ...suggestedParams,
        },
      }).then((result) => transformOptions(result.data)),
    enabled:
      fetchTrigger !== undefined
        ? fetchTrigger
        : isEditOpen && notEmpty(suggestedParams?.channel_name),
  })
}

export const useOtherOptionsQuery = (
  searchCFN: string,
  connectFields: string,
) => {
  const { data, status, fetchNextPage, hasNextPage, isLoading, refetch } =
      useInfiniteQuery({
        queryKey: ['other_optioins_api', searchCFN, connectFields],
        queryFn: async ({ pageParam = 1 }) => {
          return SecureAxios.get(`${connectFields}`, {
            params: {
              per_page: 100,
              page: pageParam,
              search: searchCFN,
            },
          })
        },
        initialPageParam: 1,
        getNextPageParam: (previousPage) => {
          if (
            previousPage.headers['current-page'] !==
            previousPage.headers['total-pages']
          ) {
            return Number(previousPage.headers['current-page']) + 1
          }
          return undefined
        },
      }),
    formattedData = useMemo(() => {
      return transformOptions(
        data?.pages.flatMap((page) => page.data.connect_fields) || [],
      )
    }, [data])
  return {
    data: formattedData,
    status,
    fetchNextPage,
    hasNextPage,
    isLoading,
    refetch,
  }
}

export const renderTooltip = (content: string): React.JSX.Element => {
  const tooltipContent = notEmpty(content)
    ? content
    : c('noDescriptionAvailable')

  return <CustomInfoTooltip tooltipContent={tooltipContent} />
}
