export type HeaderSummaryData = {
  unmapped_channels: number
  unmapped_fields: number
}

export type ChannelDetailsHeaderSummaryData = {
  field_count: number
  connect_field_unmapped_count: number
  connect_field_mapped_count: number
}

export type CFNHeaderSummaryData = {
  unmapped_fields: number
  total_fields: number
}

export type CFNType = {
  id: string
  connect_field_key: string
  description?: string | null
  mapped_marketplace_fields_count: number
  sortProp?: string
}

export type CFNResponseType = {
  connect_fields: CFNType
  total_connect_fields: number
  unmapped_connect_fields: number
}

export type CFNDetailsType = {
  id: string
  channel_field_key: string
  display_name: string
  channel_name: string
  channel_display_name: string
  updated_at: string
  updated_by: string
  sortProp?: string
}

export type EditFormDetailsType = {
  channel_field_key: string
  channel_field_name: string
  channel_display_name: string
  id: string
}
