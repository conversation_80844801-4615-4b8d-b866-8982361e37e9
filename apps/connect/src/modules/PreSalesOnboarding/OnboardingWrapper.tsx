import { APP_LOGOS } from '@patterninc/react-ui'

import styles from './_onboarding.module.scss'

const OnboardingWrapper = ({
  children,
}: {
  children: React.ReactNode
}): React.JSX.Element => {
  return (
    <div className={`${styles.fullHeight} flex justify-content-center`}>
      <div className={`${styles.maxWidth} flex flex-direction-column `}>
        <div className={`${styles.logoContainer} pat-pt-16 pat-pb-8`}>
          <img src={APP_LOGOS.PREDICT.logo} alt='logo' />
        </div>
        <div className='bdrr-16 bgc-white bdr bdrc-medium-purple pat-p-8'>
          {children}
        </div>
      </div>
    </div>
  )
}

export default OnboardingWrapper
