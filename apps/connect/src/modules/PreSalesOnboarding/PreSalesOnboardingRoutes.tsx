import { Route, Routes } from 'react-router-dom'

import Success from './Success'
import Terms from './Terms'
import Error from './Error'
import OnboardingWrapper from './OnboardingWrapper'

const PreSalesOnboardingRoutes = (): React.JSX.Element => {
  return (
    <Routes>
      <Route
        path='/onboarding/terms'
        element={
          <OnboardingWrapper>
            <Terms />
          </OnboardingWrapper>
        }
      />
      <Route
        path='/onboarding/success'
        element={
          <OnboardingWrapper>
            <Success />
          </OnboardingWrapper>
        }
      />
      <Route
        path='/onboarding/error'
        element={
          <OnboardingWrapper>
            <Error />
          </OnboardingWrapper>
        }
      />
    </Routes>
  )
}

export default PreSalesOnboardingRoutes
