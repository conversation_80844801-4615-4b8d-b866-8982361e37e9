import { Button, Checkbox } from '@patterninc/react-ui'
import React, { useMemo } from 'react'
import { useMutation } from '@tanstack/react-query'
import { useNavigate } from 'react-router-dom'

import { t } from '../../common/helpers/TranslationHelpers'
import SecureAxios from '../../common/services/SecureAxios'
import { authorizeByNonce } from '../../common/services/ConnectAuthService'

const Terms = (): React.JSX.Element => {
  const [termsAccepted, setTermsAccepted] = React.useState(false),
    navigate = useNavigate(),
    marketplaceName = useMemo(() => {
      const queryParams = new URLSearchParams(window.location.search)
      return queryParams.get('marketplace') || 'Amazon'
    }, []),
    nonce = useMemo(() => {
      const queryParams = new URLSearchParams(window.location.search)
      return queryParams.get('nonce')
    }, []),
    authorizeAccess = useMutation({
      mutationFn: (nonce: string) => {
        return SecureAxios.get(authorizeByNonce, {
          params: {
            nonce,
          },
        })
      },
      onSuccess: (result) => {
        window.location.href = result.data.auth_uri
      },
      onError: () => {
        navigate('/onboarding/error')
      },
    })
  return (
    <>
      <p className='fs-22 fw-semi-bold pat-mt-0 fc-dark-blue'>
        {t('preSalesOnboarding:terms.title', { marketplaceName })}
      </p>
      <p className='fs-16'>
        {t('preSalesOnboarding:terms.previewData', { marketplaceName })}
      </p>
      <p className='fs-16'>{t('preSalesOnboarding:terms.readAndAccept')}</p>
      <div className='bgc-faint-gray bdrr-8 bdr bdrc-medium-purple pat-p-4 pat-mt-2 flex pat-gap-2 align-items-flex-start pat-mb-8'>
        <Checkbox
          label=''
          hideLabel
          checked={termsAccepted}
          callout={(_, value) => setTermsAccepted(value)}
        />
        <div>
          <p className='fs-14 pat-mt-0'>
            {t('preSalesOnboarding:terms.agreeTerms')}
          </p>
          <br />
          <p>
            {t('preSalesOnboarding:terms.privacyPolicy')}{' '}
            <Button
              as='externalLink'
              styleType='text-blue'
              href='https://pattern.com/privacy-policy'
            >
              {t('preSalesOnboarding:terms.privacyPolicyLink')}
            </Button>
          </p>
        </div>
      </div>
      <p className='fs-18'>{t('preSalesOnboarding:terms.accountOwner')}</p>
      <Button
        disabled={!termsAccepted}
        as='button'
        styleType='primary-blue'
        className='pat-mt-4'
        onClick={() => {
          authorizeAccess.mutate(nonce || '')
        }}
      >
        {t('preSalesOnboarding:terms.authorizeAccess')}
      </Button>
    </>
  )
}

export default Terms
