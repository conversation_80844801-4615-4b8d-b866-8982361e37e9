.fullHeight {
  min-height: 100vh;
  overflow-y: scroll;
  margin-top: auto;
  margin-bottom: auto;
  padding-top: 1rem;
  background-image: url('https://images.pattern.com/login_background_light_compressed.jpg');
  background-size: cover;
  background-position: center;

  @media (max-width: 600px) {
    padding-top: 0;
  }
}

.maxWidth {
  // this is the ratio in the design file for this feature
  width: 53vw;
  height: 100%;

  @media (max-width: 900px) {
    width: 70vw;
  }

  @media (max-width: 600px) {
    width: 90vw;
  }
}

.logoContainer {
  max-height: 40px;

  img {
    max-height: 40px;
  }
}
