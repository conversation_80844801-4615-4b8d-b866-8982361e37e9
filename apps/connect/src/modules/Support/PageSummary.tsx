import React, { useCallback, useMemo } from 'react'
import { useNavigate } from 'react-router-dom'
import { Button } from '@patterninc/react-ui'

import { type SupportPageType } from './types'
import { useTranslate } from '../../common/helpers/TranslationHelpers'

interface PageSummaryProps {
  page: SupportPageType
}

const PageSummary = ({ page }: PageSummaryProps): React.JSX.Element => {
  const { t } = useTranslate('support')

  const summaryText = useMemo(() => {
      const summaryTexts: string[] = []
      const characterLimit = 100
      for (const component of page.attributes.Content) {
        if (component.__component === 'content.content-text') {
          for (const block of component.text) {
            if (block.type === 'text') {
              summaryTexts.push(block.text)
            } else if (block.type === 'paragraph') {
              for (const textBlock of block.children) {
                if (textBlock.type === 'text') {
                  summaryTexts.push(textBlock.text)
                }
              }
            } else if (block.type === 'list') {
              for (const listItem of block.children) {
                for (const textBlock of listItem.children) {
                  summaryTexts.push(textBlock.text)
                }
              }
            }
          }
        }
      }
      const summaryTextsFinal = []
      let characterCount = 0
      while (characterCount < characterLimit && summaryTexts.length > 0) {
        const nextText = summaryTexts.shift() || ''
        if (characterCount + nextText.length < characterLimit) {
          characterCount += nextText.length
          summaryTextsFinal.push(nextText)
        } else {
          const remainingCharacters = characterLimit - characterCount
          summaryTextsFinal.push(nextText.substring(0, remainingCharacters))
          characterCount = characterLimit
        }
      }
      return summaryTextsFinal
    }, [page.attributes.Content]),
    daysAgo = useMemo(() => {
      const updatedAt = new Date(page.attributes.updatedAt),
        now = new Date(),
        diff = now.getTime() - updatedAt.getTime(),
        days = Math.floor(diff / (1000 * 60 * 60 * 24))
      return days
    }, [page.attributes.updatedAt]),
    navigate = useNavigate(),
    navigateToPage = useCallback(() => {
      return () => {
        navigate(
          `/support/${page.attributes.Type.toLowerCase().replace(/ /g, '_')}/${page.id}`,
        )
      }
    }, [navigate, page.attributes.Type, page.id])

  return (
    <div className='bdrb bdrc-light-gray pat-py-4' onClick={navigateToPage()}>
      <div className='flex justify-content-between'>
        <p className='fs-18 fw-bold pat-my-0'>{page.attributes.Title}</p>
        <span className='fc-purple fs-14'>{daysAgo} Days Ago</span>
      </div>
      {summaryText.map((text, index) => {
        if (index === summaryText.length - 1)
          return <p key={index}>{text}...</p>
        return <p key={index}>{text}</p>
      })}
      <div className='flex justify-content-end'>
        <Button
          styleType='text-blue'
          onClick={() => {
            // TODO: Implement navigateToPage
          }}
        >
          {t('readMore')}
        </Button>
      </div>
    </div>
  )
}

export default PageSummary
