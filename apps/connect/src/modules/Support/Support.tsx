import React, { useContext, useEffect } from 'react'
import {
  Navigate,
  Route,
  Routes,
  useLocation,
  useNavigate,
} from 'react-router-dom'
import { RouterTabs } from '@patterninc/react-ui'
import { NavLink } from 'react-router-dom'

import { ThemeContext } from '../../Context'
import SupportCategory from './SupportCategory'
import { useTranslate } from '../../common/helpers/TranslationHelpers'

const Support = (): React.JSX.Element => {
  const { t } = useTranslate('support'),
    { updateBreadcrumbs } = useContext(ThemeContext),
    { pathname } = useLocation(),
    navigate = useNavigate(),
    mobileTabs = [
      { label: t('userGuides'), link: 'user_guides' },
      { label: t('faqs'), link: 'faq' },
      { label: t('releaseNotes'), link: 'release_notes' },
    ]

  useEffect(() => {
    updateBreadcrumbs({
      name: t('support'),
      link: pathname,
      changeType: 'rootLevel',
    })
  }, [pathname, updateBreadcrumbs, t])

  return (
    <>
      <RouterTabs
        mobileConfig={mobileTabs}
        navigate={navigate}
        currentPath={pathname}
      >
        <NavLink to='/support/user_guides'>{t('userGuides')}</NavLink>
        <NavLink to='/support/faq'>{t('faqs')}</NavLink>
        <NavLink to='/support/release_notes'>{t('releaseNotes')}</NavLink>
      </RouterTabs>

      <Routes>
        <Route path='/' element={<Navigate to='user_guides' replace />} />
        <Route
          path='user_guides'
          element={<SupportCategory category='User Guide' />}
        />
        <Route path='faq' element={<SupportCategory category='FAQ' />} />
        <Route
          path='release_notes'
          element={<SupportCategory category='Release' />}
        />
      </Routes>
    </>
  )
}

export default Support
