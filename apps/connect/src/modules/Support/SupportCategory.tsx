import React, { useMemo, useState } from 'react'
import { useQuery } from '@tanstack/react-query'
import { ListLoading, SearchBar } from '@patterninc/react-ui'

import { type supportCategory, type SupportPagesResponse } from './types'
import {
  populateParams,
  StrapiAxios,
  supportCategoryParams,
  supportPages,
} from '../../common/services/CmsService'
import PageSummary from './PageSummary'
import { c } from '../../common/helpers/TranslationHelpers'

interface SupportCategoryProps {
  category: supportCategory
}

const SupportCategory = ({
  category,
}: SupportCategoryProps): React.JSX.Element => {
  const [search, setSearch] = useState(''),
    { data: data, status } = useQuery<SupportPagesResponse>({
      queryKey: ['supportPages', category, search],
      queryFn: async () => {
        const response = await StrapiAxios.get(supportPages, {
          params: {
            ...supportCategoryParams(category),
            ...populateParams,
            'filters[Title][$containsi]': search,
          },
        })
        return response.data
      },
    }),
    pages = useMemo(() => {
      return data ? data.data : []
    }, [data])
  return (
    <>
      <SearchBar
        value={search}
        onChange={setSearch}
        placeholder={c('search')}
      />
      {status === 'pending' ? (
        <ListLoading />
      ) : (
        pages?.map((page) => <PageSummary key={page.id} page={page} />)
      )}
    </>
  )
}

export default SupportCategory
