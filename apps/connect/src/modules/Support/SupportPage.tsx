import React, { useMemo } from 'react'
import { useQuery } from '@tanstack/react-query'
import { useParams } from 'react-router-dom'
import { ListLoading } from '@patterninc/react-ui'

import { type SupportPageType } from './types'
import {
  populateParams,
  StrapiAxios,
  supportPageApi,
} from '../../common/services/CmsService'
import Image from './blocks/Image'
import List from './blocks/List'
import Paragraph from './blocks/Paragraph'
import Heading from './blocks/Heading'
import Quote from './blocks/Quote'
import Code from './blocks/Code'

const SupportPage = (): React.JSX.Element => {
  const { id } = useParams<{ id: string }>(),
    { data: page, status } = useQuery<SupportPageType>({
      queryKey: ['supportPage', id],
      queryFn: async () => {
        return StrapiAxios.get(supportPageApi(id || ''), {
          params: populateParams,
        }).then((res) => res.data.data)
      },
    }),
    content = useMemo(() => {
      return page?.attributes?.Content || []
    }, [page])
  return (
    <>
      {status === 'pending' ? (
        <ListLoading />
      ) : (
        <>
          <h1>{page?.attributes?.Title}</h1>
          {content.map((component) => {
            if (component.__component === 'content.content-text') {
              return component.text.map((block, index) => {
                switch (block.type) {
                  case 'paragraph':
                    return <Paragraph key={index} paragraph={block} />
                  case 'list':
                    return <List key={index} list={block} />
                  case 'heading':
                    return <Heading key={index} heading={block} />
                  case 'quote':
                    return <Quote key={index} quote={block} />
                  case 'code':
                    return <Code key={index} code={block} />
                  default:
                    return null
                }
              })
            } else if (component.__component === 'content.image') {
              return <Image key={component.id} image={component} />
            }
          })}
        </>
      )}
    </>
  )
}

export default SupportPage
