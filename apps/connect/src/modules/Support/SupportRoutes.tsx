import React from 'react'
import { Route, Routes } from 'react-router-dom'

import PrivateRoute from '../../common/components/PrivateRoute/PrivateRoute'
import Support from './Support'
import SupportPage from './SupportPage'

const MappingsRoutes = (): React.JSX.Element => {
  return (
    <Routes>
      <Route
        path='/*'
        element={
          <PrivateRoute>
            <Support />
          </PrivateRoute>
        }
      />
      <Route path='user_guide/:id' element={<SupportPage />} />
      <Route path='faq/:id' element={<SupportPage />} />
      <Route path='release/:id' element={<SupportPage />} />
    </Routes>
  )
}

export default MappingsRoutes
