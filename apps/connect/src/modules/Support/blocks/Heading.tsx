import React from 'react'

import { type HeadingBlock } from '../types'
import Text from './Text'

type HeadingProps = {
  heading: HeadingBlock
}

const Heading = ({ heading }: HeadingProps): React.JSX.Element => {
  switch (heading.level) {
    case 1:
      return (
        <h1>
          <Text text={heading.children[0]} />
        </h1>
      )
    case 2:
      return (
        <h2>
          <Text text={heading.children[0]} />
        </h2>
      )
    case 3:
      return (
        <h3>
          <Text text={heading.children[0]} />
        </h3>
      )
    case 4:
      return (
        <h4>
          <Text text={heading.children[0]} />
        </h4>
      )
    case 5:
      return (
        <h5>
          <Text text={heading.children[0]} />
        </h5>
      )
    case 6:
      return (
        <h6>
          <Text text={heading.children[0]} />
        </h6>
      )
  }
}

export default Heading
