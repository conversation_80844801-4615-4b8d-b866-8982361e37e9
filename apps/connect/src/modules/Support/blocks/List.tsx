import React from 'react'

import { type ListBlock } from '../types'

type ListProps = {
  list: ListBlock
}

const List = ({ list }: ListProps): React.JSX.Element => {
  if (list.format === 'ordered') {
    return (
      <>
        <ol>
          {list.children.map((child, index) => {
            return <li key={index}>{child.children[0].text}</li>
          })}
        </ol>
      </>
    )
  }
  return (
    <>
      <ul>
        {list.children.map((child, index) => {
          return <li key={index}>{child.children[0].text}</li>
        })}
      </ul>
    </>
  )
}

export default List
