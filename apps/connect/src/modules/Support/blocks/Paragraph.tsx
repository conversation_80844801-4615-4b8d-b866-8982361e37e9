import React from 'react'
import { But<PERSON> } from '@patterninc/react-ui'
import { Link } from 'react-router-dom'

import { type ParagraphBlock } from '../types'
import Text from './Text'

type ParagraphProps = {
  paragraph: ParagraphBlock
}

const Paragraph = ({ paragraph }: ParagraphProps): React.JSX.Element => {
  return (
    <p>
      {paragraph.children.map((child, index) => {
        if (child.type === 'link') {
          return (
            <Button
              as='link'
              styleType='text-blue'
              key={`paragraph-link-${index}`}
              to={child.url}
              routerComponent={Link}
            >
              <Text text={child.children[0]} />
            </Button>
          )
        }
        return <Text key={`paragraph-text-${index}`} text={child} />
      })}
    </p>
  )
}

export default Paragraph
