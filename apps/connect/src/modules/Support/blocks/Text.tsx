import React, { useMemo } from 'react'

import { type TextBlock } from '../types'

type TextProps = {
  text: TextBlock
}

const Text = ({ text }: TextProps): React.JSX.Element => {
  const formattedContent = useMemo(() => {
    let currentContent = <>{text.text}</>
    if (text.bold) {
      currentContent = <strong>{currentContent}</strong>
    }
    if (text.italic) {
      currentContent = <em>{currentContent}</em>
    }
    if (text.underline) {
      currentContent = <u>{currentContent}</u>
    }
    if (text.code) {
      currentContent = <code>{currentContent}</code>
    }
    if (text.strikethrough) {
      currentContent = <del>{currentContent}</del>
    }

    return currentContent
  }, [text])
  return <span>{formattedContent}</span>
}

export default Text
