export type supportCategory = 'User Guide' | 'FAQ' | 'Release'
type SupportPageAttributes = {
  Title: string
  Type: string
  createdAt: string
  updatedAt: string
  publishedAt: string
  Content: (SupportPageComponentImage | SupportPageComponentText)[]
}
type SupportPageComponent = {
  id: number
  __component: 'content.content-text' | 'content.image'
}
type SupportContentBlock = {
  type:
    | 'paragraph'
    | 'list'
    | 'list-item'
    | 'text'
    | 'heading'
    | 'quote'
    | 'code'
    | 'link'
}
export interface HeadingBlock extends SupportContentBlock {
  type: 'heading'
  level: 1 | 2 | 3 | 4 | 5 | 6
  children: TextBlock[]
}
export interface QuoteBlock extends SupportContentBlock {
  type: 'quote'
  children: TextBlock[]
}
export interface CodeBlock extends SupportContentBlock {
  type: 'code'
  children: TextBlock[]
}
export interface TextBlock extends SupportContentBlock {
  type: 'text'
  text: string
  bold?: boolean
  italic?: boolean
  underline?: boolean
  strikethrough?: boolean
  code?: boolean
}
export interface LinkBlock extends SupportContentBlock {
  type: 'link'
  children: TextBlock[]
  url: string
}
export interface ParagraphBlock extends SupportContentBlock {
  type: 'paragraph'
  children: (TextBlock | LinkBlock)[]
}
export interface ListItemBlock extends SupportContentBlock {
  type: 'list-item'
  children: TextBlock[]
}
export interface ListBlock extends SupportContentBlock {
  type: 'list'
  format: 'ordered' | 'unordered'
  children: ListItemBlock[]
}
export interface SupportPageComponentImage extends SupportPageComponent {
  __component: 'content.image'
  alt_text: string
  image: {
    data: {
      attributes: {
        url: string
      }
    }
  }
}
export interface SupportPageComponentText extends SupportPageComponent {
  __component: 'content.content-text'
  text: (
    | TextBlock
    | ListBlock
    | ParagraphBlock
    | HeadingBlock
    | QuoteBlock
    | CodeBlock
  )[]
}
export type SupportPageType = {
  id: number
  attributes: SupportPageAttributes
}
export type SupportPagesResponse = {
  data: SupportPageType[]
  meta: {
    pagination: {
      page: number
      total: number
      pageSize: number
      pageCount: number
    }
  }
}
