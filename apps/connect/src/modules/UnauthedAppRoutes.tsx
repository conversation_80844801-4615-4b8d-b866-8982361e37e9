import { Navigate, Route, Routes } from 'react-router-dom'

import PreSalesOnboardingRoutes from './PreSalesOnboarding/PreSalesOnboardingRoutes'

const AppRoutes = (): React.JSX.Element => {
  return (
    <Routes>
      <Route path='/'>
        <Route path='onboarding/*' element={<PreSalesOnboardingRoutes />} />
      </Route>
      {/* Redirect to the home page when a route does not exist. */}
      <Route path='*' element={<Navigate to='/connections' replace />} />
    </Routes>
  )
}

export default AppRoutes
