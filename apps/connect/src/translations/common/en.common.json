{"activate": "Activate", "active": "Active", "advertising": "Advertising", "all": "All", "areYouSureYouWantToDeleteThisSetting": "Are you sure you want to delete this setting?", "callbackURL": "Callback URL", "cancel": "Cancel", "completed": "Completed", "completedAt": "Completed At", "completedOn": "Completed On", "connectedOn": "Connected On", "connection": "Connection", "connections": "Connections", "count": "Count", "created": "Created", "createdAt": "Created At", "createdBy": "Created By", "createNew": "Create New", "createNewSetting": "Create New Setting", "day_one": "{{count}} Day", "day_other": "{{count}} Days", "deactivate": "Deactivate", "delete": "Delete", "deleteSetting?": "Delete Setting?", "description": "Description", "destinations": "Destinations", "details": "Details", "download": "Download", "downloadCSV": "Download CSV", "downloadJSON": "Download JSON", "edit": "Edit", "editSetting": "Edit Setting", "endpoints": "Endpoints", "errorMessage": "Error: {{message}}", "expiration": "Expiration", "failed": "Failed", "history": "History", "hr": "Hr", "inactive": "Inactive", "invalidJSON": "Invalid JSON", "inventory": "Inventory", "iUnderstandDelete": "I understand, delete", "jobs": "Jobs", "lastUpdatedBy": "Last Updated By", "marketplace": "Marketplace", "marketplace(s)": "Marketplace(s)", "marketplaces": "Marketplaces", "max": "Max", "min": "Min", "missingCredentialIdInPath": "Missing Credential Id in Path", "name": "Name", "next": "Next", "noDescriptionAvailable": "No description available", "notes": "Notes", "orders": "Orders", "others": "Others", "partiallyFailed": "Partially Failed", "percentage": "Percentage", "queryPath": "Query Path", "queryString": "Query String", "queued": "Queued", "redirectingToPreviousPage": "Redirecting to previous page", "running": "Running", "save": "Save", "saveChanges": "Save Changes", "saveChangesAndExit": "Save Changes & Exit", "scheduledFor": "Scheduled For {{scheduleAt}}", "search": "Search", "selectConnections": "Select Connections", "selectEndpoints": "Select Endpoints", "selectMarketplaces": "Select Marketplaces", "selectOption": "Select Option", "selectOptions": "Select Options", "setting": "Setting", "settingDeleted": "Setting deleted", "settingName": "Setting Name", "settings": "Settings", "settingUpdatedSuccessfully": "Setting updated successfully", "settingValue": "Setting Value", "startedAt": "Started At", "startedOn": "Started On", "status": "Status", "statuses": "Statuses", "successful": "Successful", "supportId": "Support ID", "timestamp": "Timestamp", "type": "Type", "updatedBy": "Updated By", "updatedOn": "Updated On", "upload": "Upload", "url": "URL", "urlPostfix": "URL Postfix", "value": "Value", "viewDetails": "View Details"}