{"accountCountry": "Account Country", "accountRegion": "Account Region", "accountRegions": "Account Regions", "activateConnection?": "Activate Connection?", "activateConnectionBody": "Connection will be activated.", "aCustomizedNameForThisAccount": "A customized name for this account", "addMultipleEmailAddressesSeparatedByComma": "Add multiple email addresses separated by comma (,)", "addNewConnection": "Add New Connection", "addNewSettings": "Add New Settings", "aListOfTheCountriesThatThisAccountWillBeUsedToSellTo": "A list of the countries that this account will be used to sell to", "allYourConnectionsWillBeListedHere": "All your connections will be listed here. Click the button below to create one now.", "anErrorOccurredWhileFetchingData": "An error occurred while fetching data", "areYouSure": "Are you sure?", "aUniqueIdentifierForThisAccount": "A unique identifier for this account", "callLimit": "Call Limit", "callLimits": "Call Limits", "callsHour": "Calls/Hour", "checkingConnection": "Checking Connection", "connect": "Connect", "connectedBy": "Connected by", "connectionActivated": "Connection activated", "connectionCreatedSuccessfully": "Connection created successfully", "connectionDeactivated": "Connection deactivated", "connectionDeleted": "Connection deleted.", "connectionSettings": "Connection Settings", "connectionStatusChangeBody": "Connection status will be set to {{status}}. You may reverse this later", "connectionStatusChangeHeader": "Change Connection Status to {{status}}?", "connectionSuccessfulMessage": "Your connection to {{destinationName}} was successful", "connectToSlack": "Connect to Slack", "createANewSetting": "Create a new setting", "createSetting": "Create Setting", "credentialDefinitionError": "Credential Definition for '{{name}}' {{reason}}.", "credentialLocationKey": "Credential Location Key", "daily": "Daily", "deactivateConnection?": "Deactivate Connection?", "deactivateConnectionBody": "Connection will be deactivated. You may resume the connection later", "deleteConnection?": "Delete Connection?", "deleteConnectionConfirmationBody": "Connection will be deleted. This is NOT reversible!", "disablingThisWillDeleteYourWhiteGloveCredentials": "Disabling this will delete your white glove credentials for this connection. This cannot be undone! A service provider will contact you with additional steps.", "disconnectSlack": "Disconnect S<PERSON>ck", "disconnectSlack?": "Disconnect Slack?", "disconnectSlackBody": "After disconnecting from Slack you won't receive any notifications in your Slack channels. However, you can reconfigure this later.", "displayName": "Display Name", "editCallLimits": "Edit Call Limits", "editConnection": "Edit Connection", "editLocation": "Edit Location: {{countryCode}}", "editNotifications": "Edit Notifications", "editNotificationSettings": "Edit Notification Settings", "enablingThisWillEnrollYouInPatternsWhiteGloveService": "Enabling this will enroll you in Pattern's White Glove Service. Our service providers will contact you shortly via email regarding the rest of the setup process", "enterValidJSONAsASettingValue": "Enter valid JSON as setting value.", "errorCredentialDefinitionNotImplmementedForMarketPlace": "Error: Credential Definition not implemented for {{marketplaceName}}", "events": "Events", "every10Minutes": "Every 10 minutes", "every5Minutes": "Every 5 minutes", "expiresOn": "Expires On", "frequency": "Frequency", "frequencyDaysAfter": "Frequency (Days After)", "frequencyDaysBefore": "Frequency (Days Before)", "generateLink": "Generate Link", "generateLinkDescription": "Generate an onboarding link to share with the marketplace account owner", "hourly": "Hourly", "invalidInputPleaseMakeSureEntryIsValidJSON": "Invalid input. Please ensure your entry is a correctly formatted JSON.", "invalidInputTheSettingNameMustContainOnlyLowercaseLetters": "Invalid input. The setting name must contain only lowercase letters and underscores", "invalidLocationKey": "The key can only contain letters, numbers, underscores, and hyphens", "invalidRedirectURI": "Invalid redirect URI: {{redirectUri}}", "iUnderstandActivate": "I understand, activate", "iUnderstandChangeStatus": "I understand, change status", "iUnderstandDeactivate": "I understand, deactivate", "jobFail": "<PERSON>", "jobFailure": "Job Failure", "jobSuccess": "Job Success", "jobType": "Job Type", "linkCopied": "Link copied to clipboard: {{link}}", "location": "Location", "locations": "Locations", "marketPlaceAccountRegionOrSupportID": "Marketplace, Account Region, or Support ID", "monthly": "Monthly", "nameYourConnection": "Name your Connection", "never": "Never", "noCallLimitsAvailable": "No Call Limits Available", "noConnectionDetailsAvailable": "No connection details available", "noConnectionNotificationSettingsAvailable": "No Connection Notification Settings Available.", "noConnectionsAvailable": "No Connections Available", "noHistoryForThisConnectionYet": "No History for this connection yet!", "noLocationsAvailable": "No Locations Available", "noOtherSettingAvailable": "No Other Setting Available.", "notifications": "Notifications", "notifyOnSlack": "Notify on Slack", "notifyPeople": "Notify People", "notifyWhen": "Notify When", "others": "Others", "refreshingOauthForConnection": "Refreshing <PERSON><PERSON> for connection: {{connectionName}}", "refreshOath": "Ref<PERSON>", "saveLocationKeyError": "Error while update location key from {{oldKey}} to {{newKey}}", "searchByNameEGAmazon": "Search by name Eg. Amazon", "selectAccountCountries": "Select Account Country(s)", "selectAccountRegions": "Select Account Region(s)", "selectAMarketplace": "Select a marketplace", "selectMarketplaces": "Select Marketplace(s)", "selectUsers": "Select Users", "setStatusPrompt": "Set status to {{status}}", "slackNotifications": "Slack Notifications", "statusUpdated": "Connection status updated to {{status}}", "successfullyCreatedNewSetting": "Successfully created new setting: {{settingName}}", "successfullyUpdatedConnection": "Successfully updated connection: {{connectionName}}", "successfullyUpdatedConnectionSettings": "Successfully updated {{id}} connection settings", "successfullyUpdatedNotificationSettings": "Successfully updated {{id}} notification settings", "supportIDRequired": "Support ID is required", "supportIDTooLong": "Support ID is too long. Maximum length is 40 characters", "thisFieldRepresentsTheLiveStatusOfYourConnection": "This field represents the live status of your connection. If the Credential State of your connection is still inactive after a few moments, select 'Refresh OAuth' to try again or delete and re-add the connection", "thisSupportIDisNotUniqueTrySuggestion": "This Support ID is not unique! Try {{uniqueSupportIdSuggestion}} or any other unique value", "useFollowingSettingsToNotifyPeopleAboutEvents": "Use following settings to notify people about different events and the frequency of notifications to receive via Email.", "weekly": "Weekly", "whiteGlove": "White Glove", "whiteGloveContact": "White Glove Contact", "whiteGloveContactStatus": "White Glove Status", "whiteGloveServiceDescription": "Enabling this option will enroll you in the white glove service for the selected channel", "yourAccountHasNotBeenAuthentiated": "Your {{destinationName}} account has not been authenticated. Please try again.", "yourAcountIsReadyForSyndication": "Your {{destinationName}} account is ready for syndication. Returning to Amplifi!", "youreNotConnectedToSlack": "You're not connected to Slack. Connect for updates directly in your workpace channels.", "yourSupportIDMustContainOnlyNumbersLowercaseLettersAndHyphens": "Your Support ID must contain only numbers, lowercase letters, and hyphens"}