import { type TranslationNamespace } from '../common/helpers/TranslationHelpers'
import EN_COMMON from './common/en.common.json'
import EN_JOBS from './jobs/en.jobs.json'
import EN_CONNECTIONS from './connections/en.connections.json'
import EN_DESTINATIONS from './destinations/en.destinations.json'
import EN_MAPPINGS from './mappings/en.mappings.json'
import EN_API_TRAFFIC from './apiTraffic/en.apiTraffic.json'
import EN_SUPPORT from './support/en.support.json'
import EN_PRE_SALES_ONBOARDING from './preSalesOnboarding/en.preSalesOnboarding.json'

export const RESOURCES: Record<
  string,
  Record<TranslationNamespace, Record<string, string>>
> = {
  en: {
    apiTraffic: EN_API_TRAFFIC,
    common: EN_COMMON,
    connections: EN_CONNECTIONS,
    destinations: EN_DESTINATIONS,
    jobs: EN_JOBS,
    mappings: EN_MAPPINGS,
    support: EN_SUPPORT,
    preSalesOnboarding: EN_PRE_SALES_ONBOARDING,
  },
} as const
