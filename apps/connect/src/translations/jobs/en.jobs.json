{"callbackUrl": "Callback URL", "connection": "Connection", "createAJob": "Create a Job", "createAndRunJob": "Create & Run Job", "creationDate": "Creation Date", "details": "Details", "endDateAndTime": "End date and time", "errorCannotScheduleJobInThePast": "Error: <PERSON><PERSON> schedule job in the past", "exactSearch": "Exact Search", "filterJobsEndDateTooltip": "Enter the date and time (24-hour format) until which you want to filter jobs. Jobs created at or before this time will be shown", "filterJobsStartDateTooltip": "Enter the date and time (24-hour format) from which you want to start filtering jobs. Jobs created at or after this time will be shown", "fullRequestBody": "Full Request Body", "fullResponseBody": "Full Response Body", "invalidRequestBody": "Invalid request body, please enter a valid JSON.", "jobDetails": "Job Details", "jobId": "Job Id", "jobName": "Job Name", "jobPurpose": "<PERSON> Purpose", "jobType": "Job Type", "jobTypes": "Job Types", "noJobsAvailable": "No Jobs Available.", "noRequestBodyAvailable": "No Request Body Available", "noRequestPathAvailable": "No Request Path Available", "noResponseBodyAvailable": "No Response Body Available", "noSubJobsAvailable": "No Sub-Jobs Available.", "pleaseFormatInputAsAQueryStringWithOptionalPathParameterAtBeginning": "Please format input as a query string, with an optional path parameter at the beginning.", "requestBody": "Request Body", "requestPath": "Request Path", "rerunAJob": "<PERSON><PERSON> a <PERSON>", "rerunAsNewJob": "<PERSON><PERSON> as <PERSON>", "rerunJob": "<PERSON><PERSON>", "rerunThisJob": "Rerun this job", "resendCallback": "<PERSON><PERSON><PERSON>", "resendCallbackFailed": "Failed to resend callback.", "resendCallbackSuccess": "<PERSON><PERSON> successfully resent.", "responseBody": "Response Body", "retryAttempt": "Retry Attempt", "runTime": "Runtime", "runTimeRanges": "Runtime Range(ms)", "scheduledFor": "Schedule For", "scheduleJob": "Schedule Job", "searchJobs": "Search Jobs", "selectAConnection": "Select a Connection", "selectADate": "Select a Date", "selectATimezone": "Select a Timezone", "selectDateAndTime": "Select Date and Time", "selectJobTypes": "Select Job Types", "selectMarketplaces": "Select Marketplaces", "selectSellerAccounts": "Select Seller Accounts", "selectStatuses": "Select Statuses", "selectUsers": "Select Users", "sellerAccounts": "Seller Accounts", "startDateAndTime": "Start date and time", "subJobId": "Sub-Job Id", "subJobs": "Sub Jobs", "successfullyCreatedJob": "Successfully created job:", "totalSubJobs": "Total Sub-Jobs", "unrecognizedURLQueryParamFilterOptions": "Unrecognized URL Query Param filter options", "uploadACSVFile": "Upload a .CSV File"}