{"error.contact": "Please reach out to your Pattern contact for more assistance.", "error.subtitle": "Account not found.", "error.title": "Error", "success.subtitle": "Your account has been successfully linked.", "success.thankYou": "Thank you", "success.title": "Success!", "terms.accountOwner": "You must be the account owner to grant this access.", "terms.agreeTerms": "By checking, you agree to the following terms. You are allowing <PERSON><PERSON> access to your Amazon account via API connection. <PERSON><PERSON> will have full visibility into your Amazon account data. <PERSON><PERSON> will use such data to demo data insights services. <PERSON><PERSON> may also use that data and any other data that <PERSON><PERSON> obtains or acquires through provision of the Services within the scope of its regular business operations. Such data and information include but are not limited to: (i) sales and product information; (ii) information provided by you to <PERSON><PERSON> via email or other file transfer; (iii) information obtained via API connection.", "terms.authorizeAccess": "Authorize access", "terms.error": "Account not found. Please reach out to the person who sent you this link for a new one.", "terms.previewData": "To provide <PERSON><PERSON> with your historical {{marketplaceName}} data, you will have to link your account. You will only have to link your account once.", "terms.privacyPolicy": "<PERSON><PERSON>'s use of any personal data will be governed by the Privacy Policy found", "terms.privacyPolicyLink": "at pattern.com/privacy-policy", "terms.readAndAccept": "Before you link your account, please read and accept our terms and conditions.", "terms.title": "Link your {{marketplaceName}} Seller Central account."}