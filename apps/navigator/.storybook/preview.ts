import type { Preview } from '@storybook/react'
import './_storybook-styles.scss'

const preview: Preview = {
  parameters: {
    layout: 'centered',
    controls: {
      matchers: {
        color: /(background|color)$/i,
        date: /Date$/,
      },
    },
    options: {
      storySort: {
        order: [
          'General',
          'Components',
          'Helper Functions',
          'Hooks',
          'Helper Variables',
          '*',
        ],
        method: 'numerical',
      },
    },
    chromatic: { disableSnapshot: true },
  },
}

export default preview
