{"$schema": "../../node_modules/nx/schemas/project-schema.json", "name": "navigator", "projectType": "application", "sourceRoot": "apps/navigator/src", "tags": ["scope:ui", "type:lib"], "targets": {"storybook": {"options": {"port": 4401}}, "serve-static": {"executor": "@nx/web:file-server", "dependsOn": ["build"], "options": {"buildTarget": "navigator:build", "spa": true}}, "typegen": {"command": "tsc --emitDeclarationOnly || exit 0"}, "prettier": {"executor": "nx:run-commands", "options": {"command": "prettier --write 'apps/navigator/src/**/*' --ignore-path apps/navigator/.prettierignore || true"}}, "get-types": {"command": "pnpm update @patterninc/react-ui"}, "prettier-check": {"executor": "nx:run-commands", "options": {"command": "prettier --check 'apps/navigator/src/**/*'"}}, "chromatic": {"executor": "nx:run-commands", "options": {"commands": ["nx run navigator:build-storybook", "pnpm dlx chromatic --project-token=chpt_d042117f5768ea0 --only-changed --storybook-build-dir=apps/navigator/storybook-static"], "parallel": false}}}}