import path from 'path'

import { defineConfig } from '@rsbuild/core'
import { pluginReact } from '@rsbuild/plugin-react'
import { pluginSass } from '@rsbuild/plugin-sass'
import { pluginSvgr } from '@rsbuild/plugin-svgr'
import { pluginAssetsRetry } from '@rsbuild/plugin-assets-retry'
import { pluginTypeCheck } from '@rsbuild/plugin-type-check'
const shouldKeepBrowserClosed = process.env.OPEN_BROWSER === 'false'

export default defineConfig({
  plugins: [
    pluginReact(),
    pluginSvgr({
      svgrOptions: {
        exportType: 'default',
      },
    }),
    pluginSass({
      sassLoaderOptions: {
        api: 'legacy',
      },
    }),
    pluginAssetsRetry(),
    pluginTypeCheck(),
  ],

  resolve: {
    extensions: ['.tsx', '.ts', '.js', '.jsx', '.json'],
    alias: {
      '@navigator-services': path.resolve(__dirname, 'src/common/services/'),
    },
  },

  source: {
    define: {
      'process.env': JSON.stringify(process.env),
    },
  },

  output: {
    distPath: { root: path.resolve(__dirname, '../../dist/apps/navigator') },
    cleanDistPath: true,
  },

  server: {
    // https: true, // uncomment this line if you want to use overrides for stage/prod
    host: 'navigator.localhost',
    port: 4040,
    historyApiFallback: true,
    open: !shouldKeepBrowserClosed,
    compress: true,
    headers: { 'Access-Control-Allow-Origin': '*' },
    proxy: [
      {
        context: ['/iserve'],
        target: 'https://protect-api.usepredict.com',
        pathRewrite: { '^/iserve': '' },
        changeOrigin: true,
        secure: false,
      },
      {
        context: ['/adminczar'],
        target: 'https://adminczar.usepredict.com',
        pathRewrite: { '^/adminczar': '' },
        changeOrigin: true,
        secure: false,
      },
      {
        context: ['/user-settings'],
        target: 'https://api.pattern.com/user-settings',
        pathRewrite: { '^/user-settings': '' },
        changeOrigin: true,
        secure: false,
      },
      {
        context: ['/toggle'],
        target: 'https://toggle-api.usepredict.com',
        pathRewrite: { '^/toggle': '' },
        changeOrigin: true,
        secure: false,
      },

      // STAGING
      {
        context: ['/staging-iserve'],
        target: 'https://stage-protect.usepredict.com',
        pathRewrite: { '^/staging-iserve': '' },
        changeOrigin: true,
        secure: false,
      },
      {
        context: ['/staging-adminczar'],
        target: 'https://stage-adminczar.usepredict.com',
        pathRewrite: { '^/staging-adminczar': '' },
        changeOrigin: true,
        secure: false,
      },
      {
        context: ['/staging-user-settings'],
        target: 'https://stage-api.pattern.com/user-settings',
        pathRewrite: { '^/staging-user-settings': '' },
        changeOrigin: true,
        secure: false,
      },
      {
        context: ['/staging-navigator'],
        target: 'https://stage-navigator-api.usepredict.com',
        // target: 'http://localhost:3000', // use this when running the backend locally. The DevTools backend override is causing CORS errors.
        pathRewrite: { '^/staging-navigator': '' },
        changeOrigin: true,
        secure: false,
      },
    ],
  },
  tools: {
    htmlPlugin: {
      title: 'Navigator',
    },
  },
})
