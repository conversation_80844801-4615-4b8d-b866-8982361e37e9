import React from 'react'
import {
  Ellipsis,
  getEnvironmentName,
  PatternToastContainer,
} from '@patterninc/react-ui'
import { c } from '@navigator-services'

import './common/scss/main.scss'

import { useAuth } from './context/auth-context'

const AuthenticatedApp = React.lazy(
  () => import(/* webpackChunkName: "authenticatedApp" */ './AuthenticatedApp'),
)
const UnauthenticatedApp = React.lazy(
  () =>
    import(/* webpackChunkName: "unauthenticatedApp" */ './UnauthenticatedApp'),
)

export function getCurrentEnv(): 'development' | 'staging' | 'production' {
  const environmentName = getEnvironmentName()
  if (environmentName === 'demo' || environmentName === 'stage') {
    return 'staging'
  }
  return environmentName
}

export default function App(): React.JSX.Element {
  const { user } = useAuth()

  return (
    <>
      <React.Suspense
        fallback={
          <div className='m-48'>
            {c('loading')} <Ellipsis />
          </div>
        }
      >
        {user ? <AuthenticatedApp /> : <UnauthenticatedApp />}
      </React.Suspense>
      <PatternToastContainer />
    </>
  )
}
