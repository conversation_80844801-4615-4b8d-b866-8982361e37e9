import auth0, { type Auth0Result, type Auth0UserProfile } from 'auth0-js'

import { AUTH_CONFIG } from './auth0-variables'
import { getCurrentEnv } from '../App'
import SecureAxios from '../common/services/SecureAxios'

type GenericFunction = () => void
type User = Auth0UserProfile

interface Auth0Response extends Auth0Result {
  idTokenPayload?: {
    /** actual expiration time (in seconds) */
    exp: number
  }
}

const ACCESS_TOKEN_KEY = 'access_token'
const ID_TOKEN_KEY = 'id_token'
const EXPIRES_AT_KEY = 'expires_at'
const USER_KEY = 'user'
const REDIRECT_URL_KEY = 'redirect_url'
const USERINFO_API =
  getCurrentEnv() === 'production'
    ? 'https://adminczar.usepredict.com/api/v1/userinfo'
    : 'https://stage-adminczar.usepredict.com/api/v1/userinfo'

export default class Auth {
  accessToken = localStorage.getItem(ACCESS_TOKEN_KEY)
  idToken = localStorage.getItem(ID_TOKEN_KEY)
  expiresAt = localStorage.getItem(EXPIRES_AT_KEY)
  user: User | null = null

  auth0 = new auth0.WebAuth({
    domain: AUTH_CONFIG.domain,
    clientID: AUTH_CONFIG.clientId,
    redirectUri: `${window.location.protocol}//${window.location.host}${AUTH_CONFIG.callbackUrl}`,
    responseType: 'token id_token',
    scope: 'openid profile email',
  })

  constructor() {
    this.login = this.login.bind(this)
    this.logout = this.logout.bind(this)
    this.handleAuthentication = this.handleAuthentication.bind(this)
    this.isAuthenticated = this.isAuthenticated.bind(this)
    this.getAccessToken = this.getAccessToken.bind(this)
    this.getIdToken = this.getIdToken.bind(this)
    this.getUser = this.getUser.bind(this)
    this.user = null
  }

  login(): void {
    const pathname = window.location.pathname,
      search = window.location.search
    localStorage.setItem(REDIRECT_URL_KEY, `${pathname}${search}`)
    this.auth0.authorize()
  }

  handleAuthentication(callback: GenericFunction): void {
    this.auth0.parseHash((err, authResult) => {
      if (authResult && authResult.accessToken && authResult.idToken) {
        this.setSession(authResult)
        // get the user now
        this.setupUser(callback)
      } else if (err) {
        this.auth0.authorize()
      }
    })
  }

  getAccessToken(): string {
    return (this.accessToken || localStorage.getItem(ACCESS_TOKEN_KEY)) ?? ''
  }

  getIdToken(): string | null {
    return this.idToken || localStorage.getItem(ID_TOKEN_KEY)
  }

  setSession(authResult: Auth0Response): void {
    this.expiresAt = ((authResult?.idTokenPayload?.exp ?? 0) * 1000).toString()
    this.accessToken = authResult.accessToken ?? ''
    this.idToken = authResult.idToken ?? ''

    localStorage.setItem(ACCESS_TOKEN_KEY, this.accessToken ?? '')
    localStorage.setItem(ID_TOKEN_KEY, this.idToken ?? '')
    localStorage.setItem(EXPIRES_AT_KEY, this.expiresAt)
  }

  setupUser(callback: GenericFunction): void {
    SecureAxios.get(USERINFO_API)
      .then((userinfo) => {
        localStorage.setItem(USER_KEY, JSON.stringify(userinfo.data))
        this.user = userinfo.data
        callback?.()
      })
      .catch((err) => {
        console.error(err)
      })
  }

  getUser(): User | null {
    const localUser = JSON.parse(localStorage.getItem(USER_KEY) ?? '')
    if (!this.user && localUser) this.user = localUser
    return this.user
  }

  logout(): void {
    // Remove tokens and expiry time
    this.accessToken = null
    this.idToken = null
    this.expiresAt = '0'
    localStorage.removeItem(ACCESS_TOKEN_KEY)
    localStorage.removeItem(ID_TOKEN_KEY)
    localStorage.removeItem(EXPIRES_AT_KEY)
    this.user = null
    this.auth0.logout({
      returnTo: `${window.location.protocol}//${window.location.host}/`,
      clientID: AUTH_CONFIG.clientId,
    })
  }

  authenticationValid(): boolean {
    // Check whether the current time is past the
    // access token's expiry time
    return (
      new Date().getTime() < Number(localStorage.getItem(EXPIRES_AT_KEY)) &&
      !!localStorage.getItem(USER_KEY)
    )
  }

  isAuthenticated(): User | boolean | null {
    return this.authenticationValid() ? this.getUser() : false
  }
}

export { REDIRECT_URL_KEY }
