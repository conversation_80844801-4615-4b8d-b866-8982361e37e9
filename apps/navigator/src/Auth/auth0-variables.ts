import { getCurrentEnv } from '../App'

const CLIENT_ID = {
  PRODUCTION: '', // TODO: add when the prod Auth0 app is setup
  STAGING: 'C8mJkxHOTnuQT05kgal8IPdleLvzxnCh',
} as const

export const AUTH_CONFIG = {
  domain:
    getCurrentEnv() === 'production'
      ? 'login.pattern.com'
      : 'login.staging.pattern.com',
  clientId:
    getCurrentEnv() === 'production' ? CLIENT_ID.PRODUCTION : CLIENT_ID.STAGING,
  callbackUrl: '/authenticate',
}
