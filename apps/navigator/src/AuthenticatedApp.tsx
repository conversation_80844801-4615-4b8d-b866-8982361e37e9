import React from 'react'
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from 'react-router-dom'

import { ThemeProvider } from './Context'
import Main from './common/components/Main'
import ScrollToTop from './common/ScrollToTop'

function AuthenticatedApp(): React.JSX.Element {
  return (
    <BrowserRouter
      future={{
        v7_relativeSplatPath: true,
        v7_startTransition: true,
      }}
    >
      <ThemeProvider>
        <ScrollToTop>
          <Main />
        </ScrollToTop>
      </ThemeProvider>
    </BrowserRouter>
  )
}

export default AuthenticatedApp
