import React, { useEffect, useState } from 'react'
import { <PERSON><PERSON>, Ellipsis } from '@patterninc/react-ui'
import { c } from '@navigator-services'

import { useAuth } from './context/auth-context'

const REDIRECT_URL_KEY = 'redirect_url'

const UnauthenticatedApp = () => {
  const {
    login,
    logout,
    handleAuthentication,
    setUser,
    isAuthenticated,
    getUser,
  } = useAuth()
  const pathname = window.location.pathname
  const hash = window.location.hash
  const [showErrorPage, setShowErrorPage] = useState(false)
  const pageUrl = new URL(window.location.href.replace(/#/g, '?'))
  const error = pageUrl.searchParams.get('error')
  const error_description = pageUrl.searchParams.get('error_description')

  useEffect(() => {
    if (
      pathname === '/authenticate' &&
      /Unauthorized|access_denied/.test(hash)
    ) {
      setShowErrorPage(true)
    } else {
      setShowErrorPage(false)
      if (
        pathname === '/authenticate' &&
        /access_token|id_token|error/.test(hash)
      ) {
        if (isAuthenticated()) {
          const redirectUrl = localStorage.getItem(REDIRECT_URL_KEY)
            ? localStorage.getItem(REDIRECT_URL_KEY)
            : ''
          window.location.replace(`${window.location.origin}${redirectUrl}`)
        } else {
          handleAuthentication(() => setUser(getUser))
        }
      } else {
        if (isAuthenticated()) {
          setUser(getUser())
        } else {
          login()
        }
      }
    }
  }, [
    pathname,
    hash,
    handleAuthentication,
    setUser,
    getUser,
    isAuthenticated,
    login,
  ])

  return (
    <div>
      {showErrorPage ? (
        <div className='m-48'>
          <Alert
            type='error'
            text={
              <div>
                <div>
                  {c('error')}: {error}.
                </div>
                <div>{error_description}</div>
              </div>
            }
            buttons={[
              {
                children: c('login'),
                onClick: () => logout(),
              },
            ]}
          />
        </div>
      ) : (
        <div className='m-48'>
          {c('loading')} <Ellipsis />
        </div>
      )}
    </div>
  )
}

export default UnauthenticatedApp
