import type React from 'react'
import { useEffect } from 'react'
import { useLocation } from 'react-router-dom'

type ScrollToTopProps = {
  children?: React.ReactNode
}

const ScrollToTop = ({ children }: ScrollToTopProps): React.JSX.Element => {
  const location = useLocation()

  useEffect(() => {
    const doc = document.querySelector('.app-content-layout')
    doc && (doc.scrollTop = 0)
  }, [location.pathname])

  return children as React.JSX.Element
}

export default ScrollToTop
