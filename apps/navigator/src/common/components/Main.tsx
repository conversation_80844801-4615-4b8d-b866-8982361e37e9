import React, { useRef } from 'react'

import AppRoutes from '../../modules/AppRoutes'

const Main = (): React.JSX.Element => {
  const appWindowRef = useRef(null)

  return (
    <div className='App relative'>
      <div
        className='app-content-layout'
        id='app-content-layout'
        ref={appWindowRef}
      >
        <div className='App-content'>
          <div className='page-container'>
            <AppRoutes />
          </div>
        </div>
      </div>
    </div>
  )
}

export default Main
