const authKey = 'https://pattern.com/user_authorization'
export const haveRoles = (
  user: {
    [authKey]: {
      roles: string[]
    }
  },
  rolesRequired: string[] = [],
): boolean => {
  if (rolesRequired.length === 0) return true
  if (!user?.[authKey]?.roles) return false
  return rolesRequired.some((role) => user[authKey].roles.includes(role))
}

export const tryLocalStorageParse = (
  key: string,
): Record<string, unknown> | string | null => {
  try {
    return JSON.parse(localStorage.getItem(key) ?? '')
  } catch {
    return null
  }
}
