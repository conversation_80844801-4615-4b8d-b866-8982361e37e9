import { getApiUrlPrefix } from '@patterninc/react-ui'
import { type JwtPayload as BaseJwtPayload, jwtDecode } from 'jwt-decode'

type JwtPayload = BaseJwtPayload & {
  'https://pattern.com/metadata'?: {
    id: string
  }
}

export const getUserSettingEndpoint = (
  key: string,
): { endpoint: string; decoded: JwtPayload } => {
  const token = localStorage.getItem('id_token')
  const decoded = jwtDecode<JwtPayload>(token ?? '')
  const userId = decoded?.['https://pattern.com/metadata']?.id || decoded?.sub

  return {
    endpoint: `${getApiUrlPrefix('user-settings')}/v1/${userId}/navigator/${key}`,
    decoded,
  }
}
