import './set-public-path.js'
import React from 'react'
import { createRoot } from 'react-dom/client'
import { QueryClient, QueryClientProvider } from '@tanstack/react-query'
import { ReactQueryDevtools } from '@tanstack/react-query-devtools'
import { TranslationProvider } from '@patterninc/react-ui'

import AppProviders from './context/app-providers.js'
import App from './App.js'
import {
  i18nNavigatorInstance,
  initNavigatorI18nInstance,
} from './common/services/TranslationService.js'

initNavigatorI18nInstance()

const queryClient = new QueryClient({
    defaultOptions: {
      queries: {
        refetchOnWindowFocus: false,
      },
    },
  }),
  container = document.getElementById('root') as HTMLElement,
  root = createRoot(container),
  enableReactQueryDevTools =
    localStorage.getItem('react-query-devtools') === 'true'

root.render(
  <React.StrictMode>
    <TranslationProvider i18nInstance={i18nNavigatorInstance}>
      <QueryClientProvider client={queryClient}>
        <AppProviders>
          <App />
        </AppProviders>
        {enableReactQueryDevTools ? <ReactQueryDevtools /> : null}
      </QueryClientProvider>
    </TranslationProvider>
  </React.StrictMode>,
)
