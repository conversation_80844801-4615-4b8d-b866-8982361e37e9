import React from 'react'
import { Navigate, Outlet, Route, Routes } from 'react-router-dom'

import LoginRedirect from '../LoginRedirect'
import Home from './Home/Home'

const AppRoutes = (): React.JSX.Element => {
  return (
    <Routes>
      <Route path='/' element={<Outlet />}>
        <Route path='/' element={<Navigate to='home' replace />} />
        <Route path='home' element={<Home />} />
        <Route path='authenticate' element={<LoginRedirect />} />
      </Route>
      {/* Redirect to the home page when a route does not exist. */}
      <Route path='*' element={<Navigate to='/home' replace />} />
    </Routes>
  )
}

export default AppRoutes
