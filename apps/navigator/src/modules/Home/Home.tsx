import { c } from '@navigator-services'
import { Button } from '@patterninc/react-ui'

import { useAuth } from '../../context/auth-context'

const TEST_URL = 'https://sellercentral.amazon.com/' // TODO this is just a test url, replace with API data

export const Home = () => {
  const { logout } = useAuth()
  const { user } = useAuth()
  const id_token = localStorage.getItem('id_token')

  const initiateFlow = () => {
    if (id_token) {
      window.postMessage(
        {
          flow_id: 'TODO', // replace with actual flow_id, determined by API data
          id_token,
          navigator: true,
          reimbursement_id: 'TODO', // TODO use the value from the API
          type: 'PATTERN_OPEN_PANEL',
        },
        '*',
      )
      setTimeout(() => {
        window.location.href = TEST_URL
      }, 1) // need to wait a tick to let the postMessage be processed by the chrome extension before navigating away
    }
  }

  return (
    <div className='m-32'>
      <div className='mb-16'>
        <div>
          {c('user')}: {user?.email}
        </div>
      </div>
      <div className='flex gap-16'>
        <Button className='mb-16' styleType='primary' onClick={initiateFlow}>
          {c('loadFirstCase')}
        </Button>
        <Button onClick={logout}>{c('logout')}</Button>
      </div>
    </div>
  )
}

export default Home
