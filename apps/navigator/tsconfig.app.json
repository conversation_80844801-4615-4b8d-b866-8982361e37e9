{"compilerOptions": {"outDir": "../../dist/out-tsc", "types": ["node", "@nx/react/typings/cssmodule.d.ts", "@nx/react/typings/image.d.ts"], "resolveJsonModule": true, "composite": true, "declaration": true}, "exclude": ["src/**/*.spec.ts", "src/**/*.test.ts", "src/**/*.spec.tsx", "src/**/*.test.tsx", "src/**/*.spec.js", "src/**/*.test.js", "src/**/*.spec.jsx", "src/**/*.test.jsx"], "extends": "./tsconfig.json", "include": ["src/**/*.js", "src/**/*.jsx", "src/**/*.ts", "src/**/*.tsx", "src/**/*.json"], "references": [{"path": "../../lib/react-ui/tsconfig.lib.json"}]}