{
  "compilerOptions": {
    "jsx": "react-jsx",
    "allowJs": true,
    "esModuleInterop": true,
    "allowSyntheticDefaultImports": true,
    "strict": true,
    "moduleResolution": "node",
    "skipLibCheck": true,
    "forceConsistentCasingInFileNames": true,
    "resolveJsonModule": true,
    "baseUrl": ".",
    "paths": {
      "@navigator-services": ["src/common/services"]
      // "@patterninc/react-ui": ["../../lib/react-ui/src/module.ts"],
      // "@patterninc/react-ui/dist/variables": [
      //   "../../lib/react-ui/src/scss/base/_variables.scss"
      // ]
    }
  },
  "extends": "../../tsconfig.base.json",
  "files": [],
  "include": ["typings/**/*", "src/**/*", "global.d.ts"],
  "references": [
    {
      "path": "./tsconfig.app.json"
    },
    {
      "path": "./tsconfig.spec.json"
    }
  ]
}
