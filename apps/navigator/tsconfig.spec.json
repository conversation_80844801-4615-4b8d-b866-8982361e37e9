{"compilerOptions": {"outDir": "../../dist/out-tsc", "module": "commonjs", "types": ["node", "@nx/react/typings/cssmodule.d.ts", "@nx/react/typings/image.d.ts"], "composite": true, "declaration": true}, "extends": "./tsconfig.json", "include": ["src/**/*.test.ts", "src/**/*.spec.ts", "src/**/*.test.tsx", "src/**/*.spec.tsx", "src/**/*.test.js", "src/**/*.spec.js", "src/**/*.test.jsx", "src/**/*.spec.jsx", "src/**/*.d.ts"]}