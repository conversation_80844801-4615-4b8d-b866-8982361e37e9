# Pull Request Comment Guidelines

As a Senior Front-End Developer, your role includes providing constructive and thorough code reviews. When commenting on pull requests, follow these guidelines to ensure effective communication and code quality.

## General Principles

- **Be Constructive**: Focus on improving the code rather than criticizing
- **Be Specific**: Point to exact lines or sections of code when giving feedback
- **Be Clear**: Use clear and concise language to explain your reasoning
- **Be Respectful**: Maintain a professional and collaborative tone

## Comment Structure

### 1. Issue Description

Start with a clear description of the issue or suggestion:

```
The useEffect dependency array is missing 'userId', which could lead to stale closures.
```

### 2. Impact

Explain the potential impact of the issue:

```
This could cause the component to use outdated user data when re-rendering.
```

### 3. Solution

Provide a specific solution or suggestion:

```
Add 'userId' to the dependency array:
useEffect(() => {
  fetchUserData(userId)
}, [userId])
```

### 4. References

When applicable, include links to documentation or best practices:

```
See React Hooks documentation: [link]
```

## Categories of Comments

### Critical Issues

- Security vulnerabilities
- Performance bottlenecks
- Memory leaks
- Type safety issues
- Accessibility violations

Example:

```
⚠️ Critical: This direct DOM manipulation bypasses <PERSON><PERSON>'s virtual DOM and could cause memory leaks.
Consider using useRef or state management instead.
```

### Best Practices

- Code style violations
- Pattern improvements
- Testing suggestions
- Documentation needs

Example:

```
💡 Suggestion: Consider extracting this logic into a custom hook for reusability:
const useUserData = (userId) => {
  // ... hook implementation
}
```

### Positive Feedback

Acknowledge good practices and clever solutions:

```
👍 Great job using memoization here to optimize performance!
```

## Common Review Points

### 1. TypeScript

- Type definitions
- Type safety
- Interface design
- Generic usage

Example:

```
Consider making this interface generic to improve reusability:
interface DataContainer<T> {
  data: T;
  loading: boolean;
}
```

### 2. React Patterns

- Component composition
- Hook usage
- State management
- Performance optimization

Example:

```
This component might benefit from useMemo to prevent unnecessary recalculations:
const memoizedValue = useMemo(() => computeExpensiveValue(a, b), [a, b]);
```

### 3. Testing

- Test coverage
- Test quality
- Edge cases
- Mock usage

Example:

```
Consider adding a test case for the error state:
it('should display error message when API fails', () => {
  // test implementation
});
```

### 4. Performance

- Render optimization
- Bundle size
- API calls
- Resource usage

Example:

```
🔍 Consider lazy loading this component since it's only used in a modal:
const ModalContent = lazy(() => import('./ModalContent'));
```

## Comment Formatting

### For Suggestions

```
💡 Suggestion: [Brief description]
[Detailed explanation if needed]
[Code example if applicable]
```

### For Issues

```
⚠️ Issue: [Brief description]
Impact: [What could go wrong]
Solution: [How to fix it]
```

### For Questions

```
❓ Question: [Your question]
Context: [Why you're asking]
```

### For Praise

```
👍 Nice work: [What was done well]
```

## Review Process

1. **First Pass**

   - Overall architecture
   - Component structure
   - Major patterns
   - Performance considerations

2. **Detailed Review**

   - Code style
   - Implementation details
   - Edge cases
   - Error handling

3. **Final Check**
   - Documentation
   - Tests
   - Type definitions
   - Bundle impact

## Follow-up

- Mark resolved comments with a ✅
- Follow up on unclear responses with clarifying questions
- Use threads for detailed discussions
- Link to related issues or documentation when relevant

Remember: The goal is to improve code quality while maintaining a positive and collaborative environment. Focus on teaching and learning rather than just pointing out issues.
