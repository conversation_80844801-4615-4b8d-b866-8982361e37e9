# Testing Guidelines for [Your Project Name]

This document outlines our unit testing practices using Vitest.

## Testing Framework

We use **Vitest** for unit testing in our React project.

## Test Types

Our focus is on **unit testing** to ensure individual components and functions work correctly.

## Test Structure

We recommend the **Arrange-Act-Assert** pattern:

1. **Arrange**: Prepare the testing environment and data.
2. **Act**: Execute the function or component.
3. **Assert**: Verify the outcome.

## Setup and Teardown

Utilize Vitest's `vi.mock` and `vi.spyOn` for mocking dependencies. As our suite grows, we may define common setup and teardown functions.

## Mocking and Stubbing

- Use `vi.mock` to mock modules or functions.
- Use `vi.spyOn` to verify function interactions.

## Edge Cases

Document and test edge cases to ensure comprehensive coverage. Copilot can assist in identifying potential edge cases.

## Best Practices

- Place test files in the same folder as the source file for a small number of functions.
- For larger suites, use a `__tests__` folder and group tests by functionality.

## Environment Configuration

General configuration is established. Implement local configurations for specific tests as needed.

---

These guidelines aim to maintain a consistent and effective testing strategy to ensure high-quality code.
