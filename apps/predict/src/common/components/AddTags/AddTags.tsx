import React, { useCallback, useEffect, useRef, useState } from 'react'
import {
  <PERSON>ton,
  <PERSON><PERSON>abe<PERSON>,
  ListLoading,
  notEmpty,
  SearchBar,
  toast,
} from '@patterninc/react-ui'
import { c, diff } from '@predict-services'

import {
  assignTags,
  cancelAssignTags,
  cancelCreateBulkTags,
  cancelGetCommonTags,
  cancelGetTags,
  cancelGetTagsSuggestions,
  createBulkTags,
  getCommonTags,
  getTagsSuggestions,
} from '../../../modules/Settings/components/Pages/Products/Products/services/TagsService'
import {
  cancelGetMasterProductTags,
  getMasterProductTags,
} from '../../services/MasterProductsService'
import SuggestedTags from './SuggestedTags'
import TagListItem from './TagListItem'

export interface Tag {
  id: number
  name: string
  active?: boolean
  newTag?: boolean
  removed?: boolean
  tagGroup?: string
  searchTag?: boolean
}

interface AddTagsProps {
  customer: { id: number }
  close: (updated?: boolean) => void
  productIds?: number[]
  brandId?: number
  settings?: boolean
  determineIsEdit?: (tags: string[]) => void
  isEdit?: boolean
  loading?: boolean
}

interface AddTagsState {
  suggestedTags: Tag[]
  searchTags: Tag[]
  selectedTags: Tag[]
  initialTags: Tag[]
  searchInput: string
  disabledTags: boolean
}

const systemGeneratedTags = [
  'Bundle',
  'Not Bundle',
  'Discontinued',
  'Not Discontinued',
  'Glass',
  'Not Glass',
  'Enrolled in Transparency Program',
  'Not Enrolled in Transparency Program',
  'Pattern Applied Transparency Label',
  'No Transparency Label',
  'DNO',
  'Not DNO',
].map((tag) => tag.toLowerCase())

const AddTags: React.FC<AddTagsProps> = ({
  customer,
  close,
  productIds,
  brandId,
  settings,
  determineIsEdit,
  isEdit,
  loading,
}) => {
  const isCancelled = useRef<boolean>(false)

  const [state, setState] = useState<AddTagsState>({
    suggestedTags: [],
    searchTags: [],
    selectedTags: [],
    initialTags: [],
    searchInput: '',
    disabledTags: true,
  })
  const { suggestedTags, searchTags, selectedTags, initialTags, searchInput } =
    state

  const inputHandler = (value: string): void => {
    const tag: Tag = { id: 0, name: value.trim() }
    !isCancelled.current &&
      setState((prevState) => ({
        ...prevState,
        searchTags: value ? [tag] : [],
        searchInput: value,
      }))
  }

  const generateTagObj = (): void => {
    if (notEmpty(searchInput)) {
      if (systemGeneratedTags.includes(searchInput.toLowerCase().trim())) {
        toast({
          type: 'error',
          message: c('cannotCreateSystemGeneratedTagsToast', {
            tagName: searchInput,
          }),
        })
      } else {
        const tag: Tag = {
          id: 0,
          name: searchInput,
          searchTag: true,
        }
        !isCancelled.current && updateSelectedTags(tag)
        if (!isDuplicate(tag)) {
          const updatedSearchTags = [...searchTags]
          updatedSearchTags.splice(0, 1)
          !isCancelled.current &&
            setState((prevState) => ({
              ...prevState,
              searchTags: updatedSearchTags,
            }))
        }
      }
    }
  }

  const removeTag = (tag: Tag): void => {
    const updatedTags = selectedTags.filter((s) => {
      return !s.removed
    })
    !isCancelled.current &&
      setState((prevState) => ({
        ...prevState,
        selectedTags: updatedTags,
      }))
    if (tag.tagGroup === 'suggested') {
      !isCancelled.current &&
        setState((prevState) => ({
          ...prevState,
          suggestedTags: updateSuggestedTags(tag.id, suggestedTags, false),
        }))
    } else if (tag.tagGroup === 'searched') {
      !isCancelled.current &&
        setState((prevState) => ({
          ...prevState,
          searchTags: updateSuggestedTags(tag.id, searchTags, false),
        }))
    }
  }

  const updateSuggestedTags = (
    tagId: number,
    arr: Tag[],
    isActive: boolean,
  ): Tag[] => {
    return arr.map((l) => {
      if (l.id === tagId) {
        l.active = isActive
      }
      return l
    })
  }

  const addTags = (tag: Tag, tagGroup?: string): void => {
    if (systemGeneratedTags.includes(tag?.name?.toLowerCase())) {
      toast({
        type: 'error',
        message: c('cannotCreateSystemGeneratedTagsToast', {
          tagName: tag?.name,
        }),
      })
    } else {
      tag.newTag = true
      tag.removed = false
      !isCancelled.current && updateSelectedTags(tag, tagGroup)
      if (tagGroup === 'suggested') {
        !isCancelled.current &&
          setState((prevState) => ({
            ...prevState,
            suggestedTags: updateSuggestedTags(tag.id, suggestedTags, true),
          }))
      } else if (tagGroup === 'searched') {
        const updatedSearchTags = searchTags.filter((s) => {
          return s.id !== tag.id
        })
        !isCancelled.current &&
          setState((prevState) => ({
            ...prevState,
            searchTags: updatedSearchTags,
          }))
      }
    }
  }

  const clearInput = (): void => {
    !isCancelled.current &&
      setState((prevState) => ({
        ...prevState,
        searchInput: '',
        searchTags: [],
      }))
  }

  const isDuplicate = useCallback(
    (tag: Tag): boolean => {
      return selectedTags
        .map((t) => t.name.toLowerCase())
        .includes(tag.name.toLowerCase())
    },
    [selectedTags],
  )

  const updateSelectedTags = (tag: Tag, tagGroup?: string): void => {
    if (!tag.active && !isDuplicate(tag)) {
      const selected = [...selectedTags]
      tag.tagGroup = tagGroup
      selected.push(tag)
      !isCancelled.current &&
        setState((prevState) => ({
          ...prevState,
          selectedTags: selected,
        }))
    }
  }

  const createTagArr = <T extends Tag, K extends keyof T>(
    arr: T[],
    field: K,
  ): T[K][] => {
    return arr.map((s) => {
      return s[field]
    })
  }

  const findRemovedTags = (): number[] => {
    return diff(
      createTagArr(initialTags, 'id'),
      createTagArr(selectedTags, 'id'),
    )
  }

  const submit = (): void => {
    if (settings) {
      settingsSave()
    } else {
      save()
    }
  }

  const toastType =
      initialTags.length < selectedTags.length ? 'success' : 'warning',
    toastMessage =
      initialTags.length < selectedTags.length
        ? `${
            selectedTags.length - initialTags.length
          } Tags have been added to products.`
        : `${
            initialTags.length - selectedTags.length
          } Tags have been removed from products.`

  const save = (): void => {
    const body = {
      master_product_ids: productIds || [],
      tag_names: createTagArr(selectedTags, 'name'),
      customer_id: brandId || customer.id,
      remove_tag_ids: findRemovedTags(),
    }
    assignTags(body).then(() => {
      toast({
        type: toastType,
        message: toastMessage,
      })
      close(true)
    })
  }

  const settingsSave = (): void => {
    const body = {
      tag_names: createTagArr(selectedTags, 'name'),
      customer_id: customer.id,
    }
    createBulkTags(body).then(() => {
      toast({
        type: toastType,
        message: toastMessage,
      })
      close(true)
    })
  }

  const cancelEverything = (): void => {
    isCancelled.current = true
    cancelGetTags()
    cancelCreateBulkTags()
    cancelAssignTags()
  }

  useEffect(() => {
    const getSuggestedTags = (tags: Tag[]) => {
      const params = {
        customer_id: brandId || customer.id,
        sort: 'name:asc:lowercase',
        filter: {
          name: {
            notin: tags.map((s) => s.name).join(',') || undefined,
          },
        },
      }
      getTagsSuggestions(params).then((response) => {
        if (response) {
          setState((prevState) => ({
            ...prevState,
            suggestedTags: [...response.data],
          }))
        }
      })
    }
    if (productIds && productIds?.length > 1) {
      const params = {
        master_product_ids: productIds,
      }
      getCommonTags(params).then((response) => {
        if (response) {
          setState((prevState) => ({
            ...prevState,
            initialTags: [...response.data],
            selectedTags: [...response.data],
          }))
          getSuggestedTags(response.data)
        }
      })
    } else if (productIds?.length === 1) {
      const params = {
        sort: 'name:asc:lowercase',
      }
      getMasterProductTags(productIds[0], params).then((response) => {
        if (response) {
          setState((prevState) => ({
            ...prevState,
            initialTags: [...response.data],
            selectedTags: [...response.data],
          }))
          determineIsEdit?.(response.data)
          getSuggestedTags(response.data)
        }
      })
    }
    return () => {
      cancelGetTagsSuggestions()
      cancelGetMasterProductTags()
      cancelGetCommonTags()
    }
  }, [brandId, customer.id, determineIsEdit, productIds])

  useEffect(() => {
    const initialTagIds: Record<number, number> = initialTags.reduce(
      (initialTags, { id }) => Object.assign(initialTags, { [id]: id }),
      {},
    )
    const selectedTagIds: Record<number, number> = selectedTags.reduce(
      (selectedTags, { id }) => Object.assign(selectedTags, { [id]: id }),
      {},
    )
    const tagDiff = [
      ...initialTags.filter(({ id }) => !selectedTagIds[id]),
      ...selectedTags.filter(({ id }) => !initialTagIds[id]),
    ]
    !tagDiff.length
      ? setState((prevState) => ({
          ...prevState,
          disabledTags: true,
        }))
      : setState((prevState) => ({
          ...prevState,
          disabledTags: false,
        }))
  }, [initialTags, selectedTags])

  useEffect(() => {
    isCancelled.current = false
    return () => {
      cancelEverything()
    }
  }, [])

  useEffect(() => {
    if (searchTags.length === 0) {
      !isCancelled.current && clearInput()
    }
  }, [searchTags.length, selectedTags])

  return (
    <div className={`add-tags-container`}>
      <div className='left-section'>
        {suggestedTags.length > 0 && (
          <>
            <p>
              Here are some recommended tags based on product titles,
              descriptions, and categories:
            </p>
            {/* Original Suggested Tags */}
            <SuggestedTags
              tags={suggestedTags}
              updateSuggestedTags={addTags}
              tagGroup='suggested'
              productCount={settings ? 0 : productIds?.length || 0}
            />
          </>
        )}
        <FormLabel label='Create New Tags' />
        <div className='pat-mb-2'>
          <SearchBar
            value={searchInput}
            onChange={inputHandler}
            placeholder='Type Tag'
            keyUpCallout={generateTagObj}
          />
        </div>
        {/* Search Input Tags Results */}
        {searchTags.length > 0 && (
          <SuggestedTags
            tags={searchTags}
            updateSuggestedTags={addTags}
            tagGroup='searched'
            productCount={settings ? 0 : productIds?.length || 0}
          />
        )}
      </div>
      <div className='right-section'>
        <div>
          {!settings ? (
            <p>
              {isEdit ? (
                'Tags associated with this product:'
              ) : (
                <span>
                  {c('theseTagsWillBeAddedToProduct', {
                    count: productIds?.length,
                  })}
                </span>
              )}
            </p>
          ) : (
            <p>
              <span>These tags will be added to this brand:</span>
            </p>
          )}
          {loading ? (
            <div className='global-filter-column'>
              <ListLoading numberOfRows={6} />
            </div>
          ) : selectedTags.length > 0 ? (
            selectedTags.map((e, i) => {
              return <TagListItem key={e.name} tag={e} updateTags={removeTag} />
            })
          ) : (
            <div className='no-tags'>No tags selected</div>
          )}
        </div>
      </div>
      <div className='button-section'>
        <Button onClick={() => close()}>Cancel</Button>
        <Button
          styleType='primary-green'
          onClick={submit}
          disabled={state.disabledTags}
        >
          Submit
        </Button>
      </div>
    </div>
  )
}

export default AddTags
