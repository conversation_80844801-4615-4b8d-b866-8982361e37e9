import React from 'react'
import { c } from '@predict-services'

import SimplePicker from '../SimplePicker/SimplePicker'
import { type Tag } from './AddTags'

interface SuggestedTagsProps {
  tags: Tag[]
  updateSuggestedTags: (tag: Tag, tagGroup?: string) => void
  tagGroup?: string
  productCount: number
}

const SuggestedTags: React.FC<SuggestedTagsProps> = ({
  tags,
  updateSuggestedTags,
  tagGroup,
  productCount,
}) => {
  let count = 0
  return (
    <div className='suggested-tags'>
      {tags.length > 0 && (
        <span className='description'>
          {c('tapToAddTagsToProduct', { count: productCount })}
        </span>
      )}

      <div className={`suggested-tags-container ${tagGroup ? tagGroup : ''}`}>
        {tags.map((e, i) => {
          if (count === 20) {
            count = 0
          }
          count++
          return (
            <SimplePicker
              key={i}
              text={e.name}
              active={e.active}
              callout={() => updateSuggestedTags(e, tagGroup)}
              count={count}
            />
          )
        })}
      </div>
    </div>
  )
}

export default SuggestedTags
