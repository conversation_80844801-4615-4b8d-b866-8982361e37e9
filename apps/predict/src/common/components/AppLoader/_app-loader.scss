@use '@patterninc/react-ui/dist/variables' as variables;

.app-loader {
  background: var(--white);
  height: 100vh;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  @media only screen and (min-width: variables.$breakpoint-xxl) {
    position: relative;
    top: -72px;
  }
  .message {
    margin-bottom: 135px;
    color: var(--purple);
    font-weight: var(--font-weight-regular);
  }
}
