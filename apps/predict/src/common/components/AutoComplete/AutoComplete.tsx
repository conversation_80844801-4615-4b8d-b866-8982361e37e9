import React, { type FormEvent, type ReactNode } from 'react'

interface AutoCompleteProps {
  children: ReactNode
  autoComplete?: string
}

const AutoComplete = ({ children, autoComplete }: AutoCompleteProps) => {
  return (
    <form
      autoComplete={autoComplete || 'off'}
      onSubmit={(e: FormEvent<HTMLFormElement>) => {
        e.preventDefault()
        return false
      }}
    >
      {children}
    </form>
  )
}

export default AutoComplete
