import React from 'react'
import { ListLoading } from '@patterninc/react-ui'

import styles from './_brand-group.module.scss'
import { brandGroupConstants } from './BrandGroupConstants'
import {
  type IndividualBrandGroupData,
  type WorkflowPropType,
} from './BrandGroupTypes'

type propTypes = {
  abgPresentationData?: IndividualBrandGroupData[] | undefined
  abgPresentationStatus: 'idle' | 'success' | 'error' | 'pending' | undefined
  setWorkflowType: ({ isOpen, type, groupData }: WorkflowPropType) => void
}

const AbgBrandsListAndMarketplacesCount = ({
  abgPresentationData,
  abgPresentationStatus = 'pending',
  setWorkflowType,
}: propTypes) => {
  const { AUTOMATED_BRANDS_MARKETPLACES } = brandGroupConstants

  const handleMarketplaceCountClick = (pdata: IndividualBrandGroupData) => {
    setWorkflowType({
      isOpen: true,
      type: AUTOMATED_BRANDS_MARKETPLACES,
      groupData: pdata,
    })
  }

  return abgPresentationStatus === 'success' ? (
    abgPresentationData?.map((pdata) => (
      <li className={styles.abgBrandsListContainer} key={pdata.customer_id}>
        <span className={styles.abgBrandsList}>{pdata.customer_name}</span>
        <div
          className={`${styles.abgMarketplacesCount} fw-medium`}
          onClick={() => handleMarketplaceCountClick(pdata)}
        >
          {`${pdata?.marketplace_id?.length} ${
            pdata?.marketplace_id?.length === 1 ? 'MARKETPLACE' : 'MARKETPLACES'
          }`}
        </div>
      </li>
    ))
  ) : (
    <ListLoading />
  )
}

export default AbgBrandsListAndMarketplacesCount
