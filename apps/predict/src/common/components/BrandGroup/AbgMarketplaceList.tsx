import React from 'react'

import styles from './_brand-group.module.scss'

type propTypes = {
  marketplaces?: Array<string>
}

const AbgMarketplacesList = ({ marketplaces }: propTypes) => {
  return marketplaces?.map((mrktplace) => (
    <li className={styles.abgBrandsListContainer} key={mrktplace}>
      <span className={styles.abgBrandsList}>{mrktplace}</span>
    </li>
  ))
}

export default AbgMarketplacesList
