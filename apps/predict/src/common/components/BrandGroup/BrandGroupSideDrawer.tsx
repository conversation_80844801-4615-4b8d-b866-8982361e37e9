import React, {
  use<PERSON><PERSON>back,
  useContext,
  useEffect,
  useMemo,
  useState,
} from 'react'
import {
  Button,
  EmptyState,
  getApiUrlPrefix,
  Icon,
  SearchBar,
  SideDrawer,
  type SortColumnProps,
  StandardTable,
  TextInput,
  toast,
  useMediaQuery,
} from '@patterninc/react-ui'
import {
  c,
  isPatternUser as isPatternUserHelper,
  SecureAxios,
} from '@predict-services'

import { ThemeContext } from '../../../Context'
import SwitchWrapper from '../SwitchWrapper/SwitchWrapper'
import styles from './_brand-group.module.scss'
import AbgBrandsListAndMarketplacesCount from './AbgBrandsListAndMarketplacesCount'
import AbgMarketplacesList from './AbgMarketplaceList'
import { brandGroupConstants } from './BrandGroupConstants'
import {
  type BrandDataProps,
  type BrandGroupSideDrawerProps,
  type BrandRowProp,
  type ErrorProps,
  type IndividualBrandGroupData,
  type SharedBrandGroupDataProps,
  type ToastTypes,
} from './BrandGroupTypes'
import ShareBrandGroupSideDrawer from './ShareBrandGroupSideDrawer'

const {
  CREATE_TYPE,
  EDIT_TYPE,
  SETUP_TYPE,
  MY_BRANDS_LOWERCASE,
  MY_BRANDS_TITLE_CASE,
  AUTOMATED_BRANDS,
  AUTO_BRAND_GROUPS,
  AUTOMATED_BRANDS_MARKETPLACES,
  SHARE_TYPE,
} = brandGroupConstants

const BrandGroupSideDrawer = ({
  isDrawerOpen,
  setDrawerOpen,
  brandGroupName = '',
  customers,
  workflowType = CREATE_TYPE,
  selectedBrandGroupToUpdate,
  updateRefetchCustomer,
  brandGroupsData,
  abgPresentationData,
  abgPresentationStatus,
  setWorkflowType,
  abgMarketplacesByBrand = [],
  listOfBrandsAssignedToAbg,
  setShareGroupSideDrawerOpen,
  shareGroupSideDrawerOpen,
  nonGlobal,
}: BrandGroupSideDrawerProps): React.JSX.Element => {
  const isMobileView = useMediaQuery({ type: 'max', breakpoint: 'md' })
  const isPatternUser = isPatternUserHelper()
  const [sharedBrandGroupData, setSharedBrandGroupData] =
    useState<SharedBrandGroupDataProps>({
      sharedUserList: selectedBrandGroupToUpdate?.shared_with_user_ids ?? [],
      sharedUserGroups: selectedBrandGroupToUpdate?.shared_with_group_ids ?? [],
      internalUserToggle: false,
      isSDButtonClicked: false,
    })
  const {
    isSDButtonClicked,
    sharedUserList,
    sharedUserGroups,
    internalUserToggle,
  } = sharedBrandGroupData
  const {
    customer: user,
    brandGroupCustomer,
    removeBrandGroupState,
    updateBrandGroupState,
  } = useContext(ThemeContext)
  const showSharedGroupEditOption = workflowType !== SETUP_TYPE && isPatternUser
  const getCustomerList = useMemo(() => {
      return customers.map((customer) => {
        return {
          brandId: customer.id,
          brandName: customer.customer_name,
          inGroup:
            workflowType === EDIT_TYPE ||
            workflowType === SHARE_TYPE ||
            selectedBrandGroupToUpdate?.name === '' ||
            selectedBrandGroupToUpdate?.name === MY_BRANDS_TITLE_CASE
              ? (selectedBrandGroupToUpdate?.customer_ids?.includes(
                  customer.id,
                ) ?? false)
              : false,
          vendorId: Number(customer?.vendor_id),
        }
      })
    }, [
      customers,
      selectedBrandGroupToUpdate?.customer_ids,
      selectedBrandGroupToUpdate?.name,
      workflowType,
    ]),
    brandGroupAPI = `${getApiUrlPrefix('iserve')}/api/v6/user_brand_groups`,
    brandGroupContextData =
      brandGroupCustomer as IndividualBrandGroupData | null

  const [state, setState] = useState({
      brandsGroupName: selectedBrandGroupToUpdate?.name || '',
      brandList: getCustomerList,
      savingDataInProgress: false,
      setUpTogglechecked: !(selectedBrandGroupToUpdate?.name === ''),
      isEmptySearchResult: false,
      searchedBrandList: getCustomerList,
    }),
    [search, setSearch] = useState<string>('')
  const [sort, setSort] = useState({
    prop: 'id',
    flip: false,
  })

  const {
    brandsGroupName,
    brandList,
    savingDataInProgress,
    setUpTogglechecked,
    isEmptySearchResult,
    searchedBrandList,
  } = state

  const isAutomatedBrandGroup =
    selectedBrandGroupToUpdate?.grouping_category === AUTO_BRAND_GROUPS
  const isNotAutoBrandGroup =
    !isAutomatedBrandGroup && workflowType !== AUTOMATED_BRANDS_MARKETPLACES
  const isCreatnewBrandGroup =
    workflowType !== SETUP_TYPE && isNotAutoBrandGroup
  const layer2Heading =
    `${selectedBrandGroupToUpdate?.name?.toString()} - brands`.toUpperCase()
  const layer3Heading =
    `${selectedBrandGroupToUpdate?.customer_name?.toString()} - marketplace assignments`.toUpperCase()

  const getBrandGroupDrawerTitle = useMemo(() => {
    switch (workflowType) {
      case CREATE_TYPE:
        return 'Create Brand Group'
      case EDIT_TYPE:
        return 'Edit Brand Group'
      case AUTOMATED_BRANDS:
        return selectedBrandGroupToUpdate?.name ? layer2Heading : ''
      case AUTOMATED_BRANDS_MARKETPLACES:
        return selectedBrandGroupToUpdate?.customer_name ? layer3Heading : ''
      default:
        return 'Setup My Brands'
    }
  }, [
    layer2Heading,
    layer3Heading,
    selectedBrandGroupToUpdate?.customer_name,
    selectedBrandGroupToUpdate?.name,
    workflowType,
  ])

  const toastCreator = useCallback(
    (message: string, type: ToastTypes, autoClose: number | false) => {
      const toastId = `${brandGroupName}_brand_group_toast`

      toast({
        message: message,
        type: type,
        config: {
          toastId,
          autoClose: autoClose,
        },
      })
    },
    [brandGroupName],
  )

  const setSortBy: SortColumnProps['sorter'] = (sortObj) => {
    setSort((prevState) => ({
      ...prevState,
      prop: sortObj.activeColumn,
      flip: sortObj.direction,
    }))
  }

  const brandGroupNameParam = useMemo(() => {
    if (workflowType !== SETUP_TYPE) {
      return brandsGroupName
    } else if (workflowType === SETUP_TYPE && !setUpTogglechecked) {
      return ''
    } else {
      return MY_BRANDS_TITLE_CASE
    }
  }, [brandsGroupName, setUpTogglechecked, workflowType])

  const errorResponseToastHandler = useCallback(
    (err: ErrorProps, type: string) => {
      if (
        err?.status === 422 &&
        typeof err?.data === 'object' &&
        err?.data?.message?.includes('duplicate key')
      ) {
        toastCreator('Brand group name already exist', 'error', 5000)
      } else if (err?.status === 422 && typeof err?.data === 'string') {
        toastCreator(err?.data, 'error', 5000)
      } else {
        toastCreator(`Failed to ${type} Brand Group`, 'error', 5000)
      }

      setState((prevState) => ({
        ...prevState,
        savingDataInProgress: false,
      }))
    },
    [toastCreator],
  )

  const createEditBrandGroup = useCallback(
    ({
      customerIds = [],
      isDelete = false,
      vendorIds = [],
      brandData = [],
    }: {
      customerIds?: number[]
      isDelete?: boolean
      vendorIds?: number[]
      brandData?: BrandDataProps[]
    }) => {
      const params = {
        name: brandGroupNameParam.toString().trim(),
        user_id: user.id,
        is_my_brand_group:
          workflowType === SETUP_TYPE ||
          selectedBrandGroupToUpdate?.name === MY_BRANDS_TITLE_CASE
            ? true
            : false,
        ...(customerIds?.length > 0 ? { customer_ids: customerIds } : {}),
        ...(vendorIds?.length > 0 ? { vendor_ids: vendorIds } : {}),
        ...(brandData?.length > 0 ? { brand_data: brandData } : {}),
        ...(sharedUserList?.length > 0
          ? { shared_user_ids: sharedUserList }
          : {}),
        ...(sharedUserGroups?.length > 0
          ? { shared_group_ids: sharedUserGroups }
          : {}),
        ...(internalUserToggle
          ? { shared_with_pattern_user: internalUserToggle }
          : {}),
      }

      setState((prevState) => ({
        ...prevState,
        savingDataInProgress: true,
      }))

      switch (true) {
        /* this case handles deletion of setup brand group based on condition:-
       if no setup brand is selected and setup my brand toggle is off */
        case workflowType === SETUP_TYPE &&
          !customerIds.length &&
          !setUpTogglechecked: {
          const apiUrl = `${brandGroupAPI}/${selectedBrandGroupToUpdate?.id}`
          SecureAxios.delete(apiUrl)
            .then(() => {
              setDrawerOpen(false)
              updateRefetchCustomer?.(true)
              toastCreator(
                'Your changes have been saved successfully',
                'success',
                5000,
              )
              setState((prevState) => ({
                ...prevState,
                savingDataInProgress: false,
              }))

              if (
                selectedBrandGroupToUpdate?.id === brandGroupContextData?.id
              ) {
                removeBrandGroupState()
              }
            })
            .catch((err) => {
              errorResponseToastHandler(err, 'update')
            })
          break
        }
        case workflowType === EDIT_TYPE ||
          workflowType === SHARE_TYPE ||
          selectedBrandGroupToUpdate?.name === MY_BRANDS_TITLE_CASE ||
          selectedBrandGroupToUpdate?.name === '': {
          const updateAPIUrl = `${brandGroupAPI}/${selectedBrandGroupToUpdate?.id}`
          SecureAxios({
            method: isDelete ? 'DELETE' : 'PUT',
            url: updateAPIUrl,
            ...(isDelete ? {} : { data: params }),
          })
            .then((response) => {
              // logic added on update or delete of brand group
              // if current selected brand group gets delete or update it updates the context

              if (
                selectedBrandGroupToUpdate?.id === brandGroupContextData?.id
              ) {
                removeBrandGroupState()
                if (!isDelete && response?.data) {
                  const responseData = response.data as unknown as {
                    name: string
                    customer_ids: number[]
                    vendor_ids: number[]
                    brand_data: BrandDataProps[]
                  }

                  updateBrandGroupState({
                    ...selectedBrandGroupToUpdate,
                    customer_name: responseData.name,
                    customer_ids: responseData.customer_ids,
                    vendor_ids: responseData.vendor_ids,
                    brand_data: responseData.brand_data,
                  })
                }
              }

              updateRefetchCustomer?.(true)
              setDrawerOpen(false)
              toastCreator(
                internalUserToggle
                  ? `Brand group updated. User permissions are being updated; the changes may take some time to take effect.`
                  : `Brand Group ${
                      isDelete ? 'Deleted' : 'Saved'
                    } Successfully`,
                'success',
                5000,
              )
              setState((prevState) => ({
                ...prevState,
                savingDataInProgress: false,
              }))
            })
            .catch((err) => {
              errorResponseToastHandler(err, 'update')
            })
          break
        }
        default:
          // Default case is for create brand group workflow.
          SecureAxios.post(brandGroupAPI, params)
            .then(() => {
              setDrawerOpen(false)
              updateRefetchCustomer?.(true)
              toastCreator(
                workflowType === SETUP_TYPE
                  ? 'Your changes have been saved successfully'
                  : internalUserToggle
                    ? 'Brand Group Created Successfully. User permissions are being updated; the changes may take some time to take effect'
                    : 'Brand Group Created Successfully',
                'success',
                5000,
              )
              setState((prevState) => ({
                ...prevState,
                savingDataInProgress: false,
              }))
            })
            .catch((err) => {
              errorResponseToastHandler(err, 'create')
            })
          break
      }
    },
    [
      brandGroupAPI,
      brandGroupContextData?.id,
      brandGroupNameParam,
      errorResponseToastHandler,
      internalUserToggle,
      removeBrandGroupState,
      selectedBrandGroupToUpdate,
      setDrawerOpen,
      setUpTogglechecked,
      sharedUserGroups,
      sharedUserList,
      toastCreator,
      updateBrandGroupState,
      updateRefetchCustomer,
      user.id,
      workflowType,
    ],
  )

  const toggleHandler = useCallback(
    (toggleStatus: boolean, brandName: string) => {
      const updatedBrandList = brandList.map((brand) => {
        if (brand.brandName === brandName) {
          return {
            ...brand,
            inGroup: toggleStatus,
          }
        }
        return brand
      })

      let updatedSearchedBrandList = searchedBrandList

      if (search.toString() !== '') {
        updatedSearchedBrandList = searchedBrandList.map((brand) => {
          if (brand.brandName === brandName) {
            return {
              ...brand,
              inGroup: toggleStatus,
            }
          }
          return brand
        })
      }

      setState((prevState) => ({
        ...prevState,
        brandList: updatedBrandList,
        searchedBrandList: updatedSearchedBrandList,
      }))
    },
    [brandList, search, searchedBrandList],
  )

  const setUpToggleHandler = (isToggleOn: boolean) => {
    setState((prevState) => ({
      ...prevState,
      setUpTogglechecked: isToggleOn,
    }))
  }

  const searchInputHandler = (value: string) => {
    setSearch(value ?? '')
    const inputValue = value.toString()
    const searchResultList = brandList.filter((list) => {
      return list['brandName']
        .toLowerCase()
        .normalize('NFD')
        .replace(/[\u0300-\u036f]/g, '')
        .includes(inputValue.toLowerCase())
    })

    const searchedBrandlist =
      searchResultList.length > 0 && inputValue !== ''
        ? searchResultList
        : getCustomerList

    setState((prevState) => ({
      ...prevState,
      isEmptySearchResult: searchResultList.length < 1,
      searchedBrandList: searchedBrandlist,
    }))
  }
  const selectedBrandCount = useMemo(
    () => brandList?.filter((item) => item.inGroup === true).length,
    [brandList],
  )

  const handleSaveChangeButton = useCallback(() => {
    const selectGroupBrands: string[] = []
    const selectedGroupBrandsIds: number[] = []
    const selectedGroupVendorIds: number[] = []
    const selectedGroupBrandData: BrandDataProps[] = []
    brandList.forEach((brand) => {
      if (brand.inGroup) {
        selectGroupBrands.push(brand.brandName)
        selectedGroupBrandsIds.push(brand.brandId)
        selectedGroupVendorIds.push(brand.vendorId)
        selectedGroupBrandData.push({
          customer_id: brand.brandId,
          customer_name: brand.brandName,
          vendor_id: brand.vendorId,
          user_brand_group_id: selectedBrandGroupToUpdate?.id ?? null,
        })
      }
    })

    const formattedBrandGroupName = brandsGroupName
      .toString()
      .toLowerCase()
      .trim()

    const hasExistingBrand = brandGroupsData.filter(
      (brand) =>
        formattedBrandGroupName === brand.name.toLowerCase() &&
        selectedBrandGroupToUpdate?.id !== brand.id,
    )

    switch (true) {
      // static validation for brand group name should not be my brands
      case typeof brandsGroupName === 'string' &&
        brandsGroupName.toLowerCase() === MY_BRANDS_LOWERCASE &&
        selectedBrandGroupToUpdate?.name.toString().toLowerCase() !==
          MY_BRANDS_LOWERCASE:
        return toastCreator(
          'This Brand Group name already exists or is invalid. Please provide a different name above and then save your changes.',
          'error',
          5000,
        )
      case hasExistingBrand.length > 0:
        return toastCreator(
          'This Brand Group name already exists. Please provide a different name above and then save your changes.',
          'error',
          5000,
        )
      case brandsGroupName === '' && workflowType !== SETUP_TYPE:
        return toastCreator(
          'This Brand Group does not have a name. Please provide a name above and then save your changes.',
          'warning',
          5000,
        )
      // allow less than 2 brands selection save in case of setup my brand toggle is unchecked and type is setup
      case (workflowType !== SETUP_TYPE && selectGroupBrands.length < 2) ||
        (setUpTogglechecked &&
          workflowType === SETUP_TYPE &&
          selectGroupBrands.length < 2):
        return toastCreator(
          'A Brand Group must consist of at least 2 Brands. Please select the brands you want to include in this new group and save your changes.',
          'warning',
          5000,
        )
      default:
        createEditBrandGroup({
          customerIds: selectedGroupBrandsIds,
          vendorIds: selectedGroupVendorIds,
          brandData: selectedGroupBrandData,
        })
    }
  }, [
    brandGroupsData,
    brandList,
    brandsGroupName,
    createEditBrandGroup,
    selectedBrandGroupToUpdate?.id,
    selectedBrandGroupToUpdate?.name,
    setUpTogglechecked,
    toastCreator,
    workflowType,
  ])
  useEffect(() => {
    if (isSDButtonClicked) {
      handleSaveChangeButton()
      setShareGroupSideDrawerOpen(false)
      setSharedBrandGroupData((prevState) => ({
        ...prevState,
        isSDButtonClicked: false,
      }))
    }
  }, [
    handleSaveChangeButton,
    sharedBrandGroupData,
    isSDButtonClicked,
    setShareGroupSideDrawerOpen,
  ])

  const config = useMemo(() => {
    return [
      {
        name: 'brandName',
        label: 'Brand',
        noSort: true,
        cell: {
          children: (rowData: BrandRowProp) => {
            return <span>{rowData.brandName}</span>
          },
        },
      },
      {
        name: '',
        label: 'Include',
        noSort: true,
        cell: {
          children: (rowData: BrandRowProp) => {
            return (
              <span>
                <SwitchWrapper
                  checked={rowData.inGroup}
                  callout={(value: boolean) =>
                    toggleHandler(value, rowData.brandName)
                  }
                  options={{
                    disabled: {
                      text: '',
                    },
                    enabled: {
                      text: '',
                    },
                  }}
                  customClass={undefined}
                  disabled={undefined}
                />
              </span>
            )
          },
        },
      },
    ]
  }, [toggleHandler])

  const isDisableEditOption = useMemo(() => {
    return !brandsGroupName || selectedBrandCount < 2 || savingDataInProgress
  }, [brandsGroupName, selectedBrandCount, savingDataInProgress])

  return (
    <>
      <SideDrawer
        isOpen={isDrawerOpen}
        closeCallout={() => {
          // Below if condition is used to pass the data from 3rd to the 2nd layer of automated brand group side drawer
          // to display the list of brands assigned to ABG.
          // if condition will trigger with this actions - Automated Brand Groups > 10 Brands > 10 Martetplaces > back/close button or outside click
          if (getBrandGroupDrawerTitle.includes('MARKETPLACE ASSIGNMENTS')) {
            setWorkflowType({
              isOpen: true,
              type: AUTOMATED_BRANDS,
              groupData: listOfBrandsAssignedToAbg || null,
            })
          } else {
            setDrawerOpen(false)
          }
        }}
        headerContent={getBrandGroupDrawerTitle}
        containerClassName={styles.sideDrawerContainer}
        noFooterPadding
        size={nonGlobal ? 'md' : undefined}
        footerContent={
          <>
            {showSharedGroupEditOption && (
              <div
                className={`${
                  isDisableEditOption ? styles.disabled : ''
                } flex justify-content-between bdrb bdrc-light-gray pat-p-4`}
              >
                <div className='flex align-items-center pat-gap-2 fs-12'>
                  <Icon icon='share2' iconSize='16px' />
                  <div>Share with multiple users</div>
                </div>
                <div>
                  <Button
                    as='button'
                    styleType='text-blue'
                    disabled={isDisableEditOption}
                    onClick={() => setShareGroupSideDrawerOpen(true)}
                  >
                    Share
                  </Button>
                </div>
              </div>
            )}
            <div className='flex justify-content-between pat-py-2.5 pat-px-4'>
              {isNotAutoBrandGroup ? (
                <>
                  <div className='pat-mt-2'>
                    {workflowType === EDIT_TYPE && (
                      <Button
                        as='confirmation'
                        styleType='text-red'
                        confirmation={{
                          body: 'This Brand group will no longer appear in your brand groups list. You can always make a new brand group to replace this group in the future.',
                          confirmCallout: () =>
                            createEditBrandGroup({
                              isDelete: true,
                            }),
                          header: 'Delete This Brand Group',
                          type: 'red',
                          confirmButtonText: 'Delete Brand Group',
                        }}
                      >
                        Delete
                      </Button>
                    )}
                  </div>
                  <div>
                    <Button
                      as='button'
                      className='pat-mr-4'
                      onClick={() => {
                        setDrawerOpen(false)
                      }}
                    >
                      Cancel
                    </Button>
                    <Button
                      as='button'
                      disabled={savingDataInProgress}
                      styleType='primary-green'
                      className=''
                      onClick={handleSaveChangeButton}
                    >
                      Save Changes
                    </Button>
                  </div>
                </>
              ) : null}
            </div>
          </>
        }
        layerPosition={getBrandGroupDrawerTitle === layer3Heading ? 3 : 2}
      >
        {!savingDataInProgress ? (
          <>
            {workflowType === SETUP_TYPE && (
              <div
                className={`pat-pb-4 pat-mb-4 fs-10 fc-purple bdrb bdrc-light-gray fw-semi-bold`}
              >
                BRAND ACCESS
              </div>
            )}
            <div className={isNotAutoBrandGroup ? 'pat-mb-4' : ''}>
              {isCreatnewBrandGroup && (
                <div className='pat-mb-4'>
                  <div className='fs-12 pat-mb-2 fc-purple'>
                    Brand Group Name
                  </div>
                  <TextInput
                    value={brandsGroupName}
                    callout={(_, value) => {
                      setState((prevState) => ({
                        ...prevState,
                        brandsGroupName: value,
                      }))
                    }}
                    maxLength={30}
                    type='text'
                    stateName='brandGroupName'
                    // User cannot update the "My brands" name. They can only delete the brand "My Brands".
                    disabled={
                      typeof selectedBrandGroupToUpdate?.name === 'string' &&
                      selectedBrandGroupToUpdate?.name.toLowerCase() ===
                        MY_BRANDS_LOWERCASE
                    }
                    debounce={250}
                  />
                </div>
              )}
              <div>
                {isCreatnewBrandGroup && (
                  <>
                    <div className='fs-12 pat-mb-2 fc-purple'>
                      Select Brands
                    </div>
                    <div className='full-width pat-mb-4'>
                      <SearchBar
                        value={search}
                        onChange={searchInputHandler}
                        placeholder='Search Brands'
                      />
                    </div>
                  </>
                )}
              </div>

              {isNotAutoBrandGroup && (
                <StandardTable
                  data={
                    search.toString() === '' ? brandList : searchedBrandList
                  }
                  config={config}
                  dataKey='brandName'
                  hasData={!isEmptySearchResult && !!brandList.length}
                  showGroups
                  groups={[
                    {
                      check: (dataItem) => dataItem.inGroup,
                      groupHeader: 'Selected Brands',
                    },
                    {
                      check: (dataItem) => !dataItem.inGroup,
                      groupHeader: 'Unselected Brands',
                    },
                  ]}
                  hasMore={false}
                  successStatus={true}
                  loading={false}
                  tableId={`brand-group-resp-table`}
                  noDataFields={{
                    primaryText: c('noBrandsFound'),
                  }}
                  sort={setSortBy}
                  sortBy={sort}
                  getData={() => null}
                  // turning off the mobile responsive view to adjust the height of the table to prevent overflow
                  customWidth='400'
                  // Adjusting responsive view as brandname input box will available based on workflow type
                  customHeight={`calc(100vh - ${
                    isMobileView
                      ? workflowType !== SETUP_TYPE
                        ? showSharedGroupEditOption
                          ? '343px'
                          : '313px'
                        : '276px'
                      : workflowType === SETUP_TYPE
                        ? '284px'
                        : showSharedGroupEditOption
                          ? '343px'
                          : '305px'
                  })`}
                  noMobileView={true}
                />
              )}
            </div>

            {isAutomatedBrandGroup && (
              <AbgBrandsListAndMarketplacesCount
                abgPresentationData={abgPresentationData}
                abgPresentationStatus={abgPresentationStatus}
                setWorkflowType={setWorkflowType}
              />
            )}

            {workflowType === AUTOMATED_BRANDS_MARKETPLACES && (
              <AbgMarketplacesList marketplaces={abgMarketplacesByBrand} />
            )}

            {workflowType === SETUP_TYPE && (
              <div className='flex justify-content-start pat-mt-4 pat-mb-4 pat-ml-4'>
                <div>
                  <SwitchWrapper
                    checked={setUpTogglechecked}
                    callout={(value: boolean) => setUpToggleHandler(value)}
                    options={{
                      disabled: {
                        text: '',
                      },
                      enabled: {
                        text: '',
                      },
                    }}
                    customClass={undefined}
                    disabled={undefined}
                  />
                </div>
                <div className='pat-ml-2 pat-mr-4 fs-12 fc-purple'>
                  Save Brand Group called “My Brands”
                </div>
              </div>
            )}
          </>
        ) : (
          <EmptyState
            primaryText='Hang on for one moment.'
            secondaryText='Saving Changes...'
          />
        )}
      </SideDrawer>

      <ShareBrandGroupSideDrawer
        setShareGroupSideDrawerOpen={setShareGroupSideDrawerOpen}
        shareGroupSideDrawerOpen={shareGroupSideDrawerOpen}
        selectedBrandGroupToUpdate={selectedBrandGroupToUpdate}
        setSharedBrandGroupData={setSharedBrandGroupData}
        nonGlobal={nonGlobal}
      />
    </>
  )
}

export default BrandGroupSideDrawer
