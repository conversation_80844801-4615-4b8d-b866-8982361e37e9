export interface CustomersProps {
  id: number
  customer_name: string
  vendor_id?: number
}

export interface BrandDataProps {
  user_brand_group_id: number | null
  customer_id: number
  customer_name: string
  vendor_id: number | null
}

export interface IndividualBrandGroupData {
  brand_count: number
  brand_data: BrandDataProps[]
  customer_ids: number[]
  customer_name: string
  grouping_category: string
  id: number
  is_my_brand_group: null | boolean
  label: string
  name: string | number
  vendor_ids: number[]
  isExpanded?: boolean
  reportees?: AutoBrandGroupBaseProps[] | []
  showExpandIcon?: boolean
  parent_id?: number
  customer_id?: number
  marketplace_id?: Array<number>
  marketplace_name?: Array<string>
  vendor_id?: number
  shared_with_group_ids?: number[]
  shared_with_user_ids?: number[]
  shared_with?: string
  shared_by_user_name?: string
  shared_brand_group_id?: number
}

export interface AutoBrandGroupBaseProps {
  brand_count: number
  customer_ids: number[]
  customer_name: string
  grouping_category: string
  id: number
  name: string
  vendor_ids: number[]
  isReportee?: boolean
  [key: `isReportee${number}`]: boolean
  isNestedReportee?: boolean
  isLastReportee?: boolean
  isForthReportee?: boolean
  isFifthReportee?: boolean
  isExpanded?: boolean
  showExpandIcon?: boolean
  reportees: AutoBrandGroupBaseProps[] | []
  parent_id: number
  user_name: string
}

interface Customer {
  id: number
  customer_name: string
}

export type BrandGroupDataPropsType = Customer & {
  customer_id: number
  name: string
  label: string
  grouping_category: string
  is_my_brand_group: boolean
}

export type abgPresentationDataProps = {
  customer_id: number
  customer_name: string
  marketplace_id: Array<number>
  marketplace_name: Array<string>
  vendor_id: number
}

export type WorkflowPropType = {
  isOpen: boolean
  type: string
  groupData: IndividualBrandGroupData | null
}

export interface BrandGroupSideDrawerProps {
  isDrawerOpen: boolean
  setDrawerOpen: (flag: boolean) => void
  brandGroupName?: string | number
  customers: CustomersProps[]
  workflowType: string
  selectedBrandGroupToUpdate: IndividualBrandGroupData | null
  updateRefetchCustomer?: (flag: boolean) => void
  brandGroupsData: BrandGroupDataPropsType[]
  abgPresentationData?: IndividualBrandGroupData[]
  abgPresentationStatus?: 'idle' | 'success' | 'error' | 'pending'
  setWorkflowType: ({ isOpen, type, groupData }: WorkflowPropType) => void
  abgMarketplacesByBrand?: Array<string>
  listOfBrandsAssignedToAbg?: IndividualBrandGroupData | null
  setShareGroupSideDrawerOpen: React.Dispatch<React.SetStateAction<boolean>>
  shareGroupSideDrawerOpen: boolean
  nonGlobal?: boolean
}

export interface BrandRowProp {
  brandName: string
  inGroup: boolean
}

export type ToastTypes = 'success' | 'error' | 'warning' | 'info'

export type ErrorProps = {
  status: number
  data:
    | string
    | {
        message: string
      }
}

export interface SharedBrandGroupsSDProps {
  shareGroupSideDrawerOpen: boolean
  setShareGroupSideDrawerOpen: React.Dispatch<React.SetStateAction<boolean>>
  selectedBrandGroupToUpdate: IndividualBrandGroupData | null
  setSharedBrandGroupData: React.Dispatch<
    React.SetStateAction<SharedBrandGroupDataProps>
  >
  nonGlobal?: boolean
}
export interface SharedBrandGroupDataProps {
  sharedUserGroups: number[]
  sharedUserList: number[]
  internalUserToggle: boolean
  isSDButtonClicked: boolean
}
export interface SharedBrandGroupStateProps {
  selectedUserGroups: string[]
  selectedUser: string[]
  intialSelectedUserGroups?: string[]
  intialSelectedUser?: string[]
  isInternalUserToggle: boolean
}
export interface UserListProps {
  id: number
  name: string
  email: string
  username: string
  has_shared_brand_groups: boolean
}
export interface UserGroupsProps {
  id: number
  name: string
  has_shared_group: boolean
}
export interface BrandListDrawerStateProps {
  isBrandListSDOpen: boolean
  brandListData: IndividualBrandGroupData | null
}
export interface BrandListSideDrawerProps {
  brandListSDDetails: BrandListDrawerStateProps
  setIsBrandListSDOpen: React.Dispatch<
    React.SetStateAction<BrandListDrawerStateProps>
  >
  nonGlobal?: boolean
}
