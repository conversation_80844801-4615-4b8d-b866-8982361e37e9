import React from 'react'
import {
  <PERSON>ert,
  Button,
  Cell,
  CustomTable,
  Row,
  SideDrawer,
} from '@patterninc/react-ui'

import {
  type BrandDataProps,
  type BrandListSideDrawerProps,
} from './BrandGroupTypes'
const BrandListSideDrawer = ({
  brandListSDDetails,
  setIsBrandListSDOpen,
  nonGlobal,
}: BrandListSideDrawerProps) => {
  const { brandListData, isBrandListSDOpen } = brandListSDDetails
  const sharedBy = brandListData?.shared_by_user_name
  const stickyTableConfig = {
    right: 0,
    left: 0,
  }
  const headers = [
    {
      name: 'brand',
      label: 'Brand',
      noSort: true,
    },
  ]
  const closeBrandListDrawer = () => {
    setIsBrandListSDOpen({
      isBrandListSDOpen: false,
      brandListData: null,
    })
  }
  return (
    <SideDrawer
      isOpen={isBrandListSDOpen}
      closeCallout={closeBrandListDrawer}
      headerContent={brandListData?.customer_name ?? 'Brand Group'}
      layerPosition={2}
      size={nonGlobal ? 'md' : undefined}
      footerContent={
        <div className='flex justify-content-end'>
          <Button
            as='button'
            className='pat-mr-4'
            onClick={closeBrandListDrawer}
          >
            Close
          </Button>
        </div>
      }
    >
      <>
        {sharedBy && (
          <Alert
            text={`${sharedBy} shared this brand group with you`}
            type='info'
          />
        )}
        <div className='flex justify-content-center'>
          {/* CustomTable is utilized in this context due to having a single column in the table. The standards table is not optimized for mobile devices when dealing with a single column layout. */}
          <CustomTable
            hasData
            successStatus
            hasMore={false}
            loading={false}
            tableId='brand-group-list-table'
            noDataFields={{
              primaryText: 'Brand groups not found',
              secondaryText:
                'Please use the edit option to include the brands ',
            }}
            sort={() => null}
            sortBy={{ prop: '', flip: false }}
            getData={() => {
              return
            }}
            headers={headers}
            stickyTableConfig={stickyTableConfig}
            customWidth='auto'
          >
            {brandListData?.brand_data?.map((data: BrandDataProps, index) => {
              return (
                <Row key={`${data.customer_id}-${index}`}>
                  <Cell
                    className={`last-sticky-cell-left `}
                    style={{ width: '400px' }}
                  >
                    {data.customer_name}
                  </Cell>
                </Row>
              )
            })}
          </CustomTable>
        </div>
      </>
    </SideDrawer>
  )
}

export default BrandListSideDrawer
