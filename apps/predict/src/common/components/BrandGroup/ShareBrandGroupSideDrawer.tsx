import React, { useMemo, useState } from 'react'
import { useQuery } from '@tanstack/react-query'
import {
  FormFooter,
  getApiUrlPrefix,
  MultiSelect,
  SideDrawer,
  Switch,
} from '@patterninc/react-ui'

import { useUser } from '../../../context/user-context'
import { c, SecureAxios } from '../../services'
import styles from './_brand-group.module.scss'
import {
  type SharedBrandGroupsSDProps,
  type SharedBrandGroupStateProps,
  type UserGroupsProps,
  type UserListProps,
} from './BrandGroupTypes'

const ShareBrandGroupSideDrawer = React.memo(
  ({
    shareGroupSideDrawerOpen,
    setShareGroupSideDrawerOpen,
    selectedBrandGroupToUpdate,
    setSharedBrandGroupData,
    nonGlobal,
  }: SharedBrandGroupsSDProps) => {
    const [shareBrandGroupState, setShareBrandGroupState] =
      useState<SharedBrandGroupStateProps>({
        selectedUserGroups: [],
        selectedUser: [],
        intialSelectedUserGroups: [],
        intialSelectedUser: [],
        isInternalUserToggle: false,
      })
    const {
      selectedUserGroups,
      selectedUser,
      isInternalUserToggle,
      intialSelectedUserGroups,
      intialSelectedUser,
    } = shareBrandGroupState
    const selectedBrandGroupId = selectedBrandGroupToUpdate?.id
    const url = `${getApiUrlPrefix('iserve')}/api/v7/groups`
    const userListUrl = `${getApiUrlPrefix(
      'iserve',
    )}/api/v7/user_brand_groups/users`
    const { user } = useUser()
    const hasAdminBrandGroupAccess = useMemo(
      () => user?.auth_roles.includes('write_brand_group_admin'),
      [user?.auth_roles],
    )

    const { data: groupsData, status: groupStatus } = useQuery({
      queryKey: ['groups_data', url, selectedBrandGroupId],
      queryFn: async () => {
        const params = {
          user_brand_group_id: selectedBrandGroupId,
        }
        try {
          const response = await SecureAxios.get(url, { params })
          return response.data
        } catch (err) {
          throw new Error(err instanceof Error ? err.message : String(err))
        }
      },
    })
    const { data: usersList, status: userListStatus } = useQuery({
      queryKey: ['users_list', userListUrl, selectedBrandGroupId],
      queryFn: async () => {
        const params = {
          user_brand_group_id: selectedBrandGroupId,
          user_type: 'Pattern',
        }
        try {
          const response = await SecureAxios.get(userListUrl, { params })
          return response.data
        } catch (err) {
          throw new Error(err instanceof Error ? err.message : String(err))
        }
      },
    })

    const groupsDataOptions = useMemo(() => {
      const allGroups: string[] = []
      const selectedGroups: string[] = []
      if (groupsData) {
        for (const user of groupsData) {
          const name = user?.name
          if (name) {
            allGroups.push(name)
            if (user.has_shared_group) {
              selectedGroups.push(name)
            }
          }
        }
        setShareBrandGroupState((prevState) => ({
          ...prevState,
          selectedUserGroups: selectedGroups,
          intialSelectedUserGroups: selectedGroups,
        }))
      }
      return allGroups
    }, [groupsData])

    const userOptions = useMemo(() => {
      const allUsers: string[] = []
      const selectedNames: string[] = []
      if (usersList) {
        for (const user of usersList) {
          const name = user?.name
          if (name) {
            allUsers.push(name)
            if (user.has_shared_brand_groups) {
              selectedNames.push(name)
            }
          }
        }
        setShareBrandGroupState((prevState) => ({
          ...prevState,
          selectedUser: selectedNames,
          intialSelectedUser: selectedNames,
        }))
      }
      return allUsers
    }, [usersList])

    const closeSideDrawer = () => {
      setShareGroupSideDrawerOpen(false)
    }
    const getSelectedUserIds = () => {
      return selectedUser.length
        ? usersList
            ?.filter((item: UserListProps) => selectedUser.includes(item.name))
            .map((item: UserListProps) => item.id)
        : []
    }
    const getSelectedGroupsIds = () => {
      return selectedUserGroups.length
        ? groupsData
            ?.filter((item: UserGroupsProps) =>
              selectedUserGroups.includes(item.name),
            )
            .map((item: UserGroupsProps) => item.id)
        : []
    }
    return (
      <SideDrawer
        isOpen={shareGroupSideDrawerOpen}
        closeCallout={() => closeSideDrawer()}
        headerContent='Share Brand Group'
        layerPosition={3}
        size={nonGlobal ? 'md' : undefined}
        footerContent={
          <FormFooter
            resetButtonProps={{
              onClick: () =>
                setShareBrandGroupState((prevState) => ({
                  ...prevState,
                  selectedUser: intialSelectedUser ?? [],
                  selectedUserGroups: intialSelectedUserGroups ?? [],
                  isInternalUserToggle: false,
                })),
            }}
            cancelButtonProps={{
              onClick: () => closeSideDrawer(),
            }}
            saveButtonProps={{
              onClick: () =>
                setSharedBrandGroupData({
                  isSDButtonClicked: true,
                  internalUserToggle: isInternalUserToggle,
                  sharedUserList: getSelectedUserIds(),
                  sharedUserGroups: getSelectedGroupsIds(),
                }),
              children: 'Save Changes',
            }}
          />
        }
      >
        <>
          {hasAdminBrandGroupAccess && (
            <>
              <div className='flex justify-content-between pat-pt-1'>
                <div
                  className={`fs-12  ${
                    hasAdminBrandGroupAccess ? '' : styles.disabled
                  }`}
                >
                  Share with all Internal Pattern Users
                </div>
                <Switch
                  disabled={!hasAdminBrandGroupAccess}
                  checked={isInternalUserToggle}
                  callout={() =>
                    setShareBrandGroupState((prevState) => ({
                      ...prevState,
                      isInternalUserToggle: !isInternalUserToggle,
                    }))
                  }
                />
              </div>
              <div className='pat-pt-5'>
                <MultiSelect
                  formLabelProps={{ label: c('shareWithUserGroups') }}
                  disabled={isInternalUserToggle || !hasAdminBrandGroupAccess}
                  loading={groupStatus === 'pending'}
                  options={groupsDataOptions.map((name, index) => ({
                    id: index,
                    name: name,
                  }))}
                  selectedOptions={selectedUserGroups.map((name, index) => ({
                    id: index,
                    name: name,
                  }))}
                  callout={(selectedList) => {
                    setShareBrandGroupState((prevState) => ({
                      ...prevState,
                      selectedUserGroups: selectedList.map((item) => item.name),
                      ...(selectedList.length ? { selectedUser: [] } : {}),
                    }))
                  }}
                  labelKey={'name'}
                  selectPlaceholder={c('searchUserGroup')}
                />
              </div>
            </>
          )}
          <div className='pat-pt-4'>
            <MultiSelect
              disabled={isInternalUserToggle || !!selectedUserGroups.length}
              formLabelProps={{ label: c('shareWithUsers') }}
              loading={userListStatus === 'pending'}
              options={userOptions.map((name, index) => {
                return { name: name, id: index }
              })}
              selectedOptions={selectedUser.map((name, index) => {
                return { name: name, id: index }
              })}
              callout={(selectedList) => {
                setShareBrandGroupState((prevState) => ({
                  ...prevState,
                  selectedUser: selectedList.map((item) => item.name),
                }))
              }}
              selectPlaceholder={c('searchUsers')}
              labelKey={'name'}
            />
          </div>
          <div className='fs-10 fc-purple pat-pt-1'>
            Users without access to one or more brands in this brand group will
            not see this group in their brand selector. We will notify the user
            if they do not have access.
          </div>
        </>
      </SideDrawer>
    )
  },
)

ShareBrandGroupSideDrawer.displayName = 'ShareBrandGroupSideDrawer'
export default ShareBrandGroupSideDrawer
