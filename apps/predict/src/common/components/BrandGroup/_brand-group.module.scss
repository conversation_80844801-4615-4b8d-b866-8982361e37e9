@use '@patterninc/react-ui/dist/variables' as variables;

.sideDrawerContainer {
  z-index: 9999;
}

.loadingDataCell {
  display: flex;
  justify-content: center;
  align-items: center;
  height: calc(100vh - 140px);
}

li.abgBrandsListContainer {
  display: grid;
  // 311px is the space for the first grid column to display the larger brands name
  grid-template-columns: 311px auto;
  margin-bottom: 0;
  line-height: 36px;
  font-size: var(--font-size-12);
  font-weight: var(--font-weight-regular);
  white-space: nowrap;
  transition: all 0.25s ease-in-out;
  padding-top: 0;
  padding-left: 8px;
  align-items: center;

  @media only screen and (max-width: variables.$breakpoint-md) {
    grid-template-columns: 1fr auto auto;
    line-height: 40px;
  }
}

.abgBrandsList {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.abgMarketplacesCount {
  cursor: pointer;
  font-size: var(--font-size-10);
  padding-left: 6px; // updated as per prototype style
  border-left-width: 1px;
  border-left-style: solid;
  text-transform: uppercase;
  border-color: var(--light-gray);
  color: var(--blue);
  // 92px is the width of the second grid column which displays like `10 MARKETPLACES`
  width: 92px;
}

.disabled {
  opacity: 0.5;
  pointer-events: none;
}
