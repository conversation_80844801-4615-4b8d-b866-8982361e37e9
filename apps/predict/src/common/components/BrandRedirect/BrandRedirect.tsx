import React, { useCallback, useEffect, useState } from 'react'
import { useLocation, useNavigate, useParams } from 'react-router-dom'
import {
  type BreadcrumbType,
  Button,
  getApiUrlPrefix,
} from '@patterninc/react-ui'
import { useRouteMatchTemp } from '@predict-services'
import {
  type Customer as AllCustomer,
  type Customers,
} from 'src/modules/Content/components/Pages/Content'
import { type Customer } from '@predict-types'

import { useUser } from '../../../context/user-context'
import {
  cancelGetCustomers,
  getCustomers,
} from '../../services/CustomerService'
import { handleError } from '../../services/HandleError'
import SecureAxios from '../../services/SecureAxios'
import AppLoader from '../AppLoader/AppLoader'

type BrandRedirectProps = {
  customer: Customers
  updateCustomer: (
    stateAttr: string,
    value: Customer,
    vendorId: number | null,
    callback?: () => void,
  ) => void
}

const BrandRedirect = ({ customer, updateCustomer }: BrandRedirectProps) => {
  const sellerPrefix = '/protect',
    productsPrefix = '/settings/overview'
  const [error] = useState(false)
  const location = useLocation()
  const navigate = useNavigate()
  const { baseUrl: url } = useRouteMatchTemp()
  const params = useParams<{ brandId: string; vendorId: string }>()

  const brandId = parseInt(params.brandId || ''),
    vendorId = parseInt(params.vendorId || '')

  const merchantId = location.pathname.replace(
    '/b/0/sellers/master-sellers/',
    '',
  )
  const { user } = useUser()

  const determineRoute = useCallback(
    (customers: AllCustomer[]) => {
      const pathArray = location.pathname.split('/')
      const prefix =
        pathArray[3] === 'sellers'
          ? sellerPrefix
          : pathArray[3] === 'products'
            ? productsPrefix
            : ''

      let newUrl = `${prefix}${location.pathname.slice(url.length, location.pathname.length)}`
      if (newUrl.startsWith('//')) newUrl = `${prefix}${newUrl.slice(1)}`

      switch (true) {
        case brandId === 0:
          if (customer.id !== 0) {
            const breadcrumbs = JSON.parse(
              localStorage.getItem('breadcrumbs') ?? '',
            )

            const url = `${getApiUrlPrefix(
              'iserve',
            )}/api/v6/merchants/${merchantId}/sellers?customer_id=${
              customer.id
            }`
            SecureAxios.get(url, {
              params: {},
            })
              .then((response) => {
                updateCustomer(
                  'customer',
                  {
                    id: customer.id,
                    customer_name: customer.customer_name,
                  } as Customer,
                  null,
                  () =>
                    breadcrumbs.map((breadcrumb: BreadcrumbType) => {
                      if (
                        response.data.data.length > 0 &&
                        breadcrumb?.name?.toLowerCase() === 'seller details'
                      ) {
                        return navigate(breadcrumb.link)
                      } else if (
                        response.data.data.length === 0 &&
                        breadcrumb?.name?.toLowerCase() === 'sellers'
                      ) {
                        return navigate(breadcrumb.link)
                      }
                      return breadcrumb
                    }),
                )
                return response.data
              })
              .catch((error) => {
                handleError(error, 'BrandRedirect.js', 'updateCustomer')
              })
          } else {
            updateCustomer(
              'customer',
              { id: 0, customer_name: 'All Brands' } as Customer,
              null,
              () => navigate(newUrl),
            )
          }
          break

        case brandId !== customer.id && vendorId !== customer.vendor_id: {
          const matchingCustomer = customers.find(
            (c) => c.id === brandId || c.vendor_id === vendorId,
          ) as Customer | undefined
          if (matchingCustomer) {
            updateCustomer('customer', matchingCustomer, null, () =>
              navigate(newUrl, {
                state: {
                  routedFrom: location?.state?.routedFrom,
                  search: location?.search,
                },
              }),
            )
          }
          break
        }
        default:
          navigate(newUrl)
          break
      }
    },
    [
      brandId,
      customer.customer_name,
      customer.id,
      customer.vendor_id,
      location?.search,
      location?.state?.routedFrom,
      merchantId,
      navigate,
      location.pathname,
      updateCustomer,
      url.length,
      vendorId,
    ],
  )

  useEffect(() => {
    const fetchCustomers = () => {
      getCustomers().then((response: AllCustomer[]) => {
        if (response) {
          const hasCustomerPermission =
            user.all_brands ||
            response
              .map((c) => {
                return c.id
              })
              .includes(brandId || vendorId)
          if (hasCustomerPermission) {
            determineRoute(response)
          } else {
            navigate('/insights')
          }
        }
      })
    }
    fetchCustomers()
  }, [brandId, determineRoute, navigate, user.all_brands, vendorId])

  useEffect(() => {
    return () => {
      cancelGetCustomers()
    }
  }, [])

  return (
    <div>
      {!error ? (
        <AppLoader />
      ) : (
        <div className='error-boundary-wrapper'>
          <div className='error-message-container'>
            <div className='title'>Something Went Wrong</div>
            <div className='text'>
              Sorry for the inconvenience, tap the button below and <br />
              we'll try to get everything back on track!
            </div>
            <Button
              styleType='primary-green'
              onClick={() => navigate(`/insights`)}
            >
              Reload Predict
            </Button>
          </div>
          <div className='robot-container'>
            <img
              className='broken-robot'
              src='/images/broken-robot.png'
              alt={'broken-robot'}
            />
          </div>
        </div>
      )}
    </div>
  )
}

export default BrandRedirect
