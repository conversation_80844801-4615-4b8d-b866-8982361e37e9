import React, {
  useCallback,
  useContext,
  useEffect,
  useRef,
  useState,
} from 'react'
import { useLocation, useNavigate, useResolvedPath } from 'react-router-dom'
import { useMutation, useQuery } from '@tanstack/react-query'
import {
  Button,
  getApiUrlPrefix,
  Icon,
  Menu,
  PopoverNew,
  toast,
} from '@patterninc/react-ui'
import {
  c,
  handleError,
  isInternalUser,
  SecureAxios,
  tryLocalStorageParse,
} from '@predict-services'

import { ThemeContext } from '../../../Context'
import { useUser } from '../../../context/user-context'
import { determineBrandType } from '../../../modules/Reports/components/Pages/CustomReports/helpers'
import { useFetchCustomerData } from '../../hooks/CustomHooks'
import { brandGroupConstants } from '../BrandGroup/BrandGroupConstants'
import BrandGroupSideDrawer from '../BrandGroup/BrandGroupSideDrawer'
import {
  type AutoBrandGroupBaseProps,
  type BrandDataProps,
  type BrandListDrawerStateProps,
  type IndividualBrandGroupData,
  type WorkflowPropType,
} from '../BrandGroup/BrandGroupTypes'
import BrandListSideDrawer from '../BrandGroup/BrandListSideDrawer'
import SelectWithPopoverDrawer from '../BrandSelectionDropDown/SelectWithPopoverDrawer/SelectWithPopoverDrawer'
import styles from './_brand-selection-dropdown.module.scss'
import type {
  CustomFooterConfigType,
  SecondaryValuesConfigType,
} from './SelectTypes/SelectTypes'

type SaveSharedBrandGroupProps = {
  group_shared_brand_group_id?: number
  user_shared_brand_group_id?: number
  brandGroupName?: string
}

interface Customer {
  id: number
  customer_name: string
  customer_ids?: number[]
}

type BrandDataPropsType = Customer & {
  state: string
} & IndividualBrandGroupData

type BrandGroupDataPropsType = Customer & {
  customer_id: number
  name: string
  label: string
  grouping_category: string
  is_my_brand_group: boolean
  brand_count: number
}

type BrandSelectionDropDownType = {
  refetchCustomer: boolean
  updateRefetchCustomer?: (flag: boolean) => void

  // Optional overrides for not updating the context
  selectedCustomer?: Customer
  updateSelectedCustomer?: (key: string, customer: Customer) => void

  // Optional prop to show the brand selector UI changes in notification creation
  isSingleBrandSelector?: boolean
}

type BrandDropDownState = {
  workflowType: string
  isBrandGroupDrawerOpen: boolean
  selectedBrandGroupToUpdate: IndividualBrandGroupData | null
}

const BrandSelectionDropDown = <ItemGeneric extends BrandDataPropsType>({
  refetchCustomer,
  updateRefetchCustomer,
  selectedCustomer,
  updateSelectedCustomer,
  isSingleBrandSelector,
}: BrandSelectionDropDownType): React.JSX.Element => {
  const { user } = useUser()
  const navigate = useNavigate()
  const {
    customer,
    updateCustomer,
    brandGroupCustomer,
    removeBrandGroupState,
    updateBrandGroupState,
  } = useContext(ThemeContext)

  const currentCustomer = selectedCustomer || customer,
    nonGlobal = !!selectedCustomer || !!updateSelectedCustomer

  const updateCurrentCustomer = (_: string, customer: Customer) => {
    if (nonGlobal) {
      const type = determineBrandType(customer)
      updateSelectedCustomer?.(type, customer)
    } else {
      updateCustomer(_, customer)
    }
  }

  const [state, setState] = useState<BrandDropDownState>({
    workflowType: 'create',
    isBrandGroupDrawerOpen: false,
    selectedBrandGroupToUpdate: null,
  })
  const [shareGroupSideDrawerOpen, setShareGroupSideDrawerOpen] =
    useState<boolean>(false)
  const [autoBrandGroupData, setAutoBrandGroupData] = useState<
    AutoBrandGroupBaseProps[]
  >([])
  const [listOfBrandsAssignedToAbg, setListOfBrandsAssignedToAbg] =
    useState<IndividualBrandGroupData | null>(null)
  const [searchedString, setSearchedString] = useState<string>('')
  const [isAccordionClicked, setIsAccordionClicked] = useState(false)
  const [brandListSDDetails, setBrandListSDDetails] =
    useState<BrandListDrawerStateProps>({
      isBrandListSDOpen: false,
      brandListData: null,
    })
  const basePath = useResolvedPath('').pathname,
    location = useLocation()

  const {
    AUTO_BRAND_GROUPS,
    AUTOMATED_BRANDS,
    BRAND_GROUP_SHARED_WITH_ME,
    SHARE_TYPE,
  } = brandGroupConstants

  const brandGroupContextData =
    brandGroupCustomer as IndividualBrandGroupData | null
  const isRenderedOnce = useRef<boolean>(false)
  const hasBrandGroupPermissions =
    user?.customers.length > 1 || user?.auth_roles.includes('write_brand_group')

  const { data: customerData, status: customerStatus } = useFetchCustomerData(
    refetchCustomer,
    updateRefetchCustomer,
    true,
  )

  const brandGroupsEndpoint = `${getApiUrlPrefix(
    'iserve',
  )}/api/v7/user_brand_groups`

  const { data: userBrandGroupsData, status: userBrandGroupApiStatus } =
    useQuery({
      queryKey: [brandGroupsEndpoint, refetchCustomer],
      queryFn: async ({ signal }) => {
        // Connect with real API URL in following PRs
        return SecureAxios.get(brandGroupsEndpoint, {
          params: { sort: 'name:asc' },
          signal,
        })
          .then((response) => {
            updateRefetchCustomer?.(false)
            setAutoBrandGroupData(response?.data?.auto_brand_groups)
            isRenderedOnce.current = true
            return {
              customBrandGroups: response?.data?.custom_groups.data,
              autoBrandGroups: response?.data?.auto_brand_groups,
              sharedBrandGroups: response?.data?.shared_brand_groups,
            }
          })
          .catch(() => {
            updateRefetchCustomer?.(false)
          })
      },
    })

  const abgPresentationEndpoint = `${getApiUrlPrefix(
    'iserve',
  )}/api/v7/user_brand_groups/${state?.selectedBrandGroupToUpdate?.id}`

  // API to get the list of brands and marketplaces assigned to ABG
  const { data: abgPresentationData, status: abgPresentationStatus } = useQuery(
    {
      queryKey: [abgPresentationEndpoint, refetchCustomer],
      queryFn: ({ signal }) => {
        const selectedBrandGroup = state?.selectedBrandGroupToUpdate
        if (
          !selectedBrandGroup?.id ||
          selectedBrandGroup?.grouping_category !== AUTO_BRAND_GROUPS
        ) {
          return []
        }
        return SecureAxios.get(abgPresentationEndpoint, {
          params: { include_customer_marketplace_names: true },
          signal,
        })
          .then((response) => {
            updateRefetchCustomer?.(false)
            return response.data.data
          })
          .catch(() => {
            updateRefetchCustomer?.(false)
          })
      },
    },
  )

  // Automated Brand Group section will now be visible for all routes because we have handled
  // the pages which does not support ABG by displaying No Brand Selected message on the page
  const isAbgVisible = !!(
    userBrandGroupsData?.autoBrandGroups &&
    userBrandGroupsData?.autoBrandGroups?.length
  )

  const brandGroupsData = userBrandGroupsData?.customBrandGroups
  const sharedBrandGroupsData = userBrandGroupsData?.sharedBrandGroups ?? []
  const isSharedBrandVisible = !!sharedBrandGroupsData?.length

  // redirect to brand details page
  const showBrandDetails = (customer: Customer) => {
    updateCustomer('customer', customer, null, () => {
      if (location?.pathname !== `${basePath}/details/list`) {
        navigate(`/brands/details/list`)
      }
    })
  }

  const setBrandGroupDrawerOpenStatus = (value: boolean) => {
    setState((prevState) => ({
      ...prevState,
      isBrandGroupDrawerOpen: value,
    }))
  }

  const setWorkflowType = ({ isOpen, type, groupData }: WorkflowPropType) => {
    setTimeout(() => {
      setState((prevState) => ({
        ...prevState,
        isBrandGroupDrawerOpen: isOpen,
        workflowType: type,
        selectedBrandGroupToUpdate: groupData,
      }))
    }, 100)
  }

  const enableSelectAllBrands = () =>
    user?.type === 'Pattern' && user?.all_brands

  const getFilteredBrandGroups = () =>
    brandGroupsData?.filter(
      (brandGroups: BrandGroupDataPropsType) => brandGroups?.name !== '',
    )

  const allAutoBrandGroups: AutoBrandGroupBaseProps[] = []
  /**
   * Function to form the SelectWithPopoverDrawer's optionsData compatible data
   * @param abGroups - @array of @object of auto brand group
   * @param level - number to identify current level of the hierarchy
   * @returns @array of @object of auto brand group
   */
  const getFilteredBAutoBrandGroups = (
    abGroups: AutoBrandGroupBaseProps[],
    level = 0,
  ) => {
    abGroups?.forEach((abg: AutoBrandGroupBaseProps) => {
      if (abg || searchedString) {
        if (level) {
          abg[`isReportee${level}`] = true
        }
        allAutoBrandGroups.push(abg)

        if (abg?.isExpanded || searchedString) {
          getFilteredBAutoBrandGroups(abg?.reportees, level + 1)
        }
      }
    })
    return allAutoBrandGroups
  }

  // Start traversal from the top-level CEO
  const filteredBAutoBrandGroups = getFilteredBAutoBrandGroups(
    autoBrandGroupData,
    0,
  )

  const getBrandGroups = () => {
    return !isSingleBrandSelector &&
      getFilteredBrandGroups() &&
      getFilteredBrandGroups()?.length === 0
      ? [
          {
            customer_name: 'Create New Brand',
            grouping_category: 'brand_groups',
            buttonText: 'Create New Brand Group',
            button_click: 'create',
          },
        ]
      : getFilteredBrandGroups()?.map((brandGroup: BrandGroupDataPropsType) => {
          brandGroup.customer_name = brandGroup.name
          brandGroup.grouping_category = 'brand_groups'
          return brandGroup
        }) || []
  }

  const updateSetUpMyBrands = () => {
    const selectedSetupBrand = brandGroupsData?.filter(
      (brand: { name: string }) => {
        return brand.name === '' || brand.name === 'My Brands'
      },
    )
    setWorkflowType({
      isOpen: true,
      type: 'setup',
      groupData:
        selectedSetupBrand && selectedSetupBrand.length
          ? selectedSetupBrand[0]
          : null,
    })
  }

  const getMyBrands = () => {
    const myBrandGroups = brandGroupsData?.find(
      (brandGroups: BrandGroupDataPropsType) =>
        brandGroups?.is_my_brand_group === true,
    )
    const brandData = myBrandGroups?.brand_data ?? []

    return !isSingleBrandSelector && brandData && brandData?.length === 0
      ? [
          {
            customer_name: 'Set Up My Brands',
            grouping_category: 'my_brands',
            buttonText: 'SetUp My Brands',
            button_click: 'setup',
          },
        ]
      : brandData?.map((brandGroup: BrandGroupDataPropsType) => {
          brandGroup.id = brandGroup.customer_id
          brandGroup.grouping_category = 'my_brands'
          return brandGroup
        }) || []
  }

  const getAutoBrandGroups = () => {
    return (
      filteredBAutoBrandGroups?.map((brandGroup: AutoBrandGroupBaseProps) => {
        //ToDo: Take care of groups who don't have user name
        if (brandGroup?.user_name?.trim()) {
          brandGroup.customer_name = brandGroup.user_name
        }

        brandGroup.grouping_category = AUTO_BRAND_GROUPS
        return brandGroup
      }) || []
    )
  }
  const getBrandGroupSharedWithMe = () => {
    return sharedBrandGroupsData?.map(
      (sharedGroup: BrandGroupDataPropsType) => {
        sharedGroup.customer_name = sharedGroup.name
        sharedGroup.grouping_category = BRAND_GROUP_SHARED_WITH_ME
        return sharedGroup
      },
    )
  }

  const brandGroupFromLocal = tryLocalStorageParse('brand_group')
  if (
    brandGroupFromLocal?.grouping_category === BRAND_GROUP_SHARED_WITH_ME &&
    userBrandGroupApiStatus === 'success'
  ) {
    const brandGroupsFromAPI = getBrandGroupSharedWithMe()
    const sameGroupFromAPI = brandGroupsFromAPI.find(
      (item: BrandGroupDataPropsType) =>
        item.customer_name === brandGroupFromLocal.customer_name,
    )
    if (!sameGroupFromAPI) {
      //If the shared brand group access is removed, then upon page refresh, the local storage will be cleared, and a new customer will be selected.
      removeBrandGroupState()
    } else if (sameGroupFromAPI?.id === brandGroupFromLocal?.id) {
      //If the brands within the shared brand group are changed, the local storage is updated with the modified brands on page refresh.
      const apiBrandGroupIds = sameGroupFromAPI.brand_data.map(
        (item: BrandDataProps) => item.customer_id,
      )
      const localBrandGroupIds = brandGroupFromLocal.brand_data.map(
        (item: BrandDataProps) => item.customer_id,
      )
      const areIdsSame =
        apiBrandGroupIds.length === localBrandGroupIds.length &&
        apiBrandGroupIds.every((id: number) => localBrandGroupIds.includes(id))
      if (!areIdsSame) {
        updateBrandGroupState(sameGroupFromAPI)
      }
    }
  }

  const allCustomers = [
    ...(hasBrandGroupPermissions
      ? [
          ...getBrandGroups(),
          ...getMyBrands(),
          ...(sharedBrandGroupsData?.length ? getBrandGroupSharedWithMe() : []),
          ...(userBrandGroupsData?.autoBrandGroups &&
          userBrandGroupsData?.autoBrandGroups?.length
            ? [...getAutoBrandGroups()]
            : []),
        ]
      : []),
    ...(customerData || []),
  ]

  const optionGroupSections = [
    ...(hasBrandGroupPermissions
      ? [
          {
            value: 'my_brands',
            display: 'My Brands',
            subtitle: {
              title: 'Manage',
              callout: () => {
                updateSetUpMyBrands()
              },
            },
          },
          {
            value: 'brand_groups',
            display: 'My Brand Groups',
            subtitle: {
              title: 'Create',
              callout: () => {
                setWorkflowType({
                  isOpen: true,
                  type: 'create',
                  groupData: null,
                })
              },
            },
          },
          ...(isAbgVisible
            ? [
                {
                  value: AUTO_BRAND_GROUPS,
                  display: 'Brand Groups By Assignment',
                },
              ]
            : []),
        ]
      : []),
    {
      value: 'full_partner',
      display: 'All other brands',
    },
    { value: 'advisory_partner', display: 'Advisory' },
    { value: 'prospect', display: 'Prospect' },
    { value: 'vorys_partner', display: 'Vorys' },
    { value: 'software_partner', display: 'Software' },
    { value: 'onep_partner', display: '1P' },
    { value: 'middle_mile', display: 'Middle Mile' },
    ...(isSharedBrandVisible
      ? [
          {
            value: BRAND_GROUP_SHARED_WITH_ME,
            display: 'Brand Groups Shared with Me',
          },
        ]
      : []),
  ]

  /**
   * Function to add isExpanded flag for the clicked group and update the autoBrandGroupData state accordingly
   * @param {number} clickedId clicked group id
   * @param {number} selectedAbgParentId newly selected abg's parent id
   * clickedId = -1 is used to identify outside click or the mount phase of the side drawer and set ABG hierarchy to default state
   */
  const updateAutoBrandGroupState = useCallback(
    (clickedId: number, selectedAbgParentId = 0) => {
      const abgCopy = [...autoBrandGroupData]

      // Recursive function to find and set the isExpanded property
      const findAndSetIsExpanded = (
        abg: AutoBrandGroupBaseProps,
        level = 0,
      ) => {
        // When the caret icon is clicked below condition toggles the isExpanded flag
        if (abg?.id === clickedId) {
          abg.isExpanded = !abg?.isExpanded
        }
        // level 0 is to keep the first level hierarchy open by default
        else if (level === 0) {
          abg.isExpanded = true
        }

        // When user has selected an ABG and trying to just see other levels of hierarchy by expand/collapse and closes side drawer without
        // saving any changes then below if block is used to close all those expanded hierarchy which does not belong to the selected abg
        if (clickedId === -1) {
          if (level === 0) {
            if (abg?.reportees) {
              for (const child of abg.reportees) {
                // Calling recursive function to set isExpanded flag to false for all levels other than the parents of selected abg
                if (!findAndSetIsExpanded(child)) {
                  child.isExpanded = false
                }
              }
            }
          }

          // Below condition triggers when there is no change in the selection of ABG and compares abg id with the context abg parent id
          // and add isExpanded value to true for selected abg's parents.
          // On click/selection of new abg context does not immediately updates the value that's why selectedAbgParentId is introduced
          if (
            abg?.id === brandGroupContextData?.parent_id &&
            !selectedAbgParentId
          ) {
            abg.isExpanded = true
            return true
          }

          // Below condition triggers immediately on click/selection of new abg and add isExpanded value to true for selected abg's parents
          if (abg?.id === selectedAbgParentId) {
            abg.isExpanded = true
            return true
          }
        }

        if (abg?.reportees) {
          for (const child of abg.reportees) {
            // Calling recursive function to set isExpanded flag to true for all parents of selected abg
            if (findAndSetIsExpanded(child, level + 1)) {
              abg.isExpanded = true
              return true
            }
          }
        }

        return false
      }

      for (const abg of abgCopy) {
        findAndSetIsExpanded(abg)
      }

      setAutoBrandGroupData(abgCopy)
    },
    [autoBrandGroupData, brandGroupContextData],
  )

  // Below useEffect is used to display the selected ABG expanded on component mounting
  useEffect(() => {
    if (
      isRenderedOnce.current &&
      autoBrandGroupData &&
      autoBrandGroupData.length
    ) {
      updateAutoBrandGroupState(-1)
      isRenderedOnce.current = false
    }
  }, [autoBrandGroupData, updateAutoBrandGroupState])

  const saveSystemSharedBrandGroup = useMutation({
    mutationFn: (actions: {
      params: SaveSharedBrandGroupProps
      apiUrl: string
    }) => {
      return SecureAxios.post(actions.apiUrl, actions.params)
        .then(() => {
          updateRefetchCustomer?.(true)
          toast({
            type: 'success',
            message: `Brand group ${
              actions?.params?.brandGroupName
                ? `"${actions?.params?.brandGroupName}" `
                : ''
            }saved successfully. You can view the newly added brand group under My Brand Group Section.`,
            config: {
              autoClose: 3000,
            },
          })
        })
        .catch((error) => {
          toast({
            type: 'error',
            message:
              error?.response?.status === 422
                ? 'Brand Group cannot be saved. Group name already exists.'
                : 'Something went wrong while saving Brand Group. Please try again later!',
            config: {
              autoClose: 3000,
            },
          })
          handleError(
            error,
            'BrandSelectionDropDown.tsx',
            'saveSystemSharedBrandGroup',
            actions.apiUrl,
          )
        })
    },
  })

  const saveSystemBrandGroup = (
    selectedBrandGroupId?: number,
    brandGroupType?: string,
    brandGroupName?: string,
  ) => {
    saveSystemSharedBrandGroup?.mutate({
      params: {
        [brandGroupType === 'Group'
          ? 'group_shared_brand_group_id'
          : 'user_shared_brand_group_id']: selectedBrandGroupId,
        brandGroupName: brandGroupName,
      },
      apiUrl: `${getApiUrlPrefix(
        'iserve',
      )}/api/v7/user_shared_brand_groups/save_to_my_brand_group`,
    })
  }

  const deleteSystemSharedBrandGroup = useMutation({
    mutationFn: (actions: { apiUrl: string }) => {
      return SecureAxios.delete(actions.apiUrl)
        .then(() => {
          updateRefetchCustomer?.(true)
          toast({
            type: 'success',
            message: `Brand group deleted successfully.`,
            config: {
              autoClose: 3000,
            },
          })
        })
        .catch((error) => {
          toast({
            type: 'error',
            message:
              'Something went wrong while deleting Brand Group. Please try again later!',
            config: {
              autoClose: 3000,
            },
          })
          handleError(
            error,
            'BrandSelectionDropDown.tsx',
            'deleteSystemSharedBrandGroup',
            actions.apiUrl,
          )
        })
    },
  })

  const deleteSystemBrandGroup = (selectedBrandGroupId?: number) => {
    if (!selectedBrandGroupId) {
      return toast({
        type: 'error',
        message: 'No Brand Group Id found.',
        config: {
          autoClose: 3000,
        },
      })
    }
    deleteSystemSharedBrandGroup?.mutate({
      apiUrl: `${getApiUrlPrefix(
        'iserve',
      )}/api/v7/user_shared_brand_groups/${selectedBrandGroupId}`,
    })
  }
  const getShareGroupOptions = (data: IndividualBrandGroupData) => {
    const isRemoveBrandGroupTypeDisabled =
      data?.shared_with === 'Group' || data?.shared_with === 'Pattern'
    return (
      <>
        <span className={styles.dropdownOptionSecondaryValue} />
        <div
          className='flex justify-content-end'
          onClick={(e) => {
            e.stopPropagation()
          }}
        >
          <PopoverNew
            position='top-start'
            popoverContent={
              <div className={`${styles.noTextTransform} fs-12 `}>
                <Menu
                  actions={[
                    {
                      callout: () =>
                        saveSystemBrandGroup(
                          data?.shared_brand_group_id,
                          data?.shared_with,
                          data?.name?.toString(),
                        ),
                      text: c('saveToMyBrandGroups'),
                    },
                    {
                      callout: () => {
                        if (isRemoveBrandGroupTypeDisabled) {
                          return toast({
                            type: 'warning',
                            message: c('groupTypeBrandGroupCanNotBeDeleted'),
                            config: {
                              autoClose: 3000,
                            },
                          })
                        }
                        deleteSystemBrandGroup(data?.shared_brand_group_id)
                      },
                      text: c('remove'),
                      disabled: { value: isRemoveBrandGroupTypeDisabled },
                    },
                  ]}
                />
              </div>
            }
            noPadding
          >
            {({ setVisible }) => (
              <div className='flex'>
                <Button as='unstyled' onClick={() => setVisible(true)}>
                  <Icon
                    icon='options'
                    iconSize='16px'
                    className={styles.transform90}
                  />
                </Button>
              </div>
            )}
          </PopoverNew>
        </div>
      </>
    )
  }
  const getBrandGroupOptions = (data: IndividualBrandGroupData) => {
    const groupData = {
      ...data,
    }
    return (
      <div key={groupData.customer_id}>
        <span className={styles.dropdownOptionSecondaryValue} />
        <div
          className='flex justify-content-end'
          onClick={(e) => {
            e.stopPropagation()
          }}
        >
          <PopoverNew
            position='top-start'
            appendTo='parent'
            popoverContent={
              <div className={`${styles.noTextTransform} fs-12 `}>
                <Menu
                  actions={[
                    {
                      callout: () =>
                        setWorkflowType({
                          isOpen: true,
                          type: 'edit',
                          groupData,
                        }),
                      icon: 'pencil',
                      text: c('editBrandGroup'),
                    },
                    {
                      callout: () => {
                        setWorkflowType({
                          isOpen: false,
                          type: SHARE_TYPE,
                          groupData,
                        })
                        setTimeout(() => {
                          setShareGroupSideDrawerOpen(true)
                        }, 100)
                      },
                      icon: 'share2',
                      text: c('shareBrandGroup'),
                    },
                  ]}
                />
              </div>
            }
            noPadding={true}
          >
            {({ setVisible }) => (
              <div className='flex'>
                <Button as='unstyled' onClick={() => setVisible(true)}>
                  <Icon
                    icon='options'
                    className={styles.transform90}
                    iconSize='16px'
                  />
                </Button>
              </div>
            )}
          </PopoverNew>
        </div>
      </div>
    )
  }
  const secondaryValuesConfig: SecondaryValuesConfigType<ItemGeneric> = {
    children: ({ data, downshift, closePopoverOrDrawer }) => {
      if (data?.brand_count) {
        const brandCountStyle =
          (data?.brand_count || 0) <= 9 ? 'pat-ml-0.5' : ''
        return (
          <>
            <span className={styles.dropdownOptionSecondaryValue} />
            <div
              className={`${styles.brandCountText} flex`}
              onClick={(e) => {
                e.stopPropagation()
              }}
            >
              <div className='flex align-items-center'>
                <div
                  className={`${styles.brandCountContainer} fw-medium`}
                  onClick={() => {
                    const groupData = {
                      ...data,
                    }
                    setListOfBrandsAssignedToAbg(groupData)

                    if (groupData?.grouping_category === AUTO_BRAND_GROUPS) {
                      setWorkflowType({
                        isOpen: true,
                        type: AUTOMATED_BRANDS,
                        groupData: data,
                      })
                    }
                    if (
                      groupData?.grouping_category === 'brand_groups' ||
                      groupData?.grouping_category ===
                        BRAND_GROUP_SHARED_WITH_ME
                    ) {
                      setBrandListSDDetails({
                        isBrandListSDOpen: true,
                        brandListData: groupData,
                      })
                    }
                  }}
                >
                  <span className={`${brandCountStyle} pat-mr-1`}>
                    {data?.brand_count}
                  </span>
                  {data?.brand_count === 1 ? 'Brand' : 'Brands'}
                </div>
                {data.grouping_category === AUTO_BRAND_GROUPS ? (
                  data?.reportees && data?.reportees?.length ? (
                    <span
                      onClick={(e) => {
                        e.stopPropagation()

                        if (data.grouping_category === AUTO_BRAND_GROUPS) {
                          updateAutoBrandGroupState(data.id)
                          setIsAccordionClicked(true)
                        }
                      }}
                    >
                      <Icon
                        icon='caretDown'
                        className={`${styles.caret} ${
                          data.isExpanded ? styles.flipped : ''
                        }`}
                      />
                    </span>
                  ) : null
                ) : data.grouping_category === BRAND_GROUP_SHARED_WITH_ME ? (
                  getShareGroupOptions(data)
                ) : isInternalUser(user) ? (
                  getBrandGroupOptions(data)
                ) : (
                  <span
                    onClick={() => {
                      const groupData = {
                        ...data,
                      }
                      if (groupData?.grouping_category === 'brand_groups') {
                        setWorkflowType({
                          isOpen: true,
                          type: 'edit',
                          groupData,
                        })
                      }
                    }}
                  >
                    <Icon
                      icon='pencil'
                      className='block pat-ml-4 align-items-center'
                      iconSize='12px'
                      color='dark-blue'
                    />
                  </span>
                )}
              </div>
            </div>
          </>
        )
      }
      return (
        <span
          className={`${styles.dropdownViewItemTextOption} fw-medium`}
          onClick={(e) => {
            e.stopPropagation()
            showBrandDetails?.(data)
            downshift.closeMenu()
            closePopoverOrDrawer?.()
          }}
        >
          Details
        </span>
      )
    },
  }

  const getSelectAllBrands: CustomFooterConfigType<ItemGeneric> = {
    children: ({ downshift, setPopoverMenuState }) => {
      const selectAllItem = {
        id: 0,
        customer_name: 'All Brands',
      } as ItemGeneric
      return !isSingleBrandSelector ? (
        <div
          key='select-all-brands'
          {...(downshift
            ? downshift.getItemProps({
                item: selectAllItem,

                className: styles.selectAllBrandsFooter,
                onClick: () => {
                  // Update and set all brands as a new selected item
                  updateCurrentCustomer('customer', selectAllItem)
                  setPopoverMenuState?.(false)
                },
                index: allCustomers?.length,
              })
            : {})}
        >
          {'All Brands' === downshift?.selectedItem?.['customer_name'] ? (
            <span className={styles.allBrandsActive}>All Brands Selected</span>
          ) : (
            'Select All Brands'
          )}
        </div>
      ) : (
        <div className='flex justify-content-end'>
          <Button
            as='button'
            onClick={() => {
              setPopoverMenuState?.(false)
            }}
          >
            {c('cancel')}
          </Button>
        </div>
      )
    },
  }

  const { isBrandGroupDrawerOpen, workflowType, selectedBrandGroupToUpdate } =
    state

  return (
    <div
      id='brand_selector_dropdown_container'
      data-testid='brand-selection-dropdown-container'
      className={`flex justify-content-between ${nonGlobal ? `align-items-center ${styles.fullHeight}` : ''}`}
    >
      <SelectWithPopoverDrawer
        selectedItem={
          selectedCustomer
            ? selectedCustomer
            : brandGroupCustomer
              ? brandGroupCustomer
              : currentCustomer
        }
        updateCustomer={updateCurrentCustomer}
        name='company'
        options={{
          keyName: 'customer_name',
          optionsData: allCustomers || [],
        }}
        groups={{
          keyName: 'grouping_category',
          groupData: optionGroupSections,
        }}
        loading={customerStatus === 'pending'}
        buttonAction={({ data }) => {
          if (data?.button_click) {
            setWorkflowType({
              isOpen: true,
              type: String(data?.button_click),
              groupData: null,
            })
          }
        }}
        secondaryValuesConfig={secondaryValuesConfig}
        customFooterConfig={
          enableSelectAllBrands() ? getSelectAllBrands : undefined
        }
        isAccordionClicked={isAccordionClicked}
        setIsAccordionClicked={setIsAccordionClicked}
        setSearchedString={setSearchedString}
        searchedString={searchedString}
        updateAutoBrandGroupState={updateAutoBrandGroupState}
        isBrandGroupDrawerOpen={isBrandGroupDrawerOpen}
        inputSearchPlaceHolder='Search Brands'
        searchBar
        nonGlobal={nonGlobal}
        isSingleBrandSelector={isSingleBrandSelector}
      />

      {(isBrandGroupDrawerOpen || shareGroupSideDrawerOpen) && (
        <BrandGroupSideDrawer
          isDrawerOpen={isBrandGroupDrawerOpen}
          setDrawerOpen={setBrandGroupDrawerOpenStatus}
          customers={customerData || []}
          workflowType={workflowType} // replace with type state of workflow to open namely 'create', 'edit', 'setup'
          selectedBrandGroupToUpdate={selectedBrandGroupToUpdate}
          updateRefetchCustomer={updateRefetchCustomer}
          brandGroupsData={brandGroupsData}
          abgPresentationData={abgPresentationData}
          abgPresentationStatus={abgPresentationStatus}
          setWorkflowType={setWorkflowType}
          abgMarketplacesByBrand={
            selectedBrandGroupToUpdate?.marketplace_name || []
          }
          listOfBrandsAssignedToAbg={listOfBrandsAssignedToAbg}
          setShareGroupSideDrawerOpen={setShareGroupSideDrawerOpen}
          shareGroupSideDrawerOpen={shareGroupSideDrawerOpen}
          nonGlobal={nonGlobal}
        />
      )}
      <BrandListSideDrawer
        brandListSDDetails={brandListSDDetails}
        setIsBrandListSDOpen={setBrandListSDDetails}
        nonGlobal={nonGlobal}
      />
    </div>
  )
}

export default BrandSelectionDropDown
