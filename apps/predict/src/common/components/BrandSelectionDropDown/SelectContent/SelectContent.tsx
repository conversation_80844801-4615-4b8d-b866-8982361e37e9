import React, { useRef } from 'react'
import { EmptyState, Icon, ListLoading, Tooltip } from '@patterninc/react-ui'
import { c } from '@predict-services'

import { brandGroupConstants } from '../../BrandGroup/BrandGroupConstants'
import SelectOptionHeader from '../SelectOptionHeader/SelectOptionHeader'
import SelectOptionValue from '../SelectOptionValue/SelectOptionValue'
import SelectSearchBox from '../SelectSearchBox/SelectSearchBox'
import {
  type FormattedType,
  type GroupSections,
  type SelectContentValues,
  type SelectPopoverProps,
} from '../SelectTypes/SelectTypes'
import styles from './_select-content.module.scss'

const SelectContent = <ItemGeneric extends SelectContentValues<ItemGeneric>>({
  name,
  options,
  groups,
  searchBar,
  noOptionsText,
  loading,
  buttonAction,
  downshift,
  secondaryValuesConfig,
  closePopoverOrDrawer,
  inputSearchPlaceHolder,
  setSearchedString,
  updateA<PERSON>BrandGroupState,
  updateCustomer,
  nonGlobal,
  isSingleBrandSelector,
}: SelectPopoverProps<ItemGeneric>): React.JSX.Element => {
  const dropdownRef = useRef<HTMLDivElement>(null)
  const { AUTO_BRAND_GROUPS } = brandGroupConstants

  const keyWiseGrouping = (
    optionsData: ItemGeneric[],
    key: keyof ItemGeneric,
  ) => {
    return optionsData.reduce(
      (result: Record<string, ItemGeneric[]>, currentValue: ItemGeneric) => {
        const currentValueKey = String(currentValue?.[key])

        if (currentValueKey) {
          result[currentValueKey] = result[currentValueKey] || []

          result[currentValueKey].push(currentValue)
        }

        return result
      },
      {},
    )
  }
  let formattedData: FormattedType<ItemGeneric>[] = []
  if (groups?.keyName && groups?.groupData) {
    //  Data ordering like in given optGroupSections sequence
    const groupedData = keyWiseGrouping(options?.optionsData, groups?.keyName)

    formattedData =
      groups?.groupData?.reduce(
        (result: FormattedType<ItemGeneric>[], currentValue: GroupSections) => {
          result.push({
            title: currentValue.display,
            subtitle: currentValue?.subtitle,

            data: groupedData[currentValue.value],
          })
          return result
        },
        [],
      ) ?? []
  }
  const isSystemTabEmpty = !formattedData.length && !loading

  const filteredDataFunction = (searchInput: string | null) => {
    const inputVal = searchInput?.trim().toLowerCase() ?? ''
    const filteredData = formattedData
      .map((section) => {
        // filterBySearchData contains the result filtered by the search input
        const filterBySearchData =
          section?.data?.filter((data: ItemGeneric) =>
            data[options?.keyName]
              ?.toString()
              ?.toLowerCase()
              ?.normalize('NFD')
              ?.replace(/[\u0300-\u036f]/g, '')
              ?.includes(inputVal),
          ) ?? []

        // The intention of adding below if block is to support expand/collapse functionality on search for the Automated Brand Groups section.
        // Finds records whose isExpanded is true and pushes them into filterBySearchData which helps to display nested hierarchy with expand/collapse on search.
        // IMPORTANT NOTE: The expand/collapse logic is written in BrandSelectionDropDown.tsx file inside of updateAutoBrandGroupState()
        if (searchInput && filterBySearchData?.length) {
          filterBySearchData.forEach((abg) => {
            if (
              abg?.grouping_category === AUTO_BRAND_GROUPS &&
              abg?.reportees &&
              abg?.isExpanded
            ) {
              const findIsExpanded = (reportees: ItemGeneric[]) => {
                reportees?.forEach((child) => {
                  if (filterBySearchData.indexOf(child) === -1) {
                    filterBySearchData.push(child)
                  }

                  if (child?.isExpanded) {
                    findIsExpanded(child?.reportees || [])
                  }
                })
              }

              findIsExpanded(abg?.reportees)
            }
          })
        }

        return {
          title: section.title,
          ...(section.subtitle
            ? {
                subtitle: section?.subtitle,
              }
            : {}),
          data: filterBySearchData ?? [],
        }
      })
      .filter((section) => section.data.length > 0)

    return filteredData
  }

  const indexedFilterData = (inputValue: string | null) => {
    let itemIndex = 0
    let sectionIndex = 0
    return filteredDataFunction(inputValue)?.reduce(
      (result: FormattedType<ItemGeneric>[], currentValue) => {
        result.push({
          id: sectionIndex++,
          title: currentValue.title,
          ...(currentValue?.subtitle
            ? {
                subtitle: currentValue?.subtitle,
              }
            : {}),
          data: currentValue.data.map((item: ItemGeneric) => ({
            ...item,
            itemIndex: itemIndex++,
          })),
        })
        return result
      },
      [],
    )
  }

  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchedString?.(e?.target?.value)
    // force the dropdown list to scroll to the top result in desktop view
    if (dropdownRef && dropdownRef.current) {
      dropdownRef.current.scrollTop = 0
    }
  }
  const getEmptyResultFoundJSX = () => (
    <EmptyState
      icon='info'
      primaryText='No System Groups Available'
      secondaryText='Groups shared with you will appear on this tab'
    />
  )
  const getNoResultFoundJSX = () => (
    <div className={styles.noResultsFound}>
      {noOptionsText || 'No results found.'}
    </div>
  )
  return (
    <ul className={styles.listStyleNone} {...downshift.getMenuProps()}>
      {searchBar && (
        <SelectSearchBox
          downshift={downshift}
          searchId={`select-component-${name}`}
          handleSearchChange={handleSearchChange}
          inputSearchPlaceHolder={inputSearchPlaceHolder}
          setSearchedString={setSearchedString}
          updateAutoBrandGroupState={updateAutoBrandGroupState}
        />
      )}

      {isSingleBrandSelector && (
        <div className='flex bdr bdrc-medium-gray pat-p-2 pat-mt-4 pat-gap-2'>
          <Tooltip
            tooltipContent={c('selectBrandNotificationTooltip')}
            position='bottom'
          >
            <Icon icon='info' color='dark-blue' iconSize='12px' />
          </Tooltip>
          <span className='fs-12 fw-medium'>
            {c('onlyOneBrandPerNotification')}
          </span>
        </div>
      )}
      {isSystemTabEmpty && getEmptyResultFoundJSX()}
      {/* dropdown-group-options-container class identifier need to scroll to the selected value */}
      <div
        className={`${styles.dropdownGroupOptionsContainer} dropdown-group-options-container`}
        ref={dropdownRef}
      >
        {!loading ? (
          <>
            {groups?.keyName &&
              options?.optionsData?.length > 0 &&
              filteredDataFunction(downshift.inputValue).length > 0 && (
                <>
                  {indexedFilterData(downshift.inputValue).map((section) => {
                    return (
                      <div key={section.id}>
                        <SelectOptionHeader
                          key={section.id}
                          title={section.title}
                          {...(section?.subtitle
                            ? { subtitle: section?.subtitle }
                            : {})}
                          closePopoverOrDrawer={closePopoverOrDrawer}
                          isSingleBrandSelector={isSingleBrandSelector}
                        />
                        {section.data.map((data: ItemGeneric) => {
                          return (
                            <SelectOptionValue
                              key={Number(data.id)}
                              downshift={downshift}
                              data={data}
                              index={
                                data?.itemIndex ? Number(data?.itemIndex) : 0
                              }
                              optionKeyName={
                                // Added name as optionKeyName for the auto_brand_groups category becasue there are duplication of the names
                                // in the ABG list and due to this the downshift selects both the same name ABG at a time which is not valid.
                                data.grouping_category === AUTO_BRAND_GROUPS
                                  ? 'name'
                                  : options?.keyName
                              }
                              buttonAction={buttonAction}
                              secondaryValuesConfig={secondaryValuesConfig}
                              closePopoverOrDrawer={closePopoverOrDrawer}
                              updateAutoBrandGroupState={
                                updateAutoBrandGroupState
                              }
                              updateCurrentCustomer={updateCustomer}
                              nonGlobal={nonGlobal}
                              isSingleBrandSelector={isSingleBrandSelector}
                            />
                          )
                        })}
                      </div>
                    )
                  })}
                </>
              )}
            {(options?.optionsData?.length === 0 ||
              (searchBar &&
                !indexedFilterData(downshift?.inputValue)?.length &&
                options?.optionsData?.filter((option) => {
                  const optionValue = option[options?.keyName]
                  return (
                    !downshift?.inputValue ||
                    (typeof optionValue === 'string' &&
                      String(optionValue)
                        ?.toLowerCase()
                        ?.normalize('NFD')
                        ?.replace(/[\u0300-\u036f]/g, '')
                        ?.includes(downshift.inputValue.toLowerCase()))
                  )
                })?.length === 0)) &&
              getNoResultFoundJSX()}
          </>
        ) : (
          <div
            className={styles.dropdownGroupOptionsContainer}
            ref={dropdownRef}
          >
            <ListLoading />
          </div>
        )}
      </div>
    </ul>
  )
}

export default SelectContent
