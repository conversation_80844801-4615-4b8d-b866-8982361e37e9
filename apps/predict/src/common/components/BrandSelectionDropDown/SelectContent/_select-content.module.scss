@use '@patterninc/react-ui/dist/variables' as variables;

.listStyleNone {
  list-style-type: none;
  padding-inline-start: 0;
  margin-block-end: 0;
  margin-top: -18px;
}

.dropdownGroupOptionsContainer {
  border-top: 10px solid var(--white);
  overflow-y: scroll;
  max-height: calc(100vh - 180px);
  min-height: 24px;
  border-radius: 4px;
}

.noResultsFound {
  display: flex;
  justify-content: center;
  margin: 16px 0px;
  padding: 0px 8px;
  color: var(--purple);
  font-size: var(--font-size-14);
}
