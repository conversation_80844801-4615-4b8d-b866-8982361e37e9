import React, { useState } from 'react'
import { Button, PopoverNew, SelectDisplay } from '@patterninc/react-ui'

import { type SelectOptionHeaderProps } from '../SelectTypes/SelectTypes'
import styles from './_select-option-header.module.scss'

type getSubTitle = {
  visible?: boolean
  setVisible?: (visible: boolean) => void
}

const SelectOptionHeader = ({
  title,
  subtitle,
  closePopoverOrDrawer,
  isSingleBrandSelector,
}: SelectOptionHeaderProps): React.JSX.Element => {
  const [popoverVisible, setPopoverVisible] = useState<boolean>()

  const getSubTitle = (params?: getSubTitle) => {
    return (
      <span key={subtitle?.title}>
        <Button
          className={styles.groupSubHeaderWrapper}
          onClick={(e: React.FormEvent<HTMLButtonElement>) => {
            e.stopPropagation()
            subtitle?.callout?.({
              title: subtitle?.title,
              closePopoverOrDrawer,
            })
            if (subtitle?.options) {
              setPopoverVisible(params?.visible)

              params?.visible
                ? params?.setVisible?.(false)
                : params?.setVisible?.(true)
            }
          }}
        >
          <span className={styles.groupSubHeaderStyle}>{subtitle?.title}</span>
        </Button>
      </span>
    )
  }
  return (
    <div
      className={`${!popoverVisible ? styles.groupHeaderWrapper : ''}  ${
        styles.groupHeaderContainer
      }`}
      key={title}
    >
      <div className={styles.groupTitleContainer}>
        <div className={styles.groupTitle}>{title}</div>
      </div>
      {!isSingleBrandSelector ? (
        subtitle &&
        subtitle?.options &&
        subtitle?.options?.options?.length > 0 ? (
          <PopoverNew
            position='bottom-end'
            noPadding
            popoverContent={
              <SelectDisplay
                key={subtitle?.title}
                options={subtitle?.options?.options ?? []}
                callout={(option) => {
                  subtitle?.options?.callout?.({
                    data: option,
                    closePopoverOrDrawer,
                  })
                }}
                hasTippy
              />
            }
          >
            {({ visible, setVisible }) => {
              return getSubTitle({ visible, setVisible })
            }}
          </PopoverNew>
        ) : (
          subtitle?.title && getSubTitle()
        )
      ) : null}
    </div>
  )
}

export default SelectOptionHeader
