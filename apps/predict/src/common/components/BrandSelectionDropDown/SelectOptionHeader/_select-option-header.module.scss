$headerWidth: 98px; // updated as per prototype style
$headerheight: 18px; // updated as per prototype style

.groupSubHeaderWrapper {
  min-width: $headerWidth;
  height: $headerheight;
  text-align: center;
  cursor: pointer;
  border-radius: 2px;
  padding: 0px 8px;
  color: var(--blue);
  background-color: var(--lighter-gray);
  border-color: var(--medium-blue);

  &:hover,
  &:focus {
    box-shadow: 0px 3px 0px inset var(--blue);
    background-color: var(--blue);
    color: var(--lighter-gray);
    border-color: var(--blue);
  }
}

.groupHeaderWrapper {
  position: sticky;
  top: 0;
  z-index: 1;
}
.groupHeaderContainer {
  display: flex;
  justify-content: space-between;
  background-color: var(--lighter-gray);
  margin: 8px 0px;
  align-items: center;
  height: $headerheight;
}

.groupTitleContainer {
  text-transform: uppercase;
  padding: 0px;
}

.groupTitle {
  font-weight: var(--font-weight-bold);
  color: var(--purple);
  font-size: var(--font-size-10);
  padding: 0px 8px;
}
.groupSubHeaderStyle {
  min-width: $headerWidth;
  text-align: center;
  text-transform: uppercase;
  font-size: var(--font-size-10);
  padding: 0 4px;
}
