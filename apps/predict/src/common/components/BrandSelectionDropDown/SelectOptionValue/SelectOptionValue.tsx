import React, { useContext } from 'react'
import { <PERSON><PERSON>, <PERSON><PERSON>, Toolt<PERSON>, useMediaQuery } from '@patterninc/react-ui'

import { ThemeContext } from '../../../../Context'
import { brandGroupConstants } from '../../BrandGroup/BrandGroupConstants'
import {
  type SelectContentValues,
  type SelectOptionValueProps,
} from '../SelectTypes/SelectTypes'
import styles from './_select-option-value.module.scss'

type SelectData = {
  workday_title?: string
  buttonText?: string
}

const SelectOptionValue = <
  ItemGeneric extends SelectContentValues<ItemGeneric> & SelectData,
>({
  downshift,
  data,
  index,
  optionKeyName,
  secondaryValuesConfig,
  buttonAction,
  closePopoverOrDrawer,
  updateAutoBrandGroupState,
  updateCurrentCustomer,
  nonGlobal,
  isSingleBrandSelector,
}: SelectOptionValueProps<ItemGeneric>): React.JSX.Element => {
  const { updateCustomer } = useContext(ThemeContext)
  const isMobileView = useMediaQuery({ type: 'max', breakpoint: 'md' })
  const updateSelectedCustomer = updateCurrentCustomer || updateCustomer

  // Currently we have supported 7 level ABG hierachy
  const hierarchyLevels = [1, 2, 3, 4, 5, 6, 7]
  const isChildReportee = hierarchyLevels.some((hl) => {
    return Object.prototype.hasOwnProperty.call(data, `isReportee${[hl]}`)
  })

  const { AUTO_BRAND_GROUPS, NAME_MISSING, MANAGER, INDIVIDUAL_CONTRIBUTOR } =
    brandGroupConstants

  if (!isSingleBrandSelector && data?.buttonText && buttonAction) {
    return (
      <li key={`btn-${data.id}`} className={styles.dropdownButtonOptionValue}>
        <Button
          as='button'
          onClick={() => {
            buttonAction?.({
              data,
            })
          }}
        >
          {data?.buttonText}
        </Button>
      </li>
    )
  }

  // TODO: Make below code dynamic
  const getLeftSpacing = () => {
    if (data?.isReportee1) {
      return isMobileView ? 4 : 8
    } else if (data?.isReportee2) {
      return isMobileView ? 14 : 28
    } else if (data?.isReportee3) {
      return isMobileView ? 21 : 42
    } else if (data?.isReportee4) {
      return isMobileView ? 28 : 56
    } else if (data?.isReportee5) {
      return isMobileView ? 35 : 70
    } else if (data?.isReportee6) {
      return isMobileView ? 42 : 84
    }
    // 7th level hierarchy
    return isMobileView ? 49 : 98
  }

  const leftSpacing = getLeftSpacing()

  const getliconJSX = () => {
    return isChildReportee ? (
      <div style={{ marginLeft: `${leftSpacing}px` }}>
        <Icon icon='l' iconSize='8px' className={styles.lIcon} />
      </div>
    ) : null
  }

  const getBrandAssignmentTitle = () => {
    if (data?.name) {
      // e.g. data?.name = Hugh Hinkson (Director of Ecommerce)
      const nameTitleArray = data?.name?.split('(')
      // nameTitleArray = ['Hugh Hinkson ', 'Director of Ecommerce)']
      if (nameTitleArray) {
        const titleIndex = nameTitleArray.length === 3 ? 2 : 1
        // e.g. data.name = Adam Christensen (NAM (Named Account Manager)) then we need index as 2 to get `Named Account Manager` as title
        const titleArray = nameTitleArray?.[titleIndex]?.split(')')
        // titleArray = ['Director of Ecommerce', '']
        const title = titleArray?.[0]?.trim()
        // title = Director of Ecommerce
        return title
      }
    }
    return ''
  }

  const brandAssignmentTitle = getBrandAssignmentTitle()

  const getAbgTooltipContent = () => {
    return (
      <div className='fs-12'>
        <div className='fw-bold'>Brand Assignment:</div>
        <span className='fw-regular'>{brandAssignmentTitle}</span>
        <div className='fw-bold'>Assignment Type:</div>
        <span className='fw-regular'>
          {data?.is_manager_group ? MANAGER : INDIVIDUAL_CONTRIBUTOR}
        </span>
        <div className='fw-bold'>Workday Business Title:</div>
        <span className='fw-regular'>{data?.workday_title}</span>
      </div>
    )
  }

  const renderIcon = getliconJSX()
  const abgTooltipContent = getAbgTooltipContent()

  const item =
    data?.grouping_category === AUTO_BRAND_GROUPS
      ? data?.user_name?.toString()?.trim() === ''
        ? NAME_MISSING
        : data?.user_name?.toString()
      : data?.[optionKeyName]?.toString()

  const getOptionItemJSX = (isItemSelected: boolean) => {
    const itemJSX = (
      <div className='flex align-items-center'>
        {renderIcon}
        <div className={isChildReportee ? 'pat-ml-2' : ''}>
          <span className={isItemSelected ? 'fw-bold' : ''}>{item}</span>
        </div>
      </div>
    )

    if (data.grouping_category === AUTO_BRAND_GROUPS && !isMobileView) {
      return (
        <Tooltip tooltipContent={abgTooltipContent} position='bottom'>
          {itemJSX}
        </Tooltip>
      )
    }

    return itemJSX
  }

  const selectedItemJSX = getOptionItemJSX(true)
  const optionItemJSX = getOptionItemJSX(false)

  return (
    <li
      key={Number(data.id)}
      {...downshift.getItemProps({
        item: data,
        onClick: () => {
          // Update and set new selected item
          updateSelectedCustomer('customer', data)
          closePopoverOrDrawer?.()
          updateAutoBrandGroupState?.(-1, data?.parent_id)
        },
        className: `${styles.dropdownItemOptionsValue} ${nonGlobal ? styles.wide : ''} scroll-${data.id} ${
          data.customClass ? data.customClass : ''
        } ${downshift.highlightedIndex === index ? styles.bgcLightBlue : ''} `,
        index,
      })}
    >
      <span className={styles.dropdownOptionValue}>
        {downshift.selectedItem &&
        data[optionKeyName] === downshift.selectedItem[optionKeyName]
          ? selectedItemJSX
          : optionItemJSX}
      </span>
      {!isSingleBrandSelector &&
        secondaryValuesConfig &&
        secondaryValuesConfig.children({
          data,
          downshift,
          index,
          closePopoverOrDrawer,
        })}
    </li>
  )
}

export default SelectOptionValue
