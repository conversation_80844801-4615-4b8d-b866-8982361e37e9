@use '@patterninc/react-ui/dist/variables' as variables;

.dropdownButtonOptionValue {
  display: flex;
  justify-content: center;
  padding: 16px 0px;
  text-transform: uppercase;
}

.bgcLightBlue {
  background-color: var(--light-blue);
}

li.dropdownItemOptionsValue {
  display: grid;
  grid-template-columns: 311px auto auto;
  margin-bottom: 0;
  line-height: 36px;
  font-size: var(--font-size-12);
  font-weight: var(--font-weight-regular);
  cursor: pointer;
  white-space: nowrap;
  transition: all 0.25s ease-in-out;
  padding-top: 0;
  padding-left: 8px;
  cursor: pointer;
  align-items: center;

  &.wide {
    grid-template-columns: 450px auto auto;
  }

  @media only screen and (max-width: variables.$breakpoint-md) {
    grid-template-columns: 1fr auto auto;
    line-height: 40px;
  }
}

.dropdownOptionValue {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.lIcon {
  opacity: 0.25;
  width: 10px;
}
