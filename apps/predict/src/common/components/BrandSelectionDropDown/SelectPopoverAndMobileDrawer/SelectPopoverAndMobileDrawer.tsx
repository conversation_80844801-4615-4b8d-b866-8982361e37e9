import React, { useContext, useState } from 'react'
import {
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  useMediaQ<PERSON>y,
} from '@patterninc/react-ui'
import { isInternalUser } from '@predict-services'

import { ThemeContext } from '../../../../Context'
import { useAuth } from '../../../../context/auth-context'
import {
  brandGroupConstants,
  brandSelectorTabConstants,
} from '../../BrandGroup/BrandGroupConstants'
import { type IndividualBrandGroupData } from '../../BrandGroup/BrandGroupTypes'
import SelectContent from '../SelectContent/SelectContent'
import {
  type GroupSections,
  type SelectContentValues,
  type SelectPopoverDrawerProps,
} from '../SelectTypes/SelectTypes'

/**
 * This component will be use to get closePopoverOrDrawer method from PopoverAndMobileDrawer
 * to keep SelectContent component more reusable and common
 * @param selectPopoverProps all the common props recieved from parent component
 * @returns SelectContent component with all required content
 */
type ToggleOption = {
  id: number
  text: string
  value: string
}
const { ALL_BRANDS, SYSTEM_GROUPS } = brandSelectorTabConstants
const { BRAND_GROUP_SHARED_WITH_ME, AUTO_BRAND_GROUPS } = brandGroupConstants
const tabOption = [
  {
    id: 0,
    text: ALL_BRANDS,
    value: ALL_BRANDS,
  },
  {
    id: 1,
    text: SYSTEM_GROUPS,
    value: SYSTEM_GROUPS,
  },
]

const BrandSelectorSideDrawer = <
  ItemGeneric extends SelectContentValues<ItemGeneric>,
>(
  selectPopoverProps: SelectPopoverDrawerProps<ItemGeneric>,
): React.JSX.Element => {
  const { brandGroupCustomer } = useContext(ThemeContext)
  const brandGroupContextData =
    brandGroupCustomer as IndividualBrandGroupData | null
  const {
    popoverMenuState,
    setPopoverMenuState,
    customFooterConfig,
    downshift,
    groups,
    nonGlobal,
    isSingleBrandSelector,
  } = selectPopoverProps
  const user = useAuth().user

  //to maintain tab selection as per group category
  const isDefaultSystemTab =
    brandGroupContextData?.grouping_category === AUTO_BRAND_GROUPS ||
    brandGroupContextData?.grouping_category === BRAND_GROUP_SHARED_WITH_ME

  const [tabInputSelected, setTabInputSelected] = useState<ToggleOption>(
    isDefaultSystemTab ? tabOption[1] : tabOption[0],
  )
  const isSystemGroupTabSelected = tabInputSelected.value === SYSTEM_GROUPS
  const systemGroups: GroupSections[] = []
  const allbrandGroups: GroupSections[] = []

  groups?.groupData.forEach((item) => {
    if (
      item.value === AUTO_BRAND_GROUPS ||
      item.value === BRAND_GROUP_SHARED_WITH_ME
    ) {
      systemGroups.push(item)
    } else {
      allbrandGroups.push(item)
    }
  })

  // Display brand selector content without ALL BRANDS and SYSTEM GROUPS tabs
  const renderSelectContent = (
    <SelectContent
      {...selectPopoverProps}
      groups={{
        keyName: 'grouping_category',
        groupData: isSystemGroupTabSelected ? systemGroups : allbrandGroups,
      }}
      closePopoverOrDrawer={() => {
        setPopoverMenuState?.(false)
      }}
    />
  )

  // Display brand selector content with ALL BRANDS and SYSTEM GROUPS tabs
  const renderShareToggleTab = (
    <>
      <div className='pat-pb-4'>
        <Picker
          options={tabOption}
          state={tabInputSelected}
          stateName='value'
          callout={(_, value) => {
            setTabInputSelected(
              tabOption?.filter((item) => item.value === value)?.[0],
            )
            setPopoverMenuState?.(true)
          }}
          customClass='every-options full-width'
        />
      </div>
      {renderSelectContent}
    </>
  )

  return (
    <SideDrawer
      isOpen={popoverMenuState}
      closeCallout={() => {
        setPopoverMenuState?.(false)
        setTabInputSelected(isDefaultSystemTab ? tabOption[1] : tabOption[0])
      }}
      layerPosition={
        selectPopoverProps.isSingleBrandSelector ? 4 : nonGlobal ? 1 : undefined
      }
      size={nonGlobal ? 'md' : undefined}
      headerContent='BRAND SELECTOR'
      noGradient
      footerContent={
        customFooterConfig &&
        customFooterConfig.children({
          downshift,
          setPopoverMenuState,
        })
      }
    >
      {isInternalUser(user) && !isSingleBrandSelector
        ? renderShareToggleTab
        : renderSelectContent}
    </SideDrawer>
  )
}

const SelectPopoverAndMobileDrawer = <
  ItemGeneric extends SelectContentValues<ItemGeneric>,
>({
  name,
  options,
  groups,
  searchBar,
  noOptionsText,
  loading,
  buttonAction,
  downshift,
  secondaryValuesConfig,
  customFooterConfig,
  setPopoverMenuState,
  popoverMenuState,
  inputSearchPlaceHolder,
  setSearchedString,
  updateAutoBrandGroupState,
  updateCustomer,
  nonGlobal,
  isSingleBrandSelector,
}: SelectPopoverDrawerProps<ItemGeneric>): React.JSX.Element => {
  const isMobileView = useMediaQuery({ type: 'max', breakpoint: 'md' })
  const brandSelectorWidth = document?.getElementById?.(
    'brand_selector_dropdown_container',
  )?.offsetWidth

  const selectedItem = downshift?.selectedItem,
    isBrandSelected =
      nonGlobal && (selectedItem?.id as unknown as number) !== -2

  const closeMenu = () => {
    downshift.toggleMenu()
    setPopoverMenuState?.(!popoverMenuState)
  }

  const componentProps = {
    name: name,
    options: options,
    searchBar: searchBar,
    groups: groups,
    noOptionsText: noOptionsText,
    loading: loading,
    buttonAction: buttonAction,
    downshift: downshift,
    secondaryValuesConfig: secondaryValuesConfig,
    setPopoverMenuState: setPopoverMenuState,
    popoverMenuState: popoverMenuState,
    customFooterConfig: customFooterConfig,
    inputSearchPlaceHolder: inputSearchPlaceHolder,
    setSearchedString: setSearchedString,
    updateAutoBrandGroupState: updateAutoBrandGroupState,
    updateCustomer: updateCustomer,
    nonGlobal: nonGlobal,
    isSingleBrandSelector: isSingleBrandSelector,
  }

  return (
    <>
      <BrandSelectorSideDrawer {...componentProps} />
      <Button
        style={
          isMobileView
            ? {
                width: `${brandSelectorWidth ?? 144}px`, // 144px is the width of the BrandSelector button for mobile
                overflow: 'hidden',
                textOverflow: 'ellipsis',
              }
            : {}
        }
        onClick={closeMenu}
      >
        <span className='flex pat-gap-2'>
          <Icon icon='sellers' iconSize='16px' />
          {nonGlobal
            ? isBrandSelected
              ? 'Change Brand Selection'
              : 'Select Brand(s) to Apply'
            : ((selectedItem?.[options?.keyName] as React.ReactNode) ?? '')}
        </span>
      </Button>
    </>
  )
}

export default SelectPopoverAndMobileDrawer
