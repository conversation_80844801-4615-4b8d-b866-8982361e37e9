import React, { useEffect, useRef } from 'react'
import { Icon } from '@patterninc/react-ui'

import { type SelectSearchBoxProps } from '../SelectTypes/SelectTypes'
import styles from './_select-search.module.scss'

const SelectSearchBox = <ItemGeneric,>({
  downshift,
  searchId,
  handleSearchChange,
  inputSearchPlaceHolder = 'Search...',
  setSearchedString,
  updateAutoBrandGroupState,
}: SelectSearchBoxProps<ItemGeneric>): React.JSX.Element => {
  const inputRef = useRef<HTMLInputElement | null>(null)

  useEffect(() => {
    if (downshift.isOpen) {
      setTimeout(() => {
        inputRef?.current?.focus()
      }, 250)
    }
  }, [downshift.isOpen])

  return (
    <div className={styles.dropdownSearchItemWrapper}>
      {/* {isSearchConsolidation ? (
        // TODO: Will need to update SearchBar to handle the downshift props
        <SearchBar
          // value={query}
          // onChange={inputHandler}
          // placeholder='Search Groups'
          // {...downshift.getInputProps({
          // id: searchId,
          value=''
          placeholder={inputSearchPlaceHolder}
          onChange={handleSearchChange}
          // })}
          ref={inputRef}
          autoFocus={true}
        />
      ) : ( */}
      <li id='select-search-dropdown' className={styles.dropdownListSearch}>
        <input
          {...downshift.getInputProps({
            id: searchId,
            placeholder: inputSearchPlaceHolder,
            onChange: handleSearchChange,
          })}
          autoComplete='off'
          ref={inputRef}
          autoFocus={true}
        />
        <span
          className={`${styles.clearButtonStyle} ${
            downshift?.inputValue ? 'fadeIn visible' : ''
          }`}
          onClick={() => {
            // @ts-expect-error TODO: convert Select.js to .tsx
            downshift?.getInputProps()?.onChange({
              // @ts-expect-error TODO: convert Select.js to .tsx
              target: { value: '' },
            })
            setSearchedString?.('')
            // To reset the hierarchy to its default state and display selected abg
            updateAutoBrandGroupState?.(-1)
          }}
        >
          Clear
        </span>

        <Icon
          icon='magnifier'
          iconSize='16px'
          className={styles.dropdownSearchItemIcon}
        />
      </li>
      {/* )} */}
    </div>
  )
}

export default SelectSearchBox
