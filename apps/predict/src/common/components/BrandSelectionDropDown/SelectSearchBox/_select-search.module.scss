@use '@patterninc/react-ui/dist/variables';

.dropdownSearchItemWrapper {
  span.clearButtonStyle {
    position: absolute;
    top: 8px;
    right: 8px;
    font-size: var(--font-size-10);
    color: var(--purple);
    transition: all 0.25s ease-in-out;
    text-transform: uppercase;
    visibility: hidden;
    cursor: pointer;

    animation-duration: var(--animate-duration);
    animation-fill-mode: both;
  }
  & {
    position: sticky;
  }

  input {
    @media only screen and (max-width: variables.$breakpoint-sm-max) {
      /* CSS specific to iOS devices */
      @supports (-webkit-touch-callout: none) {
        /* Font size is 16px on iOS to prevent zooming on an iPhone */
        font-size: var(--font-size-16);
      }
    }
    & {
      font-size: var(--font-size-12);
      border: var(--medium-gray) solid 1px;
      font-family: 'Wix Madefor Display', sans-serif;
      border-radius: 4px;
      height: 20px;
      font-weight: var(--font-weight-regular);
      padding: 8px;
      outline: none;
      transition: all 0.25s ease-in-out;
      padding-left: 32px;
      width: calc(100% - 84px);
      padding-right: 48px;
    }
    &:focus {
      color: var(--blue);
      border-color: var(--blue);
    }
  }
}

.dropdownListSearch {
  position: relative;
  margin-top: 16px;
  margin-right: 2px;
  margin-bottom: 8px;
}

.dropdownSearchItemIcon {
  position: absolute;
  width: 20px;
  top: 12px;
  left: 8px;
}
