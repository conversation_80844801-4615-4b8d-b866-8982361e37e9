import type { ControllerStateAndHelpers } from 'downshift'

export type SelectOptionValues<ItemGeneric> = Record<string, ItemGeneric>
export interface SelectOptionValueProps<
  ItemGeneric extends SelectContentValues<ItemGeneric>,
> {
  /** Object of downshift with state and helper methods. */
  downshift: ControllerStateAndHelpers<ItemGeneric>
  /** Object data passed into the option values. */
  data: ItemGeneric
  /** Index number to rende values in option. */
  index: number
  /** Unique key from our data objects. Used for `key` in options. */
  optionKeyName: keyof ItemGeneric
  /** Function for button action handle. */
  buttonAction?: ({
    data,
    closePopoverOrDrawer,
  }: {
    data: ItemGeneric
    closePopoverOrDrawer?: () => void
  }) => void
  /** Config of secondary values to show in option. */
  secondaryValuesConfig?: {
    children: ({
      data,
      downshift,
      index,
      closePopoverOrDrawer,
    }: {
      data: ItemGeneric
      downshift: ControllerStateAndHelpers<ItemGeneric>
      index?: number
      closePopoverOrDrawer?: () => void
    }) => React.ReactNode
  }
  closePopoverOrDrawer?: () => void
  /** Function to add isExpanded flag for the clicked group and update the autoBrandGroupData state accordingly */
  updateAutoBrandGroupState?: (
    clickedId: number,
    selectedAbgParentId?: number,
  ) => void
  updateCurrentCustomer?: (type: string, value?: ItemGeneric | null) => void
  nonGlobal?: boolean

  isSingleBrandSelector?: boolean
}

// This type is copied over from react-ui. We do not export this from react-ui as it is not a standard type to be used in other components. However, this is a special case (for now) where we need to use this type to create the Brand Selection Drawer experience. Once we have moved the Brand Selection Drawer (or whatever common name we decide for this) to react-ui, we can remove this type from here and these files will get cleaned up.
type SelectDisplayOption = {
  /** Identifier for an option */
  name: string
  /** Label display for an option */
  label: string
  /** Object of tippy to show children on option value */
  children?: {
    /** Array of options for tippy */
    options: SelectDisplayOption[]
  }
  /** Optional secondary content to display */
  secondaryContent?: React.ReactNode
  /** Optional image to appear to the left of the option */
  image?: string
}

type SubtitleOptionTypes = {
  subtitle?: {
    /** Text for the subtitle of the header. */
    title: string
    /** Callout when the subtitle is clicked. */
    callout?: ({
      title,
      closePopoverOrDrawer,
    }: {
      title: string
      closePopoverOrDrawer?: () => void
    }) => void
    options?: {
      /** Available options that appear in the Tippy dropdown. */
      options: SelectDisplayOption[]
      /** Callout when the subtitle option is clicked. */
      callout: ({
        data,
        closePopoverOrDrawer,
      }: {
        data: SelectDisplayOption
        closePopoverOrDrawer?: () => void
      }) => void
    }
  }
}

export type GroupSections = {
  /** Text value for the group section. */
  value: string
  /** Text display value for the group section. */
  display: string
} & SubtitleOptionTypes

export interface SelectSearchBoxProps<ItemGeneric> {
  /** Object of downshift with state and helper methods.  */
  downshift: ControllerStateAndHelpers<ItemGeneric>
  /** Unique search id for search box.  */
  searchId: string
  /** Function for on change of search.  */
  handleSearchChange: (e: React.ChangeEvent<HTMLInputElement>) => void
  /** Placeholder text for input search  */
  inputSearchPlaceHolder?: string
  /** Function to set searched value of the search bar */
  setSearchedString?: React.Dispatch<React.SetStateAction<string>>
  /** Function to add isExpanded flag for the clicked group and update the autoBrandGroupData state accordingly */
  updateAutoBrandGroupState?: (
    clickedId: number,
    selectedAbgParentId?: number,
  ) => void
}

export type SelectOptionHeaderProps = {
  /** Text for the title of the header. */
  title: string
  /** Function close popover drawer. */
  closePopoverOrDrawer?: () => void

  isSingleBrandSelector?: boolean
} & SubtitleOptionTypes

export type SelectContentValues<ItemGeneric> =
  SelectOptionValues<ItemGeneric> & {
    /** String value to map for option name. */
    name: string
    /** Text to show for the option value. */
    label: string
    /** String to identify category assigned to the options. */
    // e.g.auto_brand_groups, my_brands, brand_groups, full_partner, prospect, vorys_partner, software_partner, etc.
    grouping_category: string
    /** Unique parent id for the options used only when grouping_category is auto_brand_groups. */
    parent_id?: number
    /** Array of data passed into the Select. */
    reportees?: ItemGeneric[]

    isSingleBrandSelector?: boolean
  }

export type SelectPopoverProps<
  ItemGeneric extends SelectOptionValues<ItemGeneric>,
> = {
  /** String name of the select dropdown. */
  name: string
  /** Flag show / hide the search bar. */
  searchBar?: boolean
  /** This is the text that is displayed when no data is available. */
  noOptionsText?: string
  /** Flag to determine if the data is being loaded. */
  loading: boolean
  /** Function for button action handle. */
  buttonAction?: ({
    data,
    closePopoverOrDrawer,
  }: {
    data: ItemGeneric
    closePopoverOrDrawer?: () => void
  }) => void
  options: {
    /** Unique key from our options data. Used for `key` in `options`. */
    keyName: keyof ItemGeneric
    /** Array of data passed into the Select. */
    optionsData: ItemGeneric[]
  }
  groups?: {
    /** Unique key from group sections data. Used for `key` in `optGroupSections`. */
    keyName: keyof ItemGeneric
    /** Array of data to make group section on options data. */
    groupData: GroupSections[]
  }
  /** Config of secondary values to show in option. */
  secondaryValuesConfig?: SecondaryValuesConfigType<ItemGeneric>
  /** Object of downshift with state and helper methods. */
  downshift: ControllerStateAndHelpers<ItemGeneric>
  /** Config of custom footer section to show elements. */
  customFooterConfig?: CustomFooterConfigType<ItemGeneric>
  /** Function close popover drawer. */
  closePopoverOrDrawer?: () => void
  /** Placeholder text for input search  */
  inputSearchPlaceHolder?: string
  /** Function to set searched value of the search bar */
  setSearchedString?: React.Dispatch<React.SetStateAction<string>>
  /** Function to add isExpanded flag for the clicked group and update the autoBrandGroupData state accordingly */
  updateAutoBrandGroupState?: (
    clickedId: number,
    selectedAbgParentId?: number,
  ) => void
  updateCustomer: (type: string, value?: ItemGeneric | null) => void
  nonGlobal?: boolean

  isSingleBrandSelector?: boolean
}

export type SecondaryValuesConfigType<ItemGeneric> = {
  children: ({
    data,
    downshift,
    index,
    closePopoverOrDrawer,
  }: {
    data: ItemGeneric & {
      shared_brand_group_id?: number
      shared_with?: string
      name?: string
    }
    downshift: ControllerStateAndHelpers<ItemGeneric>
    index?: number
    closePopoverOrDrawer?: () => void
  }) => React.ReactNode
}

export type CustomFooterConfigType<ItemGeneric> = {
  children: ({
    downshift,
    closePopoverOrDrawer,
    setPopoverMenuState,
  }: {
    downshift: ControllerStateAndHelpers<ItemGeneric>
    closePopoverOrDrawer?: () => void
    setPopoverMenuState?: React.Dispatch<React.SetStateAction<boolean>>
  }) => React.ReactNode
}

export type SelectPopoverDrawerProps<
  ItemGeneric extends SelectContentValues<ItemGeneric>,
> = SelectPopoverProps<ItemGeneric> & {
  /** Function to set popover state from child to parent. */
  setPopoverMenuState: React.Dispatch<React.SetStateAction<boolean>>
  /** Boolean state for popover drawer. */
  popoverMenuState: boolean

  isSingleBrandSelector?: boolean
}

type SelectWithPopoverDrawerUnionProps<
  ItemGeneric extends SelectContentValues<ItemGeneric>,
> = SelectPopoverProps<ItemGeneric> & {
  /** Function to update the latest selected option item */
  updateCustomer: (type: string, value?: ItemGeneric | null) => void
  /** Object data of selected option item. */
  selectedItem?: ItemGeneric
  /** Boolean to identify if the accordion is clicked */
  isAccordionClicked?: boolean
  /** Function to set boolean value for accordion click */
  setIsAccordionClicked?: React.Dispatch<React.SetStateAction<boolean>>
  /** Function to set searched value of the search bar */
  setSearchedString?: React.Dispatch<React.SetStateAction<string>>
  /** Function to add isExpanded flag for the clicked group and update the autoBrandGroupData state accordingly */
  updateAutoBrandGroupState?: (
    clickedId: number,
    selectedAbgParentId?: number,
  ) => void
  /** Search value of the brand selector search bar */
  searchedString?: string
  /** Boolean to identify if the brand group side drawer is open or close */
  isBrandGroupDrawerOpen?: boolean

  isSingleBrandSelector?: boolean
}

export type SelectWithPopoverDrawerProps<
  ItemGeneric extends SelectContentValues<ItemGeneric>,
> = Omit<SelectWithPopoverDrawerUnionProps<ItemGeneric>, 'downshift'>

export type FormattedType<
  ItemGeneric extends SelectContentValues<ItemGeneric>,
> = {
  /** Number id to iterate and make group sections. */
  id?: number
  /** Text for the title of the header. */
  title: string
  /** Array of objects data passed into the option values. */
  data: ItemGeneric[]
} & SubtitleOptionTypes
