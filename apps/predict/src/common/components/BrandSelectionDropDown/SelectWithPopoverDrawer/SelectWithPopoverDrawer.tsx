import React, { useEffect, useState } from 'react'
import type { ReactNode } from 'react'
import Downshift, {
  type ControllerStateAndHelpers,
  type DownshiftState,
  type StateChangeOptions,
} from 'downshift'
import { useMediaQuery } from '@patterninc/react-ui'

import SelectPopoverAndMobileDrawer from '../SelectPopoverAndMobileDrawer/SelectPopoverAndMobileDrawer'
import type {
  SelectContentValues,
  SelectWithPopoverDrawerProps,
} from '../SelectTypes/SelectTypes'

const SelectWithPopoverDrawer = <
  ItemGeneric extends SelectContentValues<ItemGeneric>,
>({
  selectedItem,
  name,
  options,
  searchBar,
  groups,
  noOptionsText,
  loading,
  buttonAction,
  secondaryValuesConfig,
  customFooterConfig,
  inputSearchPlaceHolder,
  isAccordionClicked,
  setIsAccordionClicked,
  setSearchedString,
  updateAutoBrandGroupState,
  updateCustomer,
  searchedString,
  nonGlobal,
  isSingleBrandSelector,
}: SelectWithPopoverDrawerProps<ItemGeneric>): React.JSX.Element => {
  const isMobileView = useMediaQuery({ type: 'max', breakpoint: 'md' }),
    [popoverMenuState, setPopoverMenuState] = useState(false)

  useEffect(() => {
    if (!popoverMenuState) {
      setIsAccordionClicked?.(false)

      // If Brand is selected by search below code clears the value of searchbar and reset ABG hierarchy to its default state
      if (searchedString) {
        updateAutoBrandGroupState?.(-1)
        setSearchedString?.('')
      }
    }
  }, [
    popoverMenuState,
    searchedString,
    setIsAccordionClicked,
    setSearchedString,
    updateAutoBrandGroupState,
  ])

  let highlight = false
  const stateReducer = (
    state: DownshiftState<ItemGeneric>,
    changes: StateChangeOptions<ItemGeneric>,
  ): Partial<StateChangeOptions<ItemGeneric>> => {
    const type = changes.type,
      changeTypes = Downshift.stateChangeTypes
    // to prevent from closing side-drawer on blur event
    if (
      type === changeTypes.blurInput &&
      changes.inputValue === state?.selectedItem?.customer_name
    ) {
      changes.isOpen = true
      changes.inputValue = ''
      return changes
    }

    // on Enter key press update the selected customer and close side drawer
    if (type === changeTypes.keyDownEnter) {
      changes.inputValue = ''
      updateCustomer('customer', changes.selectedItem)
      setPopoverMenuState(false)
    }

    // clear the search input except for whatever conditions you want to exclude
    if (
      type !== changeTypes.changeInput &&
      type !== changeTypes.itemMouseEnter &&
      type !== changeTypes.keyDownArrowDown &&
      type !== changeTypes.keyDownArrowUp
    ) {
      changes.inputValue = ''
    }

    // set highlighted item on selected item on initial open of dropdown
    if (!highlight && !isAccordionClicked) {
      const findMatch = options?.optionsData?.find(
        (o) =>
          o?.[options?.keyName] === state?.selectedItem?.[options?.keyName],
      )
      changes.highlightedIndex =
        state?.selectedItem && options && findMatch
          ? options?.optionsData?.indexOf(findMatch)
          : 0
    } else if (changes.inputValue) {
      changes.highlightedIndex = 0
    }
    return changes
  }

  const itemToString = (item: ItemGeneric | null): string => {
    return item === null ? '' : String(item[options.keyName])
  }

  const scrollIntoView = (item: ItemGeneric) => {
    const minPos = isMobileView ? 400 : 230
    if (item) {
      if (!highlight) {
        const myElement: HTMLElement | null = document.querySelector(
          `.scroll-${item}`,
        )
        let topPos = 0
        if (myElement) {
          topPos = myElement.offsetTop - minPos
        }
        setTimeout(() => {
          highlight = true
          const dropdownSelector = document.querySelector(
            '.dropdown-group-options-container',
          )
          if (dropdownSelector) {
            dropdownSelector.scrollTop = topPos
          }
        }, 100)
      }
    } else {
      highlight = true
    }
  }

  const onOuterClick = () => {
    highlight = false
    updateAutoBrandGroupState?.(-1)
  }

  const resetHighlight = () => {
    highlight = false
  }

  const currentItem: ItemGeneric | undefined =
    typeof selectedItem === 'object'
      ? selectedItem
      : options?.optionsData?.find(
          (o: ItemGeneric) => o[options?.keyName] === selectedItem,
        )

  const downshiftActions = (
    ds: ControllerStateAndHelpers<ItemGeneric>,
  ): ReactNode => {
    ds.isOpen && !isAccordionClicked
      ? ds.selectedItem && scrollIntoView(ds.selectedItem.id)
      : resetHighlight()
    return null
  }
  return (
    <Downshift
      stateReducer={stateReducer}
      onOuterClick={() => onOuterClick()}
      onChange={() => resetHighlight()}
      selectedItem={currentItem}
      itemToString={itemToString}
      initialHighlightedIndex={0}
      isOpen={popoverMenuState}
    >
      {(ds) => {
        return (
          <div
            style={nonGlobal ? { height: '100%', display: 'flex' } : undefined}
          >
            {downshiftActions(ds)}
            <SelectPopoverAndMobileDrawer
              name={name}
              options={options}
              searchBar={searchBar}
              groups={groups}
              noOptionsText={noOptionsText}
              loading={loading}
              buttonAction={buttonAction}
              downshift={ds}
              secondaryValuesConfig={secondaryValuesConfig}
              customFooterConfig={customFooterConfig}
              popoverMenuState={popoverMenuState}
              setPopoverMenuState={setPopoverMenuState}
              inputSearchPlaceHolder={inputSearchPlaceHolder}
              setSearchedString={setSearchedString}
              updateAutoBrandGroupState={updateAutoBrandGroupState}
              updateCustomer={updateCustomer}
              nonGlobal={nonGlobal}
              isSingleBrandSelector={isSingleBrandSelector}
            />
          </div>
        )
      }}
    </Downshift>
  )
}

export default SelectWithPopoverDrawer
