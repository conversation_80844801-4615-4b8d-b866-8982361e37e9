@use '@patterninc/react-ui/dist/variables' as variables;

.dropdownOptionSecondaryValue {
  display: flex;
  align-items: center;
  text-transform: uppercase;
  color: var(--purple);
  font-size: var(--font-size-10);
  height: 100%;
}

.dropdownViewItemTextOption {
  border-left-width: 1px;
  border-left-style: solid;
  text-transform: uppercase;
  border-color: var(--light-gray);
  color: var(--blue);
  font-size: var(--font-size-10);
  padding: 0 20px; // updated as per prototype style
  @media only screen and (max-width: variables.$breakpoint-md) {
    padding: 0 27px; // updated as per prototype style
  }
}

.brandCountText {
  font-size: var(--font-size-10);
  padding-left: 6px; // updated as per prototype style
  border-left-width: 1px;
  border-left-style: solid;
  text-transform: uppercase;
  border-color: var(--light-gray);
  color: var(--blue);
  width: 90px;
}

.selectAllBrandsFooter {
  z-index: 1;
  padding: 0 18px;
  background-color: var(--lighter-gray);
  font-weight: var(--font-weight-medium);
  font-size: var(--font-size-12);
  position: absolute;
  left: -1px;
  right: -1px;
  border: 1px solid var(--light-gray);
  box-shadow: 0px 3px 6px 0 var(--button-shadow-color);
  white-space: nowrap;
  border-radius: 0 0 4px 4px;

  line-height: 36px;
  display: flex;
  align-items: center;
  cursor: pointer;
  transition: all 0.25s ease-in-out;
  margin-top: -22px;

  @media only screen and (max-width: variables.$breakpoint-md) {
    bottom: 0;
  }
}

.allBrandsActive {
  font-size: var(--font-size-12);
  font-weight: var(--font-weight-bold);
}

.flipped {
  transform: rotate(180deg);
}

.caret {
  margin-left: 13px;

  svg path {
    stroke: var(--medium-blue);
  }
}

.brandCountContainer {
  width: 60px;
}
.transform90 {
  transform: rotate(90deg);
}

.fullWidthBtn {
  width: 100%;
}

.fullHeight {
  height: 100%;
}

.disabledText {
  opacity: 0.5;
}
.noTextTransform {
  text-transform: none;
}
