import { useContext, useEffect, useState } from 'react'
import { useLocation } from 'react-router-dom'
import { isMatchPath } from '@predict-services'

import { ThemeContext } from '../../../Context'

export const useShowBrandSelector = () => {
  const { hideGlobalBrandSelector } = useContext(ThemeContext)

  const [isBrandSelectorVisible, setIsBrandSelectorVisible] = useState(true)
  const { pathname } = useLocation()
  const disableOnRoutes = [
    '/reports/custom',

    // Disable Brand Selector on MARKETSHARE > DETAILS PAGES
    '/insights/share/marketshare/categories/:id/*',
    '/insights/share/marketshare/brands/:id/*',
    '/insights/share/marketshare/keywords/:id/*',

    // Disable Brand Selector on Advisory > Market Analytics > Details Pages
    '/advisory/market-analytics/categories/:id/*',
    '/advisory/market-analytics/brands/:id/*',
    '/advisory/market-analytics/keywords/:id/*',
  ]
  const disableOnModules = ['/support', '/planning']
  const exceptionEnabledModules = ['/planning/joint-business-plan']

  const isExceptionRoute = exceptionEnabledModules.some((route) =>
    pathname.startsWith(route),
  )

  const isDisabledRoute = disableOnRoutes.some((route) =>
    isMatchPath(route, pathname),
  )

  const isDisabledModule = disableOnModules.some(
    (module) => isMatchPath(module, pathname, false) && !isExceptionRoute,
  )

  useEffect(() => {
    setIsBrandSelectorVisible(
      !(isDisabledRoute || isDisabledModule || hideGlobalBrandSelector),
    )
  }, [hideGlobalBrandSelector, isDisabledModule, isDisabledRoute])

  return isBrandSelectorVisible
}
