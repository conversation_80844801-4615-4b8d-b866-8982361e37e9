import type { Meta, StoryObj } from '@storybook/react'

import <PERSON><PERSON><PERSON><PERSON> from './ButtonWithOptions'

// More on how to set up stories at: https://storybook.js.org/docs/writing-stories#default-export
const meta = {
  title: 'Components/ButtonWithOptions',
  component: ButtonWO,
  parameters: {
    // Optional parameter to override the layout of the component. Default is 'fullscreen'. More info: https://storybook.js.org/docs/configure/story-layout
    layout: 'centered',
  },
  // This component will have an automatically generated Autodocs entry: https://storybook.js.org/docs/writing-docs/autodocs
  tags: ['autodocs'],
} satisfies Meta<typeof ButtonWO>

export default meta
type Story = StoryObj<typeof meta>

export const Basic: Story = {
  args: {
    tippyPlacement: 'bottom',
    id: 'button_with_options_storybook_example',
    options: [
      {
        text: 'Download As',
        callout: () => null,
      },
      // The existing CSV download option
      {
        csv: {
          linkName: 'CSV LinkName',
          csvName: 'Storybook CSV File Name',

          csvFormat: {
            api: (params) =>
              new Promise((resolve) => {
                console.log('CSV Download params:', params)
                resolve(new Blob(['CSV Blob']))
              }),
            params: {
              csvParams: 'the csv params for the csv download api call.',
            },
          },
        },
      },

      // Section divider && header
      {
        hasDivider: true,
        text: 'Download Chart As',
        callout: () => null,
      },
      // The new image capture options
      {
        text: 'Square Image',
        icon: 'frame',
        callout: () => {
          console.log('Square Image')
        },
      },
      {
        text: 'Landscape Image',
        icon: 'fullScreen',
        callout: () => {
          console.log('Landscape Image')
        },
      },
    ],
  },
}
