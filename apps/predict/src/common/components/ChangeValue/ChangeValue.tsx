import React from 'react'
import { NumericFormat } from 'react-number-format'
import { Icon } from '@patterninc/react-ui'
import {
  type IconColorList,
  type IconSizes,
} from '@patterninc/react-ui/dist/components/Icons/Icon.models'
import { c } from '@predict-services'

import { DetermineNumberType } from '../../services/NumberFormatService'
import { getChangeObj, type SecondChangeProps } from './ChangeValueHelpers'

type ChangeValueProps = {
  /** The change value determines what icon / color to show. This number will be displayed next to the icon by default */
  change: number
  /** Used for displaying a number different than the number used to calculate the change */
  displayValue?: number
  /** Used for displaying a second change value with a divider */
  secondChangeProps?: SecondChangeProps
  /** Changes the icon class */
  customIconClass?: IconColorList
  /** Changes the displayed number's class */
  customClass?: string
  /** Determines the number format of the displayed number. Defaults to number */
  changeFormat?: 'percentage' | 'currency' | 'number'
  /** Determines how many decimals to show of the display number. Defaults to 0 (no decimal) */
  decimalScale?: number
  /** Used for displaying a currency symbol and code for monetary values */
  currency?: { currency_code: string; currency_symbol: string }
  /** Used for percentages. Determines if the percent should be displayed if it is less than 1%. False by default, displaying a "< 1%" message */
  showLessThanOnePercent?: boolean
  /** Used to display the icon below the display number. Defaults to false */
  multilineValue?: boolean
  /** Used to reverse the icon direction and font colors for certain metrics. Defaults to false */
  reverse?: boolean
  /** Used to reverse only the icon direction. Defaults to false */
  reverseIcon?: boolean
  /** Used to center the display and icon. Defaults to false */
  center?: boolean
  /** Used to make the font size larger and removes top padding. Defaults to false */
  fullSize?: boolean
  /** Used to show the trend icon after the second change value and make divider color green or red */
  changeValueNewFormat?: boolean
  /** Optional prop to provide custom size for trend icon. Defaults to '12px' */
  customIconSize?: IconSizes
}

const ChangeValue = ({
  change,
  displayValue,
  secondChangeProps,
  customIconClass,
  customClass = '',
  changeFormat = 'number',
  decimalScale = 0,
  currency,
  showLessThanOnePercent = false,
  multilineValue = false,
  reverse = false,
  reverseIcon = false,
  center = false,
  fullSize = false,
  changeValueNewFormat = false,
  customIconSize = '12px',
}: ChangeValueProps): React.JSX.Element => {
  const { fontClassName, iconClassName, icon, dividerClassName } = getChangeObj(
    {
      change,
      reverse,
      reverseIcon,
    },
  )

  const value = displayValue || change,
    fixedDecimalScale =
      !!decimalScale ||
      (!!currency?.currency_code && currency?.currency_code !== 'USD')

  return (
    <div
      className={`${
        multilineValue
          ? 'flex flex-direction-column pat-gap-1'
          : 'flex align-items-center pat-gap-2'
      } ${center ? 'justify-content-center' : ''}
       ${fullSize ? '' : 'fs-10 pat-pt-1'}`}
    >
      {change === 0 ? (
        <span className={fontClassName}>{c('noChange')}</span>
      ) : (
        <DetermineNumberType
          val={Math.abs(value)}
          customClass={`${fontClassName} ${customClass}`}
          type={changeFormat}
          decimalScale={decimalScale}
          showLessThanZero={showLessThanOnePercent}
          currency={currency}
        />
      )}
      {!changeValueNewFormat && (
        <Icon
          icon={icon}
          iconSize={customIconSize}
          color={iconClassName || customIconClass}
        />
      )}

      {secondChangeProps && !!secondChangeProps.change && (
        <>
          <div
            className={
              changeValueNewFormat ? dividerClassName : 'bgc-medium-purple'
            }
            style={{ height: '10px', width: '1px' }}
          />
          <NumericFormat
            className={`${fontClassName} ${secondChangeProps.customClass ?? ''}`}
            decimalScale={secondChangeProps.decimalScale ?? 0}
            displayType='text'
            fixedDecimalScale={fixedDecimalScale}
            suffix={secondChangeProps.suffix}
            thousandSeparator
            value={Math.abs(secondChangeProps.change)}
          />
        </>
      )}
      {changeValueNewFormat && (
        <Icon
          icon={icon}
          iconSize={customIconSize as IconSizes}
          color={(iconClassName || customIconClass) as IconColorList}
        />
      )}
    </div>
  )
}

export default ChangeValue
