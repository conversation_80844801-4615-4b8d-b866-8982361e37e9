import { type IconColorList } from '@patterninc/react-ui/dist/components/Icons/Icon.models'

type GetChangeObjType = {
  change: number
  reverse?: boolean
  reverseIcon?: boolean
}

type ChangeObj = {
  iconClassName: IconColorList
  fontClassName: string
  dividerClassName?: string
  text: string
  icon: 'trendEven' | 'trendUp' | 'trendDown'
}

export type SecondChangeProps = {
  change: number
  suffix?: string
  decimalScale?: number
  customClass?: string
}

export const getChangeObj = ({
  change,
  reverse,
  reverseIcon,
}: GetChangeObjType): ChangeObj => {
  if (change < 0) {
    return {
      fontClassName: reverse ? 'fc-dark-green' : 'fc-dark-red',
      iconClassName: reverse ? 'dark-green' : 'dark-red',
      dividerClassName: reverse ? 'bgc-dark-green' : 'bgc-dark-red',
      ...(!reverseIcon
        ? { text: 'Decrease', icon: 'trendDown' }
        : {
            text: 'Increase',
            icon: 'trendUp',
          }),
    }
  } else if (change > 0) {
    return {
      fontClassName: reverse ? 'fc-dark-red' : 'fc-dark-green',
      iconClassName: reverse ? 'dark-red' : 'dark-green',
      dividerClassName: reverse ? 'bgc-dark-red' : 'bgc-dark-green',
      ...(!reverseIcon
        ? { text: 'Increase', icon: 'trendUp' }
        : {
            text: 'Decrease',
            icon: 'trendDown',
          }),
    }
  } else {
    return {
      fontClassName: 'fc-dark-blue',
      iconClassName: 'dark-blue',
      text: 'No Change',
      icon: 'trendEven',
    }
  }
}

export const getChangeFormat = (
  type?: string,
): 'number' | 'currency' | 'percentage' => {
  switch (type) {
    case 'currency':
      return 'currency'
    case 'percentage':
    case 'percent':
      return 'percentage'
    default:
      return 'number'
  }
}
