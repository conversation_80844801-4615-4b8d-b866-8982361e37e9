import React from 'react'
import { type ConfigItemType, useIsMobileView } from '@patterninc/react-ui'

import styles from './_custom-legend.module.scss'
import { type LegendProps } from '../chart.type'
import LegendTable from './LegendTable'

type ChartCustomLegendType<DataItem> = {
  generateConfig: () => ConfigItemType<
    DataItem & { color?: string },
    Record<string, unknown>
  >[]
  tableId: string
  data: DataItem[]
  idKey: keyof DataItem
  /** Pass the charts responsive container's height in px. Defaults to 282px */
  responsiveContainerHeight?: number
}

const ChartCustomLegend = <DataItem,>({
  generateConfig,
  tableId,
  payload,
  data,
  idKey,
  responsiveContainerHeight = 282,
}: ChartCustomLegendType<DataItem> & LegendProps): React.JSX.Element => {
  const isMobileView = useIsMobileView()

  // Assigning DataItem a `color` key, which will be used as color indicator
  const dataWithColor = data?.map((item) =>
    Object.assign(item as DataItem & Record<string, unknown>, {
      color:
        // TODO: assign the default color based on common `rank` key from the response
        payload?.find((p) =>
          // Removing Duplicate products
          idKey === 'id'
            ? p.payload.id === item[idKey]
            : p.value === item[idKey],
        )?.color ?? 'var(--light-gray)',
    }),
  )

  return (
    <div
      className={
        isMobileView
          ? 'bdrt bdrc-light-gray'
          : 'bdrl bdrc-light-gray pat-ml-6 pat-pl-4'
      }
    >
      <div
        style={{ height: responsiveContainerHeight }}
        className={styles.scrollContainer}
      >
        <LegendTable
          config={generateConfig()}
          data={dataWithColor ?? []}
          dataKey={idKey}
          tableId={tableId}
          customHeight={!isMobileView ? responsiveContainerHeight : undefined} // `282px` to match the height of the chart responsive container in desktop view
          customWidth='100%'
        />
      </div>
    </div>
  )
}

export default ChartCustomLegend
