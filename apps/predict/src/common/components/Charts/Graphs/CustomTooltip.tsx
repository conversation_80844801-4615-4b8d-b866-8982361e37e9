import React from 'react'
import { NumericFormat } from 'react-number-format'
import moment from 'moment'
import { Mdash, sortFilter } from '@patterninc/react-ui'
import { currencySuffix, useTranslate } from '@predict-services'
import { formatDate } from 'src/common/services/translations/translationHelpers'

interface TooltipItem {
  key: string
  tooltipLabel?: string
  barColor?: string
  width?: string
  countColor?: string
  stroke?: string
  prefix?: string
  suffix?: string
  thousandSeparator?: boolean
  currencySymbol?: string
  currencyCode?: string
  hideTooltip?: boolean
  value?: number | string
}

interface PayloadItem {
  dataKey: string
  tooltipId?: number
  payload: Record<string, unknown>
  [key: string]: unknown
}

interface CustomTooltipProps {
  active?: boolean
  payload?: PayloadItem[]
  label?: string
  className?: string
  tooltipSecondDate?: boolean
  tooltipOrder?: string[]
  keyLegend: TooltipItem[]
  customDateFormat?: string
  minimumFractionDigits?: number
}

const CustomTooltip = ({
  active,
  payload = [],
  label,
  className = '',
  tooltipSecondDate,
  tooltipOrder,
  keyLegend,
  customDateFormat,
  minimumFractionDigits = 2,
}: CustomTooltipProps) => {
  const { t: tf } = useTranslate('timeframe')

  // Handle tooltipOrder - sort payload based on the order
  let sortedPayload = [...payload]
  if (tooltipOrder) {
    sortedPayload = sortedPayload.map((e) => {
      const tooltipId = tooltipOrder.indexOf(e.dataKey) + 1
      return { ...e, tooltipId }
    })
    sortedPayload = sortFilter(sortedPayload, 'tooltipId', false)
  }

  // Set date format and prepare tooltip data
  const dateFormat = customDateFormat ? customDateFormat : 'MMM Do'
  const tooltipPayload = sortedPayload?.[0]?.payload
    ? sortedPayload[0].payload
    : {}
  const tooltipData = keyLegend.map((item) => ({
    ...item,
    value: tooltipPayload[item.key],
  }))
  const day = formatDate(dateFormat, label ?? '', tf('dateTimeFormat'))

  if (active) {
    return (
      <div className={`${className}-tooltip basic-tooltip`}>
        <div className='top-section flex'>
          <div className='pat-pb-4 fc-dark-purple fs-12 fw-bold'>{day}</div>
          {tooltipSecondDate && (
            <React.Fragment>
              <span className='second-date-mdash'>
                <Mdash />
              </span>
              <h1 className='title second-title'>
                {moment(new Date(label as string | number | Date))
                  .add(6, 'days')
                  .format('MMM Do')}
              </h1>
            </React.Fragment>
          )}
        </div>
        <div className='stat-box justify-content-between'>
          {tooltipData
            .filter(
              (item) => !item.hideTooltip && !item.key.includes('dontuse'),
            )
            .map((e) => (
              <div
                key={e.key}
                className='stat-container pat-my-2 flex justify-content-between'
              >
                <div className='flex'>
                  {e.barColor && (
                    <div
                      className='pat-mr-2.5'
                      style={{
                        borderRadius: '2px',
                        height: '100%',
                        width: e.width || '4px',
                        backgroundColor: e.barColor,
                      }}
                    ></div>
                  )}
                  <div className='title'>
                    {e.tooltipLabel
                      ? e.tooltipLabel
                      : e.key
                          .toLocaleString()
                          .split('_')
                          .map(
                            (word) =>
                              word.charAt(0).toUpperCase() + word.slice(1),
                          )
                          .join(' ')}
                  </div>
                </div>
                <div
                  className='stat pat-ml-8'
                  style={{ color: e.countColor || e.stroke }}
                >
                  {(e.value || e.value === 0) && e.prefix ? e.prefix : ''}
                  {!(e.value || e.value === 0) ? (
                    <Mdash />
                  ) : e.prefix && e.prefix === '$' ? (
                    (e.value as number).toLocaleString(undefined, {
                      minimumFractionDigits: minimumFractionDigits,
                    })
                  ) : e.thousandSeparator ? (
                    <>
                      <NumericFormat
                        value={e.value as string | number}
                        displayType='text'
                        thousandSeparator
                        className={''}
                        {...(e.currencySymbol
                          ? { prefix: e.currencySymbol }
                          : {})}
                      />
                      {e.currencyCode && (
                        <span className='currency-code'>
                          {currencySuffix(e.currencyCode)}
                        </span>
                      )}
                    </>
                  ) : (
                    (e.value as number).toLocaleString()
                  )}
                  {(e.value || e.value === 0) && e.suffix ? e.suffix : ''}
                </div>
              </div>
            ))}
        </div>
      </div>
    )
  }
  return null
}

export default CustomTooltip
