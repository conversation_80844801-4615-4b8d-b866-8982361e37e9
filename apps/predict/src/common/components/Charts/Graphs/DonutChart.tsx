import React, { useCallback, useEffect } from 'react'
import { NumericFormat } from 'react-number-format'
import { <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, Responsive<PERSON>ontainer, Toolt<PERSON> } from 'recharts'
import {
  checkboxColorOptions,
  HeaderMetric,
  largeNumConversion,
  Mdash<PERSON>heck,
  PercentageCheck,
  <PERSON><PERSON><PERSON> as TooltipComponent,
  useWindowSize,
} from '@patterninc/react-ui'
import { type TrafficGeneralType } from 'src/modules/Traffic/services/TrafficTableHeaderHelperService'

import styles from './_donut-chart.module.scss'

type DataItem = {
  name: string
  count: number
  tooltipMetricDetails?: TrafficGeneralType
  percentage?: number | string
  tooltipText?: string
}
interface Props {
  data: DataItem[]
  dataKey?: string
  radius?: { outer: number; inner: number }
  colors?: string[]
  tooltipContent?: (param: {
    name: string
    count: number
    tooltipMetricDetails: TrafficGeneralType
    percentage?: number
  }) => React.JSX.Element
  showPercentage?: boolean
}

const DonutChart = ({
  data,
  dataKey = 'count',
  radius = { outer: 80, inner: 55 },
  colors = [],
  tooltipContent = (param) => {
    let decimalScale = 0
    const { val: countValue, suffix: countSuffix } = largeNumConversion(
      param?.count,
    )

    if (Math.abs(Number(param?.count)) >= 1.0e6) {
      decimalScale = 2
    }
    const headerMetricProps = {
      title: param?.name,
      value: param?.percentage,
      ...(param?.tooltipMetricDetails?.change
        ? { change: param.tooltipMetricDetails.change }
        : {}),
      comparison: param?.tooltipMetricDetails?.comparison,
      ...(param?.tooltipMetricDetails?.pct_change
        ? {
            pctChange: param.tooltipMetricDetails.pct_change
              ? param.tooltipMetricDetails.pct_change * 100
              : param.tooltipMetricDetails.pct_change,
          }
        : {}),
      type: param?.tooltipMetricDetails?.type,
      formatType: 'percentage' as 'number' | 'percentage' | undefined,
      loading: false,
      fontSize: 'fs-12' as 'fs-22' | 'fs-16' | 'fs-18' | undefined,
      className: styles.tooltipHeader,
      noPctConversion: true,
      truncateValues: true,
      decimalScale: decimalScale,
      changeValueFormatType: 'number' as 'number' | 'percentage' | undefined,
    }
    if (param?.tooltipMetricDetails) {
      return (
        <div
          className={`bgc-white bdr bdrr-4 ${styles.tooltipText}`}
          style={{
            border: '1px solid var(--light-gray)',
            boxShadow: '0 2px 2px 0 var(--button-shadow-color)',
          }}
        >
          <HeaderMetric {...headerMetricProps} />
        </div>
      )
    } else {
      return (
        <div
          className={`bgc-white bdr bdrr-4 pat-p-2 fw-bold ${styles.tooltipText}`}
          style={{
            border: '1px solid var(--light-gray)',
            boxShadow: '0 2px 2px 0 var(--button-shadow-color)',
          }}
        >
          <div className={styles.legendItem}>
            <div>
              <div className={styles.legendText}>{param?.name}</div>
              <div className={`flex ${styles.metricStyle}`}>
                <div>
                  <NumericFormat
                    value={countValue?.toFixed(decimalScale)}
                    displayType='text'
                    thousandSeparator
                    className={'fs-12'}
                    suffix={countSuffix}
                    decimalScale={decimalScale}
                  />
                </div>
                <div>
                  <PercentageCheck
                    noConversion
                    percent={Number(param?.percentage)}
                    showLessThanZero
                    customClass='fs-12'
                    decimalScale={decimalScale}
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
      )
    }
  },
}: Props) => {
  const updatedColors = colors.length
      ? colors.concat(checkboxColorOptions)
      : checkboxColorOptions,
    dataWithColors = data.map((item, index) => ({
      ...item,
      fill: `var(--${updatedColors[index]})`,
    }))

  const total = dataWithColors.reduce((acc, item) => acc + item.count, 0)
  const updatedData = dataWithColors.map((item) => ({
    ...item,
    percentage: Number.isInteger((item.count / total) * 100)
      ? (item.count / total) * 100
      : ((item.count / total) * 100).toFixed(2),
  }))
  const size = useWindowSize()
  const [showMetricBelow, setShowMetricBelow] = React.useState(false)

  const updateWidth = useCallback(() => {
    const w = size?.width || 0

    if ((w < 1200 && w > 768) || w < 568) setShowMetricBelow(true)
    else setShowMetricBelow(false)
  }, [size?.width])

  useEffect(() => {
    updateWidth()
  }, [updateWidth])

  const RenderCusomizedLegend = () => (
    <div className={styles.legendContainer}>
      {updatedData.map((item, index) => {
        const getPercentValue = () => {
          return (
            <MdashCheck check={!!item.percentage}>
              <PercentageCheck
                noConversion
                percent={Number(item.percentage)}
                decimalScale={2}
                showLessThanZero
                customClass='fs-12'
              />
            </MdashCheck>
          )
        }
        const getTooltipContent = (status: string) => {
          switch (status) {
            case 'Successful':
              return 'Successful bid optimizations shows how many times your bids were successfully optimized by Predict.'
            case 'Pending':
              return `Pending bid optimizations shows how many optimizations Predict has scheduled but has not yet submitted to the marketplace.`
            case 'Rejected':
              return `Rejected bid optimizations shows how many times bid optimizations were submitted by Predict but were rejected by the marketplace.`
            case 'Missed':
              return `Missed bid optimizations shows how many optimizations Predict scheduled but did not properly submit to the marketplace.`
          }
        }
        let decimalScale = 0
        const { val: countValue, suffix: countSuffix } = largeNumConversion(
          item?.count,
        )
        if (Math.abs(Number(item?.count)) >= 1.0e6) {
          decimalScale = 2
        }
        const getLegendItem = (item: DataItem) => (
          <div>
            <div className={styles.legendText}>{item?.name} </div>
            <div className={`flex ${styles.metricStyle}`}>
              <div>
                <NumericFormat
                  value={countValue.toFixed(2)}
                  displayType='text'
                  thousandSeparator
                  className={'fs-12'}
                  suffix={countSuffix}
                  decimalScale={decimalScale}
                />
              </div>
              <div className={showMetricBelow ? styles.displayNone : ''}>
                {getPercentValue()}
              </div>
            </div>
            <div
              className={
                showMetricBelow ? styles.alignLeftTxt : styles.displayNone
              }
            >
              {getPercentValue()}
            </div>
          </div>
        )
        const showTooltip = [
          'Successful',
          'Pending',
          'Rejected',
          'Missed',
        ].includes(item?.name)
        return (
          <div key={index} className={styles.legendItem}>
            <div
              className={styles.legendColor}
              style={{ backgroundColor: item.fill }}
            />
            {showTooltip ? (
              <TooltipComponent
                tooltipContent={
                  <div className={`flex flex-direction-column fs-12`}>
                    <span>{getTooltipContent(item?.name)}</span>
                  </div>
                }
                position='top'
                maxWidth='300px'
                className='pat-pl-2'
              >
                {getLegendItem(item)}
              </TooltipComponent>
            ) : (
              getLegendItem(item)
            )}
          </div>
        )
      })}
    </div>
  )

  return (
    <div
      className={`${styles.chartContainer} ${size?.width && size?.width < 1200 ? styles.appendHeight : ''}`}
    >
      <ResponsiveContainer>
        <PieChart
          margin={{
            top: 5,
            right: 60,
            left: 0,
            bottom: 5,
          }}
        >
          <Pie
            data={updatedData}
            dataKey={dataKey}
            innerRadius={radius.inner}
            outerRadius={radius.outer}
            blendStroke
            style={{ outline: 'none' }}
          />
          <Tooltip
            content={(p) =>
              updatedData.length > 3
                ? tooltipContent(p?.payload?.[0]?.payload)
                : undefined
            }
          />
          <Legend
            layout='vertical'
            verticalAlign='middle'
            align='right'
            content={<RenderCusomizedLegend />}
            wrapperStyle={{ left: 97 }}
          />
        </PieChart>
      </ResponsiveContainer>
    </div>
  )
}

export default DonutChart
