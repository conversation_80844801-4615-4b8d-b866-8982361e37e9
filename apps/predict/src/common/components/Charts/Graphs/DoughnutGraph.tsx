import React, { useMemo } from 'react'
import { <PERSON>, Pie<PERSON><PERSON>, ResponsiveContainer } from 'recharts'

const GREEN_COLOR = 'var(--green)',
  RED_COLOR = 'var(--red)',
  YELLOW_COLOR = 'var(--yellow)',
  BLUE_COLOR = 'var(--blue)'

const statColors: Record<string, string> = {
  success: GREEN_COLOR,
  error: RED_COLOR,
  middle: YELLOW_COLOR,
  primary: BLUE_COLOR,
}

interface DoughnutGraphProps {
  pctComplete: string | number
  graphColor?: string
  startPosition?: number
  animate?: boolean
  animationDelay?: number
  noDataGraph?: boolean
}

const DoughnutGraph = ({
  pctComplete,
  graphColor,
  startPosition = 90,
  animate = false,
  animationDelay = 0,
  noDataGraph = false,
}: DoughnutGraphProps) => {
  const data = useMemo(
    () => [{ value: parseFloat(pctComplete?.toString()) }],
    [pctComplete],
  )

  const displayColor = useMemo(() => {
    return graphColor && statColors[graphColor]
      ? statColors[graphColor]
      : graphColor
        ? graphColor
        : RED_COLOR
  }, [graphColor])

  const endPosition: number = useMemo(() => {
    return startPosition - 360 * parseFloat(pctComplete?.toString())
  }, [startPosition, pctComplete])

  return (
    <ResponsiveContainer width='99%'>
      <PieChart>
        <Pie
          data={[{ value: 1 }]}
          innerRadius={noDataGraph ? '0%' : '90%'}
          outerRadius='91%'
          startAngle={startPosition}
          endAngle={startPosition - 360}
          isAnimationActive={false}
          paddingAngle={0}
          blendStroke
          fill={noDataGraph ? 'var(--faint-gray)' : 'var(--light-gray)'}
          dataKey='value'
        />
        <Pie
          data={data}
          innerRadius='88%'
          outerRadius='93%'
          startAngle={startPosition}
          endAngle={endPosition}
          cornerRadius={20}
          fill={displayColor}
          dataKey='value'
          blendStroke
          labelLine={false}
          isAnimationActive={animate}
          animationBegin={animationDelay}
          animationDuration={1000}
        />
      </PieChart>
    </ResponsiveContainer>
  )
}

export default DoughnutGraph
