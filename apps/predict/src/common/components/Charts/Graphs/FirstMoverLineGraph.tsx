import React from 'react'
import {
  CartesianGrid,
  Line,
  LineChart,
  ReferenceLine,
  ResponsiveContainer,
  Tooltip,
  XAxis,
  YAxis,
} from 'recharts'

import { getGraphColors } from '../../../../modules/Settings/components/Pages/Products/Products/services/ProductsCommonService'
import TwoLineHourlyLabel from '../Labels/TwoLineHourlyLabel'
import FirstMoverTooltip from '../Tooltips/FirstMoverTooltip'

const GREEN_COLOR = 'var(--green)',
  RED_COLOR = 'var(--red)',
  PURPLE_COLOR = 'var(--purple)',
  BLUE_COLOR = 'var(--lavender)'

const statColors = {
  success: GREEN_COLOR,
  error: RED_COLOR,
  standard: BLUE_COLOR,
}

const colors = getGraphColors()

const createDataKeyLegend = (obj: { tooltip?: string; date?: string }) => {
  if (obj.tooltip) {
    delete obj.tooltip
  }
  if (obj.date) {
    delete obj.date
  }
  const length = Object.keys(obj).length,
    keyLegend = []
  let colorIndex = 0
  for (let i = 0; i < length; i++) {
    if (colorIndex === colors.length) {
      colorIndex = 0
    }
    keyLegend.push({
      key: Object.keys(obj)[i],
      stroke: colors[colorIndex],
    })
    colorIndex++
  }
  return keyLegend
}

type FirstMoverLineGraphProps = {
  graphData: Record<string, unknown>[]
  isImageCapture?: boolean
  lineColor?: string
  productMapPrice: number
  prefix?: string
  suffix?: string
}

const FirstMoverLineGraph = ({
  graphData,
  isImageCapture,
  lineColor,
  productMapPrice,
  prefix,
  suffix,
}: FirstMoverLineGraphProps) => {
  const singleObj = Object.assign({}, graphData[0]),
    keyLegend = createDataKeyLegend(singleObj)
  return (
    <ResponsiveContainer height={200} width='99%'>
      <LineChart
        data={graphData}
        margin={{
          top: 30,
          right: 20,
          left: 5,
          bottom: 5,
        }}
      >
        <CartesianGrid vertical={false} />
        <Tooltip
          content={
            <FirstMoverTooltip
              productMapPrice={productMapPrice}
              prefix={prefix}
              suffix={suffix}
            />
          }
        />
        <YAxis
          axisLine={false}
          tickLine={false}
          tickCount={5}
          tickMargin={5}
          width={45}
          orientation='right'
          tickFormatter={(t) => {
            let tick = t.toLocaleString()
            if (prefix) {
              tick = prefix + tick
            }
            if (suffix) {
              tick += suffix
            }
            return tick
          }}
          style={{
            fontSize: '12px',
            fill: PURPLE_COLOR,
          }}
          type='number'
          domain={
            keyLegend.length > 0
              ? [
                  (dataMin: number) => {
                    if (productMapPrice && productMapPrice < dataMin) {
                      return productMapPrice - (productMapPrice % 20)
                    } else {
                      return dataMin - (dataMin % 20)
                    }
                  },
                  (dataMax: number) => {
                    if (productMapPrice && productMapPrice > dataMax) {
                      return productMapPrice - (productMapPrice % 20) + 20
                    } else {
                      return dataMax - (dataMax % 20) + 20
                    }
                  },
                ]
              : [
                  0,
                  productMapPrice
                    ? Math.round(productMapPrice) -
                      (Math.round(productMapPrice) % 20) +
                      20
                    : 'auto',
                ]
          }
        />
        <XAxis
          dataKey='date'
          axisLine={false}
          domain={
            keyLegend.length > 0
              ? [() => graphData[0]?.date, (dataMax: number) => dataMax]
              : ['auto', 'auto']
          }
          tickCount={11}
          type='number'
          padding={{ right: 80 }}
          interval='preserveStartEnd'
          height={45}
          tickLine={false}
          tickMargin={10}
          tick={(props) => <TwoLineHourlyLabel {...props} />}
        />
        <ReferenceLine
          x={graphData[graphData.length - 1]?.date as string}
          label={{
            position: 'top',
            value: 'NOW',
            fill: PURPLE_COLOR,
          }}
          isFront={true}
          stroke='var(--medium-purple)'
          strokeWidth={1}
        />
        <ReferenceLine
          y={productMapPrice}
          stroke={GREEN_COLOR}
          strokeWidth={4}
        />
        {keyLegend.map((line, i) => {
          return (
            <Line
              key={i}
              type='stepAfter'
              dataKey={line.key}
              dot={false}
              activeDot={false}
              strokeWidth={4}
              stroke={
                lineColor
                  ? statColors[lineColor as keyof typeof statColors]
                  : line.stroke
              }
              animationDuration={700}
              isAnimationActive={!isImageCapture}
            />
          )
        })}
      </LineChart>
    </ResponsiveContainer>
  )
}

export default FirstMoverLineGraph
