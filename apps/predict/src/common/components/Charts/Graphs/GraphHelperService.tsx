import moment, { type DurationInputArg2, type MomentInput } from 'moment'
import omit from 'lodash/omit'

import { type Event } from '../../../../modules/Traffic/components/common/AdvertisingGraphData/TrafficEventList'
import {
  type BarChartGraphData,
  type DataSetItem,
} from '../../../../modules/Traffic/components/common/AdvertisingGraphData/trafficGraphData.models'
import { type LineGraphData } from './LineGraph'

const GREEN_COLOR = 'var(--green)',
  RED_COLOR = 'var(--red)',
  PURPLE_COLOR = 'var(--purple)',
  BLUE_COLOR = 'var(--lavender)',
  ORANGE_COLOR = 'var(--orange)',
  transparent = 'var(--white)'

const colors = [GREEN_COLOR, RED_COLOR, PURPLE_COLOR, BLUE_COLOR, transparent]

type CreateDataKeyLegendType = {
  obj: {
    tooltip?: React.ReactNode
    date?: number
  }
  colorSet?: string[]
}

export const createDataKeyLegend = ({
  obj,
  colorSet,
}: CreateDataKeyLegendType) => {
  const colorPalette = colorSet ? colorSet : colors
  if (obj.tooltip) {
    delete obj.tooltip
  }
  if (obj.date) {
    delete obj.date
  }
  const length = Object.keys(obj).length
  const keyLegend: { key: string; stroke: string; strokeWidth?: number }[] = []
  for (let i = 0; i < length; i++) {
    keyLegend.push({
      key: Object.keys(obj)[i],
      stroke: colorPalette[i],
    })
  }
  return keyLegend
}

export const abbreviateNumber = (num: number, suffix?: string) => {
  if (suffix === '%') {
    return num
  }
  if (num >= 1000000000) {
    return (num / 1000000000).toFixed(1).replace(/\.0$/, '') + 'B'
  }
  if (num >= 1000000) {
    return (num / 1000000).toFixed(1).replace(/\.0$/, '') + 'M'
  }
  if (num >= 1000) {
    return (num / 1000).toFixed(1).replace(/\.0$/, '') + 'K'
  }
  return num
}

export const statColors = () => {
  return {
    success: GREEN_COLOR,
    error: RED_COLOR,
    standard: BLUE_COLOR,
    average: ORANGE_COLOR,
  }
}

export const graphColors = {
  green: 'var(--green)',
  red: 'var(--red)',
  lavender: 'var(--lavender)',
  blue: 'var(--blue)',
  gray: 'var(--medium-purple)',
  purple: 'var(--purple)',
  chartDark3Teal: 'var(--chart-dark-3-teal)',
  darkPurple: 'var(--dark-purple)',
  chartDark3Red: 'var(--chart-dark-3-red)',
  chartDarkBlue: 'var(--dark-blue)',
}

export const graphColorsCodes = {
  green: 'green',
  red: 'red',
  lavender: 'lavender',
  blue: 'blue',
  gray: 'medium-purple',
  purple: 'purple',
  chartDark3Teal: 'chart-dark-3-teal',
  darkPurple: 'dark-purple',
  chartDark3Red: 'chart-dark-3-red',
  chartDarkBlue: 'dark-blue',
}

type TransformBarGraphDataType = {
  data: BarChartGraphData[]
  values?: string[]
  sortBy?: string
  topPerformers?: {
    keyName: string
    displayName: string
  }
  topPerformingCount?: number
}

export function transformBarGraphData({
  data,
  values = ['attributed_sales.value', 'cost.value'],
  sortBy = 'attributed_sales',
  topPerformers,
  topPerformingCount = 5,
}: TransformBarGraphDataType): BarChartGraphData[] {
  if (data.length === 0) {
    return []
  }
  const arr = data.map((d) => {
    const obj = {
      ...d,
    }
    values.forEach((v) => {
      let newVal = d[v],
        key = v
      if (v.includes('.')) {
        const subData = d as unknown as Record<string, Record<string, number>>
        const strArr = v.split('.')
        newVal = subData[strArr[0]][strArr[1]]
        key = strArr[0]
      }
      obj[key] = newVal
    })
    return obj
  })
  let newArr = [...arr]
  if (topPerformers && arr.length > topPerformingCount) {
    newArr = arr.filter((d, index) => {
      return index < topPerformingCount && d
    })
    if (typeof newArr[0][sortBy] === 'number') {
      const numArr = newArr as Record<string, number>[]
      newArr = numArr.sort((a, b) => b[sortBy] - a[sortBy])
    } else {
      const strArr = newArr as Record<string, string>[]
      newArr = strArr.sort((a, b) => a[sortBy].localeCompare(b[sortBy]))
    }
    const otherValues = (arr as { attributed_sales: number; cost: number }[])
      .splice(topPerformingCount, arr.length - 1)
      .reduce((a, b) => {
        return {
          attributed_sales: a.attributed_sales + b.attributed_sales,
          cost: a.cost + b.cost,
          ...(topPerformers
            ? {
                [topPerformers.keyName]: `All Other ${topPerformers.displayName}`,
              }
            : {}),
        }
      })
    newArr.push(otherValues)
  } else if (sortBy) {
    if (typeof newArr[0][sortBy] === 'number') {
      const numArr = newArr as Record<string, number>[]
      newArr = numArr.sort((a, b) => b[sortBy] - a[sortBy])
    } else {
      const strArr = newArr as Record<string, string>[]
      newArr = strArr.sort((a, b) => a[sortBy].localeCompare(b[sortBy]))
    }
  }
  return newArr
}

export type DataItem = {
  change?: number
  comparison?: number
  display?: string
  pct_change?: number
  pct_of_total?: number
  type?: string
  value?: number
  decimalScale?: number
  date?: number
  color?: string
} & Record<string, unknown>

export type DynamicGraphDataType = Record<string, DataItem | string>

type TransformDynamicGraphDataType = {
  arr: DynamicGraphDataType[]
  endDate?: string
  metricKeys: string[]
  overrideGraphEndDate?: boolean
  overrideWeekStartEndDates?: boolean
  selectedStartDate?: string
  selectedEndDate?: string
  enableIntradayReporting?: boolean
  aggregatedBy?: string
}

export function transformDynamicGraphData({
  arr,
  endDate,
  metricKeys,
  overrideGraphEndDate,
  overrideWeekStartEndDates,
  selectedStartDate,
  enableIntradayReporting,
  aggregatedBy,
}: TransformDynamicGraphDataType) {
  const showIntraDayHour = enableIntradayReporting && aggregatedBy === 'hour'
  const firstMetric = metricKeys[0],
    secondMetric = metricKeys[1]

  const transformedArr: DataSetItem[] = arr?.map((a, index) => {
    const firstMetricObj = a[firstMetric] as DataItem,
      secondMetricObj = a[secondMetric] as DataItem
    const modifiedWeekDate = showIntraDayHour
        ? a?.report_hour_ts
        : a.report_date,
      dateValue =
        showIntraDayHour && modifiedWeekDate?.toString().includes('.000Z') // temp fix for intraday reporting, it will be handled in the backend
          ? (modifiedWeekDate?.toString().split('.000Z') as string[])?.[0]
          : a.report_date
    let weekDate
    if (overrideWeekStartEndDates && index === 0) {
      weekDate =
        moment(dateValue).diff(selectedStartDate, 'days') > 0
          ? dateValue
          : moment(selectedStartDate).format('Y-MM-DD')
    } else {
      weekDate = dateValue
    }
    return {
      [firstMetric]:
        firstMetricObj?.value != null ? firstMetricObj?.value : '-',
      ...(metricKeys.length > 1
        ? {
            [secondMetric]:
              secondMetricObj?.value != null ? secondMetricObj?.value : '-',
          }
        : {}),
      date: Date.parse(weekDate as unknown as string),
      ...(overrideGraphEndDate
        ? { overriddenEndDate: Date.parse(a.end_date as unknown as string) }
        : {}),
      dataTypes: [
        firstMetricObj?.type,
        ...(metricKeys.length > 1 ? [secondMetricObj?.type] : []),
      ],
      displays: [
        firstMetricObj?.display,
        ...(metricKeys.length > 1 ? [secondMetricObj?.display] : []),
      ],
      metricKeys: metricKeys,
    }
  })
  if (
    !overrideWeekStartEndDates &&
    moment(endDate).isSameOrAfter(moment(), 'day') &&
    transformedArr.length !== 1
  ) {
    transformedArr.push({
      date: moment().startOf('day').valueOf(),
    })
  }
  const graphData = {
    data: {
      dataSet: transformedArr,
      max: Math.max(
        ...transformedArr.map((o) => {
          const { ...rest } = omit(o, ['date'])
          const arr = Object.values(rest)
          return Math.max(...arr)
        }),
      ),
    },
  }
  return graphData
}

export type EventMarkerGraphDataType = {
  data: {
    dataSet: LineGraphData[]
    max: number
  }
}

type HandleEventMarkerDataType = {
  eventMarkerData: {
    current_date: string
    events: {
      [key: string]: Event[]
    }
  }[]
  graphInitialData: EventMarkerGraphDataType
}

type ReturnType = {
  data: {
    dataSet: {
      date: number
      metricKeys: string[]
      displays: string[]
      dataTypes: string[]
      [key: string]: unknown
    }[]
    max: number
    eventMarkerKeys: string[]
  }
}

export function handleEventMarkerData({
  eventMarkerData,
  graphInitialData,
}: HandleEventMarkerDataType): ReturnType {
  const eventMarkerKeys: string[] = []
  // Calculate the lowest value in the graph data. This is used for placing y coordinate for event marker.
  // So that it remains in the bottom of the graph.
  let leastValue = Number.MAX_VALUE
  if (graphInitialData?.data?.dataSet) {
    for (const dataSet of graphInitialData.data.dataSet) {
      if (!dataSet?.metricKeys) {
        continue
      }
      for (const metricKey of dataSet.metricKeys) {
        const key = metricKey as keyof typeof dataSet
        if (dataSet[key] && Number(dataSet[key]) < leastValue) {
          leastValue = Number(dataSet[key])
        }
      }
    }
  }
  const transformedArr = graphInitialData?.data?.dataSet?.map((a) => {
    const eventMarkerObj = eventMarkerData?.find(
      // In case of comparison view, the date is a string. So, we need to convert it to number.
      (o) =>
        Date.parse(o?.current_date) ===
        (typeof a.date === 'number'
          ? a.date
          : Date.parse(moment(a.date)?.format('YYYY-MM-DD'))),
    )
    const aggregateEventData: Event[] = []
    if (eventMarkerObj?.events) {
      Object.keys(eventMarkerObj.events).forEach((key) => {
        if (eventMarkerObj.events[key]) {
          aggregateEventData.push(...eventMarkerObj.events[key])
        }
      })
    }

    let eventMarkerKeyValuePair = {}
    aggregateEventData?.forEach((event) => {
      const eventMarkerKey = `event_marker_${event.id}`
      eventMarkerKeyValuePair = {
        ...eventMarkerKeyValuePair,
        [eventMarkerKey]: leastValue < 0 ? leastValue : 0,
      }
      eventMarkerKeys.push(eventMarkerKey)
    })
    return {
      ...a,
      ...(eventMarkerObj?.events
        ? {
            event_marker_data: aggregateEventData,
          }
        : {}),
      ...(a?.displays
        ? {
            event_marker: eventMarkerObj?.events ? 0 : null,
            displays: [...a.displays, 'Event Marker'],
            ...eventMarkerKeyValuePair,
          }
        : {}),
      ...(a?.dataTypes ? { dataTypes: [...a.dataTypes, 'integer'] } : {}),
    }
  })

  const graphData = {
    data: {
      dataSet: transformedArr,
      eventMarkerKeys: eventMarkerKeys,
      max: Math.max(
        ...transformedArr.map((o) => {
          const { ...rest } = omit(o, ['date', 'metricKeys'])
          const arr = Object.values(rest)
          return Math.max(...(arr as number[]))
        }),
      ),
    },
  }
  return graphData
}

type TransformMultilineGraphDataType = {
  dataArr: DynamicGraphDataType[]
  totalsArr: Record<string, unknown>[]
  endDate?: string
  metricKey: string
  aggregationKey: string
  overrideWeekStartEndDates?: boolean
  selectedStartDate?: string
  aggregatedBy?: string
  enableIntradayReporting?: boolean
}

type TransformComparisonGraphDataType = {
  dataArr: DynamicGraphDataType[]
  metricKey: string
  startDate: string
  endDate: string
  comparisonStart: string
  comparisonEnd: string
  aggregation?: string
  enableIntradayReporting?: boolean
}

type TransformedDataType = {
  date: number | string
  displays?: string[]
  dataTypes?: (string | undefined)[]
  metricKeys?: string[]
  [key: string]: unknown
  type?: string
}[]

// Used when multiline graphs are used. Eg: Marketplaces or Tags view.
export const transformMultilineGraphData = ({
  dataArr,
  totalsArr,
  endDate,
  metricKey,
  aggregationKey,
  overrideWeekStartEndDates,
  selectedStartDate,
  aggregatedBy,
  enableIntradayReporting,
}: TransformMultilineGraphDataType) => {
  const showIntraDayHour = enableIntradayReporting && aggregatedBy === 'hour'
  const type = (dataArr?.[0]?.[metricKey] as DataItem)?.type,
    dates = [
      ...new Set(
        dataArr?.map((obj) =>
          aggregatedBy === 'hour' && enableIntradayReporting
            ? obj?.report_hour_ts
            : (obj?.report_date as string),
        ),
      ),
    ]
  const transformedData: TransformedDataType = dates?.map((date, index) => {
    const graphObj: Record<string, unknown> = {}
    const currentDateAggregatedData = dataArr?.filter(
      (obj) =>
        (showIntraDayHour ? obj?.report_hour_ts : obj?.report_date) === date,
    )

    currentDateAggregatedData?.forEach((obj) => {
      const dataObj = obj as Record<string, DataItem>,
        strObj = obj as Record<string, string>
      graphObj[strObj?.[aggregationKey]?.toLowerCase() as string] =
        dataObj?.[metricKey]?.value
      graphObj[`display_${strObj?.[aggregationKey]?.toLowerCase()}`] =
        dataObj?.[aggregationKey]
      graphObj[metricKey] = dataObj?.[metricKey]
      graphObj['total_value'] = (
        totalsArr?.find(
          (currentDateTotal) =>
            (showIntraDayHour
              ? currentDateTotal?.report_hour_ts
              : currentDateTotal?.report_date) === date,
        )?.[metricKey] as DataItem
      )?.value
    })
    const displays = currentDateAggregatedData
      ?.map((obj) => {
        return obj?.[aggregationKey] as string
      })
      ?.sort()
    const modifiedDate =
      showIntraDayHour && date?.toString().includes('.000Z')
        ? date?.toString().split('.000Z')?.[0]
        : date
    let weekDate
    if (overrideWeekStartEndDates && index === 0) {
      weekDate =
        moment(modifiedDate).diff(selectedStartDate, 'days') > 0
          ? modifiedDate
          : moment(selectedStartDate).format('Y-MM-DD')
    } else {
      weekDate = modifiedDate
    }
    const obj = {
      date: Date.parse(weekDate as string),
      ...graphObj,
      displays,
      dataTypes: currentDateAggregatedData?.map(() => type),
      metricKeys: displays?.map((value) => value?.toLowerCase()),
      type: aggregationKey,
    }
    return obj
  })

  if (
    moment(endDate).isSameOrAfter(moment(), 'day') &&
    transformedData.length !== 1
  ) {
    transformedData.push({
      date: moment().startOf('day').valueOf(),
    })
  }

  return {
    data: {
      dataSet: transformedData,
    },
  }
}

// Used when comparison graphs are used. Eg: Comparison view in Traffic & Loyalty.
const isDateInRange = (date: string, startDate: string, endDate: string) => {
  return (
    moment(date).isSameOrAfter(startDate) &&
    moment(date).isSameOrBefore(endDate)
  )
}
const getNextDate = (date: MomentInput, aggregateBy: DurationInputArg2) => {
  return moment(date).add(1, aggregateBy).format('YYYY-MM-DD')
}
export const transformComparisonGraphData = ({
  dataArr,
  metricKey,
  startDate,
  endDate,
  comparisonStart,
  comparisonEnd,
  aggregation,
  enableIntradayReporting,
}: TransformComparisonGraphDataType) => {
  const showIntraDayHour = enableIntradayReporting && aggregation === 'hour'
  const currentDatesData = dataArr?.filter((obj) => {
    const dateStr = showIntraDayHour
      ? (obj?.report_hour_ts as string)
      : (obj?.report_date as string)
    const date = showIntraDayHour ? dateStr?.split('Z')[0] : dateStr
    return isDateInRange(moment(date).format('YYYY-MM-DD'), startDate, endDate)
  })
  const comparisonDatesData = dataArr?.filter((obj) => {
    const dateStr = showIntraDayHour
      ? (obj?.report_hour_ts as string)
      : (obj?.report_date as string)
    const date = showIntraDayHour ? dateStr?.split('Z')[0] : dateStr
    return isDateInRange(
      moment(date).format('YYYY-MM-DD'),
      comparisonStart,
      comparisonEnd,
    )
  })

  const totalNoOfRecords =
    currentDatesData?.length === comparisonDatesData?.length
      ? currentDatesData?.length
      : Math.max(currentDatesData?.length, comparisonDatesData?.length)

  const combinedData: TransformedDataType = []
  for (let i = 0; i < totalNoOfRecords; i++) {
    const currentData = currentDatesData?.[i]
    const comparisonData = comparisonDatesData?.[i],
      aggregateBy = aggregation ? aggregation : 'day'
    const currentDate = currentData
      ? showIntraDayHour
        ? currentData?.report_hour_ts
        : currentData?.report_date
      : getNextDate(
          showIntraDayHour
            ? currentDatesData[i - 1]?.report_hour_ts
            : currentDatesData[i - 1]?.report_date,
          aggregateBy as DurationInputArg2,
        )
    const comparisonDate = comparisonData
      ? showIntraDayHour
        ? comparisonData?.report_hour_ts
        : comparisonData?.report_date
      : getNextDate(
          showIntraDayHour
            ? comparisonDatesData[i - 1]?.report_hour_ts
            : comparisonDatesData[i - 1]?.report_date,
          aggregateBy as DurationInputArg2,
        )

    const formattedCurrentDate = showIntraDayHour
        ? moment.utc(currentDate).format('MMM DD, hh:mm A')
        : moment(currentDate).format('MM/DD/YY'),
      formattedComparisonDate = showIntraDayHour
        ? moment.utc(comparisonDate).format('MMM DD, hh:mm A')
        : moment(comparisonDate).format('MM/DD/YY')

    const obj: Record<string, unknown> = {
      currentDate: formattedCurrentDate,
      [`display_currentValue`]: formattedCurrentDate,
      comparisonDate: formattedComparisonDate,
      [`display_comparisonValue`]: formattedComparisonDate,
    }
    if (currentData) {
      obj['currentValue'] = (currentData?.[metricKey] as DataItem)?.value
    } else {
      obj['currentValue'] = null
      currentDatesData?.push({
        report_date: currentDate,
      })
    }
    if (comparisonData) {
      obj['comparisonValue'] = (comparisonData?.[metricKey] as DataItem)?.value
    } else {
      obj['comparisonValue'] = null
      comparisonDatesData?.push({
        report_date: comparisonDate,
      })
    }
    const dataObj = (currentData ?? comparisonData) as Record<string, DataItem>
    combinedData.push({
      date: `${currentDate} | ${comparisonDate}`,
      type: 'comparison_view',
      metricKeys: ['currentValue', 'comparisonValue'],
      displays: [formattedCurrentDate, formattedComparisonDate],
      dataTypes: [dataObj?.[metricKey]?.type, dataObj?.[metricKey]?.type],
      ...obj,
    })
  }
  return {
    data: {
      dataSet: combinedData,
    },
  }
}
