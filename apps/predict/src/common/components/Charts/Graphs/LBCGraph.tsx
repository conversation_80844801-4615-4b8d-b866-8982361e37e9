import React, { useState } from 'react'
import moment from 'moment'
import {
  Bar,
  CartesianGrid,
  Cell,
  ComposedChart,
  Line,
  type TooltipProps as RechartsTooltipProps,
  ReferenceArea,
  ReferenceLine,
  ResponsiveContainer,
  Tooltip,
  XAxis,
  YAxis,
} from 'recharts'
import {
  abbreviateNumber,
  createDataKeyLegend,
  createMonthAgoData,
  hasValue,
  lineAnimationDelay,
  lineAnimationDuration,
  statColors as statColorsForGraph,
} from '@patterninc/react-ui'
import { translationDateFormat } from '@predict-services'

import TwoLineDateLabel from '../Labels/TwoLineDateLabel'
import ReferenceLabel from './ReferenceLabel'
import SparkLineCustomTooltip from '../SparkLineCustomTooltip/SparkLineCustomTooltip'
import type CustomTooltip from './CustomTooltip'

type LegendProps = {
  key: string
  stroke: string
  type?: string
  hoverBarColor?: string
  yAxisId?: string
  xAxisId?: number
  stackId?: string
  opacity?: number
}

type Tooltips = {
  CustomTooltip?: typeof CustomTooltip
  tooltipOrder?: string[]
  tooltipSecondDate: boolean
  customKeyLegend: LegendProps[]
  minimumFractionForChartToolTip: number
  tooltipDateFormat: string
}

type ToolTipCustom = {
  tooltip: 'custom'
}

type BarChartProps = {
  barColor?: 'success' | 'error' | 'standard'
  barGap?: number
  barStrokeWidth?: number
}

type LineChartProps = {
  lineColor?: 'success' | 'error' | 'standard'
  lineType?:
    | 'basis'
    | 'basisClosed'
    | 'basisOpen'
    | 'linear'
    | 'linearClosed'
    | 'natural'
    | 'monotoneX'
    | 'monotoneY'
    | 'monotone'
    | 'step'
    | 'stepBefore'
    | 'stepAfter'
}

type ChartProps = {
  changeGraphColorOnHover?: boolean
  chartMargin?: {
    top: number
    right: number
    left: number
    bottom: number
  }
  colorPalette?: Record<string, string>
  cursor?: RechartsTooltipProps<string, string>['cursor']
  customDomain?: [
    'auto' | 'dataMin' | number | ((dataMin: number) => number),
    'auto' | 'dataMax' | number | ((dataMax: number) => number),
  ]
  customKeyLegend?: LegendProps[]
  customXTicks?: (string | number)[]
  customY1Domain?: [
    'auto' | 'dataMin' | number | ((dataMin: number) => number),
    'auto' | 'dataMax' | number | ((dataMax: number) => number),
  ]
  customY1TickFormat?: (t: number) => string
  customY1Ticks?: (string | number)[]
  customYTicks?: (string | number)[]
  xAxisDataKey?: string
  xAxisType?: 'number' | 'category'
  xInterval?: 'preserveStart' | 'preserveEnd' | 'preserveStartEnd' | number
  xTickCount?: number
  y1Max?: number
  y2Max?: number
  yAxisWidth?: number
  minimumFractionForChartToolTip?: number
  multipleYaxis?: boolean
  overlapBar?: boolean
  strokeWidth?: number
}

type ChartData = {
  graphData: {
    [key: string]: string | number | unknown
    date: string
  }[]
  modNumber?: number
  monthAgo?: string | string[]
  percentage?: boolean
  prefix?: string
  suffix?: string
  today?: boolean
}

type AnimationControl = {
  isImageCapture?: boolean
}

type LBCGraphProps = AnimationControl &
  ChartData &
  Tooltips &
  ToolTipCustom &
  BarChartProps &
  LineChartProps &
  ChartProps

const statColors = statColorsForGraph()

const LineBarCompositionGraph = ({
  barColor,
  barGap = 0,
  barStrokeWidth = 0,
  changeGraphColorOnHover,
  chartMargin,
  colorPalette,
  cursor,
  customDomain,
  customKeyLegend,
  CustomTooltip,
  customXTicks,
  customY1Domain,
  customY1TickFormat,
  customY1Ticks,
  customYTicks,
  graphData = [],
  isImageCapture,
  lineColor,
  lineType = 'linear',
  minimumFractionForChartToolTip = 2,
  modNumber,
  monthAgo,
  multipleYaxis,
  overlapBar,
  percentage,
  prefix,
  strokeWidth,
  suffix,
  today,
  tooltip,
  tooltipDateFormat,
  tooltipOrder,
  tooltipSecondDate,
  xAxisDataKey = 'date',
  xAxisType,
  xInterval,
  xTickCount,
  y1Max,
  y2Max,
  yAxisWidth,
}: LBCGraphProps): React.JSX.Element => {
  const [cellIndex, setCellIndex] = useState<number | null>(null)
  // tick formatter used in the Yaxis tickformatter
  const formatTick = (t: number) => {
    let tick = abbreviateNumber(t, suffix).toLocaleString()
    if (prefix) {
      tick = prefix + tick
    }
    if (suffix) {
      tick += suffix
    }
    return tick
  }

  const singleObj = Object.assign({}, graphData?.[0] ? graphData[0] : {}),
    keyLegend = customKeyLegend
      ? customKeyLegend
      : (createDataKeyLegend(singleObj, colorPalette) as LegendProps[])
  let oneMonthAgo, oneMonthAgoValue

  if (monthAgo) {
    const monthData = createMonthAgoData(graphData, monthAgo, tooltipSecondDate)
    oneMonthAgo = monthData.date
    oneMonthAgoValue = monthData.value
  }

  return (
    <ResponsiveContainer width='99%'>
      <ComposedChart
        data={graphData}
        barCategoryGap={barGap || 0}
        barGap={barGap || 0}
        margin={
          chartMargin
            ? chartMargin
            : {
                top: 5,
                right: 5,
                left: 5,
                bottom: 5,
              }
        }
      >
        <CartesianGrid vertical={false} />
        {tooltip === 'custom' ? (
          <Tooltip
            content={
              CustomTooltip && (
                <CustomTooltip
                  active={false}
                  payload={undefined}
                  label=''
                  className={tooltip}
                  customDateFormat={tooltipDateFormat}
                  keyLegend={keyLegend}
                  minimumFractionDigits={minimumFractionForChartToolTip}
                  tooltipOrder={tooltipOrder}
                  tooltipSecondDate={tooltipSecondDate}
                />
              )
            }
            cursor={cursor}
          />
        ) : (
          <Tooltip
            content={
              <SparkLineCustomTooltip
                className={tooltip}
                prefix={prefix ? prefix : ''}
                suffix={suffix ? suffix : ''}
                tooltipOrder={tooltipOrder}
                active={false}
                payload={[]}
                label={''}
              />
            }
            cursor={cursor}
          />
        )}
        {multipleYaxis && (
          <YAxis
            axisLine={false}
            tickLine={false}
            tickCount={5}
            tickMargin={5}
            width={yAxisWidth ? yAxisWidth : 44}
            orientation='left'
            yAxisId='left'
            ticks={customY1Ticks && customY1Ticks}
            tickFormatter={(t) =>
              customY1TickFormat ? customY1TickFormat(t) : formatTick(t)
            }
            style={{
              fontSize: '12px',
              fill: 'rgb(116, 121, 157)',
            }}
            type='number'
            domain={
              customY1Ticks
                ? [customY1Ticks[0], customY1Ticks[customY1Ticks.length - 1]]
                : modNumber
                  ? [
                      (dataMin: number) => {
                        if (dataMin === y1Max) {
                          return y1Max === 0
                            ? 0
                            : dataMin - (dataMin % modNumber) - modNumber
                        } else {
                          return dataMin - (dataMin % modNumber)
                        }
                      },
                      (dataMax: number) => {
                        let max = dataMax - (dataMax % modNumber) + modNumber
                        if ((percentage || suffix === '%') && max > 100) {
                          max = 100
                        }
                        return max
                      },
                    ]
                  : customY1Domain
                    ? customY1Domain
                    : customDomain
                      ? customDomain
                      : ['auto', 'auto']
            }
          />
        )}
        <YAxis
          axisLine={false}
          tickLine={false}
          tickCount={5}
          tickMargin={5}
          width={yAxisWidth ? yAxisWidth : 44}
          orientation='right'
          yAxisId='right'
          ticks={customYTicks && customYTicks}
          tickFormatter={(t) => formatTick(t)}
          style={{
            fontSize: '12px',
            fill: 'rgb(116, 121, 157)',
          }}
          type='number'
          domain={
            customYTicks
              ? [customYTicks[0], customYTicks[customYTicks.length - 1]]
              : modNumber
                ? [
                    (dataMin: number) => {
                      if (dataMin === y2Max) {
                        return y2Max === 0
                          ? 0
                          : dataMin - (dataMin % modNumber) - modNumber
                      } else {
                        return dataMin - (dataMin % modNumber)
                      }
                    },
                    (dataMax: number) => {
                      let max = dataMax - (dataMax % modNumber) + modNumber
                      if ((percentage || suffix === '%') && max > 100) {
                        max = 100
                      }
                      return max
                    },
                  ]
                : customDomain
                  ? customDomain
                  : ['auto', 'auto']
          }
        />
        <XAxis
          dataKey={xAxisDataKey}
          xAxisId={0}
          axisLine={false}
          tickCount={xTickCount ? xTickCount : 8}
          domain={
            customXTicks
              ? [customXTicks[0], customXTicks[customXTicks.length - 1]]
              : [(dataMin: number) => dataMin, (dataMax: number) => dataMax]
          }
          type={xAxisType ? xAxisType : 'category'}
          interval={xInterval ? xInterval : 'preserveStart'}
          height={45}
          tickLine={false}
          tickMargin={10}
          ticks={customXTicks && customXTicks}
          tick={({ x, y, payload, removeDay }) => (
            <TwoLineDateLabel
              x={x}
              y={y}
              payload={payload}
              removeDay={removeDay}
              isUsingIntlTranslations
              customLabelDateFormat={translationDateFormat('MMM D')}
            />
          )}
        />
        {overlapBar && (
          <XAxis
            dataKey='date'
            hide
            xAxisId={1}
            axisLine={false}
            tickCount={xTickCount ? xTickCount : 8}
            domain={
              customXTicks
                ? [customXTicks[0], customXTicks[customXTicks.length - 1]]
                : [(dataMin: number) => dataMin, (dataMax: number) => dataMax]
            }
            type={xAxisType ? xAxisType : 'category'}
            interval={xInterval ? xInterval : 'preserveStart'}
            height={45}
            tickLine={false}
            tickMargin={10}
            ticks={customXTicks && customXTicks}
            tick={({ x, y, payload, removeDay }) => (
              <TwoLineDateLabel
                x={x}
                y={y}
                payload={payload}
                removeDay={removeDay}
                isUsingIntlTranslations
                customLabelDateFormat={translationDateFormat('MMM D')}
              />
            )}
          />
        )}
        {overlapBar && (
          <XAxis
            dataKey='date'
            hide
            xAxisId={2}
            axisLine={false}
            tickCount={xTickCount ? xTickCount : 8}
            domain={
              customXTicks
                ? [customXTicks[0], customXTicks[customXTicks.length - 1]]
                : [(dataMin: number) => dataMin, (dataMax: number) => dataMax]
            }
            type={xAxisType ? xAxisType : 'category'}
            interval={xInterval ? xInterval : 'preserveStart'}
            height={45}
            tickLine={false}
            tickMargin={10}
            ticks={customXTicks && customXTicks}
            tick={({ x, y, payload, removeDay }) => (
              <TwoLineDateLabel
                x={x}
                y={y}
                payload={payload}
                removeDay={removeDay}
                isUsingIntlTranslations
                customLabelDateFormat={translationDateFormat('MMM D')}
              />
            )}
          />
        )}
        {monthAgo && (
          <ReferenceLine
            x={oneMonthAgo && oneMonthAgo * 10000000}
            label={
              <ReferenceLabel
                textAnchor='middle'
                text={'30 DAYS AGO'}
                value={oneMonthAgoValue}
                suffix={suffix ? suffix : ''}
              />
            }
            isFront={true}
            stroke='var(--medium-purple)'
            strokeWidth={1}
          />
        )}
        {today && (
          <ReferenceLine
            x={moment(
              tooltipSecondDate
                ? graphData[graphData.length - 2].date
                : moment().startOf('day'),
            )
              .subtract(tooltipSecondDate ? 0 : 1, 'days')
              .toDate()
              .valueOf()}
            label={
              <ReferenceLabel
                textAnchor='middle'
                text={tooltipSecondDate ? 'THIS WEEK' : 'TODAY'}
              />
            }
            isFront={true}
            stroke='var(--medium-purple)'
            strokeWidth={1}
          />
        )}
        {today && (
          <ReferenceArea
            x1={moment(
              tooltipSecondDate
                ? graphData[graphData.length - 2].date
                : moment().startOf('day'),
            )
              .subtract(tooltipSecondDate ? 0 : 1, 'days')
              .toDate()
              .valueOf()}
            x2={moment(
              tooltipSecondDate
                ? graphData[graphData.length - 1].date
                : moment().startOf('day'),
            )
              .toDate()
              .valueOf()}
            fill='rgba(49, 227, 234, .4)'
          />
        )}

        {keyLegend
          .filter((legend) => legend.type === 'bar')
          .map((bar, i) => {
            const graphColor = barColor ? statColors[barColor] : bar.stroke,
              hoverColor = bar?.hoverBarColor
            return (
              <Bar
                // barGap={barGap || 0}
                fill={graphColor}
                yAxisId={bar.yAxisId}
                xAxisId={bar.xAxisId}
                // barCategoryGap={barGap || 0}
                key={`bar_${i}`}
                dataKey={bar.key}
                stackId={bar.stackId}
                strokeWidth={barStrokeWidth || 0}
                animationBegin={
                  bar.key.includes('dontuse')
                    ? lineAnimationDelay(graphData.length)
                    : 0
                }
                animationDuration={
                  bar.key.includes('dontuse')
                    ? lineAnimationDuration(graphData.length)
                    : 1000
                }
                isAnimationActive={!isImageCapture}
              >
                {graphData.map((entry, index) => {
                  return (
                    <Cell
                      key={index}
                      fillOpacity={bar.opacity && index % 2 === 0 ? 0.6 : 1}
                      {...(hasValue(cellIndex) &&
                        cellIndex === index && { fill: hoverColor })}
                      {...(changeGraphColorOnHover && {
                        onMouseOver: () => {
                          setCellIndex(index)
                        },
                        onMouseOut: () => {
                          setCellIndex(null)
                        },
                      })}
                    />
                  )
                })}
              </Bar>
            )
          })}

        {keyLegend
          .filter((legend) => legend.type === 'line')
          .map((line, i) => {
            const graphColor = lineColor ? statColors[lineColor] : line.stroke
            return (
              <Line
                key={`line_${i}`}
                type={lineType ? lineType : 'monotone'}
                yAxisId={line.yAxisId}
                dataKey={line.key}
                dot={false}
                strokeWidth={strokeWidth ? strokeWidth : 4}
                xAxisId={line.xAxisId}
                activeDot={{
                  stroke: graphColor,
                  fill: 'white',
                  strokeWidth: 3,
                  r: 4.6,
                }}
                strokeDasharray={
                  line.key.includes('dontuse') ? '3 10' : undefined
                }
                stroke={graphColor}
                strokeLinecap={'round'}
                animationBegin={
                  line.key.includes('dontuse')
                    ? lineAnimationDelay(graphData.length)
                    : 0
                }
                animationDuration={
                  line.key.includes('dontuse')
                    ? lineAnimationDuration(graphData.length)
                    : 1000
                }
                isAnimationActive={!isImageCapture}
              />
            )
          })}
      </ComposedChart>
    </ResponsiveContainer>
  )
}

export default LineBarCompositionGraph
