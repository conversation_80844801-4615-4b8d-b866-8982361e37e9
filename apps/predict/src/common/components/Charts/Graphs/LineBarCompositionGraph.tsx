import React, { useState } from 'react'
import moment from 'moment'
import {
  Bar,
  CartesianGrid,
  Cell,
  ComposedChart,
  Line,
  ReferenceArea,
  ReferenceLine,
  ResponsiveContainer,
  Tooltip,
  XAxis,
  YAxis,
} from 'recharts'
import {
  abbreviateNumber,
  createDataKeyLegend,
  createMonthAgoData,
  hasValue,
  lineAnimationDelay,
  lineAnimationDuration,
  statColors as statColorsForGraph,
} from '@patterninc/react-ui'

import TwoLineDateLabel from '../Labels/TwoLineDateLabel'
import ReferenceLabel from './ReferenceLabel'
import ReviewGraphTooltip from './ReviewGraphTooltip'
import SparkLineCustomTooltip from '../SparkLineCustomTooltip/SparkLineCustomTooltip'
import type CustomTooltip from './CustomTooltip'

const statColors = statColorsForGraph()

// Type for graph data item
interface GraphDataItem<T = unknown> {
  [key: string]: T | number | string
  date: number | string
}

// KeyLegendItem interface defines the structure for each legend entry
// used by the CustomTooltip component. This will be synchronized with
// CustomTooltip's TypeScript interface once that component is converted to ts.
interface KeyLegendItem {
  key: string
  stroke: string
  type?: string
  yAxisId?: string
  xAxisId?: number
  stackId?: string
  opacity?: number
  hoverBarColor?: string
  tooltipLabel?: string
  thousandSeparator?: boolean
  barColor?: string
  countColor?: string
  suffix?: string
  currencyCode?: string
  currencySymbol?: string
  width?: number | string
  height?: number | string
}

// Type for the chart margin
interface ChartMargin {
  top: number
  right: number
  left: number
  bottom: number
}

// Props interface for LineBarCompositionGraph
interface LineBarCompositionGraphProps<T = unknown> {
  barColor?: keyof ReturnType<typeof statColorsForGraph> | ''
  barGap?: number
  barStrokeWidth?: number
  changeGraphColorOnHover?: boolean
  chartMargin?: ChartMargin
  colorPalette?: Record<string, string>
  cursor?: boolean | object
  customKeyLegend?: KeyLegendItem[]
  CustomTooltip?: typeof CustomTooltip
  customXTicks?: Array<number | string>
  customY1Domain?: [number | string, number | string]
  customY1TickFormat?: (value: number) => string
  customY1Ticks?: number[]
  customYTicks?: number[]
  graphData?: GraphDataItem<T>[]
  isImageCapture?: boolean
  lineColor?: keyof ReturnType<typeof statColorsForGraph> | ''
  lineType?: NonNullable<React.ComponentProps<typeof Line>['type']>
  minimumFractionForChartToolTip?: number
  modNumber?: number
  monthAgo?: string | string[]
  multipleYaxis?: boolean
  overlapBar?: boolean
  percentage?: boolean
  prefix?: string
  strokeWidth?: number
  suffix?: string
  today?: boolean
  tooltip?: 'plain' | 'response' | 'custom' | string
  tooltipDateFormat?: string
  tooltipOrder?: string[]
  tooltipSecondDate?: boolean
  xAxisType?: 'number' | 'category'
  xInterval?: NonNullable<React.ComponentProps<typeof XAxis>['interval']>
  xTickCount?: number
  y1Max?: number
  y2Max?: number
  yAxisWidth?: number
}

function LineBarCompositionGraph<T = unknown>({
  barColor,
  barGap = 0,
  barStrokeWidth = 0,
  changeGraphColorOnHover,
  chartMargin,
  colorPalette,
  cursor,
  customKeyLegend,
  CustomTooltip,
  customXTicks,
  customY1Domain,
  customY1TickFormat,
  customY1Ticks,
  customYTicks,
  graphData = [],
  isImageCapture,
  lineColor,
  lineType,
  minimumFractionForChartToolTip = 2,
  modNumber,
  monthAgo,
  multipleYaxis,
  overlapBar,
  percentage,
  prefix,
  strokeWidth,
  suffix,
  today,
  tooltip,
  tooltipDateFormat,
  tooltipOrder,
  tooltipSecondDate,
  xAxisType,
  xInterval,
  xTickCount,
  y1Max,
  y2Max,
  yAxisWidth,
}: LineBarCompositionGraphProps<T>) {
  const [cellIndex, setCellIndex] = useState<number | null>(null)
  // tick formatter used in the Yaxis tickformatter
  const formatTick = (t: number): string => {
    let tick = abbreviateNumber(t, suffix).toLocaleString()
    if (prefix) {
      tick = prefix + tick
    }
    if (suffix) {
      tick += suffix
    }
    return tick
  }

  const singleObj = Object.assign({}, graphData?.[0] ? graphData[0] : {}),
    keyLegend: KeyLegendItem[] = customKeyLegend
      ? customKeyLegend
      : createDataKeyLegend(singleObj, colorPalette)
  let oneMonthAgo, oneMonthAgoValue

  if (monthAgo) {
    const monthData = createMonthAgoData(graphData, monthAgo, tooltipSecondDate)
    oneMonthAgo = monthData.date
    oneMonthAgoValue = monthData.value
  }

  return (
    <ResponsiveContainer width='99%'>
      <ComposedChart
        data={graphData}
        barCategoryGap={barGap || 0}
        barGap={barGap || 0}
        margin={
          chartMargin
            ? chartMargin
            : {
                top: 5,
                right: 5,
                left: 5,
                bottom: 5,
              }
        }
      >
        <CartesianGrid vertical={false} />
        {tooltip === 'plain' ? (
          <Tooltip />
        ) : tooltip === 'response' ? (
          <Tooltip content={<ReviewGraphTooltip active payload={[]} />} />
        ) : tooltip === 'custom' && CustomTooltip ? (
          <Tooltip
            content={
              <CustomTooltip
                active
                payload={[]}
                label=''
                className={tooltip}
                keyLegend={keyLegend.map((item) => ({
                  ...item,
                  width:
                    item.width !== undefined ? String(item.width) : undefined,
                }))}
                tooltipSecondDate={tooltipSecondDate}
                tooltipOrder={tooltipOrder}
                customDateFormat={tooltipDateFormat}
                minimumFractionDigits={minimumFractionForChartToolTip}
              />
            }
            cursor={cursor}
          />
        ) : (
          <Tooltip
            content={
              <SparkLineCustomTooltip
                active
                payload={[]}
                label=''
                className={tooltip}
                prefix={prefix ? prefix : ''}
                suffix={suffix ? suffix : ''}
                tooltipSecondDate={tooltipSecondDate}
                tooltipOrder={tooltipOrder}
              />
            }
            cursor={cursor}
          />
        )}
        {multipleYaxis && (
          <YAxis
            axisLine={false}
            tickLine={false}
            tickCount={5}
            tickMargin={5}
            width={yAxisWidth ? yAxisWidth : 44}
            orientation='left'
            yAxisId='left'
            ticks={customY1Ticks && customY1Ticks}
            tickFormatter={(t) =>
              customY1TickFormat ? customY1TickFormat(t) : formatTick(t)
            }
            style={{
              fontSize: '12px',
              fill: 'rgb(116, 121, 157)',
            }}
            type='number'
            domain={
              customY1Ticks
                ? [customY1Ticks[0], customY1Ticks[customY1Ticks.length - 1]]
                : modNumber
                  ? [
                      (dataMin: number) => {
                        if (dataMin === y1Max) {
                          return y1Max === 0
                            ? 0
                            : dataMin - (dataMin % modNumber) - modNumber
                        } else {
                          return dataMin - (dataMin % modNumber)
                        }
                      },
                      (dataMax: number) => {
                        let max = dataMax - (dataMax % modNumber) + modNumber
                        if ((percentage || suffix === '%') && max > 100) {
                          max = 100
                        }
                        return max
                      },
                    ]
                  : customY1Domain
                    ? customY1Domain
                    : ['auto', 'auto']
            }
          />
        )}
        <YAxis
          axisLine={false}
          tickLine={false}
          tickCount={5}
          tickMargin={5}
          width={yAxisWidth ? yAxisWidth : 44}
          orientation='right'
          yAxisId='right'
          ticks={customYTicks && customYTicks}
          tickFormatter={(t: number) => formatTick(t)}
          style={{
            fontSize: '12px',
            fill: 'rgb(116, 121, 157)',
          }}
          type='number'
          domain={
            customYTicks
              ? [customYTicks[0], customYTicks[customYTicks.length - 1]]
              : modNumber
                ? [
                    (dataMin: number) => {
                      if (dataMin === y2Max) {
                        return y2Max === 0
                          ? 0
                          : dataMin - (dataMin % modNumber) - modNumber
                      } else {
                        return dataMin - (dataMin % modNumber)
                      }
                    },
                    (dataMax: number) => {
                      let max = dataMax - (dataMax % modNumber) + modNumber
                      if ((percentage || suffix === '%') && max > 100) {
                        max = 100
                      }
                      return max
                    },
                  ]
                : ['auto', 'auto']
          }
        />
        <XAxis
          dataKey='date'
          xAxisId={0}
          axisLine={false}
          tickCount={xTickCount ? xTickCount : 8}
          domain={
            customXTicks
              ? [customXTicks[0], customXTicks[customXTicks.length - 1]]
              : [
                  (dataMin: number | string) => dataMin,
                  (dataMax: number | string) => dataMax,
                ]
          }
          type={xAxisType ? xAxisType : 'category'}
          interval={xInterval ? xInterval : 'preserveStart'}
          height={45}
          tickLine={false}
          tickMargin={10}
          ticks={customXTicks && customXTicks}
          tick={
            <TwoLineDateLabel
              x=''
              y=''
              payload={{ value: '' }}
              removeDay={false}
              formatValue={true}
              isHour={false}
              isUsingIntlTranslations={false}
              customLabelDateFormat='MMM D'
            />
          }
        />
        {overlapBar && (
          <XAxis
            dataKey='date'
            hide
            xAxisId={1}
            axisLine={false}
            tickCount={xTickCount ? xTickCount : 8}
            domain={
              customXTicks
                ? [customXTicks[0], customXTicks[customXTicks.length - 1]]
                : [
                    (dataMin: number | string) => dataMin,
                    (dataMax: number | string) => dataMax,
                  ]
            }
            type={xAxisType ? xAxisType : 'category'}
            interval={xInterval ? xInterval : 'preserveStart'}
            height={45}
            tickLine={false}
            tickMargin={10}
            ticks={customXTicks && customXTicks}
            tick={
              <TwoLineDateLabel
                x=''
                y=''
                payload={{ value: '' }}
                removeDay={false}
                formatValue={true}
                isHour={false}
                isUsingIntlTranslations={false}
                customLabelDateFormat='MMM D'
              />
            }
          />
        )}
        {overlapBar && (
          <XAxis
            dataKey='date'
            hide
            xAxisId={2}
            axisLine={false}
            tickCount={xTickCount ? xTickCount : 8}
            domain={
              customXTicks
                ? [customXTicks[0], customXTicks[customXTicks.length - 1]]
                : [
                    (dataMin: number | string) => dataMin,
                    (dataMax: number | string) => dataMax,
                  ]
            }
            type={xAxisType ? xAxisType : 'category'}
            interval={xInterval ? xInterval : 'preserveStart'}
            height={45}
            tickLine={false}
            tickMargin={10}
            ticks={customXTicks && customXTicks}
            tick={
              <TwoLineDateLabel
                x=''
                y=''
                payload={{ value: '' }}
                removeDay={false}
                formatValue={true}
                isHour={false}
                isUsingIntlTranslations={false}
                customLabelDateFormat='MMM D'
              />
            }
          />
        )}
        {monthAgo && (
          <ReferenceLine
            x={oneMonthAgo ? oneMonthAgo * 10000000 : undefined}
            label={
              <ReferenceLabel
                textAnchor='middle'
                text={'30 DAYS AGO'}
                value={oneMonthAgoValue}
                suffix={suffix ? suffix : ''}
              />
            }
            isFront={true}
            stroke='var(--medium-purple)'
            strokeWidth={1}
          />
        )}
        {today && (
          <ReferenceLine
            x={moment(
              tooltipSecondDate
                ? graphData[graphData.length - 2].date
                : moment().startOf('day'),
            )
              .subtract(tooltipSecondDate ? 0 : 1, 'days')
              .toDate()
              .valueOf()}
            label={
              <ReferenceLabel
                textAnchor='middle'
                text={tooltipSecondDate ? 'THIS WEEK' : 'TODAY'}
              />
            }
            isFront={true}
            stroke='var(--medium-purple)'
            strokeWidth={1}
          />
        )}
        {today && (
          <ReferenceArea
            x1={moment(
              tooltipSecondDate
                ? graphData[graphData.length - 2].date
                : moment().startOf('day'),
            )
              .subtract(tooltipSecondDate ? 0 : 1, 'days')
              .toDate()
              .valueOf()}
            x2={moment(
              tooltipSecondDate
                ? graphData[graphData.length - 1].date
                : moment().startOf('day'),
            )
              .toDate()
              .valueOf()}
            fill='rgba(49, 227, 234, .4)'
          />
        )}
        {keyLegend
          .filter((legend) => legend.type === 'bar')
          .map((bar, i) => {
            const graphColor = barColor ? statColors[barColor] : bar.stroke
            const hoverColor = bar?.hoverBarColor
            return (
              <Bar
                fill={graphColor}
                yAxisId={bar.yAxisId}
                xAxisId={bar.xAxisId}
                key={`bar_${i}`}
                dataKey={bar.key}
                stackId={bar.stackId}
                strokeWidth={barStrokeWidth || 0}
                animationBegin={
                  bar.key.includes('dontuse')
                    ? lineAnimationDelay(graphData.length)
                    : 0
                }
                animationDuration={
                  bar.key.includes('dontuse')
                    ? lineAnimationDuration(graphData.length)
                    : 1000
                }
                isAnimationActive={!isImageCapture}
              >
                {graphData.map((entry, index) => {
                  return (
                    <Cell
                      key={index}
                      fillOpacity={bar.opacity && index % 2 === 0 ? 0.6 : 1}
                      {...(hasValue(cellIndex) &&
                        cellIndex === index && { fill: hoverColor })}
                      {...(changeGraphColorOnHover && {
                        onMouseOver: () => {
                          setCellIndex(index)
                        },
                        onMouseOut: () => {
                          setCellIndex(null)
                        },
                      })}
                    />
                  )
                })}
              </Bar>
            )
          })}
        {keyLegend
          .filter((legend) => legend.type === 'line')
          .map((line, i) => {
            const graphColor = lineColor ? statColors[lineColor] : line.stroke
            return (
              <Line
                key={`line_${i}`}
                type={lineType ? lineType : 'monotone'}
                yAxisId={line.yAxisId}
                dataKey={line.key}
                dot={false}
                strokeWidth={strokeWidth ? strokeWidth : 4}
                xAxisId={line.xAxisId}
                activeDot={{
                  stroke: graphColor,
                  fill: 'white',
                  strokeWidth: 3,
                  r: 4.6,
                }}
                strokeDasharray={
                  line.key.includes('dontuse') ? '3 10' : undefined
                }
                stroke={graphColor}
                strokeLinecap={'round'}
                animationBegin={
                  line.key.includes('dontuse')
                    ? lineAnimationDelay(graphData.length)
                    : 0
                }
                animationDuration={
                  line.key.includes('dontuse')
                    ? lineAnimationDuration(graphData.length)
                    : 1000
                }
                isAnimationActive={!isImageCapture}
              />
            )
          })}
      </ComposedChart>
    </ResponsiveContainer>
  )
}

export default LineBarCompositionGraph
