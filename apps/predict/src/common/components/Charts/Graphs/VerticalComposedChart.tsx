import React, { useState } from 'react'
import moment, { type Moment } from 'moment'
import {
  <PERSON>,
  Composed<PERSON><PERSON>,
  ResponsiveContainer,
  <PERSON>lt<PERSON>,
  <PERSON>A<PERSON>s,
  YAxi<PERSON>,
} from 'recharts'
import { type BaseAxisProps } from 'recharts/types/util/types'
import {
  Icon,
  Tooltip as LibraryTooltip,
  useMediaQuery,
} from '@patterninc/react-ui'

import styles from './composed-chart-tooltip.module.scss'
import { PreformattedTick } from '../Labels/OneLinePreformattedLabel'

type oneToNine = 1 | 2 | 3 | 4 | 5 | 6 | 7 | 8 | 9
type YYYY = `19${oneToNine}${oneToNine}` | `20${oneToNine}${oneToNine}`
type MM = `0${oneToNine}` | `1${0 | 1 | 2}`
type DD = `${0}${oneToNine}` | `${1 | 2}${oneToNine}` | `3${0 | 1}`

type DateYMDString = `${YYYY}-${MM}-${DD}`

type DateProps = {
  date: string | DateYMDString
}

// ToDo: Let's make it dynamic atthe time of API integration
type DataProps = DateProps & {
  is_actively_selling?: boolean
  is_discontinued?: boolean
  is_dno?: boolean
  is_marketplace_active?: boolean
  is_oos?: boolean
}

type MetadataKeysProps = {
  [key: string]: {
    label: string
  }
}

type MetadataProps = {
  keys: MetadataKeysProps
  start_date: string
  end_date: string
}

type VerticalComposedChartProps = {
  data: DataProps[]
  metaData: MetadataProps
  yKey: string
  chartMargin?: {
    top: number
    left: number
    right: number
    bottom: number
  }
  xTickCount?: number
  yAxisWidth?: number
  customTick?: BaseAxisProps['tick']
  removeDay?: boolean
  customLabelDateFormat?: string
}
type MaxKeysObjProps = {
  name?: string
  [key: number]: number[]
}

type FormattedGraphDataProps = MaxKeysObjProps[]

type CustomBarProps = {
  x?: number
  y?: number
  width?: number
  height?: number
  index?: number
}

type CustomTooltipProps = {
  active?: boolean
  payload?: {
    payload?: MaxKeysObjProps
  }[]
  label?: string
  metaData: MetadataKeysProps
  data: DataProps[]
  showYear: boolean
  setIsTooltipTooLarge: React.Dispatch<React.SetStateAction<boolean>>
}
type CustomizedBaseTickProps = {
  x?: number
  y?: number
  payload?: {
    value?: number
  }
  visibleTicksCount?: number
  index?: number
  isMobileView?: boolean
}

type CustomizedXAxisTickProps = CustomizedBaseTickProps & {
  data: DataProps[]
  removeDay: boolean
  customLabelDateFormat: string
  dayDifference: number
}

const colorsList = [
  'var(--chart-dark-2-green)',
  'var(--chart-dark-1-blue)',
  'var(--chart-standard-blue)',
  'var(--chart-light-3-blue)',
  'var(--chart-light-3-green)',
]

const CustomBar = (props: CustomBarProps) => {
  const { x, y, width, height, index } = props

  return (
    <rect
      x={x}
      y={y && y - 8}
      width={width}
      height={height && height + 16}
      rx={4} // Adjust this value to control the border radius
      ry={4} // Adjust this value to control the border radius
      fill={index ? colorsList?.[index] : colorsList?.[0]}
    />
  )
}

// Get object which has maximum keys to generate Bars
function getObjectWithMaxKeys(arr: FormattedGraphDataProps) {
  let maxKeys = 0
  let maxKeysObject = null

  for (const obj of arr) {
    const numKeys = Object.keys(obj).length
    if (numKeys > maxKeys) {
      maxKeys = numKeys
      maxKeysObject = obj
    }
  }

  return maxKeysObject
}

// function to format data with yAxis and xAxis
function getStatusWithDateRanges(
  key: string,
  data: DataProps[],
  metaData: MetadataKeysProps,
) {
  const dateRanges = []
  let startDate = null
  let endDate = null
  let status = false

  for (const item of data) {
    const { date } = item
    if (item[key as keyof DataProps]) {
      if (!status) {
        startDate = moment
          .utc(date as string)
          .toDate()
          .valueOf()
        status = true
      }
      endDate = moment
        .utc(date as string)
        .toDate()
        .valueOf()
    } else {
      if (status) {
        dateRanges.push([startDate, endDate])
        status = false
      }
    }
  }
  // Add the last actively selling range if it exists
  if (status) {
    dateRanges.push([startDate, endDate])
  }

  return {
    name: metaData[key as keyof typeof metaData]?.label,
    ...dateRanges,
  }
}

const CustomTooltip = ({
  active,
  payload,
  label,
  metaData,
  data,
  showYear,
  setIsTooltipTooLarge,
}: CustomTooltipProps) => {
  const currentStatus = payload?.[payload.length - 1]?.payload
  const dateRanges =
    currentStatus && Object.values(currentStatus).filter(Array.isArray)

  const statusColor =
    colorsList[
      Object.keys(metaData).findIndex(
        (status) => metaData[status as keyof typeof metaData]?.label === label,
      )
    ]

  if (active && payload && dateRanges?.length) {
    setIsTooltipTooLarge(payload.length >= 15)

    return (
      <div className={styles.barTooltip}>
        <div className='fs-12 fw-bold'>{label}</div>
        {dateRanges?.length &&
          dateRanges.map((dateRange: number[], index: number) => (
            <div
              key={index}
              className={`${index === 0 ? 'pat-pat-mt-1' : 'pat-mt-1'}`}
            >
              {dateRange.map((value: number, i: number) => {
                const fullDate = getFullDate(
                  moment.utc(value),
                  data,
                  showYear ? 'MMM DD, YYYY' : 'MMM DD',
                )
                return (
                  <React.Fragment key={i}>
                    {i === 0 && (
                      <div
                        className={styles.statusFlag}
                        style={{ backgroundColor: statusColor }}
                      />
                    )}
                    <span className='pat-ml-1 fc-purple'>{`${
                      i !== 0 ? ' -' : ''
                    } ${fullDate}`}</span>
                  </React.Fragment>
                )
              })}
            </div>
          ))}
      </div>
    )
  }

  return null
}

// get date from original data
const getFullDate = (date: Moment, data: DataProps[], dateFormat: string) => {
  let fullDate
  data.forEach((day: DataProps) => {
    if (date?.format('YYYY-MM-DD') === day.date) {
      fullDate = moment(day.date as string).format(dateFormat)
    }
  })

  return fullDate
}

const CustomizedXAxisTick = (props: CustomizedXAxisTickProps) => {
  const {
    x,
    y,
    visibleTicksCount,
    index,
    payload,
    data,
    removeDay,
    customLabelDateFormat,
    dayDifference,
  } = props
  const dateFormat = !removeDay
    ? dayDifference > 365
      ? 'M/D/YY'
      : 'MMM D'
    : customLabelDateFormat
  const fullDate = getFullDate(moment.utc(payload?.value), data, dateFormat)
  const isLastTick = visibleTicksCount === (index as number) + 1

  return (
    <PreformattedTick
      {...props}
      textAnchor={isLastTick ? 'right' : 'left'}
      x={x}
      y={y}
      dx={isLastTick ? '-1em' : '0'}
    >
      {fullDate}
    </PreformattedTick>
  )
}

const CustomizedYAxisTick = (props: CustomizedBaseTickProps) => {
  const { x, y, index, payload, isMobileView } = props

  return (
    <g transform={`translate(${x && index === 0 ? x - 20 : x},${y})`}>
      <text
        className='two-line-date-label'
        x={0}
        y={0}
        dy={4}
        fontSize={14}
        textAnchor='end'
        fill='rgb(116, 121, 157)'
      >
        {payload?.value}
      </text>
      {index === 0 && (
        <foreignObject x='6' y='-8' width='20' height='20'>
          <LibraryTooltip
            tooltipContent={
              <>
                <div className='fw-bold'>
                  A product is considered "Actively Selling" when:
                </div>
                <ol className='pat-m-0'>
                  <li className='pat-mb-0'>Product has been ordered before</li>
                  <li className='pat-mb-0'>
                    Product is not OOS, Discontinued, and DNO at the same time
                  </li>
                </ol>
              </>
            }
            position='bottom-start'
            maxWidth={isMobileView ? '300px' : 'auto'}
          >
            <Icon icon='info' color='dark-blue' iconSize='16px' />
          </LibraryTooltip>
        </foreignObject>
      )}
    </g>
  )
}

const VerticalComposedChart = ({
  data,
  metaData,
  yKey,
  chartMargin,
  yAxisWidth,
  xTickCount,
  customTick,
  removeDay = false,
  customLabelDateFormat = 'MMM D',
}: VerticalComposedChartProps): React.JSX.Element => {
  const isMobileView = useMediaQuery({ type: 'max', breakpoint: 'md' })
  const [isTooltipTooLarge, setIsTooltipTooLarge] = useState(false)
  // Formatting data for graph
  const formattedGraphData: FormattedGraphDataProps = []

  Object.keys(metaData?.keys).forEach((value) => {
    formattedGraphData.push(
      getStatusWithDateRanges(value, data, metaData?.keys),
    )
  })

  //Start: Create YAxis and Bar based on formatted data
  const maxKeysObj = getObjectWithMaxKeys(formattedGraphData)
  const yAxesWithBars: React.ReactElement[] = []

  Object.keys(maxKeysObj as MaxKeysObjProps).forEach((key, index) => {
    // creating YAxis and Bar array for all keys in data
    yAxesWithBars.push(
      <>
        <YAxis
          dataKey={yKey}
          yAxisId={`y${key}`}
          type='category'
          hide={index > 0}
          width={yAxisWidth || 140}
          axisLine={false}
          tickLine={false}
          orientation='left'
          style={{
            fontSize: '12px',
            fill: 'rgb(116, 121, 157)',
          }}
          tick={<CustomizedYAxisTick isMobileView={isMobileView} />}
        />
        <Bar
          yAxisId={`y${key}`}
          dataKey={key}
          background={!index}
          minPointSize={10}
          shape={<CustomBar />}
          barSize={14}
        />
      </>,
    )
  })
  //End: Create YAxis and Bar based on formatted data

  const startDate = moment(metaData?.start_date),
    endDate = moment(metaData?.end_date),
    dayDifference = endDate.diff(startDate, 'days')

  const showYear = startDate.year() !== moment().year()

  return (
    // Bar gap needs to handle with dynamic Height
    <ResponsiveContainer
      width={'99%'}
      height={60 * formattedGraphData.length}
      debounce={50}
    >
      <ComposedChart
        data={formattedGraphData}
        layout='vertical'
        margin={
          chartMargin
            ? chartMargin
            : {
                top: 5,
                right: 5,
                bottom: 5,
                left: 5,
              }
        }
      >
        <XAxis
          type='number'
          domain={[
            moment
              .utc(data?.[0]?.date as string)
              .toDate()
              .valueOf(),
            moment
              .utc(data?.[data.length - 1]?.date as string)
              .toDate()
              .valueOf(),
          ]}
          tickCount={
            !isMobileView && data?.length > 10 ? xTickCount || 8 : data?.length
          }
          axisLine={false}
          tickLine={false}
          style={{
            fontSize: '12px',
            fill: 'rgb(116, 121, 157)',
          }}
          tick={
            customTick ? (
              customTick
            ) : (
              <CustomizedXAxisTick
                data={data}
                removeDay={removeDay}
                customLabelDateFormat={customLabelDateFormat}
                dayDifference={dayDifference}
              />
            )
          }
        />
        <Tooltip
          content={
            <CustomTooltip
              metaData={metaData?.keys}
              data={data}
              showYear={showYear}
              setIsTooltipTooLarge={setIsTooltipTooLarge}
            />
          }
          wrapperStyle={{
            zIndex: 9,
            ...(isTooltipTooLarge ? { top: -470, overflowY: 'scroll' } : {}),
          }}
        />
        {yAxesWithBars.map(
          (t: React.ReactElement) =>
            (t.props as { children?: React.ReactNode }).children,
        )}
      </ComposedChart>
    </ResponsiveContainer>
  )
}

export default VerticalComposedChart
