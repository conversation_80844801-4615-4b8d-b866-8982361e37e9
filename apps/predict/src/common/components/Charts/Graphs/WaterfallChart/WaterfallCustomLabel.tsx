import React from 'react'

const WaterfallCustomLabel = ({
  x,
  y,
  width,
  fill,
  display,
}: {
  x: number
  y: number
  width: number
  fill: string
  display: string
}) => {
  // this puts the label above the bar if it's positive and below if it's negative
  const yOffSet =
    !display.includes('|') || display.split(' | ')[1].includes('+') ? -6 : 12
  return (
    <text
      x={x + width / 2}
      y={y}
      dy={yOffSet}
      fill={fill}
      fontSize={10}
      textAnchor='middle'
    >
      {display}
    </text>
  )
}

export default WaterfallCustomLabel
