import React from 'react'
import { Ellipsis } from '@patterninc/react-ui'

const WaterfallCustomLegend = ({
  loading,
  marginClass,
}: {
  loading: boolean
  marginClass: string
}) => {
  const colors = [
    { color: 'var(--chart-dark-1-red)', text: 'Detractors' },
    { color: 'var(--chart-dark-1-green)', text: 'Accelerators' },
  ]
  return (
    <div
      className={`graph-legend flex ${marginClass} bgc-white justify-content-center`}
    >
      {colors.map((colorObj, i) => {
        const { text, color } = colorObj
        return (
          <div key={text || i} className='legend-single-stat no-stat flex'>
            <div
              className='pat-p-2 bdrr-4'
              style={{ backgroundColor: color }}
            />
            <div className='fs-12 fw-regular fc-dark-purple'>
              {loading ? (
                <span>
                  Fetching Data
                  <Ellipsis />
                </span>
              ) : (
                <span>{text ?? 'No Data Found'}</span>
              )}
            </div>
          </div>
        )
      })}
    </div>
  )
}

export default WaterfallCustomLegend
