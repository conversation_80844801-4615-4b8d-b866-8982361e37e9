import { hasValue } from '@patterninc/react-ui'
import { productDetailRoute, productListingRoute } from '@predict-services'

import { type CommonDestinyConfigItemsType } from '../../../../../modules/Traffic/components/Pages/Destiny/common/DestinyFormattedValue'
import {
  type StandardTrafficObject,
  type TrafficGeneralType,
} from '../../../../../modules/Traffic/services/TrafficTableHeaderHelperService'
import { TrafficTooltipData } from '../../../../../modules/Traffic/services/TrafficTooltipHelper'

type MetricDataType = {
  name: string
} & TrafficGeneralType

type WaterfallDataType = {
  name: string
  value: number | null
  path?: string
  metricData?: MetricDataType[]
  info?: string
  type?: string
  change?: number
  pctChange?: number
  dataObj?: WaterfallInputDataType
}

export type FormattedWaterfallDataType = {
  name: string
  value: number | null
  topBar: number | null
  bottomBar: number | null
  path?: string
  display: string
  metricData?: MetricDataType[]
  info?: string
  type?: string
  dataObj?: WaterfallInputDataType
}

export const formatWaterfallData = ({
  data,
  currency,
}: {
  data: WaterfallDataType[]
  currency: { code: string; symbol: string }
}) => {
  const formattedData: FormattedWaterfallDataType[] = [],
    isCurrency = data[0].type === 'currency'

  let previousTopBar = 0,
    previousBottomBar = 0

  data.forEach((item, index) => {
    const topBar = getTopBar(data, index),
      bottomBar = getBottomBar(
        previousTopBar,
        previousBottomBar,
        index,
        data.length,
      )

    formattedData.push({
      name: item.name,
      value: item.value,
      topBar: item.value !== null ? topBar : null,
      bottomBar: item.value !== null ? bottomBar : null,
      path: item.path ?? undefined,
      display: getDisplay({
        item,
        topBar,
        index,
        totalLength: data.length,
        currency: isCurrency ? currency : undefined,
      }),
      metricData: item.metricData ?? undefined,
      info: item.info ?? undefined,
      type: item.type ?? undefined,
      dataObj: item.dataObj ?? undefined,
    })

    previousTopBar = item.value === null ? previousTopBar : topBar
    previousBottomBar = item.value === null ? previousBottomBar : bottomBar
  })
  return formattedData
}

const getCurrencyAbbreviation = ({
  item,
  topBar,
  currency,
  isEdge,
}: {
  item: WaterfallDataType
  topBar: number
  currency: { code: string; symbol: string }
  isEdge: boolean
}) => {
  const currencySuffix =
      currency?.code && currency?.code !== 'USD' ? ` ${currency?.code}` : '',
    isNegativeChange = topBar < 0,
    isPositiveChange = topBar > 0,
    changeSymbol = isNegativeChange ? '-' : isPositiveChange ? '+' : ''

  return `${currency?.symbol ?? ''}${abbreviateNumber(
    item.value as number, // value is always a number, if null it isn't displayed
  )}${currencySuffix}${
    isEdge
      ? ''
      : ` | ${changeSymbol}${currency?.symbol ?? ''}${abbreviateNumber(
          topBar,
        )}${currencySuffix}`
  }`
}

const abbreviateNumber = (num: number): string | number => {
  const absNum = Math.abs(num)
  if (absNum >= 1_000_000_000) {
    return (absNum / 1000000000).toFixed(1).replace(/\.0$/, '') + 'B'
  }
  if (absNum >= 1_000_000) {
    return (absNum / 1_000_000).toFixed(1).replace(/\.0$/, '') + 'M'
  }
  if (absNum >= 1_000) {
    return (absNum / 1_000).toFixed(1).replace(/\.0$/, '') + 'K'
  }
  if (absNum < 10) {
    return absNum.toFixed(2)
  }
  return Math.floor(absNum)
}

const getDisplay = ({
  item,
  topBar,
  index,
  totalLength,
  currency,
}: {
  item: WaterfallDataType
  topBar: number
  index: number
  totalLength: number
  currency?: { code: string; symbol: string }
}) => {
  if (item.value === null) {
    return ''
  }
  if (currency) {
    return getCurrencyAbbreviation({
      item,
      topBar,
      currency,
      isEdge: index === totalLength - 1 || index === 0,
    })
  }
  const isPositiveChange = topBar > 0,
    changeSymbol = isPositiveChange ? '+' : ''
  if (index === 0 || index === totalLength - 1) {
    return item.value?.toFixed(2)
  }
  return `${item.value?.toFixed(2)} | ${changeSymbol}${topBar?.toFixed(2)}`
}

const getLastNonNullValue = (data: WaterfallDataType[]) => {
  for (let i = data.length - 1; i >= 0; i--) {
    if (data[i].value !== null) {
      return data[i].value as number
    }
  }
  return 0
}

// see https://medium.com/2359media/tutorial-how-to-create-a-waterfall-chart-in-recharts-15a0e980d4b for top bar and bottom bar logic
const getTopBar = (data: WaterfallDataType[], index: number) => {
  const currValue = data[index].value,
    prevValue = getLastNonNullValue(data.slice(0, index))
  if (index === 0 || index === data.length - 1) {
    return currValue ?? 0
  }
  if (currValue === null) {
    return prevValue
  }
  return currValue - prevValue
}

const getBottomBar = (
  previousTopBar: number,
  previousBottomBar: number,
  index: number,
  totalLength: number,
) => {
  if (index === 0 || index === totalLength - 1) {
    return 0
  }
  return previousTopBar + previousBottomBar
}

const barTooltipInfo = {
  cannibalization: TrafficTooltipData.trueROASCannibalizedSales,
  returns: TrafficTooltipData.trueROASReturns,
  lifetimeValue: TrafficTooltipData.trueROASIncrementalSalesFromNewCustomers,
  organicRank: TrafficTooltipData.trueROASIncrementalSalesFromOrganicRank,
}

export const getWaterfallMetricTooltip = (metric: string) => {
  switch (metric) {
    case 'ROAS':
      return TrafficTooltipData.roas

    case 'True ROAS':
      return TrafficTooltipData.trueROAS

    case 'Ad Sales':
      return TrafficTooltipData.adSales

    case 'Incremental Ad Sales':
      return TrafficTooltipData.incrementalAdSales

    default:
      return ''
  }
}

export const getWaterfallNoDataText = (metric: string) => {
  switch (metric) {
    case 'Cannibalization':
      return 'Cannibalized Sales applies only to Sponsored Products and Sponsored Brands campaigns.'

    case 'Returns':
      return 'Revenue lost due to returns applies only to Amazon Sponsored Products campaigns.'

    case 'Lifetime Value':
      return 'Incremental Sales from New Customers applies only to Sponsored Products and Sponsored Brands campaigns.'

    case 'Organic Rank':
      return 'Incremental Sales from Organic Rank applies only to Sponsored Products campaigns.'

    default:
      return 'Data is not available or not applicable to this ad type.'
  }
}

export const getEndMetricHeader = (metric: string) => {
  switch (metric) {
    case 'ROAS':
      return 'Return on Ad Spend'

    case 'True ROAS':
      return 'True Return on Ad Spend'

    case 'Ad Sales':
      return 'Ad Sales'

    case 'Incremental Ad Sales':
      return 'Incremental Ad Sales'

    default:
      return ''
  }
}

export type WaterfallInputDataType = {
  cannibalized_sales: TrafficGeneralType
  returned_sales: TrafficGeneralType
  ntb_clv_sales: TrafficGeneralType
  organic_sales_increase: TrafficGeneralType
  roas: TrafficGeneralType
  true_roas: TrafficGeneralType
  attributed_sales: TrafficGeneralType
  incremental_sales: TrafficGeneralType
  true_roas_cannibalized_sales: TrafficGeneralType
  true_roas_returned_sales: TrafficGeneralType
  true_roas_ntb_clv_sales: TrafficGeneralType
  cost: TrafficGeneralType
  ntb_purchases: TrafficGeneralType
  customer_lifetime_value: TrafficGeneralType
  marketplace_id?: number
  ad_market_id?: number | string
  master_product_id?: number
  market_product_id?: number
  brand_id?: number
  campaign_id?: string | number
  ad_market?: string
  internal_target_id?: number
  pageType?: string
  ad_type?: string
  playbook_id?: number | string
  portfolio_id?: number | string
  campaign_name?: string
  portfolio_name?: string
  playbook_name?: string
  product_name?: string
  target_description?: string
  abbrv_ad_type_display?: string
  campaign?: string
  target?: string
  brand?: string
  portfolio_display?: string
  ad_type_display?: string
  isTrueRoasCannibalization?: boolean
  isOrganicRank?: boolean
  pathFrom?: string
}

export const filterWaterfallData = ({
  data,
  isDestiny = false,
}: {
  data: unknown
  isDestiny?: boolean
}): WaterfallInputDataType => {
  if (!data) return {} as WaterfallInputDataType
  let result: WaterfallInputDataType

  const { marketplace_id, master_product_id, market_product_id, ad_market_id } =
    data as StandardTrafficObject

  const isMarketProduct =
      (hasValue(marketplace_id) || hasValue(ad_market_id)) &&
      hasValue(master_product_id) &&
      hasValue(market_product_id),
    isMasterProduct = hasValue(master_product_id) && !isMarketProduct

  const productData = {
    ...(isMarketProduct
      ? {
          marketplace_id: marketplace_id || Number(ad_market_id),
          master_product_id,
          market_product_id,
        }
      : isMasterProduct
        ? { master_product_id }
        : {}),
  }

  if (isDestiny) {
    const {
      cannibalized_sales,
      returned_sales,
      ntb_clv_sales,
      organic_sales_increase,
      destiny_roas,
      true_roas,
      destiny_ad_sales,
      destiny_ad_spend,
      incremental_sales,
      true_roas_cannibalized_sales,
      true_roas_returned_sales,
      true_roas_ntb_clv_sales,
      ntb_purchases,
      customer_lifetime_value,
      brand_id,
      master_product_id,
      market_product_id,
      campaign_name,
      portfolio_name,
      playbook_name,
      product_name,
      target_description,
      abbrv_ad_type_display,
      campaign,
      target,
      brand,
      portfolio_display,
      ad_type_display,
      pageType,
      pathFrom,
    } = data as CommonDestinyConfigItemsType

    result = {
      cannibalized_sales,
      returned_sales,
      ntb_clv_sales,
      organic_sales_increase,
      roas: destiny_roas,
      true_roas: true_roas,
      attributed_sales: destiny_ad_sales,
      incremental_sales,
      true_roas_cannibalized_sales,
      true_roas_returned_sales,
      true_roas_ntb_clv_sales,
      cost: destiny_ad_spend,
      ntb_purchases,
      customer_lifetime_value,
      brand_id,
      master_product_id,
      market_product_id,
      pageType,
      campaign_name,
      portfolio_name,
      playbook_name,
      product_name,
      target_description,
      abbrv_ad_type_display,
      campaign,
      target,
      brand,
      portfolio_display,
      ad_type_display,
      pathFrom,
      ...productData,
    }
  } else {
    const {
      cannibalized_sales,
      returned_sales,
      ntb_clv_sales,
      organic_sales_increase,
      roas,
      true_roas,
      attributed_sales,
      incremental_sales,
      true_roas_cannibalized_sales,
      true_roas_returned_sales,
      true_roas_ntb_clv_sales,
      cost,
      ntb_purchases,
      customer_lifetime_value,
      brand_id,
      campaign_id,
      ad_market,
      internal_target_id,
      master_product_id,
      market_product_id,
      pageType,
      ad_type,
      playbook_id,
      portfolio_id,
      campaign_name,
      portfolio_name,
      playbook_name,
      product_name,
      target_description,
      abbrv_ad_type_display,
      campaign,
      target,
      brand,
      portfolio_display,
      ad_type_display,
      pathFrom,
    } = data as WaterfallInputDataType

    result = {
      cannibalized_sales,
      returned_sales,
      ntb_clv_sales,
      organic_sales_increase,
      roas,
      true_roas,
      attributed_sales,
      incremental_sales,
      true_roas_cannibalized_sales,
      true_roas_returned_sales,
      true_roas_ntb_clv_sales,
      cost,
      ntb_purchases,
      customer_lifetime_value,
      brand_id,
      campaign_id,
      ad_market,
      internal_target_id,
      master_product_id,
      market_product_id,
      pageType,
      ad_type,
      playbook_id,
      portfolio_id,
      campaign_name,
      portfolio_name,
      playbook_name,
      product_name,
      target_description,
      abbrv_ad_type_display,
      campaign,
      target,
      brand,
      portfolio_display,
      ad_type_display,
      pathFrom,
      ...productData,
    }
  }
  return result
}

export const getLifetimeValuePath = (
  pathFrom: string,
  data: WaterfallInputDataType,
) => {
  switch (pathFrom) {
    case 'product': {
      return data?.market_product_id
        ? `${productListingRoute}/${data?.master_product_id}/marketplace/${data?.marketplace_id}/listing/${data?.market_product_id}/loyalty/ltv`
        : data?.master_product_id
          ? `${productListingRoute}/${data?.master_product_id}/loyalty/ltv`
          : undefined
    }
    case 'campaign':
      return `/loyalty/ltv/overview`
    case 'target':
      return `/loyalty/ltv/overview`
    default:
      return undefined
  }
}

export const createDataArray = ({
  data,
  type,
  productType,
  isCannibalizationPage,
}: {
  data: WaterfallInputDataType
  type: 'roas' | 'sales'
  productType: 'master' | 'market' | undefined
  isCannibalizationPage: boolean
}) => {
  const cannibalizationData = { ...data, isTrueRoasCannibalization: true }
  const organicRankData = { ...data, isOrganicRank: true }
  const roasData: WaterfallDataType[] = [
      {
        name: 'ROAS',
        ...data?.roas,
        pctChange: data?.roas?.pct_change,
        dataObj: data,
      },
      {
        name: 'Cannibalization',
        value:
          data?.cannibalized_sales?.value === null
            ? null
            : (data?.true_roas_cannibalized_sales?.value ?? null),
        metricData: [
          {
            name: 'Cannibalized Sales',
            ...data?.cannibalized_sales,
          },
        ],
        info: barTooltipInfo.cannibalization,
        path: !isCannibalizationPage
          ? '/traffic/paid-traffic/cannibalization'
          : undefined,
        dataObj: cannibalizationData,
      },
      {
        name: 'Returns',
        value:
          data?.returned_sales?.value === null
            ? null
            : data?.true_roas_returned_sales?.value,
        metricData: [
          {
            name: 'Returns',
            ...data?.returned_sales,
          },
        ],
        info: barTooltipInfo.returns,
        path:
          productType === 'market'
            ? `${productDetailRoute.listing({
                productId: data?.master_product_id,
                marketplaceId: data?.marketplace_id,
                listingId: data?.market_product_id,
              })}/loyalty/returns`
            : productType === 'master'
              ? `${productDetailRoute.detail(data?.master_product_id)}/loyalty/returns`
              : '/loyalty/returns',
      },
      {
        name: 'Lifetime Value',
        value:
          data?.ntb_clv_sales?.value === null
            ? null
            : data?.true_roas_ntb_clv_sales?.value,
        metricData: [
          {
            name: 'Incremental Sales from New Customers',
            ...data?.ntb_clv_sales,
          },
          {
            name: 'New Customers',
            ...data?.ntb_purchases,
          },
          {
            name: 'Repeat Purchase Value',
            ...data?.customer_lifetime_value,
          },
        ],
        info: barTooltipInfo.lifetimeValue,
        path: getLifetimeValuePath(data?.pathFrom as string, data),
      },
      {
        name: 'Organic Rank',
        value:
          data?.organic_sales_increase?.value === null
            ? null
            : data?.true_roas?.value,
        metricData: [
          {
            name: 'Incremental Sales from Organic Rank',
            ...data?.organic_sales_increase,
          },
        ],
        info: barTooltipInfo.organicRank,
        path: '/traffic/paid-traffic/organic-rank',
        dataObj: organicRankData,
      },
      {
        name: 'True ROAS',
        ...data?.true_roas,
        pctChange: data?.true_roas?.pct_change,
        dataObj: data,
      },
    ],
    salesData: WaterfallDataType[] = [
      {
        name: 'Ad Sales',
        ...data?.attributed_sales,
        pctChange: data?.attributed_sales?.pct_change,
        dataObj: data,
      },
      {
        name: 'Cannibalization',
        value:
          data?.cannibalized_sales?.value === null
            ? null
            : data?.attributed_sales?.value - data?.cannibalized_sales?.value,
        metricData: [
          {
            name: 'Cannibalized Sales',
            ...data?.cannibalized_sales,
          },
        ],
        info: barTooltipInfo.cannibalization,
        dataObj: cannibalizationData,
        path: !isCannibalizationPage
          ? '/traffic/paid-traffic/cannibalization'
          : undefined,
      },
      {
        name: 'Returns',
        value:
          data?.returned_sales?.value === null
            ? null
            : data?.attributed_sales?.value -
              data?.cannibalized_sales?.value -
              data?.returned_sales?.value,
        metricData: [
          {
            name: 'Returns',
            ...data?.returned_sales,
          },
        ],
        info: barTooltipInfo.returns,
        path:
          productType === 'market'
            ? `${productListingRoute}/${data?.master_product_id}/marketplace/${data?.marketplace_id}/listing/${data?.market_product_id}/loyalty/returns`
            : productType === 'master'
              ? `${productListingRoute}/${data?.master_product_id}/loyalty/returns`
              : '/loyalty/returns',
      },
      {
        name: 'Lifetime Value',
        value:
          data?.ntb_clv_sales?.value === null
            ? null
            : data?.attributed_sales?.value -
              data?.cannibalized_sales?.value -
              data?.returned_sales?.value +
              data?.ntb_clv_sales?.value,
        metricData: [
          {
            name: 'Incremental Sales from New Customers',
            ...data?.ntb_clv_sales,
          },
          {
            name: 'New Customers',
            ...data?.ntb_purchases,
          },
          {
            name: 'Repeat Purchase Value',
            ...data?.customer_lifetime_value,
          },
        ],
        info: barTooltipInfo.lifetimeValue,
        path: getLifetimeValuePath(data?.pathFrom as string, data),
      },
      {
        name: 'Organic Rank',
        value:
          data?.organic_sales_increase?.value === null
            ? null
            : data?.incremental_sales?.value,
        metricData: [
          {
            name: 'Incremental Sales from Organic Rank',
            ...data?.organic_sales_increase,
          },
        ],
        info: barTooltipInfo.organicRank,
        path: '/traffic/paid-traffic/organic-rank',
        dataObj: organicRankData,
      },
      {
        name: 'Incremental Ad Sales',
        ...data?.incremental_sales,
        pctChange: data?.incremental_sales?.pct_change,
        dataObj: data,
      },
    ]

  return type === 'roas' ? roasData : salesData
}
