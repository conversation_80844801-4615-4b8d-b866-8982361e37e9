.chartContainer {
  text-align: center;
  font-size: 10px;
  height: 208px;
  width: 1px;
  cursor: pointer;

  svg {
    overflow: visible;
  }
}

.tooltipContainer {
  background-color: white;
}

.tooltipHeader {
  font-weight: bold;
}

.tooltipText {
  font-size: 10px;
}

.legendItem {
  display: flex;
  column-gap: 5px;
  align-items: center;
}

.legendContainer {
  display: grid;
  row-gap: 16px;
}

.legendText {
  text-align: left;
}

.legendColor {
  width: 4px;
  height: 32px;
}

.legend {
  padding: 0%;
}

.displayNone {
  display: none;
}

.alignLeftTxt {
  text-align: left;
}

.appendHeight {
  height: 280px;
}

.metricStyle {
  column-gap: 8px;
}
