import React, { useContext } from 'react'
import moment, { type MomentInput } from 'moment'
import { ThemeContext } from 'src/Context'

import { type TwoLineDateLabelProps } from './TwoLineDateLabel'

const ComparisonGraphXTick = ({
  x,
  y,
  payload,
  enableIntraDayReporting,
  comparisonViewOn,
}: TwoLineDateLabelProps): React.JSX.Element => {
  const { timeframe } = useContext(ThemeContext),
    aggregation = timeframe.aggregation

  const splitStr = payload.value.toString().split(' | ')
  const currentDate = splitStr[0],
    comparisonDate = splitStr[1]

  const getDayOfDate = (date: MomentInput) => moment(date).format('ddd')
  const getMonthOfDate = (date: MomentInput) => moment(date).format('MMM')
  const getYearOfDate = (date: MomentInput) => moment(date).format('YY')
  const getHourOfDateTime = (date: MomentInput) =>
    moment.utc(date).format('MMM D, hA')
  const getHourOfDate = (date: MomentInput) => moment(date).format('MMM D')
  const getHourOfTime = (date: MomentInput) => moment(date).format('hA')

  const formattedCurrentDate = moment(currentDate).format('MM/DD/YY'),
    formattedComparisonDate = moment(comparisonDate).format('MM/DD/YY')
  const fontSize = 12
  const formattedTick = (x: number, y: number) =>
    aggregation === 'month' ? (
      `${getMonthOfDate(currentDate)} ${getYearOfDate(currentDate)} (${getMonthOfDate(comparisonDate)} ${getYearOfDate(comparisonDate)})`
    ) : enableIntraDayReporting && !comparisonViewOn ? (
      <>
        <tspan x={x} y={y}>{`${getHourOfDate(payload?.value)}`}</tspan>
        <tspan x={x} y={y}>{`${getHourOfTime(payload?.value)}`}</tspan>
      </>
    ) : enableIntraDayReporting && comparisonViewOn ? (
      <>
        <tspan x={x} y={y}>{`${getHourOfDateTime(currentDate)}`}</tspan>
        <tspan
          x={x}
          // Giving a 2px gap.
          y={y + 2 + 2 * fontSize}
        >{` (${getHourOfDateTime(comparisonDate)})`}</tspan>
      </>
    ) : (
      // In case of daily or weekly aggregation on comparison view, the text is too long. So, we will have them in two lines.
      <>
        <tspan
          x={x}
          y={y}
        >{`${getDayOfDate(currentDate)}, ${formattedCurrentDate}`}</tspan>
        <tspan
          x={x}
          // Giving a 2px gap.
          y={y + 2 + 2 * fontSize}
        >{`(${getDayOfDate(comparisonDate)}, ${formattedComparisonDate})`}</tspan>
      </>
    )

  return (
    <g>
      <text className={`two-line-date-label fs-${fontSize}`} x={x} y={y}>
        <tspan
          fill='rgb(116, 121, 157)'
          textAnchor={payload.index !== 0 ? 'middle' : 'start'}
          key='0'
          x={x}
          dy='1.1em'
        >
          {formattedTick(Number(x), Number(y))}
        </tspan>
      </text>
    </g>
  )
}

export default ComparisonGraphXTick
