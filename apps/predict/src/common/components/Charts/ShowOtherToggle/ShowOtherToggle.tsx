import React, { useState } from 'react'
import type { ReactNode } from 'react'
import { Switch, useIsMobileView } from '@patterninc/react-ui'

import styles from '../../../scss/common-components.module.scss'

type ShowOtherToggleType<ItemGeneric> = {
  toggleText: string
  /** Prop to enable the toggle, `true` by default */
  enable?: boolean
  /** Data which is to be manipulated */
  data: ItemGeneric[]
  /** Callback Function to set the final data to be passed */
  setFinalData: React.Dispatch<React.SetStateAction<ItemGeneric[]>>
  /** Limit to show */
  limitToShow: number
}

const ShowOtherToggle = <ItemGeneric,>({
  toggleText,
  enable = true,
  data,
  setFinalData,
  limitToShow,
}: ShowOtherToggleType<ItemGeneric>): ReactNode => {
  const [toggleChecked, setToggleChecked] = useState(false)
  const isMobileView = useIsMobileView()

  return enable ? (
    <div
      className={
        isMobileView
          ? styles.showOtherToggleForMobile
          : styles.showOtherToggleForDesktop
      }
    >
      <Switch
        checked={toggleChecked}
        callout={() => {
          toggleChecked
            ? setFinalData(data.slice(0, limitToShow))
            : setFinalData(data)
          setToggleChecked(!toggleChecked)
        }}
      />
      {toggleText.toUpperCase()}
    </div>
  ) : null
}

export default ShowOtherToggle
