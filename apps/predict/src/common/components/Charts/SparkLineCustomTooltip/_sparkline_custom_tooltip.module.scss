@use '../../../scss/base/mixins' as *;
@use '@patterninc/react-ui/dist/variables' as var;

.basicTooltip,
.multilineTooltip {
  color: var(--purple);
  background: var(--white);
  border: 1px solid var(--medium-purple);
  padding: 10px;
  border-radius: 4px;
  font-size: 12px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  @include box-shadow;
}

.basicTooltip {
  min-width: 140px;
  max-width: max-content;
  min-height: 64px;

  .topSection {
    align-items: center;

    .title {
      font-size: 12px;
      font-weight: 600;
      color: var(--dark-purple) !important;

      .header-with-underline {
        padding-bottom: 6px;

        &::after {
          height: 3px;
          background: var(--medium-purple);
        }
      }

      &.secondTitle {
        margin-bottom: 8px;
      }
    }

    .secondDateMdash {
      font-size: var(--font-size-14);
      font-weight: bold;
      margin-bottom: 7px;
    }
  }

  .statContainer {
    .stat {
      color: var(--dark-purple);
      font-size: 14px;
      font-weight: 600;
    }
  }
}

.multilineTooltip {
  height: 64px;
  opacity: 1;

  .topSection {
    .title {
      font-size: 12px;
      font-weight: 600;
      color: var(--dark-purple) !important;

      .header-with-underline {
        padding-bottom: 6px;

        &::after {
          height: 3px;
          background: var(--medium-purple);
        }
      }
    }
  }

  .statBox {
    display: flex;

    .statContainer {
      &:not(:last-of-type) {
        margin-right: 20px;
      }

      .stat {
        font-size: 16px;
        font-weight: 600;
      }
    }
  }

  @media only screen and (max-width: var.$breakpoint-lg-max) and (min-width: var.$breakpoint-md) {
    height: 100%;

    .statBox {
      margin-top: 16px;
      flex-direction: column;
    }
  }
}

.firstMoverTooltip {
  background: var(--white);
  padding: 20px;
  color: var(--purple);
  font-size: var(--font-size-14);
  z-index: 10;
  @include border-radius(4px);
  @include box-shadow();

  .heading {
    margin-bottom: 20px;

    .heading-body {
      display: grid;
      grid-template-columns: 2fr 1fr;

      .title {
        font-weight: var(--font-weight-semibold);
      }
    }
  }

  .column-headers {
    font-size: var(--font-size-12);
  }

  .market-color-list {
    width: 16px;
    height: 5px;
    margin-right: 8px;
    @include border-radius(1000px);
  }

  .marketplace-name {
    font-weight: var(--font-weight-semibold);
  }

  .price-metric {
    justify-self: end;
    color: var(--purple);
    font-weight: var(--font-weight-semibold);
  }

  .tooltip-body {
    display: grid;
    grid-template-columns: 2fr 1fr;
  }
}

.bold {
  font-weight: var(--font-weight-bold);
}

.flex {
  display: flex;
  margin-bottom: 16px;
}

.justifyContentBetween {
  justify-content: space-between;
}
