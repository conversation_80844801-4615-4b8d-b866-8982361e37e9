import React, {
  use<PERSON><PERSON>back,
  useContext,
  useEffect,
  useMemo,
  useState,
} from 'react'
import {
  Area,
  AreaChart,
  CartesianGrid,
  ComposedChart,
  Legend,
  Line,
  LineChart,
  ResponsiveContainer,
  Tooltip,
  type TooltipProps,
  XAxis,
  YAxis,
  type YAxisProps,
} from 'recharts'
import { type CurveType } from 'recharts/types/shape/Curve'
import { type LayoutType } from 'recharts/types/util/types'
import {
  abbreviateNumber,
  Alert,
  GraphLoading,
  useMediaQuery,
} from '@patterninc/react-ui'
import { c, useTranslate } from '@predict-services'
import type {
  HorizontalAlignmentType,
  Payload,
  VerticalAlignmentType,
} from 'recharts/types/component/DefaultLegendContent'
import { ThemeContext } from 'src/Context'
import {
  getIntlDateTimeOptionsFromTimeframe,
  type TickFormat,
} from 'src/modules/Insights/components/Pages/AtAGlance/SalesBreakdown.helper2'
import moment from 'moment-timezone'

import styles from '../../../scss/common-components.module.scss'
import ShowOtherToggle from '../ShowOtherToggle/ShowOtherToggle'

type StandardStackedChartProps<ItemGeneric, ChartDataGeneric> = {
  // Type of chart to render - Area, Line or Composed, `Area` by default
  chartType?: 'area' | 'line' | 'composed'
  // Type of data to render - percentage (%) or number or currency ($, etc)
  type?: 'percentage' | 'number' | 'currency'
  user: {
    current_currency: {
      symbol: string
      code: string
    }
  }
  data: ItemGeneric[]
  // Flag to show the loading state
  isLoading: boolean
  tooltipContent?: React.JSX.Element
  /** Key which contains the data for Stacked Chart */
  displayKeyChart: keyof ItemGeneric
  /** Unique id for each element in the array of data */
  idKey: keyof ItemGeneric
  /** Key in the array of chart_data whose data we want to display in Stacked chart */
  dataKeyChart: keyof ChartDataGeneric & string
  /** Key which contains the name to display in tooltip and legend */
  nameKey: keyof ItemGeneric
  /** Custom Legend for the Stacked chart */
  legendContent?: React.JSX.Element
  /** Custom Legend props */
  legendProps?: {
    iconSize?: number
    layout?: LayoutType
    align?: HorizontalAlignmentType
    verticalAlign?: VerticalAlignmentType
  }
  /** Custom Tooltip styles */
  tooltipStyles?: React.CSSProperties
  /** Custom Legend styles.
   * For Mobile view defaults to {top: 282, width: '100%'}.
   * More styles can be passed as per the requirement
   */
  legendStyles?: React.CSSProperties
  /** Key which contains the date, default value is `date` */
  dateKey?: keyof ChartDataGeneric
  /** Flag to use Composed Chart */
  isComposedChart?: {
    content: React.ReactNode
    /** boolean key to distinguish Area chart data from other chart data. It must be `true` for Stacked chart data */
    uniqueKeyChart: keyof ItemGeneric
    /** If needed a Bi-Axial chart */
    YAxis?: {
      id: string
      orientation?: 'left' | 'right'
      unit?: string
      props?: YAxisProps
      /** By Default, the type is `percentage` */
      type?: 'percentage' | 'number' | 'currency'
    }
    /** Key in the array of chart_data whose data we want to display in other chart */
    dataKeyComposedChart: keyof ChartDataGeneric
  }
  /** Prop to override Default Legend Payload */
  customLegendPayload?: Payload[]
  /** Additional props to pass to the chart in case of customisation required **/
  additionalChartProps?: Record<string, unknown>
  /** Custom Tooltip props */
  tooltipProps?: TooltipProps<'', ''>
  /**
   * Optional prop to map an element of the data with a particular color.
   * Indexing starts from 0.
   */
  customColorMapping?: { index: number; color: keyof typeof colors }
  // showing lengend text below chart
  lineChartLegendText?: string
  //** set Y-axis orientation by default it will be left */
  yAxisOrientation?: 'left' | 'right'
  toggleText?: string
  /** Defaults to 282px */
  responsiveContainerHeight?: number
}

const colors = {
  LIGHT_GREEN_4: 'var(--chart-light-4-green)',
  LIGHT_RED_5: 'var(--chart-light-5-red)',
  LIGHT_ORANGE_5: 'var(--chart-light-5-orange)',
  LIGHT_YELLOW_5: 'var(--chart-light-5-yellow)',
  LIGHT_GREEN_5: 'var(--chart-light-5-green)',
  LIGHT_ROYAL_5: 'var(--chart-light-5-royal)',
  LIGHT_PURPLE_5: 'var(--chart-light-5-purple)',
  LIGHT_TEAL_5: 'var(--chart-light-5-teal)',
  LIGHT_BLUE_5: 'var(--chart-light-5-blue)',
  LIGHT_ORANGE_3: 'var(--chart-light-3-orange)',
  LIGHT_GRAY: 'var(--light-gray)',
}

const colorList = Object.values(colors)

type CombinedDataType = { date: string } & Record<string, number | string>

const StandardStackedChart = <ItemGeneric, ChartDataGeneric>({
  chartType = 'area',
  type = 'percentage',
  user,
  tooltipContent,
  data,
  isLoading = false,
  idKey,
  nameKey,
  displayKeyChart,
  dataKeyChart,
  legendContent,
  legendProps,
  tooltipStyles,
  legendStyles,
  dateKey = 'date' as keyof ChartDataGeneric,
  isComposedChart,
  customLegendPayload,
  additionalChartProps = {},
  tooltipProps,
  customColorMapping,
  lineChartLegendText,
  yAxisOrientation = 'left',
  toggleText,
  responsiveContainerHeight = 282,
}: StandardStackedChartProps<
  ItemGeneric,
  ChartDataGeneric
>): React.JSX.Element => {
  const [finalData, setFinalData] = useState<ItemGeneric[]>([])
  const { timeframeGlobal: timeframe } = useContext(ThemeContext)
  const { t: tf } = useTranslate('timeframe')
  // We will never show more than 9 lines by default. If there's a requirement in future we can make this as a prop to this component.
  const limitToShow = useMemo(() => 9, [])

  useEffect(() => {
    setFinalData(toggleText ? data.slice(0, limitToShow) : data)
  }, [limitToShow, toggleText, data])
  const showOtherToggleContent = useMemo(() => {
    return (
      data?.length > 9 &&
      toggleText && (
        <ShowOtherToggle
          data={data}
          setFinalData={setFinalData}
          limitToShow={limitToShow}
          toggleText={toggleText}
        />
      )
    )
  }, [data, toggleText, limitToShow])

  // Function to convert the response into recharts compatible format
  const combinedData = finalData.reduce((acc: CombinedDataType[], curr) => {
    const dateMap = new Map<string, CombinedDataType>()

    // First populate the map with existing entries
    acc.forEach((item) => {
      dateMap.set(item[dateKey as string] as string, item)
    })
    ;(curr[displayKeyChart] as ChartDataGeneric[])?.forEach((entry) => {
      const dateValue = entry[dateKey] as string
      const isOtherChartData =
        isComposedChart && !curr[isComposedChart.uniqueKeyChart]
      const existingEntry = dateMap.get(dateValue)
      if (existingEntry) {
        // Update existing entry
        existingEntry[curr[nameKey] as string] = isOtherChartData
          ? (entry[isComposedChart.dataKeyComposedChart] as number)
          : (entry[dataKeyChart] as number)
      } else {
        // Create new entry
        const newDataPoint: CombinedDataType = {
          date: dateValue,
          [curr[nameKey] as string]: isOtherChartData
            ? (entry[isComposedChart.dataKeyComposedChart] as number)
            : (entry[dataKeyChart] as number),
        }
        dateMap.set(dateValue, newDataPoint)
        acc.push(newDataPoint)
      }
    })
    return acc
  }, [])
  // `chartData` in case of Composed chart will be the data which has the uniqueKeyChart else it will be the complete data
  const chartData = isComposedChart
    ? data.filter((item) => item[isComposedChart.uniqueKeyChart])
    : data

  // `splice` method will mutate the original `colorList` array, hence making a local copy
  const colorListCopy = useMemo(() => [...colorList], [])

  const isMobileView = useMediaQuery({ type: 'max', breakpoint: 'md' })

  const tickFormatter = useCallback(
    (tick: number, customTickType: typeof type) => {
      return customTickType === 'percentage'
        ? `${(tick * 100).toFixed(2)}%`
        : customTickType === 'currency'
          ? `${user?.current_currency?.symbol ?? '$'}${abbreviateNumber(tick)} ${
              user?.current_currency?.code.toLocaleLowerCase() !== 'usd'
                ? user?.current_currency?.code
                : ''
            }`
          : `${abbreviateNumber(tick)}`
    },
    [user?.current_currency],
  )

  if (customColorMapping && customColorMapping.index < colorListCopy.length) {
    // If the color is already present at that index, no need to re-arrange the colors
    if (
      colorListCopy[customColorMapping.index] !==
      colors[customColorMapping.color]
    ) {
      // Index of the color to be removed
      const idx = colorListCopy.findIndex(
        (color) => color === customColorMapping.color,
      )
      // Remove the color from its original index
      colorListCopy.splice(idx, 1)
      // Add that removed color to new index
      colorListCopy.splice(
        customColorMapping.index,
        0,
        colors[customColorMapping.color],
      )
    }
  }

  const lineChartLegendContent = !isLoading && data.length > 0 && (
    <div
      className={styles.lineChartLegend}
      style={
        isMobileView
          ? { justifyContent: 'flex-end' }
          : { justifyContent: 'center' }
      }
    >
      <div>{lineChartLegendText}</div>
    </div>
  )

  const chartContent = useMemo(
    () => [
      // ToDo: Enable this CartesianGrid when we selected any specific Area on chart
      <CartesianGrid vertical={false} key='cartesian-grid' />,
      <XAxis
        key='xaxis-date'
        dataKey='date'
        type='category'
        interval={isMobileView ? 'preserveStartEnd' : 'preserveStart'}
        height={45}
        axisLine={false}
        tickLine={false}
        tickCount={8}
        tickMargin={10}
        allowDuplicatedCategory={false}
        tick={({ x, y, visibleTicksCount, index, payload }) => (
          <g>
            <text className='fs-10' x={x} y={y}>
              <tspan
                fill='var(--purple)'
                textAnchor='end'
                key='0'
                x={x}
                dy='1.1em'
                dx={visibleTicksCount === (index as number) + 1 ? '0' : '3em'}
              >
                {payload.value // To prevent passing `undefined` or empty string payload.value
                  ? new Intl.DateTimeFormat(
                      tf('dateTimeFormat'),
                      getIntlDateTimeOptionsFromTimeframe({
                        ...(timeframe as TickFormat),
                      }),
                    ).format(
                      moment.tz(payload.value, moment.tz.guess()).toDate(), // To convert the date string to date object with client's timezone
                    )
                  : ''}
              </tspan>
            </text>
          </g>
        )}
      />,
      <YAxis
        key='yaxis-value'
        axisLine={false}
        tickLine={false}
        tickCount={5}
        tickMargin={5}
        orientation={yAxisOrientation}
        tick={{ fontSize: 10, stroke: 'var(--purple)', strokeWidth: 0 }}
        tickFormatter={(tick) => tickFormatter(tick, type)}
        domain={[0, 'auto']}
        width={56}
      />,
      ...[
        isComposedChart?.YAxis
          ? [
              <YAxis
                key='yaxis-composed-chart'
                yAxisId={isComposedChart.YAxis.id}
                orientation={isComposedChart.YAxis.orientation}
                unit={isComposedChart.YAxis.unit}
                {...isComposedChart.YAxis.props}
                tickFormatter={(tick) =>
                  tickFormatter(
                    tick,
                    isComposedChart.YAxis?.type ?? 'percentage',
                  )
                }
                width={56}
                ref={undefined}
              />,
            ]
          : [],
      ],
      ...[
        tooltipContent
          ? [
              <Tooltip
                key='tooltip-custom'
                content={tooltipContent}
                wrapperStyle={{ ...tooltipStyles }}
                {...tooltipProps}
              />,
            ]
          : [<Tooltip key='tooltip-default' />],
      ],
      ...[
        chartData.map((DataItem, index) => {
          const standardChartProps = {
            key: DataItem[idKey] as React.Key,
            id: DataItem[idKey] as string,
            type: 'monotone' as CurveType,
            dataKey: DataItem[nameKey] as string,
            name: DataItem[nameKey] as string,
            stroke: colorListCopy[index],
            fill: colorListCopy[index],
            fillOpacity: 1,
            ...additionalChartProps, //the sequence is important, as it will override the default props
          }

          switch (chartType) {
            case 'line':
              return (
                <Line
                  {...standardChartProps}
                  key={standardChartProps.key}
                  strokeWidth={4}
                  dot={false}
                />
              )
            default:
              return (
                <Area
                  {...standardChartProps}
                  key={standardChartProps.key}
                  stackId='1'
                />
              )
          }
        }),
      ],
      ...[
        legendContent
          ? [
              <Legend
                key='legend-custom'
                {...legendProps}
                content={legendContent}
                wrapperStyle={{
                  ...legendStyles,
                  ...(isMobileView
                    ? { top: responsiveContainerHeight, width: '100%' }
                    : {}),
                }}
                payload={customLegendPayload}
              />,
            ]
          : [<Legend key='legend-default' />],
      ],
      isComposedChart?.content,
    ],
    [
      additionalChartProps,
      chartData,
      chartType,
      colorListCopy,
      customLegendPayload,
      idKey,
      isComposedChart?.YAxis,
      isComposedChart?.content,
      isMobileView,
      legendContent,
      legendProps,
      legendStyles,
      nameKey,
      responsiveContainerHeight,
      tf,
      tickFormatter,
      timeframe,
      tooltipContent,
      tooltipProps,
      tooltipStyles,
      type,
      yAxisOrientation,
    ],
  )

  const renderChart = () => {
    const chartProps = {
      margin: {
        top: 20,
        // 20 is for mobile view to make all the characters visible on right vertical axis
        right: isMobileView ? 20 : 5,
        left: 5,
        bottom: 5,
      },
      data: combinedData,
    }

    switch (chartType) {
      case 'line':
        return <LineChart {...chartProps}>{chartContent}</LineChart>
      case 'composed':
        return <ComposedChart {...chartProps}>{chartContent}</ComposedChart>
      default:
        return <AreaChart {...chartProps}>{chartContent}</AreaChart>
    }
  }

  return isLoading ? (
    <GraphLoading />
  ) : data.length === 0 ? (
    <Alert text={c('noDataAvailableForTheSelectedSegment')} type='info' />
  ) : (
    <div className='pat-pb-4'>
      {lineChartLegendText && isMobileView ? (
        <div className='flex align-items-center pat-mb-4'>
          {showOtherToggleContent}
          {lineChartLegendContent}
        </div>
      ) : null}
      <ResponsiveContainer
        height={
          isMobileView
            ? responsiveContainerHeight * 2 // 2 times to accommodate the legend below the chart
            : responsiveContainerHeight
        }
        width={'99%'}
      >
        {/* 99% width to make the chart responsive to browser window size changes refer: https://github.com/recharts/recharts/issues/172 */}
        {renderChart()}
      </ResponsiveContainer>
      {lineChartLegendText && !isMobileView ? (
        <div className='flex align-items-center'>{lineChartLegendContent}</div>
      ) : null}
      {!isMobileView ? <div>{showOtherToggleContent}</div> : null}
    </div>
  )
}

export default StandardStackedChart
