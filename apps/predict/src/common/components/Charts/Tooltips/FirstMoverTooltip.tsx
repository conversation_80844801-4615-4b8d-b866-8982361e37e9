import React from 'react'
import { NumericFormat } from 'react-number-format'
import moment from 'moment'
import { type TooltipProps } from 'recharts'
import {
  type NameType,
  type ValueType,
} from 'recharts/types/component/DefaultTooltipContent'
import { sortFilter } from '@patterninc/react-ui'

type FirstMoverTooltipProps = {
  productMapPrice: number
  prefix?: string
  suffix?: string
} & TooltipProps<ValueType, NameType>

const FirstMoverTooltip = ({
  active,
  payload,
  productMapPrice,
  prefix,
  suffix,
}: FirstMoverTooltipProps) => {
  if (active) {
    return (
      <div className='first-mover-tooltip'>
        <div className='heading'>
          <div className='column-headers flex justify-content-between'>
            <div>When</div>
            {productMapPrice && <div>Map Price</div>}
          </div>
          <div className='heading-body'>
            {payload && (
              <div className='title'>
                {moment(payload[0]?.payload?.date).format(
                  'hh:mm a on MMM DD, YYYY',
                )}
              </div>
            )}
            {productMapPrice && (
              <div className='price-metric flex align-items-center'>
                <div
                  className='market-color-list'
                  style={{ background: 'var(--green)' }}
                ></div>
                <div>
                  <NumericFormat
                    value={productMapPrice}
                    prefix={prefix ?? ''}
                    suffix={suffix ?? ''}
                    thousandSeparator={true}
                    fixedDecimalScale={true}
                    decimalScale={2}
                    displayType='text'
                  />
                </div>
              </div>
            )}
          </div>
        </div>
        <div className='column-headers flex justify-content-between'>
          <div>Marketplace</div>
          <div>Current Price</div>
        </div>
        {payload &&
          sortFilter(payload, 'value', true).map(
            (e: Record<string, string>, i: number) => {
              return (
                <div key={i} className='tooltip-body align-items-center'>
                  <div className='market-container flex align-items-center'>
                    <div
                      className='market-color-list'
                      style={{ background: e.stroke }}
                    ></div>
                    <span className='marketplace-name'>{e.dataKey}</span>
                  </div>
                  <NumericFormat
                    value={e.value}
                    prefix={prefix ?? ''}
                    suffix={suffix ?? ''}
                    thousandSeparator={true}
                    fixedDecimalScale={true}
                    decimalScale={2}
                    displayType='text'
                    className='price-metric'
                  />
                </div>
              )
            },
          )}
      </div>
    )
  }
  return null
}

export default FirstMoverTooltip
