type PayloadProps = {
  color: string
  dataKey: string
  fill: string
  id: string | number
  name: string
  // If we have column in the tooltip whose values are not plotted, the values of that column are referenced from the payload
  payload: {
    [key: string]: { est_share: number; est_sales: number }
  } & { date: string }
  stroke: string
  value: number | string
}

export type TooltipProps = {
  active?: boolean
  label?: string
  payload?: PayloadProps[]
}

export type LegendProps = {
  payload?: PayloadProps[]
}
