import React from 'react'
import type { Meta, StoryObj } from '@storybook/react'
import { type ChartColorsType, toast } from '@patterninc/react-ui'

import ColorPicker from './ColorPicker'

// More on how to set up stories at: https://storybook.js.org/docs/writing-stories#default-export
// figmaLink: 'https://www.figma.com/design/CysuHKYlYKQvtpEk1Ud43k/Color-Picker?node-id=0-1&node-type=canvas'
const meta = {
  title: 'Components/ColorPicker',
  component: ColorPicker,
  parameters: {
    // Optional parameter to override the layout of the component. Default is 'fullscreen'. More info: https://storybook.js.org/docs/configure/story-layout
    layout: 'centered',
  },
  // This component will have an automatically generated Autodocs entry: https://storybook.js.org/docs/writing-docs/autodocs
  tags: ['autodocs'],
} satisfies Meta<typeof ColorPicker>

export default meta

type Story = StoryObj<typeof ColorPicker>

const Template: Story = {
  render: ({ ...args }) => <ColorPicker {...args} />,
}

export const basic = {
  ...Template,
  args: {
    callout: (color: ChartColorsType) => {
      toast({ message: `You have picked color ${color}`, type: 'success' })
    },
  },
}

export const selectedDefaultColor = {
  ...Template,
  args: {
    callout: () => undefined,
    selectedDefaultColor: '#f4dc2c',
  },
}

export const addColors = {
  ...Template,
  args: {
    callout: () => undefined,
    addColors: [
      { color: '#f00', disabled: false },
      { color: '#0f0', disabled: false },
      { color: '#00f', disabled: false },
    ],
  },
}

export const overrideColors = {
  ...Template,
  args: {
    callout: () => undefined,
    colors: [
      { color: '#f00', disabled: false },
      { color: '#0f0', disabled: false },
      { color: '#00f', disabled: false },
      { color: '#f0f', disabled: false },
      { color: '#0ff', disabled: false },
      { color: '#ff0', disabled: false },
    ],
  },
}

export const CustomizeNumberOfColorPalattesInARow = {
  ...Template,
  args: {
    callout: () => undefined,
    colors: [
      { color: '#f00', disabled: false },
      { color: '#0f0', disabled: false },
      { color: '#00f', disabled: false },
      { color: '#f0f', disabled: false },
      { color: '#0ff', disabled: false },
      { color: '#ff0', disabled: false },
    ],
    numOfColorsPerRow: 3,
  },
}

export const disabledColorPicker = {
  ...Template,
  args: {
    callout: () => undefined,
    disabled: true,
  },
}

export const disabledColorPalattes = {
  ...Template,
  args: {
    callout: () => undefined,
    colorsToBeDisabled: [
      {
        color: '#f09',
        tooltip: {
          tooltipContent: <div>Already selected.</div>,
        },
      },
      { color: '#2e1ecf' },
      { color: '#85badb' },
      {
        color: '#f8eb8b',
        tooltip: {
          tooltipContent: <div>Already selected.</div>,
        },
      },
      {
        color: '#2081be',
        tooltip: {
          tooltipContent: <div>Already selected.</div>,
        },
      },
      {
        color: '#f4dc2c',
        tooltip: {
          tooltipContent: <div>Already selected.</div>,
        },
      },
      {
        color: '#00d5df',
        tooltip: {
          tooltipContent: <div>Already selected.</div>,
        },
      },
      { color: '#165984' },
    ],
  },
}

export const getColorPickerIsOpenedOrClosedState = {
  ...Template,
  args: {
    callout: () => undefined,
    onColorPickerOpenedClosed: (isOpened: boolean) => {
      if (isOpened) {
        toast({ message: `Color picker opened`, type: 'info' })
      } else {
        toast({ message: `Color picker closed`, type: 'info' })
      }
    },
  },
}

export const openColorPickerAtBottom = {
  ...Template,
  args: {
    callout: () => undefined,
    popoverProps: {
      position: 'bottom',
    },
  },
}

export const showTooltipOnColorPicker = {
  ...Template,
  args: {
    callout: () => undefined,
    tooltip: {
      tooltipContent: <div>Showing tooltip</div>,
    },
  },
}

export const showTooltipWhenColorPickerIsDisabled = {
  ...Template,
  args: {
    callout: () => undefined,
    disabled: true,
    tooltip: {
      tooltipContent: <div>ColorPicker disabled</div>,
    },
  },
}
