import React, { useEffect, useMemo, useState } from 'react'
import {
  Button,
  CHART_COLORS,
  type ChartColorsType,
  PopoverNew,
  type PopoverNewProps,
  Tooltip,
  type TooltipProps,
  useMediaQuery,
} from '@patterninc/react-ui'

import styles from './_color-picker.module.scss'

export type ColorPickerPopoverProps = Omit<
  PopoverNewProps,
  'children' | 'popoverContent' | 'appendTo' | 'onClickOutside'
>

export type ColorPickerProps = {
  /** Passes back the current value of the color picker */
  callout: (value: ChartColorsType | '') => void
  /** Optionally set the default color from the color palette */
  selectedDefaultColor?: ColorPaletteDetails['color']
  /** Optional class for the color picker */
  className?: string
  /** Optional prop for disabling color picker */
  disabled?: boolean
  /** Optional prop for Popover */
  PopoverProps?: ColorPickerPopoverProps
  /** Optional prop for showing toolip on color picker*/
  tooltip?: Omit<TooltipProps, 'children'>
  /** Optionally override existing colors */
  colors?: ColorPaletteDetails[]
  /** Optionally add more colors to the existing color palette */
  addColors?: ColorPaletteDetails[]
  /** Optionally disabled specific colors in the color picker */
  colorsToBeDisabled?: {
    /** The color we want to be disabled */
    color: string
    /** Optionally add a tooltip for the disabled color */
    tooltip?: Omit<TooltipProps, 'children'>
  }[]
  /** Optionally receive the `Popover` state `visible` to know if the color picker is opened or closed */
  onColorPickerOpenedClosed?: (value: boolean) => void
  /** Number of colors in a row */
  numOfColorsPerRow?: number
}

export type ColorPaletteDetails = {
  /** Color to be displayed in color picker */
  color: ChartColorsType | ''
} & (
  | {
      /** Optional if color palette should not be disabled*/
      disabled?: false
    }
  | {
      /** Optional if color palette should be disabled */
      disabled?: true
      /** Optional if color palette is disabled, add tooltip for it */
      tooltip?: Omit<TooltipProps, 'children'>
    }
)

/** Default color palette */
/** Adding colors in hex value since it will be stored in backend and can be utilized for download pdf or excel */
export const defaultColorPalette: ColorPaletteDetails[] = (
  [
    CHART_COLORS.chartStandardRoyal,
    CHART_COLORS.chartStandardPink,
    CHART_COLORS.chartStandardBlue,
    CHART_COLORS.chartStandardOrange,
    CHART_COLORS.chartStandardPurple,
    CHART_COLORS.chartStandardTeal,
    CHART_COLORS.chartStandardYellow,
    CHART_COLORS.chartStandardRed,
    CHART_COLORS.chartStandardGreen,
    CHART_COLORS.chartLight3Royal,
    CHART_COLORS.chartLight3Pink,
    CHART_COLORS.chartLight3Blue,
    CHART_COLORS.chartLight3Orange,
    CHART_COLORS.chartLight3Purple,
    CHART_COLORS.chartLight3Teal,
    CHART_COLORS.chartLight3Yellow,
    CHART_COLORS.chartLight3Red,
    CHART_COLORS.chartLight3Green,
    CHART_COLORS.chartDark2Royal,
    CHART_COLORS.chartDark2Pink,
    CHART_COLORS.chartDark2Blue,
    CHART_COLORS.chartDark2Orange,
    CHART_COLORS.chartDark2Purple,
    CHART_COLORS.chartDark2Teal,
    CHART_COLORS.chartDark2Yellow,
    CHART_COLORS.chartDark2Red,
    CHART_COLORS.chartDark2Green,
  ] as ChartColorsType[]
).map((color) => ({ color, disabled: false }))

const ColorPicker = ({
  callout,
  className,
  disabled,
  PopoverProps,
  tooltip,
  selectedDefaultColor,
  addColors,
  colors = defaultColorPalette,
  colorsToBeDisabled,
  onColorPickerOpenedClosed,
  numOfColorsPerRow = 9,
}: ColorPickerProps): React.JSX.Element => {
  const [selectedColor, setSelectedColor] = useState(colors[0].color)
  const [disabledColorPaletteIndexes, setDisabledColorPaletteIndexes] =
    useState<number[]>([])

  const colorList = useMemo(() => {
    let updatedColors = addColors ? [...colors, ...addColors] : [...colors]
    const disabledColorList: number[] = []
    updatedColors = updatedColors.map(
      (color: ColorPaletteDetails, index: number) => {
        const disabledColorIndex = colorsToBeDisabled
          ? colorsToBeDisabled.findIndex(
              (disabledColor) => disabledColor.color === color.color,
            )
          : -1
        disabledColorIndex >= 0 && disabledColorList.push(index)
        return colorsToBeDisabled && disabledColorIndex >= 0
          ? ({
              color: color.color,
              disabled: true,
              tooltip: colorsToBeDisabled[disabledColorIndex].tooltip,
            } as ColorPaletteDetails)
          : color
      },
    )
    setDisabledColorPaletteIndexes(disabledColorList)
    return updatedColors
  }, [addColors, colors, colorsToBeDisabled])

  const selectedColorPaletteIndex = useMemo(() => {
    const selectedColorIndex = selectedDefaultColor
      ? colorList.findIndex((color) => color.color === selectedDefaultColor)
      : 0
    const activeColorIndex = selectedColorIndex === -1 ? 0 : selectedColorIndex
    const checkIfDisabledSetNext = (index: number) => {
      if (index >= colorList.length) return -1
      if (disabledColorPaletteIndexes.indexOf(index) >= 0) {
        return checkIfDisabledSetNext(index + 1)
      } else {
        return index
      }
    }
    return checkIfDisabledSetNext(activeColorIndex)
  }, [colorList, selectedDefaultColor, disabledColorPaletteIndexes])

  useEffect(() => {
    if (selectedColorPaletteIndex === -1) {
      setSelectedColor('')
      callout('')
    } else if (selectedColor !== colors[selectedColorPaletteIndex].color) {
      setSelectedColor(colors[selectedColorPaletteIndex].color)
      callout(colors[selectedColorPaletteIndex].color)
    }
  }, [
    setSelectedColor,
    selectedColorPaletteIndex,
    colors,
    callout,
    selectedColor,
  ])

  const screenIsMdMin = useMediaQuery({ type: 'min', breakpoint: 'md' }),
    popoverPlacement = PopoverProps?.position ?? 'left'

  return (
    <PopoverNew
      popoverContent={({ setVisible }) => (
        <div
          className={styles.colorPicker}
          style={{
            gridTemplateColumns: `repeat(${numOfColorsPerRow > colorList.length ? colorList.length : numOfColorsPerRow}, 1fr)`,
          }}
        >
          {colorList.map((color, i) => {
            const colorDisplay = (
              <div
                key={color.color ?? i}
                className={`${styles.color} ${color.disabled ? styles.colorDisabled : ''} ${selectedColor === color.color ? styles.selectedColor : ''}`}
                style={{ backgroundColor: color.color }}
                tabIndex={0}
                onClick={(event) => {
                  event.stopPropagation()
                  if (!color.disabled) {
                    setSelectedColor(color.color)
                    callout(color.color)
                    onColorPickerOpenedClosed?.(false)
                    setVisible(false)
                  }
                  return false
                }}
              />
            )
            return color.disabled && color?.tooltip ? (
              <Tooltip
                key={color.color ?? i}
                {...color?.tooltip}
                position={color?.tooltip?.position ?? 'top'}
              >
                {colorDisplay}
              </Tooltip>
            ) : (
              colorDisplay
            )
          })}
        </div>
      )}
      position={screenIsMdMin ? popoverPlacement : 'bottom'}
      className={`${styles.colorPickerContainer}${
        PopoverProps?.className ?? ''
      }`}
      onClickOutside={() => {
        onColorPickerOpenedClosed?.(false)
      }}
    >
      {({ setVisible, visible }) => {
        const button = (
          <div
            className={`${styles.colorPickerSelector} ${className ?? ''} ${disabled ? styles.pickerDisabled : ''}`}
            tabIndex={0}
          >
            <Button
              as='unstyled'
              className={`${styles.colorSelector} ${disabled ? styles.pickerDisabled : ''}`}
              style={{ backgroundColor: selectedColor }}
              onClick={() => {
                onColorPickerOpenedClosed?.(!visible)
                !disabled && setVisible(!visible)
              }}
            >
              {''}
            </Button>
          </div>
        )
        return tooltip ? (
          <Tooltip {...tooltip} position={tooltip?.position ?? 'top'}>
            {button}
          </Tooltip>
        ) : (
          button
        )
      }}
    </PopoverNew>
  )
}

export default ColorPicker
