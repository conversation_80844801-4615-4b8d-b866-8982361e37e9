import type { Meta, StoryObj } from '@storybook/react'

import Currency, { type CurrencyProps } from './Currency'

const meta = {
  title: 'Components/Currency',
  component: Currency,
  parameters: {
    layout: 'centered',
  },
  tags: ['autodocs'],
} satisfies Meta<typeof Currency>

export default meta
type Story = StoryObj<typeof meta>

const basicArgs: CurrencyProps = {
  value: 123456.8976,
  currencyCode: 'USD',
  currencySymbol: '$',
  customDecimalScale: 0,
}

export const Basic: Story = {
  args: basicArgs,
}

export const WithCustomDecimalScale: Story = {
  args: {
    ...basicArgs,
    customDecimalScale: 2,
  },
}

export const WithSuffix: Story = {
  args: {
    ...basicArgs,
    suffix: 'M',
  },
}
