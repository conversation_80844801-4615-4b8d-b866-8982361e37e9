import React from 'react'
import { NumericFormat, type NumericFormatProps } from 'react-number-format'

export type CurrencyProps = NumericFormatProps & {
  currencyCode?: string
  currencySymbol?: string
  customClass?: string
  customDecimalScale?: NumericFormatProps['decimalScale']
  suffix?: string
}

const getCurrencySuffix = (currencyCode?: string) => {
  return currencyCode && currencyCode !== 'USD' ? ` ${currencyCode}` : ''
}

const Currency = ({
  value,
  currencyCode,
  currencySymbol,
  customClass = '',
  customDecimalScale = 2,
  suffix = ' ',
}: CurrencyProps): React.JSX.Element => {
  const currencySuffix = getCurrencySuffix(currencyCode)
  return (
    <span className={`currency-formatter ${customClass}`}>
      <NumericFormat
        value={value}
        thousandSeparator={true}
        fixedDecimalScale={true}
        prefix={currencySymbol}
        suffix={suffix}
        decimalScale={customDecimalScale}
        displayType='text'
        className='currency-symbol'
      />
      {currencySuffix && (
        <span className='currency-code'>{currencySuffix}</span>
      )}
    </span>
  )
}

export default Currency
