import React, { useContext, useState } from 'react'
import { useQuery } from '@tanstack/react-query'
import { getApiUrlPrefix, Select } from '@patterninc/react-ui'

import { ThemeContext } from '../../../Context'
import { SecureAxios } from '../../services'
type BrandSelectProps = {
  updateCustomer: (_: string, value: Customer) => void
}
interface Customer {
  id: number
  customer_name: string
}
interface GroupType {
  grouping_category: string
}
const BrandSelect = ({
  updateCustomer,
}: BrandSelectProps): React.JSX.Element => {
  const { customer } = useContext(ThemeContext),
    apiUrl = `${getApiUrlPrefix('iserve')}/api/v3/customers`,
    params = {
      sort: 'customer_name:asc:lowercase',
      filter: {
        customer_state_id: {
          noteql: 3,
        },
      },
    }
  const [selected, setSelected] = useState(customer)
  const { data: customerList, status: customerStatus } = useQuery({
    queryKey: ['customers', apiUrl, params.filter.customer_state_id.noteql],
    queryFn: async ({ signal }) => {
      try {
        const response = await SecureAxios.get(apiUrl, {
          params,
          signal,
        })
        return response.data
      } catch (error) {
        console.error('Error fetching customer list:', error)
        throw error
      }
    },
  })
  return (
    <Select
      labelProps={{ label: 'Brand' }}
      options={[
        {
          label: 'Partners',
          options: customerList?.data?.filter(
            (customer: GroupType) =>
              customer.grouping_category === 'full_partner',
          ) || [{ customer_name: 'No Partners Found' }],
        },
        {
          label: 'Advisory',
          options: customerList?.data?.filter(
            (customer: GroupType) =>
              customer.grouping_category === 'advisory_partner',
          ) || [{ customer_name: 'No Advisory Found' }],
        },
        {
          label: 'Prospect',
          options: customerList?.data?.filter(
            (customer: GroupType) => customer.grouping_category === 'prospect',
          ) || [{ customer_name: 'No Prospects Found' }],
        },
        {
          label: 'Vorys',
          options: customerList?.data?.filter(
            (customer: GroupType) =>
              customer.grouping_category === 'vorys_partner',
          ) || [{ customer_name: 'No Vorys Found' }],
        },
        {
          label: 'Software',
          options: customerList?.data?.filter(
            (customer: GroupType) =>
              customer.grouping_category === 'software_partner',
          ) || [{ customer_name: 'No Software Found' }],
        },
        {
          label: '1P',
          options: customerList?.data?.filter(
            (customer: GroupType) =>
              customer.grouping_category === 'onep_partner',
          ) || [{ customer_name: 'No 1P Partners Found' }],
        },
      ]}
      selectedItem={selected}
      onChange={(option) => {
        if (customerList.data && customerStatus === 'success') {
          setSelected(option)
          updateCustomer('', option)
        }
      }}
      optionKeyName='vendor_id'
      labelKeyName='customer_name'
      loading={!customerList}
    />
  )
}
export default BrandSelect
