import React from 'react'

import styles from './_divider.module.scss'

type DividerProps = {
  mobileHorizontal?: boolean
  className?: string
}

const Divider = ({
  mobileHorizontal,
  className = '',
}: DividerProps): React.JSX.Element => {
  return (
    <div
      className={`${styles.divider} ${
        mobileHorizontal ? styles.mobileHorizontal : styles.vertical
      } ${className}`}
    />
  )
}

export default Divider
