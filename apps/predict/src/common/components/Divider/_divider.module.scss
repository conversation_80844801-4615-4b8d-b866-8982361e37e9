@use '@patterninc/react-ui/dist/variables';

.divider {
  background: var(--light-gray);
}

.vertical {
  width: 1px;
}

.mobileHorizontal {
  @media only screen and (max-width: variables.$breakpoint-sm-max) {
    height: 1px;
    margin: 0 16px;
  }
  @media only screen and (min-width: variables.$breakpoint-md) {
    width: 1px;
    margin: 16px 0;
  }
}

.horizontalDivider {
  height: 1px;
  width: 100%;
  background: var(--light-gray);
}
