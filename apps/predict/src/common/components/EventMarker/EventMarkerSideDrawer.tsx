import React, { useContext, useEffect, useState } from 'react'
import { uniq } from 'lodash'
import moment, { type Moment } from 'moment'
import {
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  DatePicker,
  DatepickerNew,
  FormFooter,
  FormLabel,
  getApiUrlPrefix,
  Icon,
  Picker,
  PopoverNew,
  Select,
  SideDrawer,
  TextInput,
  toast,
  type ToastTypes,
  useIsMounted,
  useToggle,
} from '@patterninc/react-ui'
import { c } from '@predict-services'

import { ThemeContext } from '../../../Context'
import SecureAxios from '../../services/SecureAxios'
import {
  getFilteredMarketplaces,
  getNestedIds,
} from '../GlobalFilter/GlobalFilter.helpers'
import GlobalMarketplaces, {
  type InputHandler,
} from '../GlobalFilter/GlobalMarketplaces'
import styles from './_event-marker.module.scss'
import {
  useIsContentRoute,
  useIsCustomerServiceRoute,
  useIsOrganicTrafficRoute,
  useIsTrafficRoute,
} from '../GlobalFilter/GlobalFilter.hooks'
import { type IndividualBrandGroupData } from '../BrandGroup/BrandGroupTypes'
interface Customer {
  id: number
  customer_name: string
  vendor_id?: number
}

export interface BrandGroupCustomer {
  id: number
  name: string
  brand_data: [Customer]
}

interface EventMarker {
  title: string
  start_date: string
  end_date: string
  description: string | number
  customer_id: number
  event_type: string
  marketplace_ids?: number[]
  visibility: string
}

interface EventMarkerSideDrawerProps {
  customer: Customer
  brandGroupCustomer: BrandGroupCustomer | IndividualBrandGroupData | null
  vendorIds: number[]
  isDrawerOpen: boolean
  setDrawerOpen: React.Dispatch<React.SetStateAction<boolean>>
  refreshOnSave?: () => void
}

const EventMarkerSideDrawer = ({
  customer,
  brandGroupCustomer,
  vendorIds,
  isDrawerOpen,
  setDrawerOpen,
  refreshOnSave,
}: EventMarkerSideDrawerProps): React.JSX.Element => {
  const chinaDataOrganicTrafficChanges = useToggle(
    'china_data_organic_traffic_changes',
  )
  const replaceOldDatepicker = useToggle('replace_old_datepicker_in_predict')
  const isTrafficRoute = useIsTrafficRoute()
  const isOrganicTrafficRoute = useIsOrganicTrafficRoute()
  const isContentRoute = useIsContentRoute()
  const isCustomerServiceRoute = useIsCustomerServiceRoute()
  const enableChinaForOrganicTraffic = chinaDataOrganicTrafficChanges
    ? true
    : !isOrganicTrafficRoute
  const enableSpecificChineseMarketplaces =
    (isTrafficRoute && enableChinaForOrganicTraffic) ||
    isContentRoute ||
    isCustomerServiceRoute

  const isMounted = useIsMounted()
  const {
    allMarketplaceIds,
    allMarketplaces,
    allRegions,
    regions: selectedRegions,
  } = useContext(ThemeContext)
  const marketplacesPopoverRef = React.useRef<HTMLDivElement>(null)
  const rawMarketplaces = getNestedIds(allMarketplaces)
  const newFilteredMarketplaces = getFilteredMarketplaces({
    enableSpecificChineseMarketplaces,
    allMarketplaces,
    allRegions,
    selectedRegions,
    rawMarketplaces,
  })
  const [selectedMarkets, setSelectedMarkets] = useState<Array<number>>(
    uniq([0, ...allMarketplaceIds]),
  )

  const [isDatepickerMounted, setDatepickerMounted] = useState(true)

  const toastCreator = (
    message: string,
    type: ToastTypes,
    autoClose: number | false,
  ) => {
    const toastId = `${newEventMarker.title}_new_event_toast`

    toast({
      message: message,
      type: type,
      config: {
        toastId,
        autoClose: autoClose,
      },
    })
  }

  const eventTypes = [
    { text: c('accountHealth') },
    { text: c('buyBoxLoss') },
    { text: c('changeInAdvertising') },
    { text: c('contentUpload') },
    { text: c('milestone') },
    { text: c('miscellaneous') },
    { text: c('newProductLaunch') },
    { text: c('outOfStock') },
    { text: c('priceChanges') },
    { text: c('promotion') },
    { text: c('retailHoliday') },
    { text: c('suppression') },
  ]

  const visibilityOptions = [
    {
      id: 0,
      text: c('internal'),
      value: 'Internal',
    },
    {
      id: 1,
      text: c('allUsers'),
      value: 'All Users',
    },
  ]

  const eventMarkerDefaults: EventMarker = {
    title: '',
    start_date: '',
    end_date: '',
    description: '',
    customer_id: customer.id,
    event_type: '',
    visibility: 'Internal',
  }

  const [newEventMarker, setNewEventMarker] = useState<EventMarker>({
    ...eventMarkerDefaults,
  })

  const createNewEventMarkerApi = `${getApiUrlPrefix(
    'iserve',
  )}/api/v6/event_markers/new`

  const handleCreateNewEventButton = () => {
    if (
      newEventMarker.title.length === 0 ||
      newEventMarker.start_date.length === 0 ||
      newEventMarker.end_date.length === 0 ||
      newEventMarker.event_type.length === 0
    ) {
      return toastCreator(c('pleaseFillOutTheRequiredFields'), 'error', 5000)
    } else if (selectedMarkets.length === 0) {
      return toastCreator(c('pleaseSelectAtLeastOneMarketplace'), 'error', 5000)
    } else if (
      moment(newEventMarker.end_date) < moment(newEventMarker.start_date)
    ) {
      return toastCreator(
        c('theEndDateCannotComeBeforeTheStartDate'),
        'error',
        5000,
      )
    } else {
      const marketplaceIds = selectedMarkets.includes(0)
        ? customer.id === 0
          ? undefined
          : allMarketplaceIds
        : Array.from(selectedMarkets)
      SecureAxios.post(createNewEventMarkerApi, newEventMarker, {
        params: {
          customer_id: newEventMarker.customer_id,
          vendor_ids: vendorIds,
          marketplace_ids: marketplaceIds,
        },
      })
        .then(() => {
          toastCreator(c('eventCreated'), 'success', 5000)
          setSelectedMarkets(uniq([0, ...allMarketplaceIds]))
          setNewEventMarker(eventMarkerDefaults)
          setDatepickerMounted(false)
          setDrawerOpen(false)
          refreshOnSave?.()
        })
        .catch((error) => {
          toastCreator(
            c('failedCreateEventMessage', {
              message: error?.data?.message ?? error?.response?.data?.message,
            }),
            'error',
            5000,
          )
        })
    }
  }

  useEffect(() => {
    setSelectedMarkets([0, ...allMarketplaceIds])
  }, [allMarketplaceIds])

  const marketplaceHandler = (
    type: string,
    checkboxValue: boolean,
    value: { id: number; subMarketplaces: { id: number }[] },
  ) => {
    if (isMounted()) {
      if (type === 'all') {
        if (checkboxValue) {
          setSelectedMarkets([0, ...allMarketplaceIds])
        } else {
          setSelectedMarkets([])
        }
      } else {
        const currentSelection = new Set(selectedMarkets)
        if (selectedMarkets.includes(0)) {
          currentSelection.delete(0)
        }
        if (value.id) {
          currentSelection[checkboxValue ? 'add' : 'delete'](value.id)
        } else {
          value.subMarketplaces.forEach((subMarket) => {
            currentSelection[checkboxValue ? 'add' : 'delete'](subMarket.id)
          })
        }
        const selectedMarketplaces = [...currentSelection]
        setSelectedMarkets(selectedMarketplaces)
      }
    }
  }

  const brandsAlert = () => {
    let brandsAlert = c('eventMarkerWillApplyTo')
    if (brandGroupCustomer !== null) {
      brandsAlert += c('theFollowingBrands')
      brandsAlert += brandGroupCustomer?.brand_data
        ?.filter((customer) => customer.vendor_id)
        .map((customer) => customer.customer_name)
        .sort()
        .join(', ')
    } else if (customer.id === 0) {
      brandsAlert += c('allTheBrands')
    } else {
      return null
    }
    brandsAlert += '.'

    return (
      <div className='pat-mb-4'>
        <Alert type='info' text={brandsAlert} customClass='pat-mb-5' />
      </div>
    )
  }

  // Below useEffect is used to clear start date and end date on the drawer outside click
  useEffect(() => {
    if (!isDrawerOpen && isDatepickerMounted) {
      setDatepickerMounted(false)
      setNewEventMarker((prevState) => ({
        ...prevState,
        start_date: '',
        end_date: '',
      }))
    }
    if (isDrawerOpen && !isDatepickerMounted) {
      setDatepickerMounted(true)
    }
  }, [isDrawerOpen, isDatepickerMounted])

  return (
    <SideDrawer
      isOpen={isDrawerOpen}
      closeCallout={() => setDrawerOpen(false)}
      headerContent={c('createNewEventMarker')}
      footerContent={
        <FormFooter
          cancelButtonProps={{
            onClick: () => {
              setDrawerOpen(false)
              setDatepickerMounted(false)
              setNewEventMarker((prevState) => ({
                ...prevState,
                start_date: '',
                end_date: '',
              }))
            },
          }}
          saveButtonProps={{
            onClick: handleCreateNewEventButton,
            children: c('createEventMarker'),
          }}
        />
      }
    >
      <div>
        {brandsAlert()}
        <div className='pat-mb-4'>
          <TextInput
            labelText={c('eventName')}
            value={newEventMarker.title}
            placeholder={c('maximum30Characters')}
            maxLength={30}
            callout={(_, value) =>
              isMounted() &&
              setNewEventMarker((prevState) => ({
                ...prevState,
                title: value.toString(),
              }))
            }
            type='text'
            stateName='eventName'
            required={true}
            noEmpty={true}
          />
        </div>
        <div className='pat-mb-4'>
          <label className='flex fs-12 pat-mb-2 fw-regular fc-purple'>
            {c('startDate')}
            <span className='required-asterisk'>*</span>
          </label>
          {isDatepickerMounted &&
            (replaceOldDatepicker ? (
              <DatePicker
                dataTestId='single-datepicker-New-Event-Marker-Start-Date'
                type='single'
                onDateChange={(date) => {
                  if (isMounted() && date) {
                    setNewEventMarker((prevState) => ({
                      ...prevState,
                      start_date: moment(date as Date)
                        .local()
                        .toString(),
                    }))
                  }
                }}
                selectedDate={
                  newEventMarker.start_date
                    ? moment(newEventMarker.start_date).toDate()
                    : undefined
                }
              />
            ) : (
              <DatepickerNew
                customId='New-Event-Marker-Start-Date'
                isSingle
                manualInput
                onDateChange={(date: Moment | null) => {
                  isMounted() &&
                    date &&
                    setNewEventMarker((prevState) => ({
                      ...prevState,
                      start_date: date.local().toString(),
                    }))
                }}
                placeholder='MM/DD/YYYY'
                showAllDates
                startDate={
                  newEventMarker.start_date
                    ? moment(newEventMarker.start_date)
                    : null
                }
                reset={false}
              />
            ))}
        </div>
        <div className='pat-mb-4'>
          <label className='flex fs-12 pat-mb-2 fw-regular fc-purple'>
            {c('endDate')}
            <span className='required-asterisk'>*</span>
          </label>
          {isDatepickerMounted &&
            (replaceOldDatepicker ? (
              <DatePicker
                dataTestId='single-datepicker-New-Event-Marker-End-Date'
                type='single'
                onDateChange={(date) => {
                  if (isMounted() && date) {
                    setNewEventMarker((prevState) => ({
                      ...prevState,
                      end_date: moment(date as Date)
                        .local()
                        .toString(),
                    }))
                  }
                }}
                selectedDate={
                  newEventMarker.end_date
                    ? moment(newEventMarker.end_date).toDate()
                    : undefined
                }
              />
            ) : (
              <DatepickerNew
                customId='New-Event-Marker-End-Date'
                isSingle
                manualInput
                onDateChange={(date: Moment | null) =>
                  isMounted() &&
                  date &&
                  moment(date).isValid() &&
                  setNewEventMarker((prevState) => ({
                    ...prevState,
                    end_date: date.local().toString(),
                  }))
                }
                placeholder='MM/DD/YYYY'
                showAllDates
                startDate={
                  newEventMarker.end_date
                    ? moment(newEventMarker.end_date)
                    : null
                }
              />
            ))}
        </div>
        <div className='pat-mb-4'>
          <TextInput
            labelText={c('eventDescription')}
            placeholder={c('maximum140Characters')}
            maxLength={140}
            value={newEventMarker.description}
            callout={(_, value) =>
              isMounted() &&
              setNewEventMarker((prevState) => ({
                ...prevState,
                description: value,
              }))
            }
            type='text'
            stateName='eventDescription'
          />
        </div>
        <div className='pat-mb-4'>
          {isDatepickerMounted && (
            <Select
              labelProps={{ label: c('eventType') }}
              options={eventTypes}
              optionKeyName={'text'}
              labelKeyName={'text'}
              required
              selectedItem={{
                text:
                  newEventMarker.event_type.length === 0
                    ? c('select')
                    : newEventMarker.event_type,
              }}
              onChange={(value) => {
                if (isMounted() && value) {
                  setNewEventMarker((prevState) => ({
                    ...prevState,
                    event_type: value.text,
                  }))
                }
              }}
            />
          )}
        </div>
        <div className='pat-mb-4'>
          <div
            className='flex flex-direction-column'
            ref={marketplacesPopoverRef}
          >
            <FormLabel label={c('marketplaces')} required />

            <PopoverNew
              popoverContent={
                <div
                  className={styles.marketplacesPopoverContent}
                  style={{
                    width:
                      (marketplacesPopoverRef.current?.clientWidth ?? 0) - 32, // 32 is to offset the padding
                  }}
                >
                  <GlobalMarketplaces
                    marketplaces={newFilteredMarketplaces}
                    selectedMarkets={selectedMarkets}
                    inputHandler={marketplaceHandler as InputHandler}
                    isSelectiveDistribution={false}
                    disableNonAmazonMarketplaces={false}
                    disableNonAmazonAndNonWalMarketplaces={false}
                    isKeyWordRankingRoute={false}
                    isProductRankingsRoute={false}
                    setToggleState={null}
                  />
                </div>
              }
              noPadding
              position={'top'}
              className={styles.marketplacesPopover}
            >
              {({ setVisible, visible }) => (
                <Button
                  as='unstyled'
                  className={styles.marketplacesPopoverButton}
                  onClick={() => setVisible(true)}
                >
                  {selectedMarkets.includes(0) ? 'All Marketplaces' : 'Select'}

                  <Icon
                    icon='carat'
                    iconSize='8px'
                    className={`${styles.marketplacesPopoverIcon} ${
                      visible ? styles.marketplacesPopoverIconRotate : ''
                    }`}
                  />
                </Button>
              )}
            </PopoverNew>
          </div>
        </div>
        <div>
          <Picker
            labelText={c('visibility')}
            required={true}
            options={visibilityOptions}
            state={newEventMarker}
            stateName='visibility'
            callout={(_, value) =>
              setNewEventMarker((prevState) => ({
                ...prevState,
                visibility: value,
              }))
            }
            customClass='every-options full-width'
          />
        </div>
      </div>
    </SideDrawer>
  )
}

export default EventMarkerSideDrawer
