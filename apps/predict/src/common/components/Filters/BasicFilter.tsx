import React, {
  type Dispatch,
  useEffect,
  useMemo,
  useReducer,
  useRef,
  useState,
} from 'react'
import { Filter } from '@patterninc/react-ui'
import { c, reducer, useTranslate } from '@predict-services'
import { type ActionTypes } from 'src/modules/Protect/components/Pages/BuyBox/BuyBoxBrands/BuyBoxBrandsTypes'
import { type Customer } from '@predict-types'
import { useQuery } from '@tanstack/react-query'
import {
  getManufacturers,
  type ManufacturersType,
} from 'src/modules/Protect/components/Pages/Sellers/components/Pages/Sellers/SellersFilterHelpers'

import { createFilterCount } from '../../services/FilterHelperService'

export type Action = {
  type: 'UPDATE_FILTER' | 'RESET_FILTER'
  payload?: Record<string, unknown>
}
interface BasicFilterProps {
  customer: Customer
  filters: Record<string, FilterValue>
  contextDispatch: Dispatch<Action>
  caller: string
}

interface BaseFilter {
  status: {
    id: number
    name: string
    value: string
  }
  on_map: {
    id: number
    on_map: string
    value: string
  }
  manufacturer: {
    id: number
    name: string
  }
  marketplace_id?: {
    id: number
    marketplace_name: string
  }
}

export interface FilterValue {
  id: number
  name?: string
  value?: string | boolean
  on_map?: string
  state?: string
  marketplace_name?: string
  [key: string]: unknown
}

interface FilterState {
  [key: string]: FilterValue
}

interface FilterAction {
  type: string
  payload?: Record<string, FilterValue>
}

interface FilterOption {
  type: ActionTypes
  defaultValue: FilterValue
  options: Array<FilterValue | ManufacturersType>
  stateName: string
  optionKeyName: string
  labelText: string
}

const BasicFilter: React.FC<BasicFilterProps> = ({
  customer,
  filters,
  contextDispatch,
  caller,
}) => {
  const { t } = useTranslate('protect')
  const isCancelled = useRef(false)

  const originalFilter = useMemo(() => {
    const baseFilter: BaseFilter = {
      status: {
        id: 0,
        name: c('activeAndInactive'),
        value: 'all',
      },
      on_map: {
        id: 0,
        on_map: t('onAndOffMap'),
        value: 'all',
      },
      manufacturer: {
        id: 0,
        name: c('allBrands'),
      },
    }

    if (
      filters &&
      Object.prototype.hasOwnProperty.call(filters, 'marketplace_id')
    ) {
      baseFilter['marketplace_id'] = {
        id: 0,
        marketplace_name: 'All Marketplaces',
      }
    }

    return baseFilter
  }, [t, filters])

  const [filterStateCopy, dispatch]: [
    FilterState,
    React.Dispatch<FilterAction>,
  ] = useReducer(
    reducer as (
      filterStateCopy: FilterState,
      dispatch: FilterAction,
    ) => FilterState,
    filters,
  )

  const [manufacturers, setManufacturers] = useState<ManufacturersType[]>([])
  const [filterCount, setFilterCount] = useState<number | null>(null)

  const updateFilter = (
    values: Record<string, { defaultValue: FilterValue }>,
  ) => {
    const filterValues: Record<string, FilterValue> = {}
    let filterCount = 0
    for (const key in values) {
      filterValues[key] = values[key].defaultValue
      if (values[key].defaultValue.id !== 0) {
        filterCount += 1
      }
    }
    if (filterCount) {
      setFilterCount(filterCount)
    }
    if (!isCancelled.current) {
      contextDispatch({
        type: 'UPDATE_FILTER',
        payload: { filters: filterValues },
      })
    }
  }

  const resetCallout = () => {
    if (
      (!isCancelled.current && localStorage.getItem('overviewFilter')) ||
      localStorage.getItem('complianceFilter') ||
      localStorage.getItem('sd_brand_filter')
    ) {
      setFilterCount(null)
      contextDispatch({
        type: 'RESET_FILTER',
      })
    } else {
      cancelCallout()
    }
  }

  const cancelCallout = () => {
    if (!isCancelled.current) {
      dispatch({
        type: 'UPDATE',
        payload: filters,
      })
    }
  }

  const updateSelect = (...params: unknown[]) => {
    const [stateAttr, value] = params as [string, FilterValue]
    if (value && !isCancelled.current) {
      dispatch({
        type: 'UPDATE',
        payload: {
          ...filterStateCopy,
          [stateAttr]: value,
        },
      })
    }
  }

  const filter: Record<string, FilterOption> = {}

  if (
    filters &&
    Object.prototype.hasOwnProperty.call(filters, 'manufacturer')
  ) {
    filter['manufacturer'] = {
      type: 'select' as ActionTypes,
      defaultValue: filterStateCopy.manufacturer,
      options: manufacturers,
      stateName: 'manufacturer',
      optionKeyName: 'name',
      labelText: c('brand'),
    }
  }

  if (filters && Object.prototype.hasOwnProperty.call(filters, 'on_map')) {
    filter['on_map'] = {
      type: 'select' as ActionTypes,
      defaultValue: filterStateCopy.on_map,
      options: [
        {
          id: 0,
          on_map: t('onAndOffMap'),
          value: 'all',
        },
        {
          id: 1,
          on_map: t('onMap'),
          value: true,
        },
        {
          id: 2,
          on_map: t('offMap'),
          value: false,
        },
      ],
      stateName: 'on_map',
      optionKeyName: 'on_map',
      labelText: t('mapPolicy'),
    }
  }

  if (filters && Object.prototype.hasOwnProperty.call(filters, 'status')) {
    filter['status'] = {
      type: 'select' as ActionTypes,
      defaultValue: filterStateCopy.status,
      options: [
        {
          id: 0,
          state: c('activeAndInactive'),
          value: 'all',
        },
        {
          id: 1,
          state: c('active'),
          value: true,
        },
        {
          id: 2,
          state: c('inactive'),
          value: false,
        },
      ],
      stateName: 'status',
      optionKeyName: 'state',
      labelText: c('status'),
    }
  }

  useEffect(() => {
    if (!isCancelled.current) {
      setFilterCount(null)
      contextDispatch({
        type: 'RESET_FILTER',
      })
    }
  }, [contextDispatch, customer.id])

  // API for fetching manufacturers
  useQuery({
    queryKey: [`${caller}-manufacturers-api`, customer.id],
    queryFn: ({ signal }) =>
      getManufacturers({ customerId: customer.id, setManufacturers, signal }),
    enabled:
      !!customer.id &&
      filters &&
      Object.prototype.hasOwnProperty.call(filters, 'manufacturer') &&
      !isCancelled.current,
  })

  useEffect(() => {
    if (!isCancelled.current) {
      dispatch({
        type: 'UPDATE',
        payload: {
          ...filters,
        },
      })
    }
  }, [caller, customer.id, filters])

  useEffect(() => {
    isCancelled.current = false
    const count = createFilterCount(
      filters,
      originalFilter as unknown as Record<string, unknown>,
    )
    setFilterCount(count)
  }, [filters, originalFilter])

  useEffect(() => {
    return () => {
      isCancelled.current = true
    }
  }, [])

  return (
    <Filter
      // TODO: Replace any with the correct type
      filterStates={filter as Record<string, any>}
      filterCallout={(selected) => {
        const typedSelected = selected as Record<
          string,
          { defaultValue: FilterValue }
        >
        updateFilter(typedSelected)
      }}
      resetCallout={resetCallout}
      onChangeCallout={updateSelect}
      cancelCallout={cancelCallout}
      appliedFilters={filterCount}
    />
  )
}

export default BasicFilter
