import { useCallback, useEffect } from 'react'
import * as FullStory from '@fullstory/browser'
import { getEnvironmentName } from '@patterninc/react-ui'
import { useUser } from 'src/context/user-context'

const FS = {
  event: (eventName: string, properties: { [key: string]: unknown }) => {
    if (FullStory.isInitialized()) {
      FullStory.event(eventName, properties)
    }
  },
}

export default FS

export const useFullStoryInit = () => {
  const { user } = useUser()

  const getBrandAssignmentsByUser = useCallback(
    (key: string) => {
      return [
        ...new Set(
          (user?.brand_assignment_details as Record<string, string>[])?.reduce(
            (acc: string[], item: Record<string, string>) => {
              if (item[key] !== null) acc.push(item[key])
              return acc
            },
            [],
          ),
        ),
      ]
    },
    [user?.brand_assignment_details],
  )

  useEffect(() => {
    if (getEnvironmentName() === 'production') {
      FullStory.init({ orgId: '14RSSA', namespace: 'FS' })

      FullStory.isInitialized() &&
        FullStory.setUserVars({
          uid: user?.id,
          displayName: `${user?.first_name} ${user?.last_name}`,
          roles: getBrandAssignmentsByUser('role'),
          departments: getBrandAssignmentsByUser('department'),
          regions: getBrandAssignmentsByUser('region'),
          director_of_department: getBrandAssignmentsByUser(
            'director_of_department',
          ),
          group_id: user?.group_id,
          group_name: user?.group_name,
          user_type: user?.type,
        })
    }
  }, [
    getBrandAssignmentsByUser,
    user?.first_name,
    user?.group_id,
    user?.group_name,
    user?.id,
    user?.last_name,
    user?.type,
  ])
}
