import React from 'react'
import { <PERSON><PERSON>abe<PERSON>, Picker } from '@patterninc/react-ui'

import { useTranslate } from '../../../common/services'
import { type ChurnProps } from './globalFilterTypes'

type OptionSelectedProps = {
  value: string
}

type ChurnToggleProps = {
  optionSelected: OptionSelectedProps | ChurnProps
  updateChurnSelection?: (churn: ChurnProps) => void
}

const churnOptions = [
  {
    id: 0,
    text: 'Exclude',
    value: 'off',
  },
  {
    id: 1,
    text: 'Include',
    value: 'on',
  },
]

const ChurnToggle = ({
  optionSelected,
  updateChurnSelection,
}: ChurnToggleProps): React.JSX.Element => {
  const { t } = useTranslate('insights')

  return (
    <div className='pat-mt-2.5'>
      <FormLabel
        label={t('churn')}
        tooltip={{
          tooltipContent: t('churnGlobalFilterTooltip'),
          position: 'top',
        }}
      />
      <div
        style={{
          marginTop: '6px',
        }}
      >
        <Picker
          options={churnOptions}
          state={optionSelected}
          stateName='value'
          callout={(_, value: string) => {
            const selection = churnOptions.find(
              (churn) => churn.value === value,
            ) as ChurnProps
            updateChurnSelection?.(selection)
          }}
        />
      </div>
    </div>
  )
}

export default ChurnToggle
