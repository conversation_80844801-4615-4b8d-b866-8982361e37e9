import React from 'react'
import { Picker } from '@patterninc/react-ui'

type OptionSelectedProps = {
  value: string
}

type ComparisonPeriodToggleProps = {
  optionSelected: OptionSelectedProps
  setOptionSelected: (option: OptionSelectedProps) => void
  disableCompareWith?: boolean
}

const comparisonPeriodOptions = [
  {
    id: 0,
    text: 'Previous Period',
    value: 'previous_period',
  },
  {
    id: 1,
    text: 'Previous Year',
    value: 'previous_year',
  },
]

const ComparisonPeriodToggle = ({
  optionSelected,
  setOptionSelected,
  disableCompareWith = false,
}: ComparisonPeriodToggleProps): React.JSX.Element => {
  return (
    <div className='pat-mt-2.5 pat-mb-2'>
      <div className='flex'>
        <span className='fw-bold fs-12 pat-mr-2'>Compare with:</span>
      </div>
      <div
        style={{
          maxWidth: '100%',
          marginTop: '6px',
        }}
      >
        <Picker
          options={comparisonPeriodOptions}
          state={optionSelected}
          stateName='value'
          callout={(_, value: string) => {
            const optionSelected = comparisonPeriodOptions.find(
              (option) => option.value === value,
            )
            optionSelected && setOptionSelected(optionSelected)
          }}
          disabled={disableCompareWith}
        />
      </div>
    </div>
  )
}

export default ComparisonPeriodToggle
