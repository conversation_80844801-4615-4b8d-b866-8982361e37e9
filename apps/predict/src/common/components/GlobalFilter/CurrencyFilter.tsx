import React, { useEffect } from 'react'
import { capitalize } from '@patterninc/react-ui'

import { getCurrencies } from '../../services/CurrenciesService'
import { handleError } from '../../services/HandleError'
import GlobalCurrency from './GlobalCurrency'
import type { Currency, CurrencyFilter } from './globalFilterTypes'

type CurrencyFilterProps = {
  displayState: CurrencyFilter
  updateCurrency: (currencyData: CurrencyFilter) => void
}

const CurrencyFilterContainer = ({
  displayState,
  updateCurrency,
}: CurrencyFilterProps) => {
  const updateCurrentCurrency = (currency: Currency) => {
    updateCurrency({
      ...displayState,
      currentCurrency: currency,
    })
  }

  useEffect(() => {
    const apiController = new AbortController()
    const currencyLabel = (currency: Currency) => {
      const currencyName = currency?.name
        .split(' ')
        .map((str) => capitalize(str))
        .join(' ')
      return { label: `${currency.symbol} — ${currencyName}` }
    }
    const needToFetchCurrencies =
      displayState.allCurrencies.length === 0 || !displayState.currentCurrency

    needToFetchCurrencies &&
      getCurrencies(apiController.signal)
        .then((response) => {
          let formattedCurrencies: Currency[] = []
          if (response) {
            formattedCurrencies = response.data.map((currency: Currency) => {
              return {
                ...currency,
                type: 'Currency',
                ...currencyLabel(currency),
              }
            })
            const updatedCurrentCurrency = {
              ...response.current_currency,
              ...currencyLabel(response.current_currency),
            }

            updateCurrency({
              currentCurrency:
                displayState.currentCurrency ?? updatedCurrentCurrency,
              allCurrencies: formattedCurrencies,
            })
          }
        })
        .catch((error) => {
          handleError(error, 'CurrencyFilter.tsx', 'getCurrencies')
        })
    return () => {
      apiController.abort()
    }
  }, [
    displayState.allCurrencies.length,
    displayState.currentCurrency,
    updateCurrency,
  ])

  return (
    <div className='global-filter filters-container'>
      <GlobalCurrency
        currentCurrency={displayState.currentCurrency}
        currencies={displayState.allCurrencies}
        updateSelectedCurrency={updateCurrentCurrency}
      />
    </div>
  )
}

export default CurrencyFilterContainer
