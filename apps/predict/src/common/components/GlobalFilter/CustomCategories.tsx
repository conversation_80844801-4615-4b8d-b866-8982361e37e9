import React, { useEffect, useState } from 'react'
import { matchPath, useNavigate } from 'react-router-dom'
import {
  Alert,
  Checkbox,
  EmptyState,
  type IconStringList,
  SearchBar,
} from '@patterninc/react-ui'
import type { CategoriesFilter, CheckboxCategory } from '@predict-types'
import { haveRoles } from '@predict-services'

import { useUser } from '../../../context/user-context'
import styles from './_global-marketplaces.module.scss'
import {
  copyOf,
  insightsReportRoutes,
  insightsVendorAnalyticsRoute,
} from './GlobalFilter.helpers'
import { useFetchCustomCategories } from './GlobalFilter.hooks'

type CustomCategoriesProps = {
  categoriesData: CategoriesFilter
  closeFilterSideDrawer?: () => void
  setCustomCategories: (categoriesData: CategoriesFilter) => void
  setDisableApplyButton?: React.Dispatch<React.SetStateAction<boolean>>
}

const CustomCategories = ({
  categoriesData,
  closeFilterSideDrawer,
  setCustomCategories,
  setDisableApplyButton,
}: CustomCategoriesProps) => {
  const { loading, allCategories } = useFetchCustomCategories()
  const navigate = useNavigate()
  /** fullCategoryList is the list of all the categories with the current checkbox state of each category */
  const fullCategoryList = categoriesData.fullCategoryList
  const [hasCategories, setHasCategories] = useState<boolean>(false)
  const [searchTerm, setSearchTerm] = useState<string>('')
  const [filteredCategories, setFilteredCategories] = useState<
    CheckboxCategory[]
  >(categoriesData.categories)
  const searchBarRef = React.useRef<HTMLInputElement>(null)
  const searchBarTimeout =
    React.useRef<ReturnType<typeof setTimeout>>(undefined)
  const { user } = useUser()
  const hasCustomCategoriesPermission = haveRoles(user, [
    'write_products', // current permission setting for /settings/overview/products parent page
  ])

  const setUpCustomCategories = {
    secondaryText: 'Click the button below to setup Custom Categories',
    icon: 'info' as IconStringList,
    buttonProps: {
      children: 'Setup Custom Categories',
      onClick: () => {
        closeFilterSideDrawer?.()
        navigate('/settings/overview/products/customcategories')
      },
    },
  }

  useEffect(() => {
    setDisableApplyButton?.(!areAnyCategoriesSelected(fullCategoryList))
  }, [fullCategoryList, setDisableApplyButton])

  useEffect(() => {
    if (searchBarRef.current) {
      searchBarTimeout.current = setTimeout(() => {
        searchBarRef.current?.focus()
      }, 250)
    }
    return () => {
      searchBarTimeout.current = undefined
    }
  })

  const handleSearchTerm = (searchTerm: string) => {
    const fullList = copyOf(fullCategoryList)
    const mergedCategories = mergeCategories(fullList, filteredCategories)
    const searchFilteredCategories = filterBySearchTerm(
      searchTerm,
      mergedCategories,
    )

    verifyParentsOfSelectedCategories(searchFilteredCategories)
    setFilteredCategories(searchFilteredCategories)
    setSearchTerm(searchTerm)
    setCustomCategories({
      fullCategoryList: mergedCategories,
      categories: searchFilteredCategories,
      searchTerm,
    })
  }

  const handleSelect = (category: CheckboxCategory) => {
    const filteredCategoriesCopy = copyOf(filteredCategories)
    const updatedSelectedCategories = updateCheckedCategory(
      filteredCategoriesCopy,
      filteredCategoriesCopy,
      category.id,
      !category.checked,
    )
    const fullList = copyOf(fullCategoryList)
    const updatedFullCategoryList = mergeCategories(
      fullList,
      updatedSelectedCategories,
    )
    setCustomCategories({
      fullCategoryList: updatedFullCategoryList,
      categories: updatedSelectedCategories,
      searchTerm,
    })
  }

  useEffect(() => {
    const allCategoriesCopy = copyOf(allCategories)
    const customCategories =
      categoriesData?.categories?.length || searchTerm
        ? categoriesData.categories
        : // if there are no categories and no search term, use all categories as the default
          allCategoriesCopy

    verifyParentsOfSelectedCategories(customCategories)
    setHasCategories(customCategories?.[0]?.children.length > 0)

    setFilteredCategories(searchTerm ? customCategories : fullCategoryList)

    // only set searchTerm if searchTerm changes due to reset
    if (searchTerm !== categoriesData.searchTerm) {
      setSearchTerm(categoriesData.searchTerm)
    }

    // initial loading will not have fullCategoryList data
    // so default to allCategories
    if (!fullCategoryList) {
      setCustomCategories({
        fullCategoryList: allCategoriesCopy,
        categories: customCategories,
        searchTerm,
      })
    }
  }, [
    allCategories,
    categoriesData.categories,
    categoriesData.searchTerm,
    fullCategoryList,
    searchTerm,
    setCustomCategories,
  ])

  /////////////////////////////////////////////////////////////////////////////////////////////////
  // DISPLAY CATEGORY FILTER
  /////////////////////////////////////////////////////////////////////////////////////////////////

  return (
    <div className={styles.categoriesFilterContainer}>
      <label className='fw-semi-bold fc-purple pat-mb-2'>Categories</label>
      {!areAnyCategoriesSelected(fullCategoryList) && (
        <Alert
          type='error'
          text='Please select at least one category'
          customClass='pat-mb-2'
        />
      )}
      {(hasCategories || (!hasCategories && searchTerm)) && (
        <div className={styles.searchBarContainer}>
          <SearchBar
            value={searchTerm}
            onChange={handleSearchTerm}
            ref={searchBarRef}
          />
        </div>
      )}
      {!hasCategories && !loading && (
        <EmptyState
          primaryText='No Custom Categories Exist'
          {...(hasCustomCategoriesPermission ? setUpCustomCategories : {})}
        />
      )}
      {hasCategories && !loading && (
        <div className={styles.globalMarketplaces}>
          {filteredCategories?.map((FirstLevelMainCategory) => (
            <React.Fragment key={FirstLevelMainCategory.id}>
              <CheckboxGroup
                key={FirstLevelMainCategory.id}
                category={FirstLevelMainCategory}
                level={0}
                handleSelect={handleSelect}
              />
              {FirstLevelMainCategory?.children?.map(
                (secondLevelSubCategory: CheckboxCategory) => (
                  <React.Fragment key={secondLevelSubCategory.id}>
                    <CheckboxGroup
                      key={secondLevelSubCategory.id}
                      category={secondLevelSubCategory}
                      level={1}
                      handleSelect={handleSelect}
                    />
                    {secondLevelSubCategory?.children?.map(
                      (thirdLevelSubCategory: CheckboxCategory) => (
                        <React.Fragment key={thirdLevelSubCategory.id}>
                          <CheckboxGroup
                            key={thirdLevelSubCategory.id}
                            category={thirdLevelSubCategory}
                            level={2}
                            handleSelect={handleSelect}
                          />
                          {thirdLevelSubCategory?.children?.map(
                            (fourthLevelSubCategory: CheckboxCategory) => (
                              <React.Fragment key={fourthLevelSubCategory.id}>
                                <CheckboxGroup
                                  key={fourthLevelSubCategory.id}
                                  category={fourthLevelSubCategory}
                                  level={3}
                                  handleSelect={handleSelect}
                                />
                                {fourthLevelSubCategory?.children?.map(
                                  (fifthLevelSubCategory: CheckboxCategory) => {
                                    return (
                                      <CheckboxGroup
                                        key={fifthLevelSubCategory.id}
                                        category={fifthLevelSubCategory}
                                        level={4}
                                        handleSelect={handleSelect}
                                      />
                                    )
                                  },
                                )}
                              </React.Fragment>
                            ),
                          )}
                        </React.Fragment>
                      ),
                    )}
                  </React.Fragment>
                ),
              )}
            </React.Fragment>
          ))}
        </div>
      )}
    </div>
  )
}

export default CustomCategories

///////////////////////////////////////////////////////////////////////////////////////////////////
// HELPER FUNCTIONS
///////////////////////////////////////////////////////////////////////////////////////////////////

const mergeCategories = (
  allCategoriesCopy: CheckboxCategory[],
  currentStateCategories: CheckboxCategory[],
) => {
  allCategoriesCopy.forEach((baseItem) => {
    // Find matching item in update array
    const updateCategoryItem = currentStateCategories.find(
      (updateItem) => updateItem.id === baseItem.id,
    )

    // Update 'checked' state of matching category item
    if (updateCategoryItem) {
      baseItem.checked = updateCategoryItem.checked
    }

    // Recurse through children
    if (Array.isArray(baseItem.children)) {
      mergeCategories(
        baseItem.children,
        updateCategoryItem ? updateCategoryItem.children || [] : [],
      )
    }
  })
  return allCategoriesCopy
}

const filterBySearchTerm = (
  searchTerm: string,
  categories: CheckboxCategory[],
) => {
  const results: CheckboxCategory[] = []

  categories.forEach((item) => {
    // Create a shallow copy to avoid mutating the original array
    const newItem: CheckboxCategory = { ...item }
    const hasFilterTerm = newItem.name
      .toLowerCase()
      .includes(searchTerm.toLowerCase())

    newItem.children = hasFilterTerm
      ? newItem.children
      : filterBySearchTerm(searchTerm, newItem.children)

    if (hasFilterTerm || newItem.children.length > 0) {
      results.push(newItem)
    }
  })

  return results
}

const CheckboxGroup = ({
  category,
  level,
  handleSelect,
}: {
  category: CheckboxCategory
  level: number
  handleSelect?: (category: CheckboxCategory) => void
}) => (
  <div
    key={category.id}
    // ml & pl values are used to match the 20px indent values found in the global marketplaces styles
    className={`flex pat-my-2.5 ml-${(level - 1) * 16} pl-${level * 4}`}
  >
    <Checkbox
      label={category.name}
      checked={category?.checked}
      callout={() => {
        handleSelect?.(category)
      }}
      radioSize='12'
    />
  </div>
)

const findCategoryById = (id: number, categoryList: CheckboxCategory[]) => {
  let queue = [...categoryList]

  while (queue.length) {
    const category = queue.shift()
    if (category?.id === id) {
      return category
    }
    if (category?.children) {
      queue = [...queue, ...category.children]
    }
  }

  return null
}

export const findAllCheckedCategories = (categoryList: CheckboxCategory[]) => {
  let queue = [...categoryList]
  const checkedCategories: CheckboxCategory[] = []

  while (queue.length) {
    const category = queue.shift()
    if (category?.checked) {
      checkedCategories.push(category)
    }
    if (category?.children) {
      queue = [...queue, ...category.children]
    }
  }

  return checkedCategories
}

const updateCheckedCategory = (
  fullCategoryList: CheckboxCategory[],
  subCategories: CheckboxCategory[],
  categoryId: number,
  checkedState: boolean,
): CheckboxCategory[] => {
  const category = findCategoryById(categoryId, subCategories)
  if (!category) return subCategories

  // toggle selected category state
  category.checked = checkedState

  // if category has children, toggle their state
  if (category.children) {
    category.children.forEach((child: CheckboxCategory) =>
      updateCheckedCategory(
        fullCategoryList,
        category.children,
        child.id,
        checkedState,
      ),
    )
  }

  // updated the parents state based on the state of all their children
  const updateParents = (category: CheckboxCategory) => {
    if (category.parent_category_id) {
      const parent = findCategoryById(
        category.parent_category_id,
        fullCategoryList,
      )
      if (parent) {
        // if all children are checked (true) or any child is not checked (false), update parent
        const allChildrenChecked =
          parent.children.length > 0 &&
          parent.children.every(
            (child: CheckboxCategory) => child.checked === true,
          )
        parent.checked = allChildrenChecked
        updateParents(parent)
      }
    }
  }
  updateParents(category)

  return [...subCategories]
}

export const verifyParentsOfSelectedCategories = (
  categoryList: CheckboxCategory[],
) => {
  categoryList.forEach((category) => {
    if (category.children.length > 0) {
      const allChildrenChecked = category.children.every(
        (child: CheckboxCategory) => child.checked === true,
      )
      category.checked = allChildrenChecked
      verifyParentsOfSelectedCategories(category.children)
    }
  })
}

export const areAnyCategoriesSelected = (
  categories: CheckboxCategory[],
): boolean => {
  return categories.some((category) => {
    if (category.children.length > 0) {
      return areAnyCategoriesSelected(category.children)
    }
    return category.checked
  })
}

export const checkGlobalCategoriesEnabled = (locationPath: string) => {
  if (locationPath === '/insights') return true
  const allowedRoutes = [
    ...insightsReportRoutes,
    '/traffic/at-a-glance',
    '/traffic/organic-traffic',
    '/traffic/paid-traffic/products',
    '/traffic/automations/in-stock-protection',
    '/traffic/destiny/insights',
    '/traffic/destiny/opportunities',
    '/traffic/automations/inactive-ads',
    '/loyalty/ltv/product-insights',
    '/loyalty/SnS',
    '/loyalty/returns',
    '/loyalty/ltv/overview',
    '/protect/buybox/attribution',
    '/protect/buybox/suppression',
    '/protect/buybox/products',
    '/protect/buybox/sellers',
    '/content/reviews',
    '/content/retail-readiness',
    '/content/rank-tracker',
  ]

  const priceRoutes = !!matchPath('/protect/price/*', locationPath)
  const isInsightsVendorAnalyticsRoute = !!matchPath(
    insightsVendorAnalyticsRoute,
    locationPath,
  )

  return (
    allowedRoutes.includes(locationPath) ||
    priceRoutes ||
    isInsightsVendorAnalyticsRoute
  )
}
