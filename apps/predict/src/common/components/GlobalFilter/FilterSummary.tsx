/**
 *  Please be familiar with the documentation in the README.md file before making changes to this file
 *  or any of the related global filter files.
 **/

import React, { useContext, useEffect } from 'react'
import { useLocation } from 'react-router-dom'
import { uniq } from 'lodash'
import {
  Button,
  capitalize,
  Icon,
  Pill,
  Tooltip as SummaryTooltip,
  type TooltipProps,
  useIsMobileView,
  usePrevious,
  useToggle,
} from '@patterninc/react-ui'
import {
  c,
  isMatchPath,
  productListingRoute as productUrl,
  useTranslate,
} from '@predict-services'
import type {
  CheckboxCategory,
  Currency,
  Customer,
  Marketplace,
  Tag,
} from '@predict-types'

import { ThemeContext } from '../../../Context'
import { useUser } from '../../../context/user-context'
import { useGetAllBrandMarketplacesRegions } from '../../../modules/Reports/components/Pages/CustomReports/helpers'
import styles from './_globalFilter.module.scss'
import {
  allowedChineseMarketplaces,
  CheckGlobalCurrencyDisabled,
  checkGlobalLinesOfBusinessDisabled,
  checkGlobalTagsDisabled,
  getDisabledNonAmazonMarketplaces,
  getFilteredMarketplacesIds,
  insightsReportRoutes,
  renderAggregations,
  renderGlobalTimeFrameFilter,
} from './GlobalFilter.helpers'
import {
  type GlobalFilterVariables,
  useFetchCustomCategories,
  useGlobalFilterVariables,
  useIsContentRoute,
  useIsCustomerServiceRoute,
  useIsGlobalCategoriesEnabled,
  useIsGlobalCurrencyEnabled,
  useIsGlobalMarketplacesEnabled,
  useIsGlobalTimeframeEnabled,
  useIsOrganicTrafficRoute,
  useIsShareAtAGlanceRoute,
  useIsTrafficRoute,
  useIsVATSupportedRoute,
} from './GlobalFilter.hooks'

type Marketplaces = {
  [key: string]: string[]
}

type DataStructure = {
  [key: string]: Marketplaces
}

type MarketplaceDataType = {
  id: number
  marketplace_group_name: string
  marketplace_name: string
}

type FilterSummaryProps = {
  setSideDrawerOpen?: (open: boolean) => void
  summaryVariables?: GlobalFilterVariables
  isAllCategoryChecked?: boolean
  nonGlobal?: boolean
  /**
   * This flag is used to check if this component is rendered from the <GlobalFilter /> or not.
   * If this component is rendered from <GlobalFilter /> then do not call /regions_and_marketplaces API which is inside of useGetAllBrandMarketplacesRegions()
   * because this API call already exists in the <GlobalFilter />
   */
  isRenderedFromGlobalFilter?: boolean
  setActiveTabIndex?: React.Dispatch<React.SetStateAction<number | undefined>>
}

/**
 * A wrapper for `Tooltip` that stops event propagation on tooltip content,
 * preventing unintended parent clicks while allowing interactions inside.
 * Prevents the tooltip from being clicked while allowing its wrapper button to remain clickable.
 */
const Tooltip = ({ tooltipContent, children, ...props }: TooltipProps) => {
  return (
    <SummaryTooltip
      {...props}
      tooltipContent={({ setVisible }) => (
        <span>
          {typeof tooltipContent === 'function'
            ? tooltipContent({ setVisible })
            : (tooltipContent as React.ReactNode)}
        </span>
      )}
    >
      {children}
    </SummaryTooltip>
  )
}
export function isAttainmentRoute(pathname: string) {
  const isAttainmentRoute = [
    '/reports/attainment_reports',
    '/insights/attainment-reports',
  ].some((path) => isMatchPath(path, pathname))
  return isAttainmentRoute
}

export function useVatFilterToggleEnabled(pathname: string) {
  const isVATSupportedRoute = useIsVATSupportedRoute()

  const vatAdjustmentTogglePaths = [
    '/insights',
    ...insightsReportRoutes,
    `${productUrl}/:id/marketplace/:id/listing/:id/overview`,
    '/reports/custom/:reportId',
    '/reports/custom/:reportId/edit',
    '/protect/price/products',
  ]

  const showVAT = vatAdjustmentTogglePaths.some((path) =>
    isMatchPath(path, pathname),
  )

  const isVatFilterToggleEnabled =
    showVAT || isAttainmentRoute(pathname) || isVATSupportedRoute

  return isVatFilterToggleEnabled
}
///////////////////////////////////////////////////////////////////////////////////////////////////
// FilterSummary
///////////////////////////////////////////////////////////////////////////////////////////////////
const FilterSummary: React.FC<FilterSummaryProps> = ({
  setSideDrawerOpen,
  summaryVariables,
  isAllCategoryChecked = false,
  nonGlobal,
  isRenderedFromGlobalFilter = false,
  setActiveTabIndex,
}) => {
  const chinaDataOrganicTrafficChanges = useToggle(
    'china_data_organic_traffic_changes',
  )
  const isTrafficRoute = useIsTrafficRoute()
  const isOrganicTrafficRoute = useIsOrganicTrafficRoute()
  const isContentRoute = useIsContentRoute()
  const isCustomerServiceRoute = useIsCustomerServiceRoute()
  const enableChinaForOrganicTraffic = chinaDataOrganicTrafficChanges
    ? true
    : !isOrganicTrafficRoute
  const enableSpecificChineseMarketplaces =
    (isTrafficRoute && enableChinaForOrganicTraffic) ||
    isContentRoute ||
    isCustomerServiceRoute
  const { pathname } = useLocation()
  const { t } = useTranslate('insights')
  const isSelectiveDistribution = pathname?.includes('selective-distribution')
  const isKeyWordRankingRoute = isMatchPath(
    '/content/rank-tracker/products/:marketProductId/ranked-keywords',
    pathname,
  )

  const globalMarketplacesEnabled = useIsGlobalMarketplacesEnabled(),
    globalCurrencyDisabled = useIsGlobalCurrencyEnabled()

  const isCustomReportRoute = [
    '/reports/custom/:reportId',
    '/reports/custom/:reportId/edit',
  ].some((path) => isMatchPath(path, pathname))
  const { allMarketplaces, isChurnTurnedOn, LOBOptions } =
    useContext(ThemeContext)

  const { marketplaceData } = useGetAllBrandMarketplacesRegions(
      isRenderedFromGlobalFilter,
    ),
    nonGlobalAllMarketplaces = marketplaceData?.allMarketplaces ?? []
  const finalAllMarketplaces = nonGlobal
    ? nonGlobalAllMarketplaces
    : allMarketplaces

  const {
    brandGroup,
    currentMarketplaces,
    currentRegions,
    customCategories,
    customer,
    isVatAdjustmentTurnedOn,
    prevCustomer,
    rawMarketplaces,
    regions,
    sdMarketplaceIds,
    selectedCurrency,
    tags,
    linesOfBusiness,
    timeframe,
  } = useGlobalFilterVariables(summaryVariables)
  const lobFilterConversion = useToggle('lob_filter_conversion')
  const showAggregations = renderAggregations(pathname)
  const renderGlobalTimeFrameFilterToggle =
    renderGlobalTimeFrameFilter(pathname)
  const disabledNonAmazonMarketplaces = getDisabledNonAmazonMarketplaces(
    pathname,
    enableSpecificChineseMarketplaces,
  )
  const disableNonAmazonAndNonWalMarketplaces =
    isTrafficRoute || isCustomerServiceRoute
  const isMobileView = useIsMobileView()
  const isShareAtAGlanceRoute = useIsShareAtAGlanceRoute()

  /////////////////////////////////////////////////////////////////////////////////////////////////
  // TIMEFRAME SUMMARY
  /////////////////////////////////////////////////////////////////////////////////////////////////
  const globalTimeframeEnabled =
    useIsGlobalTimeframeEnabled() || !!summaryVariables // if summaryVariables is passed, then override globalTimeframeEnabled

  const getTimeframeSummary = () => {
    return globalTimeframeEnabled ? (
      showAggregations && timeframe.aggregation ? (
        <span>{`${timeframe.type === 'previous' ? 'Previous ' : ''}${
          timeframe.display
        } - BY ${timeframe?.aggregation}`}</span>
      ) : (
        <span>{`${
          !renderGlobalTimeFrameFilterToggle
            ? 'Today'
            : `${timeframe.type === 'previous' ? 'Previous ' : ''}${
                timeframe.display
              }`
        }`}</span>
      )
    ) : null
  }

  /////////////////////////////////////////////////////////////////////////////////////////////////
  // MARKETPLACE SUMMARY
  /////////////////////////////////////////////////////////////////////////////////////////////////
  const isGlobalMarketplacesEnabled = useIsGlobalMarketplacesEnabled()
  const allAmazonMarketplaceIds: number[] = finalAllMarketplaces
    ?.filter(
      (marketplaceData: MarketplaceDataType) =>
        marketplaceData?.marketplace_group_name?.toLowerCase() === 'amazon',
    )
    ?.map((marketplaceData: MarketplaceDataType) => marketplaceData?.id)

  const allWalmartMarketplaceIds: number[] = finalAllMarketplaces
    ?.filter(
      (marketplaceData: MarketplaceDataType) =>
        marketplaceData?.marketplace_name?.toLowerCase() === 'walmart',
    )
    ?.map((marketplaceData: MarketplaceDataType) => marketplaceData?.id)

  const combinedMarketplaceIds = [
    ...allAmazonMarketplaceIds,
    ...allWalmartMarketplaceIds,
  ]

  const checkMarketplaceMismatch = (allCurrentMarketplaces: number[]) => {
    const matchingMarketplaces = finalAllMarketplaces?.filter(
      ({ id }: { id: number }) => allCurrentMarketplaces.includes(id),
    )
    const isMismatch =
      matchingMarketplaces.length !==
      (allCurrentMarketplaces.includes(0)
        ? allCurrentMarketplaces.length - 1
        : allCurrentMarketplaces.length)
    return { matchingMarketplaces, isMismatch }
  }
  const amazonMarketplaces: Marketplace[] | undefined = rawMarketplaces?.find(
    (mp: Marketplace) => mp.marketplace_name === 'Amazon',
  )?.subMarketplaces

  const walmartMarketplaces: Marketplace[] | undefined = rawMarketplaces?.find(
    (mp: Marketplace) => mp.marketplace_name === 'Walmart',
  )?.subMarketplaces
  // considering chinese specific marketplaces
  const specificMarketplacesIds: number[] | undefined = rawMarketplaces
    ?.filter(
      (mp: Marketplace) =>
        mp.region_id === 3 && allowedChineseMarketplaces?.includes(mp.id),
    )
    ?.map((mp: Marketplace) => mp.id)

  const getMarketplaceLabel = () => {
    const allCurrentMarketplaces: number[] = getFilteredMarketplacesIds(
      isSelectiveDistribution,
      disabledNonAmazonMarketplaces,
      disableNonAmazonAndNonWalMarketplaces,
      enableSpecificChineseMarketplaces,
      isContentRoute,
      isShareAtAGlanceRoute,
      sdMarketplaceIds,
      currentMarketplaces,
      allAmazonMarketplaceIds,
      specificMarketplacesIds,
      combinedMarketplaceIds,
    )

    const selectedMarketplaceName = (rawMarketplaces || []).find(
      (mp) => mp.id === allCurrentMarketplaces[0],
    )?.marketplace_name

    const allMarketplacesSelected = allCurrentMarketplaces?.includes(0)
    // NOTE: allCurrentMarketplaces & currentRegions cannot be empty (except for the initial load)
    // Segregation for All marketplaces
    if (
      (allMarketplacesSelected &&
        // currentRegions.length - 1 is to exclude the 'Select All' option
        currentRegions?.length - 1 === regions?.length) ||
      (allCurrentMarketplaces.length === 0 && currentRegions.length === 0) // initial loading hasn't occurred yet so assume all marketplaces
    ) {
      return 'All'
      // Segregation for a single marketplace
    } else if (
      allCurrentMarketplaces?.length === 1 &&
      selectedMarketplaceName
    ) {
      return `${selectedMarketplaceName}${
        allCurrentMarketplaces[0] === 1 ? ' US' : ''
      }`
    }

    // Segregation for AMAZON only marketplaces
    if (selectedMarketplaceName?.toLowerCase() === 'amazon') {
      let amazonMarketplacesCount = 0,
        otherMarketplacesCount = 0
      allCurrentMarketplaces?.forEach((id) => {
        // NOTE: id 1 is for `Amazon` pseudo marketplace
        if (id === 1 || amazonMarketplaces?.find((am) => am.id === id)) {
          amazonMarketplacesCount++
        } else {
          otherMarketplacesCount++
        }
      })
      if (
        amazonMarketplaces?.length === amazonMarketplacesCount &&
        otherMarketplacesCount === 0
      ) {
        return 'Amazon (All)'
      } else if (otherMarketplacesCount === 0) {
        return `Amazon (${amazonMarketplacesCount})`
      }
    }

    //Segregation for AMAZON and WALMART only marketplaces
    if (
      selectedMarketplaceName?.toLowerCase() === 'amazon' ||
      selectedMarketplaceName?.toLowerCase() === 'walmart'
    ) {
      let amazonMarketplacesCount = 0,
        walmartMarketplacesCount = 0,
        otherMarketplacesCount = 0
      allCurrentMarketplaces?.forEach((id) => {
        // NOTE: id 1 is for `Amazon` pseudo marketplace
        if (id === 1 || amazonMarketplaces?.find((am) => am.id === id)) {
          amazonMarketplacesCount++
        } else if (
          id === 3 ||
          walmartMarketplaces?.find((wm) => wm.id === id)
        ) {
          walmartMarketplacesCount++
        } else {
          otherMarketplacesCount++
        }
      })
      if (
        amazonMarketplaces?.length === amazonMarketplacesCount &&
        walmartMarketplacesCount === 0 &&
        otherMarketplacesCount === 0
      ) {
        return 'Amazon (All)'
      } else if (
        amazonMarketplacesCount === 0 &&
        walmartMarketplaces?.length === walmartMarketplacesCount &&
        otherMarketplacesCount === 0
      ) {
        return 'Walmart (All)'
      } else if (walmartMarketplaces?.length && amazonMarketplaces?.length) {
        return `Multiple (${walmartMarketplacesCount + amazonMarketplacesCount})`
      } else if (amazonMarketplacesCount === 0) {
        return `Walmart (${walmartMarketplacesCount})`
      } else if (
        otherMarketplacesCount === 0 &&
        walmartMarketplacesCount === 0
      ) {
        return `Amazon (${amazonMarketplacesCount})`
      }
    }
    // Segregation for Custom Reports, when the current marketplaces are different than the filtered marketplace options
    const { matchingMarketplaces, isMismatch } = checkMarketplaceMismatch(
      allCurrentMarketplaces,
    )
    if (isMismatch && isCustomReportRoute) {
      return `Matching (${matchingMarketplaces.length})`
    }

    // Segregation for a single non-AMAZON marketplace
    if (currentRegions.length === 1) {
      return `${
        regions?.find((region) => region.id === currentRegions[0])?.region_name
      }${
        allCurrentMarketplaces.includes(0)
          ? ''
          : ` (${allCurrentMarketplaces.length})`
      }`
    }

    // Segregation for multiple marketplaces
    return `Multiple (${
      allCurrentMarketplaces.includes(0)
        ? allCurrentMarketplaces.length - 1
        : allCurrentMarketplaces.length
    })`
  }

  function getAllowedChinaMarketplacesNames() {
    const allowedChinaMarketplaces = finalAllMarketplaces
      .filter((mp: Marketplace) => allowedChineseMarketplaces.includes(mp.id))
      .map((mp: Marketplace) => mp.marketplace_name)
    return allowedChinaMarketplaces
  }

  const getMarketplaceTooltipContent = (label: string) => {
    const allCurrentMarketplaces: number[] = getFilteredMarketplacesIds(
      isSelectiveDistribution,
      disabledNonAmazonMarketplaces,
      disableNonAmazonAndNonWalMarketplaces,
      enableSpecificChineseMarketplaces,
      isContentRoute,
      isShareAtAGlanceRoute,
      sdMarketplaceIds,
      currentMarketplaces,
      allAmazonMarketplaceIds,
      specificMarketplacesIds,
      combinedMarketplaceIds,
    )

    // added marketplaces 10 count as per ux requirement
    if (label === 'All') {
      return (
        <div>
          All Regions and Marketplaces
          <br />
          available for selected Brand(s)
        </div>
      )
    }

    // for Custom Reports, when the current marketplaces are different than the filtered marketplace options
    const { matchingMarketplaces, isMismatch } = checkMarketplaceMismatch(
      allCurrentMarketplaces,
    )
    if (isMismatch && isCustomReportRoute) {
      return (
        <div>
          {`${matchingMarketplaces.length} of ${allCurrentMarketplaces.length} Marketplaces`}
          <br />
          match for Brand(s)
        </div>
      )
    }
    const showText = enableSpecificChineseMarketplaces
      ? allCurrentMarketplaces?.length > 10 &&
        !(currentRegions.length === 1 && currentRegions[0] === 3)
      : allCurrentMarketplaces?.length > 10
    if (showText) {
      return (
        <div>
          More than 10 Marketplaces
          <br />
          selected for Brand(s)
        </div>
      )
    }

    // Splitting markets for regions
    const tooltip: {
      [key: string]: { amazonMarkets?: string[]; otherMarkets?: string[] }
    } = {}
    allCurrentMarketplaces.forEach((id: number) => {
      if (id !== 0) {
        let market = amazonMarketplaces?.find((am) => am.id === id)
        if (market) {
          const regionName = regions.find(
            (reg) => reg.id === market?.region_id,
          )?.region_name
          const marketName =
            id === 1
              ? `${market?.marketplace_name} US`
              : market?.marketplace_name
          if (regionName) {
            if (!tooltip[regionName]) {
              tooltip[regionName] = {
                amazonMarkets: [marketName],
              }
            } else {
              tooltip[regionName].amazonMarkets = tooltip[regionName]
                .amazonMarkets
                ? [...(tooltip[regionName].amazonMarkets ?? []), marketName]
                : [marketName]
            }
          }
        } else {
          market = rawMarketplaces.find((mp) => mp.id === id)
          const regionName = regions.find(
            (reg) => reg.id === market?.region_id,
          )?.region_name
          if (market && regionName) {
            if (!tooltip[regionName]) {
              tooltip[regionName] = {
                otherMarkets: [market?.marketplace_name],
              }
            } else {
              tooltip[regionName].otherMarkets = tooltip[regionName]
                .otherMarkets
                ? [
                    ...(tooltip[regionName].otherMarkets ?? []),
                    market?.marketplace_name,
                  ]
                : [market?.marketplace_name]
            }
          }
        }
      }
    })

    let tooltipMarketplaces: { [key: string]: { [key: string]: string[] } } = {}

    allCurrentMarketplaces.forEach((id) => {
      if (id !== 0) {
        const market = rawMarketplaces?.find((raw) => raw.id === id)

        const regionName = regions.find(
          (reg) => reg.id === market?.region_id,
        )?.region_name
        if (market && regionName) {
          if (!tooltipMarketplaces[regionName]) {
            tooltipMarketplaces[regionName] = {
              [market?.marketplace_group_name]: [market?.marketplace_name],
            }
          } else {
            tooltipMarketplaces[regionName][market?.marketplace_group_name] =
              tooltipMarketplaces[regionName]?.[market?.marketplace_group_name]
                ? [
                    ...tooltipMarketplaces[regionName][
                      market?.marketplace_group_name
                    ],
                    market?.marketplace_name,
                  ]
                : [market?.marketplace_name]
          }
        }
      }
    })

    const excludeOtherMarketplacesInSort = ['B2B China']
    const filterMarketplaces = (
      data: DataStructure,
      topLevelKey: string,
      allowed: string[],
    ): DataStructure => {
      const filteredData: Marketplaces = {}

      if (data[topLevelKey]) {
        for (const category in data[topLevelKey]) {
          const filteredList = data[topLevelKey][category].filter(
            (marketplace) => allowed.includes(marketplace),
          )
          if (filteredList.length > 0) {
            filteredData[category] = filteredList
          }
        }
      }

      return { [topLevelKey]: filteredData }
    }

    if (
      Object.keys(tooltipMarketplaces).length === 1 &&
      currentRegions.length === 1 &&
      currentRegions[0] === 3
    ) {
      tooltipMarketplaces = filterMarketplaces(
        tooltipMarketplaces,
        'China',
        getAllowedChinaMarketplacesNames(),
      )
    }

    return (
      <div className='global-marketplaces-tooltip'>
        {Object.keys(tooltipMarketplaces).map((region) => (
          <span key={`marketplaces_${region}`}>
            {region && <div>{region}</div>}
            {Object.keys(tooltipMarketplaces[region])?.length > 0 &&
              Object.keys(tooltipMarketplaces[region]).map((group) => (
                <span key={`marketplaces_${group}`}>
                  {group &&
                    group !== 'null' && // added case for null as string
                    group !== 'Others' &&
                    !excludeOtherMarketplacesInSort.includes(group) && (
                      <span className='level with-icon'>
                        <Icon icon='l' iconSize='8px' />
                        <span>{group}</span>
                      </span>
                    )}
                  {tooltipMarketplaces[region]?.[group]?.map((market) => (
                    <span
                      className={`level with-icon `}
                      key={`grouped-marketplaces-${market}`}
                    >
                      <Icon
                        icon='l'
                        iconSize='8px'
                        className={`${
                          group === 'null' ||
                          group === 'Others' ||
                          excludeOtherMarketplacesInSort.includes(group)
                            ? ''
                            : 'pat-ml-4'
                        }`}
                      />
                      <span
                        className={group !== 'Others' ? 'pat-pl-4' : ''}
                      >{`${market}${
                        ['Amazon', 'ebay'].includes(market) ? ' US' : ''
                      }`}</span>
                    </span>
                  ))}
                </span>
              ))}
          </span>
        ))}
      </div>
    )
  }

  const getMarketplaceSummary = () => {
    if (!isGlobalMarketplacesEnabled) {
      return null
    }
    const label = getMarketplaceLabel()
    if (isKeyWordRankingRoute) {
      const selectedMarketplace = rawMarketplaces.find(
        (marketplace) =>
          marketplace.id ===
          Number(sessionStorage.getItem('keywordRankingMarketplaceId')),
      )

      const label =
        selectedMarketplace?.country_code === 'US'
          ? `${selectedMarketplace?.marketplace_name} ${selectedMarketplace?.country_code}`
          : selectedMarketplace?.marketplace_name

      sessionStorage.setItem('keywordRankingMarketplaceLabel', label ?? '')
    }

    const currentSdAllMarketplaces = getFilteredMarketplacesIds(
      isSelectiveDistribution,
      disabledNonAmazonMarketplaces,
      disableNonAmazonAndNonWalMarketplaces,
      enableSpecificChineseMarketplaces,
      isContentRoute,
      isShareAtAGlanceRoute,
      sdMarketplaceIds,
      currentMarketplaces,
      allAmazonMarketplaceIds,
      specificMarketplacesIds,
      combinedMarketplaceIds,
    )

    return (
      <>
        {globalTimeframeEnabled && ( // NOTE: only show divider if timeframe is displayed before marketplace
          <span className={styles.divider} />
        )}
        {currentSdAllMarketplaces?.length === 1 || label === 'Multiple (0)' ? (
          <span>{label}</span>
        ) : (
          <Tooltip
            position='bottom'
            tooltipContent={
              currentMarketplaces && getMarketplaceTooltipContent(label)
            }
          >
            {label}
          </Tooltip>
        )}
      </>
    )
  }

  /////////////////////////////////////////////////////////////////////////////////////////////////
  // CATEGORIES FILTERS SUMMARY
  /////////////////////////////////////////////////////////////////////////////////////////////////
  const { allCategories } = useFetchCustomCategories()
  const globalCategoriesEnabled = useIsGlobalCategoriesEnabled()
  const getCustomCategoriesFilterCount = () => {
    if (!globalCategoriesEnabled) {
      return null
    }

    let isAllCategories = true
    const hasNoCategories = allCategories?.[0]?.children?.length === 0
    const customCategoryIds: number[] = []
    const allCategoryIds: number[] = []

    const getIds = (
      category: Array<CheckboxCategory>,
      idArray: Array<number>,
    ) => {
      category.forEach((cat) => {
        if (cat?.children?.length) {
          getIds(cat.children, idArray)
        } else if (cat?.checked && cat?.children?.length === 0) {
          !idArray.includes(cat.id) && idArray.push(cat.id)
        } else if (!cat?.checked) {
          isAllCategories = false
        }
      })
    }

    getIds(customCategories, customCategoryIds)
    getIds(allCategories, allCategoryIds)
    const uniqCustomCategoryIds = uniq(customCategoryIds)
    const uniqAllCategoryIds = uniq(allCategoryIds)

    if (uniqCustomCategoryIds.length !== uniqAllCategoryIds.length) {
      isAllCategories = false
    }

    const filterCount = uniqCustomCategoryIds.length

    const categoryTree: { name: string; id: string }[] = []
    const buildCategoryTree = (
      categories: CheckboxCategory[],
      treeName: string,
    ) => {
      categories.forEach((category) => {
        const categoryTreeName = treeName
          ? `${treeName} > ${category.name}`
          : category.name

        // NOTE: only add leaf nodes to categoryTree
        if (category?.children.length > 0) {
          buildCategoryTree(
            category.children,
            category.id > 0 ? categoryTreeName : '',
          )
        } else {
          if (customCategoryIds.includes(category.id)) {
            categoryTree.push({
              name: categoryTreeName,
              id: `${category.id}`,
            })
          }
        }
      })
    }
    buildCategoryTree(allCategories, '')

    return (
      <>
        {hasNoCategories ? null : <span className={styles.divider} />}
        {filterCount &&
        !isAllCategories &&
        // isAllCategoryChecked is a boolean state which updates on every checkbox change on custom category
        !isAllCategoryChecked ? (
          <Tooltip
            position='bottom'
            tooltipContent={
              <div className='fc-dark-purple pat-m-2'>
                {filterCount <= 10 ? (
                  <>
                    <div className='fs-14 fw-bold pat-mt-2'>Categories</div>
                    <div className='fs-14 fw-regular'>
                      {categoryTree.map((category) => {
                        return (
                          <div key={category.id}>
                            {capitalize(category.name)}
                          </div>
                        )
                      })}
                    </div>
                  </>
                ) : (
                  <div className='fs-14 fw-regular'>
                    {`${filterCount} Categories selected for Brand`}
                  </div>
                )}
              </div>
            }
          >
            <span style={{ marginTop: '-4px' }}>
              <Pill color='blue' number={filterCount} />
            </span>
          </Tooltip>
        ) : hasNoCategories ? null : (
          <Tooltip
            position='bottom'
            tooltipContent={
              <div className='fc-dark-purple pat-m-2'>
                <div className='fs-14 fw-regular'>
                  All Custom Categories available for selected Brand
                </div>
              </div>
            }
          >
            <span>All</span>
          </Tooltip>
        )}
      </>
    )
  }

  /////////////////////////////////////////////////////////////////////////////////////////////////
  // ADDITIONAL FILTERS SUMMARY
  /////////////////////////////////////////////////////////////////////////////////////////////////
  const { user } = useUser()
  const [filterCount, setFilterCount] = React.useState<number | null>(null)
  const getAdditionalFiltersCount = () => {
    const isGlobalCurrencyDisabled = CheckGlobalCurrencyDisabled(pathname)
    let globalTagsDisabled = true
    const isBrandGroupSelected = !!brandGroup
    const isAutoBrandGroupSelected =
      brandGroup?.grouping_category === 'auto_brand_groups'
    if (!isBrandGroupSelected || isAutoBrandGroupSelected) {
      globalTagsDisabled = checkGlobalTagsDisabled(
        pathname,
        isAutoBrandGroupSelected ? null : (customer as unknown as Customer),
      )
    }

    const currency: Partial<Currency> =
      selectedCurrency ?? user?.current_currency

    if (isGlobalCurrencyDisabled && globalTagsDisabled) {
      return null
    }

    return (
      <>
        {(isGlobalMarketplacesEnabled ||
          globalTimeframeEnabled ||
          globalCategoriesEnabled) && <span className={styles.divider} />}
        {filterCount &&
        ((tags?.selectedTags?.length > 0 && !globalTagsDisabled) ||
          (linesOfBusiness?.selectedTypes?.length !== LOBOptions?.length &&
            !checkGlobalLinesOfBusinessDisabled(
              pathname,
              lobFilterConversion,
            ))) ? (
          <Tooltip
            position='bottom'
            tooltipContent={({ setVisible }) => (
              <div className='fc-dark-purple pat-m-2'>
                <div className='fs-14 fw-bold'>{c('currency')}</div>
                <div className='fs-14 fw-regular'>
                  {currency?.symbol} {currency?.code}{' '}
                </div>
                {tags?.selectedTags?.length > 0 && !globalTagsDisabled && (
                  <>
                    <div className='fs-14 fw-bold pat-mt-2'>{c('tags')}</div>
                    <div className='fs-14 fw-regular'>
                      {tags?.selectedTags
                        .map((tag: Tag) => capitalize(tag?.name))
                        .sort()
                        .join(', ')}
                    </div>
                  </>
                )}
                {linesOfBusiness?.selectedTypes?.length < LOBOptions?.length &&
                  !checkGlobalLinesOfBusinessDisabled(
                    pathname,
                    lobFilterConversion,
                  ) && (
                    <>
                      <div className='fs-14 fw-bold pat-mt-2'>
                        {c('linesOfBusiness')}
                      </div>
                      <div className='fs-14 fw-regular'>
                        {linesOfBusiness?.selectedTypes
                          ?.slice() // Creates a shallow copy of the array
                          .sort((a, b) => a.title.localeCompare(b.title))
                          .slice(0, 6)
                          .map((type, index) => (
                            <div key={index}>{type?.title}</div>
                          ))}
                        {linesOfBusiness?.selectedTypes?.length > 6 && (
                          // Show "See More" button only when we have more than 6 items
                          <Button
                            as='button'
                            styleType='text-blue'
                            className='pat-mt-4'
                            onClick={() => {
                              setVisible(false)
                              setSideDrawerOpen?.(true)

                              // The count will make sure we switch to last tab i.e 'More' tab
                              let globalFilterCount = 0
                              if (globalTimeframeEnabled) globalFilterCount++
                              if (globalMarketplacesEnabled) globalFilterCount++
                              if (globalCategoriesEnabled) globalFilterCount++
                              if (
                                !(globalCurrencyDisabled && globalTagsDisabled)
                              )
                                globalFilterCount++

                              setActiveTabIndex?.(globalFilterCount - 1)
                            }}
                          >
                            {c('seeMore')}
                          </Button>
                        )}
                      </div>
                    </>
                  )}
              </div>
            )}
          >
            <span style={{ marginTop: '-4px' }}>
              <Pill color='blue' number={filterCount} />
            </span>
          </Tooltip>
        ) : (
          <span>{`${currency?.symbol} ${currency?.code}`}</span>
        )}
      </>
    )
  }

  const prevBrandGroupData = usePrevious(brandGroup)
  useEffect(() => {
    if (prevCustomer.id !== customer?.id || prevBrandGroupData !== brandGroup) {
      setFilterCount(null)
    }
  }, [brandGroup, customer?.id, prevBrandGroupData, prevCustomer])

  useEffect(() => {
    let filterCountValue = 0

    const isTagSelected = tags?.selectedTags?.length > 0,
      isLinesOfBusinessSelected =
        !checkGlobalLinesOfBusinessDisabled(pathname, lobFilterConversion) &&
        linesOfBusiness?.selectedTypes?.length < LOBOptions?.length

    const selectedFilters = [isTagSelected, isLinesOfBusinessSelected]

    let isAnyFilterSelected = false

    // Increment the filter count if any filter is selected from the selectedFilters array
    selectedFilters.forEach((filter) => {
      if (filter) {
        isAnyFilterSelected = true
        filterCountValue++
      }
    })

    // If any filter is selected, increment the filter count for currency as well else keep count as 0
    if (isAnyFilterSelected) {
      filterCountValue += 1
    }

    setFilterCount(filterCountValue > 0 ? filterCountValue : 0)
  }, [LOBOptions?.length, linesOfBusiness, pathname, tags, lobFilterConversion])

  /////////////////////////////////////////////////////////////////////////////////////////////////
  // VAT SUMMARY
  /////////////////////////////////////////////////////////////////////////////////////////////////
  // To display VAT Adjustment toggle in the global filter only for below specified routes

  const isVATinGlobalFilterToggleEnabled = useVatFilterToggleEnabled(pathname)

  const getVATSummary = () => {
    return isVATinGlobalFilterToggleEnabled && !isVatAdjustmentTurnedOn ? (
      <>
        <span className='pat-ml-1'>- VAT INCLUDED</span>
        <Tooltip
          tooltipContent={
            <p>
              Value-added tax (VAT) adjustment is currently turned on. All sales
              totals visible show the VAT adjusted sales total.
            </p>
          }
          position='bottom'
        >
          <Icon
            icon='vat'
            color='dark-purple'
            className='pat-ml-2'
            iconSize='16px'
          />
        </Tooltip>
      </>
    ) : null
  }

  const isChurnToggleSummaryEnabled =
    customer?.id === 0 && isAttainmentRoute(pathname) && !isChurnTurnedOn

  const getChurnSummary = () => {
    return isChurnToggleSummaryEnabled ? (
      <Tooltip
        tooltipContent={<p>{t('churnSummaryTooltip')}</p>}
        position='bottom'
      >
        {t('churnSummaryText')}
      </Tooltip>
    ) : null
  }

  /////////////////////////////////////////////////////////////////////////////////////////////////
  // DISPLAY SUMMARY
  /////////////////////////////////////////////////////////////////////////////////////////////////
  return (
    <Button
      id='global_filter_summary'
      onClick={() => setSideDrawerOpen && setSideDrawerOpen(true)}
    >
      {isMobileView ? (
        <Icon icon='filter' iconSize='16px' />
      ) : (
        <div className='flex pat-gap-2'>
          <Icon icon='filter' iconSize='16px' />
          <div className='flex align-items-center pat-gap-2'>
            {/* TIMEFRAME SUMMARY */}
            {getTimeframeSummary()}

            {/* MARKETPLACE SUMMARY */}
            {getMarketplaceSummary()}

            {/* CATEGORIES FILTERS SUMMARY */}
            {getCustomCategoriesFilterCount()}

            {/* ADDITIONAL FILTERS SUMMARY */}
            {getAdditionalFiltersCount()}

            {/* VAT SUMMARY */}
            {getVATSummary()}

            {/* CHURN SUMMARY */}
            {getChurnSummary()}
          </div>
        </div>
      )}
    </Button>
  )
}

export default FilterSummary
