import React, { useCallback, useMemo } from 'react'
import { isEqual } from 'lodash'
import { Select } from '@patterninc/react-ui'
import type { Currency } from '@predict-types'

export const defaultCurrency = {
  id: 1,
  name: 'US Dollar',
  symbol: '$',
  code: 'USD',
  type: 'Currency',
  label: '$ — US Dollar',
}

export const defaultDateFormat = {
  format: 'MM/DD/YYYY',
  date_format_id: 1,
  type: 'Date',
  label: 'Month/Day/Year',
}

type GlobalCurrencyProps = {
  currentCurrency: Currency
  currencies: Array<Currency>
  updateSelectedCurrency: (c: Currency) => void
  showLable?: boolean
}
const GlobalCurrency = ({
  currentCurrency = defaultCurrency,
  currencies,
  updateSelectedCurrency,
}: GlobalCurrencyProps): React.JSX.Element => {
  const makeCurrencyText = (currency: Currency) => {
    let currencyLabel = currency?.label
    const currencyText = currencyLabel?.split(' ')

    if (currencyText?.length > 1) {
      currencyLabel = `${currencyText[0]} ${currencyText[1]} ${currency?.code}`
    }
    return currencyLabel
  }

  const makeSelectedValue = useCallback(() => {
    let label = currentCurrency?.label
    if (label) return makeCurrencyText(currentCurrency)

    const foundCurrency = currencies?.find(
      (option) => option?.code === currentCurrency?.code,
    )
    if (foundCurrency && Object.keys(foundCurrency)?.length > 0) {
      label = makeCurrencyText(foundCurrency)
    }
    return label
  }, [currentCurrency, currencies])

  const changeSelectedCurrency = (currency: Currency) => {
    if (isEqual(currency, currentCurrency)) {
      // if the selected currency is same as the current currency then do nothing
      return
    } else {
      updateSelectedCurrency(currency)
    }
  }

  const selectedOption = useMemo(() => {
    return {
      id: currentCurrency?.id,
      name: currentCurrency?.name,
      symbol: currentCurrency?.symbol,
      type: currentCurrency?.type,
      label: makeSelectedValue(),
      code: currentCurrency?.code,
    }
  }, [currentCurrency, makeSelectedValue])

  return (
    <div className='global-currency'>
      {
        <div className='global-regions'>
          <Select
            labelProps={{ label: 'Currency' }}
            options={currencies}
            optionKeyName='code'
            labelKeyName='label'
            selectedItem={selectedOption}
            onChange={(option) => {
              changeSelectedCurrency(option)
            }}
          />
        </div>
      }
    </div>
  )
}
export default GlobalCurrency
