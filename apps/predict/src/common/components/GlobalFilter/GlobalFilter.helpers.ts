import { useLocation } from 'react-router-dom'
import { uniq } from 'lodash'
import moment, { type Moment, type MomentInput } from 'moment'
import { capitalize, getTimeframeDates } from '@patterninc/react-ui'
import {
  isMatchPath,
  productCoreRoute,
  productListingRoute,
  sellersCoreRoute,
} from '@predict-services'
import {
  HISTORY_TAB_PATH,
  INVENTORY_INSIGTHS_ITEMS_PATH,
  INVENTORY_INSIGTHS_LISTINGS_PATH,
} from 'src/modules/Logistics/LogisticsRoutes'
import { changeHistoryModuleRoute } from 'src/modules/Reports/components/Pages/TrafficChangeHistory/TrafficChangeHistory'
import type {
  AvailableMarketplacesData,
  Currency,
  Customer,
  CustomTimeAggregation,
  DateType,
  GetAvailableMarketplacesProps,
  MarketplaceData,
  Region,
  RegionId,
  Timeframe,
  TimeframeFilterProps,
} from '@predict-types'
import type { Marketplace, MarketplaceId } from '@predict-types'

import { getGlobalMarketplaces } from '../../../modules/Settings/services/MarketplacesService'
import { getCurrencies } from '../../services/CurrenciesService'
import { handleError } from '../../services/HandleError'
import { useIsProtectRoute } from './GlobalFilter.hooks'

type LocationState = {
  brand?: Record<string, unknown> | null
  customer?: {
    id: number
  }
}

type DisabledTimeFrameOptions = Record<Timeframe['type'], string[]>

type ValidTimeframeAggregationsProps = {
  current: Record<string, string[]>
  previous?: Record<string, string[]>
  trailing?: Record<string, string[]> & {
    custom?: {
      minDayDifference: number
      maxDayDifference: number
      aggregations: string[]
    }[]
  }
  historical?: Record<CustomTimeAggregation, string[]> & {
    custom?: {
      minDayDifference: number
      maxDayDifference: number
      aggregations: string[]
    }[]
  }
  quarterly?: Record<string, string[]>
}

type CustomAggregationType = Record<CustomTimeAggregation, string[]> & {
  custom?: {
    minDayDifference: number
    maxDayDifference: number
    aggregations: string[]
  }[]
}

type ValidAggregationsProps = {
  timeframe: Timeframe
  value: number
  /** Use a date string or a moment */
  startDate?: DateType
  /** Use a date string or a moment */
  endDate?: DateType
  customAggregations?: CustomAggregationType
  timePeriodExtension: boolean
  /** location pathname (aggregations change based on route) */
  pathname: string
}

type PartialTimeframe = Partial<Timeframe>

type CompareDisplayTimeframeType = TimeframeFilterProps['timeframe'] & {
  compareDisplay: string
}

///////////////////////////////////////////////////////////////////////////////////////////////////
// GLOBAL FILTER HELPERS
///////////////////////////////////////////////////////////////////////////////////////////////////
/**  Return boolean, true if valid location and state */
export const useCheckForValidLocationAndState = (
  locationState: LocationState,
): boolean => {
  const isSingleBrandSelected =
    locationState?.customer?.id !== 0 && !locationState?.brand
  const isProtectRoute = useIsProtectRoute()
  const disableInTheseModules = [
    '/reports',
    '/settings',
    '/support',
    '/logistics',
    '/planning',
  ]
  const exceptionRoutesInDisabledModules = [
    '/reports/custom',
    changeHistoryModuleRoute,
    '/settings/traffic/brand/:brandId/playbooks/:playbookId/view-playbook-rules',
    `${productCoreRoute}/overview`,
    `${productCoreRoute}/overview/all`,
    `${productListingRoute}/:productId/*`,
    `${productListingRoute}/:productId/marketplace/:marketplaceId/listing/:listingId/*`,
    HISTORY_TAB_PATH,
    INVENTORY_INSIGTHS_ITEMS_PATH,
    INVENTORY_INSIGTHS_LISTINGS_PATH,
  ]

  // Specific condition for enabling Global filter for protect/buybox/brands route
  // Replace this protect/buybox/brands with protect/buybox once we start supporting brand groups on protect->buybox
  const { pathname } = useLocation()
  const buyBoxPaths = [
    '/protect/buybox/brands',
    '/protect/buybox/attribution',
    '/protect/buybox/products',
    '/protect/buybox/suppression',
  ]

  const isProtectBrandRoute = buyBoxPaths.some((path) =>
    isMatchPath(path, pathname, false),
  )
  const isAutomationsTabRoute = isMatchPath(
    '/protect/price/automations/*',
    pathname,
  )
  const isPricingProductsRoute = isMatchPath(
    '/protect/price/products',
    pathname,
  )

  const isValidProtectState =
    !isProtectRoute ||
    Boolean(
      isProtectRoute &&
        (isSingleBrandSelected ||
          isProtectBrandRoute ||
          isAutomationsTabRoute ||
          isPricingProductsRoute),
    )

  const isDisabledModule =
    disableInTheseModules.some((module) =>
      isMatchPath(module, pathname, false),
    ) &&
    !exceptionRoutesInDisabledModules.some((module) =>
      isMatchPath(module, pathname),
    )

  return isValidProtectState && !isDisabledModule
}

export const checkOnlyCoreRoutes = (pathname: string) =>
  !(
    pathname.includes('traffic') ||
    pathname.includes('content') ||
    pathname.includes('loyalty') ||
    pathname.includes('logistics') ||
    pathname.includes('planning')
  )

export const checkOnlyTrafficRoutes = (pathname: string) =>
  pathname.includes('traffic')

export const isLoyaltyRoutes = (pathname: string) =>
  pathname?.includes('/loyalty')
export const isContentRoutes = (pathname: string) =>
  pathname?.includes('/content')
/** This method will make a copy of any nested object that doesn't contain a circular reference (to itself) */
export const copyOf = (obj: unknown) => {
  if (typeof obj === 'function') {
    throw new Error('Function type is not allowed.')
  }
  // recursively go through object and convert only moment objects to strings
  const stringifyMoments = (obj: unknown) => {
    if (typeof obj === 'object' && obj !== null) {
      for (const key in obj) {
        if (Object.prototype.hasOwnProperty.call(obj, key)) {
          const newKey = key as keyof typeof obj
          if (moment.isMoment(obj[newKey])) {
            const momentObj: moment.Moment = obj[newKey]
            obj[newKey] = momentObj.format() as never
          } else {
            stringifyMoments(obj[newKey])
          }
        }
      }
    }
  }
  stringifyMoments(obj)
  return JSON.parse(JSON.stringify(obj))
}

export const getFullCurrencyList = (apiController?: AbortController) => {
  const currencyLabel = (currency: Currency) => {
    const currencyName = currency?.name
      .split(' ')
      .map((str: string) => capitalize(str))
      .join(' ')
    return { label: `${currency.symbol} — ${currencyName}` }
  }

  const currencyList: Promise<Currency[]> = getCurrencies(apiController?.signal)
    .then((response) => {
      if (response) {
        const formattedCurrencies = response.data.map((currency: Currency) => {
          return {
            ...currency,
            type: 'Currency',
            ...currencyLabel(currency),
          }
        })

        return formattedCurrencies
      }
    })
    .catch((error) => {
      handleError(error, 'GlobalFilter.helpers.ts', 'getFullCurrencyList')
      return []
    })
  return currencyList
}

///////////////////////////////////////////////////////////////////////////////////////////////////
// TIMEFRAME HELPERS
///////////////////////////////////////////////////////////////////////////////////////////////////
export const defaultCurrentDayTimeframe = {
  type: 'current',
  display: 'Day',
  timeValue: 'day',
  aggregation: 'hour',
}

/** Trailing/Historical `Day` dictionary */
const dayTranslation: Record<string, PartialTimeframe> = {
  '24HRS': {
    display: '1D',
    timeValue: 'day',
    value: 1,
    type: 'historical',
  },
  '1D': {
    display: '24HRS',
    timeValue: 'hours',
    value: 24,
    type: 'trailing',
  },
}

/** Trailing/Historical `Week` dictionary */
const weekTranslation: Record<string, PartialTimeframe> = {
  '1W': {
    display: '7D',
    timeValue: 'day',
    value: 7,
    type: 'trailing',
  },
  '7D': {
    display: '1W',
    timeValue: 'week',
    value: 1,
    type: 'historical',
  },
}

/** Trailing/Historical `Year` dictionary */
const yearTranslation: Record<string, PartialTimeframe> = {
  '1Y': {
    display: '12M',
    timeValue: 'month',
    value: 12,
    type: 'trailing',
  },
  '12M': {
    display: '1Y',
    timeValue: 'year',
    value: 1,
    type: 'historical',
  },
}

/** Trailing/Historical ...rest (30D, 3M, 6M) dictionary; other values are the same between trailing/historical */
const restTranslation: Record<string, PartialTimeframe> = {
  trailing: {
    type: 'historical',
  },
  historical: {
    type: 'trailing',
  },
}

const trailingTimeValues: Record<string, string> = {
  '24HRS': 'hours',
  '7D': 'day',
  '30D': 'day',
  '3M': 'month',
  '6M': 'month',
  '12M': 'month',
  '1D': 'day',
  '1W': 'week',
  '1Y': 'year',
  custom: 'custom',
}
const historicalChangeTypes: Record<
  string,
  keyof ValidTimeframeAggregationsProps
> = {
  historical: 'trailing',
  trailing: 'historical',
}

type ComparisonDateRanges = {
  firstRange_startDate: MomentInput
  firstRange_endDate: MomentInput
  secondRange_startDate?: MomentInput
  secondRange_endDate?: MomentInput
}

export const getComparisonDateRanges = (
  comparisonDates?: ComparisonDateRanges,
  comparisonPeriod?: string,
): ComparisonDateRanges | undefined => {
  if (
    !comparisonDates ||
    !comparisonDates?.firstRange_endDate ||
    !comparisonDates?.firstRange_startDate
  ) {
    return comparisonDates
  }

  const {
    firstRange_startDate,
    firstRange_endDate,
    secondRange_startDate,
    secondRange_endDate,
  } = comparisonDates

  if (secondRange_startDate && secondRange_endDate) {
    // If the second date range exists, return the comparison date ranges
    return comparisonDates
  } else {
    let newSecondRange_startDate: Moment, newSecondRange_endDate: Moment
    if (comparisonPeriod !== 'previous_period') {
      newSecondRange_startDate = moment(firstRange_startDate)?.subtract(
        1,
        'year',
      )
      newSecondRange_endDate = moment(firstRange_endDate)?.subtract(1, 'year')
    } else {
      const endDate = moment(firstRange_endDate),
        startDate = moment(firstRange_startDate)

      // if the duration is the same day (0 days), set it to 1 day
      const duration = Math.max(
        moment.duration(endDate?.diff(startDate)).asDays(),
        1,
      )
      newSecondRange_startDate = startDate?.subtract(duration, 'days')
      newSecondRange_endDate = endDate?.subtract(duration, 'days')
    }
    const timeframeFormat = 'YYYY-MM-DDTHH:mm:ssZ'

    return {
      firstRange_startDate,
      firstRange_endDate,
      secondRange_startDate: newSecondRange_startDate.format(timeframeFormat),
      secondRange_endDate: newSecondRange_endDate.format(timeframeFormat),
    }
  }
}

export const checkCurrentTimeframeIsDisabled = (
  pathname: string,
  currentTimeframe: Timeframe,
  tf: (key: string) => string,
) => {
  const disabledTimeframeOptions = getDisabledTimeframeOptions(pathname, tf)

  if (!currentTimeframe) return false

  const timeframeType = currentTimeframe?.type
  const timeframeDisplay = currentTimeframe?.display.includes(' - ') // If custom dates, we get MM/DD/YYYY - MM/DD/YYYY
    ? 'custom'
    : currentTimeframe?.display.toLocaleLowerCase()

  return disabledTimeframeOptions[timeframeType]
    ?.map((i) => i.toLocaleLowerCase())
    ?.includes(timeframeDisplay)
}

//////////////////////////////////////////////////////////////////////////////////////////
/** PROVIDES OBJECT WITH ALL DISABLED TIMEFRAME AGGREGATION OPTIONS FOR THE GIVEN ROUTE */
//////////////////////////////////////////////////////////////////////////////////////////
export const getDisabledTimeframeOptions = (
  location: string,
  tf: (key: string) => string,
): DisabledTimeFrameOptions => {
  const isCoreRoute = checkOnlyCoreRoutes(location)
  const isTrafficRoute = checkOnlyTrafficRoutes(location)
  const isLoyaltyRoute = isLoyaltyRoutes(location)
  const timePeriodExtension = isCoreRoute || isTrafficRoute || isLoyaltyRoute
  const defaultDisabledOptions = {
    current: [],
    previous: [],
    trailing: [],
    historical: [],
    quarterly: [],
  }

  // Disabling Trailing 24HRS on Protect modules as we don't support time at hourly grain that do not have sales data.
  const protectPathsWithoutHourlyGrain = [
    `${productListingRoute}/:id/protect/at-a-glance`,
    `${productListingRoute}/:id/protect/buybox`,
    `${productListingRoute}/:id/protect/suppression`,
    `${productListingRoute}/:id/protect/attribution`,
    `${productListingRoute}/:id/protect/compliance`,
    `${productListingRoute}/:id/protect/at-a-glance`,
    `${productListingRoute}/:id/protect/price_changes`,
    `${productListingRoute}/:id/marketplace/:id/listing/:id/protect/at-a-glance`,
    `${productListingRoute}/:id/marketplace/:id/listing/:id/protect/buybox`,
    `${productListingRoute}/:id/marketplace/:id/listing/:id/protect/suppression`,
    `${productListingRoute}/:id/marketplace/:id/listing/:id/protect/attribution`,
    `${productListingRoute}/:id/marketplace/:id/listing/:id/protect/compliance`,
    `${productListingRoute}/:id/marketplace/:id/listing/:id/protect/price_changes`,
    `${sellersCoreRoute}/:id/protect/at-a-glance`,
    `${sellersCoreRoute}/:id/protect/buybox`,
    `${sellersCoreRoute}/:id/protect/compliance`,
  ]
  if (
    protectPathsWithoutHourlyGrain.some((path) => isMatchPath(path, location))
  )
    return {
      ...defaultDisabledOptions,
      [timePeriodExtension ? 'trailing' : 'historical']: [
        timePeriodExtension ? '24HRS' : '1D',
      ],
    }

  // Disabling 'Day', '1D' & '24HRS' options for Content module since we don't support time at hourly grain
  if (
    [
      '/content',
      `${productListingRoute}/:id/marketplace/:id/listing/:id/content/*`,
    ].some((path) => isMatchPath(path, location, false))
  )
    return {
      ...defaultDisabledOptions,
      current: ['Day'],
      historical: ['1D'],
      previous: ['Day'],
      trailing: ['24HRS'],
    }

  // Loyalty SnS page Global timeframe limitations
  if (
    [
      '/loyalty/SnS',
      `${productListingRoute}/:id/loyalty/SnS`,
      `${productListingRoute}/:id/marketplace/:id/listing/:id/loyalty/SnS`,
    ].some((path) => isMatchPath(path, location, false))
  ) {
    return {
      ...defaultDisabledOptions,
      current: ['Day'],
      ...(timePeriodExtension ? { previous: ['Day'] } : {}),
      [timePeriodExtension ? 'trailing' : 'historical']: [
        timePeriodExtension ? '24HRS' : '1D',
      ],
    }
  }
  // Loyalty LTV page Global timeframe limitations
  if (
    [
      '/loyalty/ltv',
      `${productListingRoute}/:id/loyalty/ltv`,
      `${productListingRoute}/:id/marketplace/:id/listing/:id/loyalty/ltv`,
    ].some((path) => isMatchPath(path, location, false))
  ) {
    return {
      ...defaultDisabledOptions,
      current: ['Day', 'Week', 'Month'],
      ...(timePeriodExtension ? { previous: ['Day', 'Week', 'Month'] } : {}),
      trailing: ['24HRS', '7D', '30D'],
    }
  }

  // Destiny 2.0 Preview page Global timeframe limitations
  if (
    ['/traffic/destiny-automation/preview'].some((path) =>
      isMatchPath(path, location, false),
    )
  ) {
    return {
      ...defaultDisabledOptions,
      current: ['Day', 'Week', 'Month', 'Year'],
      previous: ['Day', 'Week', 'QTR', 'Year'],
      trailing: ['24HRS', '7D', '30D', '3M', '6M', '12M'],
      quarterly: ['1Y', 'Q1', 'Q2', 'Q3', 'Q4'],
    }
  }

  // Advisory > Price Segment && Advisory > Market Analytics Routes Disable Options
  if (
    ['/advisory/price-segments', '/advisory/market-analytics'].some((path) =>
      isMatchPath(path, location, false),
    )
  ) {
    return {
      ...defaultDisabledOptions,
      current: ['Day', 'Week', 'Month'],
      previous: ['Day', 'Week', 'Month'],
      trailing: ['24HRS', '7D', '30D', 'custom'], // custom is for the date range picker
    }
  }

  // Same as above Advisory > Price Segment Disable Options
  if (isMatchPath('/insights/share/price-segments', location)) {
    return {
      ...defaultDisabledOptions,
      current: ['Day', 'Week', 'Month'],
      previous: ['Day', 'Week', 'Month'],
      trailing: ['24HRS', '7D', '30D', 'custom'], // custom is for the date range picker
    }
  }

  // Global Filters timeframe limitations for all MARKETSHARE Routes, Product Listing > Share Tab Routes and Second External Product Listing > Share Tab Routes
  if (
    [
      '/insights/share/marketshare',
      `${productListingRoute}/:productId/marketplace/:marketplaceId/listing/:listingId/share/*`,
      `/insights/share/product-details/:asin/:country_code/share/*`,
      `/insights/share/external-listing/:asin/share/*`,
    ].some((path) => isMatchPath(path, location, false))
  ) {
    return {
      ...defaultDisabledOptions,
      current: ['Day', 'Week', 'Month'],
      previous: ['Day', 'Week', 'Month'],
      trailing: ['24HRS', '7D', '30D', 'custom'], // custom is for the date range picker
    }
  }

  // Disabling 'Day' & '24HRS' options for Customer Serivce Module
  if (isMatchPath('/custom-service/*', location, false)) {
    return {
      ...defaultDisabledOptions,
      current: ['Day'],
      previous: ['Day'],
      trailing: ['24HRS'],
    }
  }

  switch (location) {
    case '/insights/attainment-reports':
    case '/reports/attainment_reports':
      return {
        ...defaultDisabledOptions,
        current: [tf('day'), tf('week'), tf('month'), tf('year')],
        ...(timePeriodExtension
          ? {
              previous: [
                tf('day'),
                tf('week'),
                tf('month'),
                tf('quarter'),
                tf('year'),
              ],
            }
          : {}),
        [timePeriodExtension ? 'trailing' : 'historical']: [
          timePeriodExtension ? '24HRS' : '1D',
          timePeriodExtension ? '7D' : '1W',
          '30D',
          '3M',
          '6M',
          timePeriodExtension ? '12M' : '1Y',
        ],
      }

    // Disabling Trailing 24HRS on Protect module as we don't support time at hourly grain that do not have sales data.
    case '/protect/at-a-glance':
    case '/protect/buybox/sellers':
    case '/protect/buybox/products':
    case '/protect/buybox/suppression':
    case '/protect/buybox/attribution':
    case '/protect/selective-distribution':
    case '/protect/compliance/sellers':
    case '/protect/compliance/products':
    case '/protect/compliance/marketplaces':
      return {
        ...defaultDisabledOptions,
        [timePeriodExtension ? 'trailing' : 'historical']: [
          timePeriodExtension ? '24HRS' : '1D',
        ],
      }

    default:
      return defaultDisabledOptions
  }
}

////////////////////////////////////////////////////////////////////////////////
/** PROVIDES OBJECT WITH ALL VALID TIMEFRAME AGGREGATIONS FOR THE GIVEN ROUTE */
////////////////////////////////////////////////////////////////////////////////
export const getValidTimeframeAggregations = (
  locationPathname: string,
  tf: (key: string) => string,
): NonNullable<ValidTimeframeAggregationsProps> => {
  const isCoreRoute = checkOnlyCoreRoutes(locationPathname)
  const isTrafficRoute = checkOnlyTrafficRoutes(locationPathname)
  const isLoyaltyRoute = isLoyaltyRoutes(locationPathname)
  const isContentRoute = isContentRoutes(locationPathname)
  const timePeriodExtension =
    isCoreRoute || isTrafficRoute || isLoyaltyRoute || isContentRoute

  // custom aggregations for Seller, Listing and Master product details
  const productDetailsPaths = [
    `${productListingRoute}/:id/marketplace/:id/listing/:id/protect/at-a-glance`,
    `${productListingRoute}/:id/marketplace/:id/listing/:id/protect/attribution`,
    `${productListingRoute}/:id/marketplace/:id/listing/:id/protect/buybox`,
    `${productListingRoute}/:id/marketplace/:id/listing/:id/protect/compliance`,
    `${productListingRoute}/:id/marketplace/:id/listing/:id/protect/price_changes`,
    `${productListingRoute}/:id/marketplace/:id/listing/:id/protect/suppression`,
    `${productListingRoute}/:id/marketplace/:id/listing/:id/protect/price`,
    `${productListingRoute}/:id/marketplace/:id/listing/:id/overview`,
    `${productListingRoute}/:id/overview`,
    `${productListingRoute}/:id/protect/at-a-glance`,
    `${productListingRoute}/:id/protect/attribution`,
    `${productListingRoute}/:id/protect/buybox`,
    `${productListingRoute}/:id/protect/price`,
    `${productListingRoute}/:id/protect/compliance`,
    `${productListingRoute}/:id/protect/price_changes`,
    `${productListingRoute}/:id/protect/suppression`,
    `${sellersCoreRoute}/:id/protect/at-a-glance`,
    `${sellersCoreRoute}/:id/protect/buybox`,
    `${sellersCoreRoute}/:id/protect/compliance`,
  ]
  if (productDetailsPaths.some((path) => isMatchPath(path, locationPathname))) {
    return {
      current: {
        day: [],
        week: ['day'],
        month: ['day', 'week'],
        year: ['month', 'qtr', 'day', 'week'],
      },
      ...(timePeriodExtension
        ? {
            previous: {
              day: [],
              week: ['day'],
              month: ['day', 'week'],
              quarter: ['month', 'day', 'week'],
              year: ['month', 'qtr', 'day', 'week'],
            },
          }
        : {}),
      [timePeriodExtension ? 'trailing' : 'historical']: {
        ...(timePeriodExtension ? { '24HRS': [] } : { '1D': [] }),
        ...(timePeriodExtension ? { '7D': ['day'] } : { '1W': ['day'] }),
        '30D': ['day', 'week'],
        '3M': ['week', 'day', 'month'],
        '6M': ['week', 'day', 'month'],
        ...(timePeriodExtension
          ? { '12M': ['month', 'day', 'week', 'qtr'] }
          : { '1Y': ['month', 'day', 'week', 'qtr'] }),
        custom: [
          {
            minDayDifference: 0,
            maxDayDifference: 1,
            aggregations: [],
          },
          {
            minDayDifference: 2,
            maxDayDifference: 60,
            aggregations: ['day', 'week'],
          },
          {
            minDayDifference: 61,
            maxDayDifference: 365,
            aggregations: ['week', 'day', 'month'],
          },
          {
            minDayDifference: 366,
            aggregations: ['month', 'day', 'week', 'qtr'],
          },
        ],
      },
      quarterly: {
        year: ['week', 'day', 'month', 'qtr'],
        quarter: ['month', 'day', 'week'],
      },
    }
  }

  if (
    ['/traffic/destiny-automation/preview'].some((path) =>
      isMatchPath(path, locationPathname, false),
    )
  ) {
    return {
      current: {
        day: [],
      },
      previous: {
        day: [],
        week: [],
        month: ['day', 'week'],
      },
      quarterly: {
        year: [],
        quarter: [],
      },
    }
  }
  // Disabling 'Day', '1D' & '24HRS' options for Content module since we don't support time at hourly grain
  const retailReadinessRoutes = [
    '/content/retail-readiness',
    `${productListingRoute}/:id/marketplace/:id/listing/:id/content/details/retail-readiness`,
  ]
  const anyRetailReadinessRoute = retailReadinessRoutes.some((path) =>
    isMatchPath(path, locationPathname, false),
  )
  if (anyRetailReadinessRoute) {
    return {
      current: {
        day: [],
        week: ['day'],
        month: ['day', 'week'],
        year: ['month', 'day', 'week', 'qtr'],
      },
      ...(timePeriodExtension
        ? {
            previous: {
              day: [],
              week: ['day'],
              month: ['day', 'week'],
              quarter: ['month', 'day', 'week'],
              year: ['month', 'qtr', 'day', 'week'],
            },
          }
        : {}),
      [timePeriodExtension ? 'trailing' : 'historical']: {
        ...(timePeriodExtension ? { '24HRS': [] } : { '1D': [] }),
        ...(timePeriodExtension ? { '7D': ['day'] } : { '1W': ['day'] }),
        '30D': ['day', 'week'],
        '3M': ['week', 'day', 'month'],
        '6M': ['week', 'day', 'month'],
        ...(timePeriodExtension
          ? {
              '12M': ['month', 'day', 'week'],
            }
          : {
              '1Y': ['month', 'day', 'week', 'qtr'],
            }),
        custom: [
          {
            minDayDifference: 0,
            maxDayDifference: 2,
            aggregations: [],
          },
          {
            minDayDifference: 3,
            maxDayDifference: 60,
            aggregations: ['day', 'week'],
          },
          {
            minDayDifference: 61,
            maxDayDifference: 180,
            aggregations: ['week', 'day', 'month'],
          },
          {
            minDayDifference: 181,
            maxDayDifference: 365,
            aggregations: ['week', 'day', 'month', 'qtr'],
          },
          {
            minDayDifference: 366,
            aggregations: ['month', 'day', 'week', 'qtr'],
          },
        ],
      },
      quarterly: {
        year: ['week', 'day', 'month', 'qtr'],
        quarter: ['month', 'day', 'week'],
      },
    }
  } else if (locationPathname.includes('/content')) {
    return {
      current: {
        day: [],
        week: ['day'],
        month: ['day', 'week'],
        year: ['month', 'day', 'week', 'qtr'],
      },
      ...(timePeriodExtension
        ? {
            previous: {
              day: [],
              week: ['day'],
              month: ['day', 'week'],
              quarter: ['month', 'day', 'week'],
              year: ['month', 'qtr', 'day', 'week'],
            },
          }
        : {}),
      [timePeriodExtension ? 'trailing' : 'historical']: {
        ...(timePeriodExtension ? { '24HRS': [] } : { '1D': [] }),
        ...(timePeriodExtension ? { '7D': ['day'] } : { '1W': ['day'] }),
        '30D': ['day', 'week'],
        '3M': ['week', 'day', 'month'],
        '6M': ['week', 'day', 'month', timePeriodExtension ? 'qtr' : ''],
        ...(timePeriodExtension
          ? { '12M': ['month', 'day', 'week', 'qtr'] }
          : { '1Y': ['month', 'day', 'week', 'qtr'] }),
        custom: [
          {
            minDayDifference: 0,
            maxDayDifference: 2,
            aggregations: [],
          },
          {
            minDayDifference: 3,
            maxDayDifference: 60,
            aggregations: ['day', 'week'],
          },
          {
            minDayDifference: 61,
            maxDayDifference: 180,
            aggregations: ['week', 'day', 'month'],
          },
          {
            minDayDifference: 181,
            maxDayDifference: 365,
            aggregations: ['week', 'day', 'month', 'qtr'],
          },
          {
            minDayDifference: 366,
            aggregations: [
              'month',
              'day',
              'week',
              timePeriodExtension ? 'qtr' : '',
            ],
          },
        ],
      },
      quarterly: {
        year: ['week', 'day', 'month', 'qtr'],
        quarter: ['month', 'day', 'week'],
      },
    }
  }

  /** Disabled qtr aggregation option for Traffic module */
  if (locationPathname.includes('/traffic')) {
    return {
      current: {
        day: ['hour'],
        week: ['day'],
        month: ['day', 'week'],
        year: ['month', 'day', 'week', 'qtr'],
      },
      ...(timePeriodExtension
        ? {
            previous: {
              day: ['hour'],
              week: ['day'],
              month: ['day', 'week'],
              quarter: ['month', 'day', 'week'],
              year: ['month', 'day', 'week', 'qtr'],
            },
          }
        : {}),
      [timePeriodExtension ? 'trailing' : 'historical']: {
        ...(timePeriodExtension ? { '24HRS': ['hour'] } : { '1D': ['hour'] }),
        ...(timePeriodExtension ? { '7D': ['day'] } : { '1W': ['day'] }),
        '30D': ['day', 'week'],
        '3M': ['week', 'day', 'month'],
        '6M': ['week', 'day', 'month', timePeriodExtension ? 'qtr' : ''],
        ...(timePeriodExtension
          ? { '12M': ['month', 'day', 'week', 'qtr'] }
          : { '1Y': ['month', 'day', 'week'] }),
        custom: [
          {
            minDayDifference: 0,
            maxDayDifference: 1,
            aggregations: ['hour'],
          },
          {
            minDayDifference: 2,
            maxDayDifference: 60,
            aggregations: ['day', 'week'],
          },
          ...(timePeriodExtension
            ? [
                {
                  minDayDifference: 61,
                  maxDayDifference: 180,
                  aggregations: ['week', 'day', 'month'],
                },
                {
                  minDayDifference: 181,
                  maxDayDifference: 365,
                  aggregations: ['week', 'day', 'month', 'qtr'],
                },
              ]
            : [
                {
                  minDayDifference: 61,
                  maxDayDifference: 365,
                  aggregations: ['week', 'day', 'month'],
                },
              ]),
          {
            minDayDifference: 366,
            aggregations: [
              'month',
              'day',
              'week',
              timePeriodExtension ? 'qtr' : '',
            ],
          },
        ],
      },
      quarterly: {
        year: ['week', 'day', 'month', timePeriodExtension ? 'qtr' : ''],
        quarter: ['month', 'day', 'week'],
      },
    }
  }

  /** Disabled qtr and hour aggregation option for logistic -  History tab */
  if (locationPathname.includes(HISTORY_TAB_PATH)) {
    return {
      current: {
        day: [''],
        week: ['day'],
        month: ['day', 'week'],
        year: ['day', 'week', 'month'],
      },
      ...(timePeriodExtension
        ? {
            previous: {
              day: [''],
              week: ['day'],
              month: ['day', 'week'],
              quarter: ['day', 'week', 'month'],
              year: ['day', 'week', 'month'],
            },
          }
        : {}),
      [timePeriodExtension ? 'trailing' : 'historical']: {
        ...(timePeriodExtension ? { '24HRS': [''] } : { '1D': [''] }),
        ...(timePeriodExtension ? { '7D': ['day'] } : { '1W': ['day'] }),
        '30D': ['day', 'week'],
        '3M': ['day', 'week', 'month'],
        '6M': ['day', 'week', 'month'],
        ...(timePeriodExtension
          ? { '12M': ['day', 'week', 'month'] }
          : { '1Y': ['day', 'week', 'month'] }),
        custom: [
          {
            minDayDifference: 0,
            maxDayDifference: 1,
            aggregations: [],
          },
          {
            minDayDifference: 1,
            maxDayDifference: 60,
            aggregations: ['day', 'week', 'month'],
          },
          ...(timePeriodExtension
            ? [
                {
                  minDayDifference: 61,
                  maxDayDifference: 180,
                  aggregations: ['day', 'week', 'month'],
                },
                {
                  minDayDifference: 181,
                  maxDayDifference: 365,
                  aggregations: ['day', 'month', 'week'],
                },
              ]
            : [
                {
                  minDayDifference: 1,
                  maxDayDifference: 365,
                  aggregations: ['day', 'month', 'week'],
                },
              ]),
          {
            minDayDifference: 366,
            aggregations: ['day', 'month', 'week'],
          },
        ],
      },
      quarterly: {
        year: ['day'],
        quarter: ['day'],
      },
    }
  }

  // Loyalty SnS page Global timeframe limitations
  if (locationPathname.includes('/loyalty/SnS')) {
    return {
      current: {
        day: [],
        week: ['week'],
        month: ['week'],
        year: ['week'],
      },
      ...(timePeriodExtension
        ? {
            previous: {
              day: [],
              week: ['week'],
              month: ['week'],
              quarter: ['week'],
              year: ['week'],
            },
          }
        : {}),
      [timePeriodExtension ? 'trailing' : 'historical']: {
        ...(timePeriodExtension ? { '24HRS': [] } : { '1D': [] }),
        ...(timePeriodExtension ? { '7D': ['week'] } : { '1W': ['week'] }),
        '30D': ['week'],
        '3M': ['week'],
        '6M': ['week'],
        ...(timePeriodExtension ? { '12M': ['week'] } : { '1Y': ['week'] }),
        // TODO: Still need to alter logic in upcoming PRs once requirements are clear.
        custom: [
          {
            minDayDifference: 7,
            maxDayDifference: 63,
            aggregations: ['week'],
          },
          {
            minDayDifference: 63,
            maxDayDifference: 365,
            aggregations: ['week'],
          },
          { minDayDifference: 366, aggregations: ['week'] },
        ],
      },
      quarterly: {
        year: ['week'],
        quarter: ['week'],
      },
    }
  }
  // Loyalty LTV page Global timeframe limitations
  if (locationPathname.includes('/loyalty/ltv')) {
    return {
      current: {
        day: [],
        week: [],
        month: [],
        year: ['month'],
      },
      ...(timePeriodExtension
        ? {
            previous: {
              day: [],
              week: [],
              month: [],
              quarter: ['month'],
              year: ['month'],
            },
          }
        : {}),
      [timePeriodExtension ? 'trailing' : 'historical']: {
        ...(timePeriodExtension ? { '24HRS': [] } : { '1D': [] }),
        ...(timePeriodExtension ? { '7D': [] } : { '1W': [] }),
        '30D': [],
        '3M': ['month'],
        '6M': ['month'],
        ...(timePeriodExtension ? { '12M': ['month'] } : { '1Y': ['month'] }),
        // TODO: Still need to alter logic in upcoming PRs once requirements are clear.
        custom: [
          {
            minDayDifference: 1,
            maxDayDifference: 30,
            aggregations: ['month'],
          },
          {
            minDayDifference: 31,
            maxDayDifference: 62,
            aggregations: ['month'],
          },
          {
            minDayDifference: 63,
            maxDayDifference: 365,
            aggregations: ['month'],
          },
          { minDayDifference: 366, aggregations: ['month'] },
        ],
      },
      quarterly: {
        year: ['month'],
        quarter: ['month'],
      },
    }
  }

  /** Advisory > PriceSegments */
  /** Insights > Share > PriceSegments */
  if (locationPathname.includes('price-segments')) {
    return {
      current: {
        day: [],
        week: [],
        month: [],
        year: ['month'],
      },
      previous: {
        day: [],
        week: [],
        month: [],
        quarter: ['month'],
        year: ['month'],
      },
      trailing: {
        '24HRS': [],
        '7D': [],
        '30D': [],
        '3M': ['month'],
        '6M': ['month'],
        '12M': ['month'],
      },
      quarterly: {
        year: ['month'],
        quarter: ['month'],
      },
    }
  }

  /** Advisory > Market Analytics */
  if (locationPathname.includes('market-analytics')) {
    return {
      current: {
        day: [],
        week: [],
        month: [],
        year: ['month', 'qtr'],
      },
      previous: {
        day: [],
        week: [],
        month: [],
        quarter: ['month'],
        year: ['month', 'qtr'],
      },
      trailing: {
        '24HRS': [],
        '7D': [],
        '30D': [],
        '3M': ['month', 'qtr'],
        '6M': ['month', 'qtr'],
        '12M': ['month', 'qtr'],
      },
      quarterly: {
        year: ['month', 'qtr'],
        quarter: ['month'],
      },
    }
  }

  /** SHARE > AT A GLANCE PAGE */
  /** Product Listing > Share Tab */
  /** External Product Listing > Share Tab */
  /** Second External Product Listing > Share Tab */
  if (
    locationPathname.includes('/marketshare') ||
    [
      `${productListingRoute}/:productId/marketplace/:marketplaceId/listing/:listingId/share/*`,
      `/insights/share/product-details/:asin/:country_code/share/*`,
      `/insights/share/external-listing/:asin/share/*`,
    ].some((path) => isMatchPath(path, locationPathname))
  ) {
    return {
      current: {
        day: [],
        week: [],
        month: [],
        year: ['month', 'qtr'],
      },
      previous: {
        day: [],
        week: [],
        month: [],
        quarter: ['month'],
        year: ['month', 'qtr'],
      },
      trailing: {
        '24HRS': [],
        '7D': [],
        '30D': [],
        '3M': ['month', 'qtr'],
        '6M': ['month', 'qtr'],
        '12M': ['month', 'qtr'],
      },
      quarterly: {
        year: ['month', 'qtr'],
        quarter: ['month'],
      },
    }
  }

  switch (locationPathname) {
    /** Insights Page */
    case '/insights':
      return {
        current: {
          day: ['hour'],
          week: ['day'],
          month: ['day', 'week'],
          year: ['month', 'day', 'week', 'qtr'],
        },
        previous: {
          day: ['hour'],
          week: ['day'],
          month: ['day', 'week'],
          quarter: ['month', 'day', 'week'],
          year: ['month', 'day', 'week', 'qtr'],
        },
        [timePeriodExtension ? 'trailing' : 'historical']: {
          ...(timePeriodExtension ? { '24HRS': ['hour'] } : { '1D': ['hour'] }),
          ...(timePeriodExtension ? { '7D': ['day'] } : { '1W': ['day'] }),
          '30D': ['day', 'week'],
          '3M': ['day', 'week', 'month', 'qtr'],
          '6M': ['day', 'week', 'month', 'qtr'],
          ...(timePeriodExtension
            ? {
                '12M': ['month', 'day', 'week', 'qtr'],
              }
            : {
                '1Y': ['month', 'day', 'week', 'qtr'],
              }),
          custom: [
            {
              minDayDifference: 0,
              maxDayDifference: 1,
              aggregations: ['hour'],
            },
            {
              minDayDifference: 2,
              maxDayDifference: 60,
              aggregations: ['day', 'week'],
            },
            {
              minDayDifference: 61,
              maxDayDifference: 365,
              aggregations: ['month', 'day', 'week', 'qtr'],
            },
            {
              minDayDifference: 366,
              aggregations: ['month', 'day', 'week', 'qtr'],
            },
          ],
        },
        quarterly: {
          year: ['month', 'day', 'week', 'qtr'],
          quarter: ['week', 'day', 'month'],
        },
      }

    /** Insights Report */
    case '/insights/insights-report':
    case '/insights/insights-report/brand':
    case '/insights/insights-report/product':
    case '/insights/insights-report/marketplace':
    case '/insights/insights-report/category':
      // NOTE: disabling `day` aggregation for year+ time frames (download data limitation)
      return {
        current: {
          day: ['hour'],
          week: ['day'],
          month: ['day', 'week'],
          year: ['month', 'week', 'qtr'],
        },
        previous: {
          day: ['hour'],
          week: ['day'],
          month: ['day', 'week'],
          quarter: ['month', 'day', 'week'],
          year: ['month', 'week', 'qtr'],
        },
        [timePeriodExtension ? 'trailing' : 'historical']: {
          ...(timePeriodExtension ? { '24HRS': ['hour'] } : { '1D': ['hour'] }),
          ...(timePeriodExtension ? { '7D': ['day'] } : { '1W': ['day'] }),
          '30D': ['day', 'week'],
          '3M': ['day', 'week', 'month', 'qtr'],
          '6M': ['day', 'week', 'month', 'qtr'],
          ...(timePeriodExtension
            ? { '12M': ['month', 'week', 'qtr'] }
            : { '1Y': ['month', 'week', 'qtr'] }),
          custom: [
            {
              minDayDifference: 0,
              maxDayDifference: 1,
              aggregations: ['hour'],
            },
            {
              minDayDifference: 2,
              maxDayDifference: 60,
              aggregations: ['day', 'week'],
            },
            {
              minDayDifference: 61,
              maxDayDifference: 365,
              aggregations: ['month', 'week', 'qtr'],
            },
            {
              minDayDifference: 366,
              aggregations: ['month', 'week', 'qtr'],
            },
          ],
        },
        quarterly: {
          year: ['month', 'week', 'qtr'],
          quarter: ['week', 'day', 'month'],
        },
      }
    case '/insights/attainment-reports':
    case '/reports/attainment_reports':
      return {
        current: {
          week: ['day'],
          month: ['day'],
          year: ['month', 'day'],
        },
        previous: {
          week: ['day'],
          month: ['day'],

          quarter: ['month'],
          year: ['month', 'day'],
        },
        quarterly: {
          year: ['month'],
          quarter: ['month'],
        },
      }
    /** Disabled hourly aggregation option for Protect module */
    case '/protect/at-a-glance':
    case '/protect/price/products':
    case '/protect/buybox/brands':
    case '/protect/buybox/attribution':
    case '/protect/buybox/products':
    case '/protect/buybox/sellers':
    case '/protect/buybox/suppression':
    case '/protect/compliance/marketplaces':
    case '/protect/compliance/products':
    case '/protect/compliance/sellers':
      return {
        current: {
          day: [],
          week: ['day'],
          month: ['day', 'week'],
          year: ['month', 'qtr', 'day', 'week'],
        },
        previous: {
          day: [],
          week: ['day'],
          month: ['day', 'week'],
          quarter: ['month', 'day', 'week'],
          year: ['month', 'day', 'week', 'qtr'],
        },
        [timePeriodExtension ? 'trailing' : 'historical']: {
          ...(timePeriodExtension ? { '24HRS': [] } : { '1D': [] }),
          ...(timePeriodExtension ? { '7D': ['day'] } : { '1W': ['day'] }),
          '30D': ['day', 'week'],
          '3M': ['week', 'day', 'month'],
          '6M': ['week', 'day', 'month'],
          ...(timePeriodExtension
            ? { '12M': ['month', 'day', 'week', 'qtr'] }
            : { '1Y': ['month', 'day', 'week', 'qtr'] }),
          custom: [
            {
              minDayDifference: 0,
              maxDayDifference: 1,
              aggregations: [],
            },
            {
              minDayDifference: 2,
              maxDayDifference: 60,
              aggregations: ['day', 'week'],
            },
            {
              minDayDifference: 61,
              maxDayDifference: 365,
              aggregations: ['week', 'day', 'month'],
            },
            {
              minDayDifference: 366,
              aggregations: ['month', 'day', 'week', 'qtr'],
            },
          ],
        },
        quarterly: {
          year: ['week', 'day', 'month', 'qtr'],
          quarter: ['month', 'day', 'week'],
        },
      }

    default:
      return {
        current: {
          day: ['hour'],
          week: ['day'],
          month: ['day', 'week'],
          year: ['month', 'day', 'week'],
        },
        ...(timePeriodExtension
          ? {
              previous: {
                day: ['hour'],
                week: ['day'],
                month: ['day', 'week'],
                quarter: ['month', 'day', 'week'],
                year: ['month', 'day', 'week', 'qtr'],
              },
            }
          : {}),
        [timePeriodExtension ? 'trailing' : 'historical']: {
          ...(timePeriodExtension ? { '24HRS': ['hour'] } : { '1D': ['hour'] }),
          ...(timePeriodExtension ? { '7D': ['day'] } : { '1W': ['day'] }),
          '30D': ['day', 'week'],
          '3M': ['week', 'day', 'month'],
          '6M': ['week', 'day', 'month'],
          ...(timePeriodExtension
            ? { '12M': ['month', 'day', 'week', 'qtr'] }
            : { '1Y': ['month', 'day', 'week', 'qtr'] }),
          custom: [
            {
              minDayDifference: 0,
              maxDayDifference: 1,
              aggregations: ['hour'],
            },
            {
              minDayDifference: 2,
              maxDayDifference: 60,
              aggregations: ['day', 'week'],
            },
            {
              minDayDifference: 61,
              maxDayDifference: 365,
              aggregations: ['week', 'day', 'month'],
            },
            {
              minDayDifference: 366,
              aggregations: ['month', 'day', 'week', 'qtr'],
            },
          ],
        },
        quarterly: {
          year: ['week', 'day', 'month', 'qtr'],
          quarter: ['month', 'day', 'week'],
        },
      }
  }
}

export function getValidAggregationsForHistoricalTimeframe({
  timeframe,
  value,
  startDate,
  endDate,
  customAggregations,
  timePeriodExtension,
  pathname,
}: ValidAggregationsProps) {
  const isCoreRoute = checkOnlyCoreRoutes(pathname)
  const isTrafficRoute = checkOnlyTrafficRoutes(pathname)
  // The first item in the array will be the default for each timeValue
  const { timeValue } = timeframe
  if (
    timeValue === 'hours' &&
    value === 24 &&
    timePeriodExtension &&
    (isCoreRoute || isTrafficRoute)
  )
    return customAggregations ? customAggregations['24HRS'] : ['hour']

  if (timeValue === 'day') {
    if (value === 1)
      return customAggregations ? customAggregations['1D'] : ['hour']

    if (value === 7 && timePeriodExtension && (isCoreRoute || isTrafficRoute))
      return customAggregations ? customAggregations['7D'] : ['day']

    return customAggregations ? customAggregations['30D'] : ['day', 'week']
  }
  if (timeValue === 'week')
    return customAggregations ? customAggregations['1W'] : ['day']
  if (timeValue === 'month') {
    if (value === 3 && customAggregations) return customAggregations['3M']
    if (value === 6 && customAggregations) return customAggregations['6M']
    if (value === 12 && timePeriodExtension && (isCoreRoute || isTrafficRoute))
      return customAggregations
        ? customAggregations['12M']
        : ['month', 'week', 'day', 'qtr']

    return ['week', 'day', 'month']
  }
  if (timeValue === 'year')
    return customAggregations
      ? customAggregations['1Y']
      : ['month', 'day', 'week', 'qtr']

  if (timeValue === 'custom') {
    if (!moment.isMoment(startDate)) {
      startDate = moment(startDate)
    }
    if (!moment.isMoment(endDate)) {
      endDate = moment(endDate)
    }

    const dayDifference = endDate.diff(startDate, 'days')

    if (customAggregations?.custom) {
      for (const obj of customAggregations.custom) {
        if (obj.maxDayDifference) {
          if (
            dayDifference >= (obj.minDayDifference ?? 0) && // Updated condition to '>=' if same day is selected as start date and end date
            dayDifference < obj.maxDayDifference
          ) {
            return obj.aggregations
          }
        } else if (
          dayDifference > (obj.minDayDifference && obj.minDayDifference)
        ) {
          return obj.aggregations
        }
      }
    }

    if (dayDifference < 2) return ['hour']
    if (dayDifference >= 2 && dayDifference < 60) return ['day', 'week']
    if (dayDifference >= 60 && dayDifference < 365)
      return ['week', 'day', 'month']
    if (dayDifference >= 365) return ['month', 'day', 'week']
  }

  return []
}

export const getValidAggregation = (
  timeframe: Timeframe,
  validAggregationOptions: ValidTimeframeAggregationsProps,
): Timeframe['aggregation'] | undefined => {
  const currentAggregation = timeframe.aggregation
  const timeframeType: keyof ValidTimeframeAggregationsProps = timeframe.type
  const aggregationOptions: Record<string, string[]> = validAggregationOptions[
    timeframeType
  ] ?? { [timeframe.timeValue]: [] }

  switch (timeframeType) {
    case 'current':
    case 'previous':
    case 'quarterly':
    case 'historical':
    case 'trailing':
      if (timeframe.timeValue !== 'custom') {
        const options =
          aggregationOptions[
            timeframeType === 'trailing'
              ? timeframe.display
              : timeframe.timeValue
          ]
        return options?.includes(timeframe.aggregation)
          ? timeframe.aggregation
          : options?.[0]
      } else {
        const dateRangeString =
          timeframe.value && typeof timeframe.value === 'string'
            ? timeframe.value
            : ''
        const [startDate, endDate] = dateRangeString.split(' - ')
        const dayDifference = moment(endDate).diff(moment(startDate), 'days')
        const customAggregationOptions =
          aggregationOptions?.custom as unknown as Array<{
            minDayDifference: number
            maxDayDifference?: number
            aggregations: string[]
          }>
        const customAggregationOption = customAggregationOptions?.find(
          (option) => {
            if (option?.maxDayDifference) {
              return (
                dayDifference >= option.minDayDifference &&
                dayDifference <= option.maxDayDifference
              )
            } else {
              return dayDifference > option.minDayDifference
            }
          },
        )

        const options = customAggregationOption?.aggregations ?? []
        return options?.includes(timeframe.aggregation)
          ? timeframe.aggregation
          : options?.[0]
      }

    default:
      return currentAggregation
  }
}

export const insightsReportRoutes = [
  '/insights/insights-report',
  '/insights/insights-report/product',
  '/insights/insights-report/marketplace',
  '/insights/insights-report/category',
  '/insights/insights-report/brand',
  '/insights/insights-report/store',
]

export const insightsVendorAnalyticsRoute = '/insights/1p-vendor-analytics/*'

export function renderAggregations(locationPathname: string) {
  const dashboardPaths = [
      '/insights',
      ...insightsReportRoutes,
      '/insights/attainment-reports',
      '/insights/share/marketshare/*',
      insightsVendorAnalyticsRoute,
      '/advisory/market-analytics/*',
      HISTORY_TAB_PATH,
    ],
    isDashboardRoute = dashboardPaths.some((path) =>
      isMatchPath(path, locationPathname),
    )
  const protectBuyBoxPaths = [
      '/protect/buybox/sellers',
      '/protect/buybox/brands',
      '/protect/buybox/products',
      '/protect/buybox/suppression',
      `${productListingRoute}/:id/protect/buybox`,
      `${productListingRoute}/:id/protect/suppression`,
      `${productListingRoute}/:id/marketplace/:id/listing/:id/protect/buybox`,
      `${productListingRoute}/:id/marketplace/:id/listing/:id/protect/suppression`,
      `${sellersCoreRoute}/:id/protect/buybox`,
    ],
    isProtectBuyboxRoute = protectBuyBoxPaths.some((path) =>
      isMatchPath(path, locationPathname),
    )

  const retailReadinessRoutes = [
    '/content/retail-readiness',
    `${productListingRoute}/:id/marketplace/:id/listing/:id/content/details/retail-readiness`,
  ]
  const isRetailReadinessRoute = retailReadinessRoutes.some((path) =>
      isMatchPath(path, locationPathname, false),
    ),
    isConversionReportRoute = isMatchPath(
      '/content/conversion',
      locationPathname,
    ),
    reviewRoutes = ['/content/reviews/*'],
    isReviewsRoute = reviewRoutes.some((path) =>
      isMatchPath(path, locationPathname, false),
    ),
    isAtGlanceContentRoute = isMatchPath(
      '/content/at-a-glance',
      locationPathname,
    ),
    isProductAtGlanceContentRoute = isMatchPath(
      `${productListingRoute}/:id/marketplace/:id/listing/:id/content/details/at-a-glance`,
      locationPathname,
    ),
    isProductConversionReportRoute = isMatchPath(
      `${productListingRoute}/:id/marketplace/:id/listing/:id/content/details/conversion`,
      locationPathname,
    ),
    rankTrackerRoutes = ['/content/rank-tracker/*'],
    isRankTrackerRoute = rankTrackerRoutes.some((path) =>
      isMatchPath(path, locationPathname, false),
    )
  const trafficRoutes = [
      '/traffic',
      `${productListingRoute}/:id/traffic`,
      `${productListingRoute}/:id/marketplace/:id/listing/:id/traffic`,
    ],
    advertisingRouteValid =
      trafficRoutes.some((path) =>
        isMatchPath(path, locationPathname, false),
      ) &&
      !locationPathname.includes('/traffic/destiny'.concat('/opportunities'))
  const isAdTabPerformancePaths = [
      '/traffic/reports/product-performance-report/:type',
      '/traffic/reports/keywords-performance-report/:marketProductId/:type',
    ],
    isAdTabPerformanceRoute = isAdTabPerformancePaths.some((path) =>
      isMatchPath(path, locationPathname),
    )
  const loyaltyModulePaths = [
      '/loyalty',
      `${productListingRoute}/:id/loyalty`,
      `${productListingRoute}/:id/marketplace/:id/listing/:id/loyalty`,
    ],
    isLoyaltyModuleRoutes = loyaltyModulePaths.some((path) =>
      isMatchPath(path, locationPathname, false),
    )
  const isProtectPriceTabRoute = isMatchPath(
    '/protect/price/products',
    locationPathname,
  )
  const isListingDetailsProtectPriceTabRoute = isMatchPath(
    `${productListingRoute}/:productId/marketplace/:marketplaceId/listing/:listingId/protect/price`,
    locationPathname,
  )
  const isProductDetailsProtectPriceTabRoute = isMatchPath(
    `${productListingRoute}/:productId/protect/price`,
    locationPathname,
  )

  const isProductListingShareRoute = [
    `${productListingRoute}/:productId/marketplace/:marketplaceId/listing/:listingId/share/*`,
    `/insights/share/product-details/:asin/:country_code/share/*`, // External Product Listing > Share Tab
    `/insights/share/external-listing/:asin/share/*`, //Second External Product Listing > Share Tab
  ].some((path) => isMatchPath(path, locationPathname))

  const isListingOrProductDetailsPaths = [
    `${productListingRoute}/:productId/marketplace/:marketplaceId/listing/:listingId/overview`,
    `${productListingRoute}/:productId/overview`,
  ].some((path) => isMatchPath(path, locationPathname))

  return (
    isProtectBuyboxRoute ||
    isDashboardRoute ||
    isRetailReadinessRoute ||
    isConversionReportRoute ||
    isProductAtGlanceContentRoute ||
    isProductConversionReportRoute ||
    isAtGlanceContentRoute ||
    isRankTrackerRoute ||
    (advertisingRouteValid && !isAdTabPerformanceRoute) ||
    isLoyaltyModuleRoutes ||
    isProtectPriceTabRoute ||
    isListingDetailsProtectPriceTabRoute ||
    isProductDetailsProtectPriceTabRoute ||
    isProductListingShareRoute ||
    isReviewsRoute ||
    isListingOrProductDetailsPaths
  )
}

/**
 * Render global time frame filter helper
 * @param location object from useLocation
 */
export const renderGlobalTimeFrameFilter = (pathname: string) => {
  const paths = [
    '/settings/traffic/brand/:brandId/playbooks/:playbookId/view-playbook-rules',
    '/traffic/brand/:brandId/playbooks/:playbookId/view-playbook-rules',
  ]
  return !paths.some((path) => isMatchPath(path, pathname))
}

///////////////////////////////////////////////////////////
// GENERAL TIMEFRAME VALIDATION FOR ALL ROUTES //
///////////////////////////////////////////////////////////
export const getValidTimeframe = (
  currentTimeframe: TimeframeFilterProps,
  pathname: string,
  tf: (key: string) => string,
): TimeframeFilterProps => {
  const isCoreRoute = checkOnlyCoreRoutes(pathname) // used to check for exceptions for non-core routes
  const isTrafficRoute = checkOnlyTrafficRoutes(pathname)
  const timePeriodExtension = !(isCoreRoute || isTrafficRoute)

  const computedTimeframeProps = copyOf(currentTimeframe), // start with working copy of initial timeframe
    currentTimeframeType: keyof ValidTimeframeAggregationsProps =
      computedTimeframeProps.timeframe.type,
    isTransitionType = ['trailing', 'historical'].includes(
      currentTimeframeType,
    ),
    rawTimeframeTimeValue =
      computedTimeframeProps.timeframe[
        isTransitionType ? 'display' : 'timeValue'
      ],
    timeframeTimeValue: string = isTransitionType
      ? rawTimeframeTimeValue
      : rawTimeframeTimeValue.toLowerCase(),
    currentTimeframeAggregation = computedTimeframeProps.timeframe.aggregation

  // if custom dates are selected then the type is either trailing or historical
  // return the timeframe as is
  if (
    isTransitionType &&
    computedTimeframeProps?.timeframe?.value === 'custom'
  ) {
    return computedTimeframeProps
  }

  // get all valid aggregations for the given route
  const allValidAggregationsOptions = getValidTimeframeAggregations(
    pathname,
    tf,
  )

  ////////////////////////////////
  // VALIDATE timeframe TYPE //
  ////////////////////////////////
  // get the correct type as it will be the key to validate timeValue, display & aggregation options
  // create a list of valid timeframe types
  const possibleTimeframeTypes = Object.keys(
    allValidAggregationsOptions,
  ) as Array<keyof typeof allValidAggregationsOptions>

  // go through the list of valid timeframe types and find one that has the current timeframe timeValue
  // from the valid aggregations options as an alternative timeframe type option
  const alternativeTimeframeType = possibleTimeframeTypes.find((vtft) => {
    const subTypeOptions = Object.keys(allValidAggregationsOptions[vtft] ?? {})
    return subTypeOptions.includes(timeframeTimeValue)
  })

  // set a valid timeframe type; keep the current type if valid, otherwise set the first valid type
  const validTimeframeType = possibleTimeframeTypes.includes(
    currentTimeframeType,
  )
    ? currentTimeframeType // keep the current timeframe type if valid
    : isTransitionType // if not valid, check if it's a transition type
      ? historicalChangeTypes[currentTimeframeType] // if it's a transition type, use the historical type
      : (alternativeTimeframeType ?? // if not a transition type, use the alternative valid type if it exists
        (possibleTimeframeTypes[0] as keyof typeof allValidAggregationsOptions)) // otherwise use the first valid type

  // EXCEPTION: use special translation for trailing/historical timeframe types
  if (
    isTransitionType &&
    !possibleTimeframeTypes.includes(currentTimeframeType)
  ) {
    const isDay = timeframeTimeValue === '24HRS' || timeframeTimeValue === '1D'
    const isWeek = timeframeTimeValue === '7D' || timeframeTimeValue === '1W'
    const isYear = timeframeTimeValue === '12M' || timeframeTimeValue === '1Y'

    // NOTE: validate that the selected day timeframe has not been disabled (due to hour aggregation limitations)
    const dayTimeframeNotDisabled =
      !isDay ||
      (isDay &&
        (Number(allValidAggregationsOptions?.['historical']?.['1D']?.length) >
          0 ||
          Number(allValidAggregationsOptions?.['trailing']?.['24hrs']?.length) >
            0))

    if (dayTimeframeNotDisabled) {
      return {
        ...computedTimeframeProps,
        timeframe: {
          ...computedTimeframeProps.timeframe,
          ...restTranslation[currentTimeframeType],
          ...(isDay ? dayTranslation[timeframeTimeValue] : {}),
          ...(isWeek ? weekTranslation[timeframeTimeValue] : {}),
          ...(isYear ? yearTranslation[timeframeTimeValue] : {}),
        },
      }
    }
  }

  // update timeframe with a valid timeframe type
  computedTimeframeProps.timeframe.type = validTimeframeType

  //////////////////////////////////
  // VALIDATE timeframe TIMEVALUE //
  //////////////////////////////////
  // select only the list of valid aggregations for the given timeframe type
  const validTimeframeAggregationOptions =
      allValidAggregationsOptions[validTimeframeType] ?? {},
    aggregationOptionKeys = Object.keys(
      validTimeframeAggregationOptions,
    ) as Array<keyof typeof validTimeframeAggregationOptions>

  // filter out timeValues that don't have any valid aggregations
  const validTimeValues = aggregationOptionKeys.filter(
    (tv) => validTimeframeAggregationOptions[tv]?.length > 0,
  ) as Array<Timeframe['timeValue']>
  aggregationOptionKeys.forEach((aok) => {
    if (validTimeframeAggregationOptions[aok]?.length === 0) {
      delete validTimeframeAggregationOptions[aok]
    }
  })

  // set a valid timeValue; keep the current timeValue if valid, otherwise set the first valid timeValue
  const validTimeValue: keyof typeof validTimeframeAggregationOptions =
    validTimeValues.includes(timeframeTimeValue)
      ? (timeframeTimeValue as keyof typeof validTimeframeAggregationOptions)
      : (validTimeValues[0] as keyof typeof validTimeframeAggregationOptions)

  const historicTimeValue = (
    validTimeValues.includes(timeframeTimeValue)
      ? timeframeTimeValue
      : validTimeValues[0]
  ) as keyof typeof validTimeframeAggregationOptions

  // set a valid timeValue; exception for transition from historical to trailing
  const needToSetAlternateTimeValue =
    validTimeframeType === 'trailing' ||
    (timePeriodExtension && validTimeframeType === 'historical')

  computedTimeframeProps.timeframe.timeValue = needToSetAlternateTimeValue
    ? trailingTimeValues[validTimeValue]
    : validTimeValue

  ////////////////////////////////////
  // VALIDATE timeframe AGGREGATION //
  ////////////////////////////////////
  const validAggregations =
    validTimeframeAggregationOptions[
      timePeriodExtension ? validTimeValue : historicTimeValue
    ]

  const validAggregation = validAggregations?.includes(
    currentTimeframeAggregation,
  )
    ? currentTimeframeAggregation
    : validAggregations[0]

  // set a valid aggregation
  computedTimeframeProps.timeframe.aggregation = validAggregation

  ////////////////////////////////
  // VALIDATE timeframe DISPLAY //
  ////////////////////////////////
  const year =
    computedTimeframeProps.timeframe.year ?? moment().year().toString()

  // set a valid display
  computedTimeframeProps.timeframe.display =
    validTimeframeType !== 'quarterly'
      ? timePeriodExtension
        ? capitalize(validTimeValue)
        : capitalize(historicTimeValue)
      : computedTimeframeProps.timeframe.quarter
        ? `${year} Q${computedTimeframeProps.timeframe.quarter}`
        : year

  // Set the display value to "QTR" for the previous quarter timeframe instead of "Quarter",
  // because the global filter does not recognize or select the value "QTR" when "Quarter" is used as the display value.
  if (
    validTimeframeType === 'previous' &&
    (historicTimeValue as string) === 'quarter'
  ) {
    computedTimeframeProps.timeframe.display = tf('quarter')
  }

  if (validTimeframeType === 'quarterly') {
    const timeframeYear = computedTimeframeProps.timeframe.year ?? year
    computedTimeframeProps.timeframe.year = timeframeYear
    const validQuarterlyTimeValue = computedTimeframeProps.timeframe.display
      .split(' ')[1]
      ?.includes('Q')
      ? computedTimeframeProps.timeframe.timeValue
      : 'year'
    computedTimeframeProps.timeframe.timeValue = validQuarterlyTimeValue
  }

  ////////////////////////////////////////////
  // VALIDATE timeframe STARTDATE & ENDDATE //
  ////////////////////////////////////////////
  if (
    isTransitionType &&
    (computedTimeframeProps.timeframe.type === 'trailing' ||
      computedTimeframeProps.timeframe.type === 'historical')
  ) {
    switch (computedTimeframeProps.timeframe.display) {
      case '1D':
      case '1W':
      case '1Y':
        computedTimeframeProps.timeframe.value = 1
        break
      case '24HRS':
        computedTimeframeProps.timeframe.value = 24
        break
      case '7D':
        computedTimeframeProps.timeframe.value = 7
        break
      case '30D':
        computedTimeframeProps.timeframe.value = 30
        break
      case '3M':
        computedTimeframeProps.timeframe.value = 3
        break
      case '6M':
        computedTimeframeProps.timeframe.value = 6
        break
      case '12M':
        computedTimeframeProps.timeframe.value = 12
        break
      default:
        computedTimeframeProps.timeframe.value = 'custom'
    }
  }
  // reset startDate
  computedTimeframeProps.startDate = getTimeframeDates(
    computedTimeframeProps.timeframe,
    'startDate',
  )

  // reset endDate
  computedTimeframeProps.endDate = getTimeframeDates(
    computedTimeframeProps.timeframe,
    'endDate',
  )

  return computedTimeframeProps
}

export const getDefaultTimeframeForAttainment = (
  usersTimeFrame: TimeframeFilterProps,
  tf: (key: string) => string,
): TimeframeFilterProps => {
  const { timeframe } = usersTimeFrame,
    currentYear = moment().year()?.toString(),
    toggledTimeframeCondition = timeframe.type !== 'quarterly',
    timeframeType: keyof ValidTimeframeAggregationsProps =
      timeframe.type ?? 'quarterly' // default for attainment reports is quarterly

  const allValidAggregationsOptions = getValidTimeframeAggregations(
    '/insights/attainment-reports',
    tf,
  )
  const validTimeframeTypes = Object.keys(allValidAggregationsOptions) as Array<
      keyof typeof allValidAggregationsOptions
    >,
    validTimeframeType = validTimeframeTypes.includes(timeframeType)
      ? timeframeType
      : (validTimeframeTypes[2] as keyof typeof allValidAggregationsOptions), // attainment reports default timeframe type is quarterly
    validAggregationOptions =
      allValidAggregationsOptions[validTimeframeType] ?? {},
    timeValues = Object.keys(validAggregationOptions) as Array<
      keyof typeof validAggregationOptions
    >,
    validTimeValues = timeValues.filter(
      (tv) => validAggregationOptions[tv].length > 0,
    ) as Array<Timeframe['timeValue']>,
    validTimeValue = (
      validTimeValues.includes(timeframe.timeValue)
        ? timeframe.timeValue
        : 'year'
    ) as keyof typeof validAggregationOptions,
    validAggregations = validAggregationOptions[validTimeValue],
    validAggregation = validAggregations?.includes(timeframe.aggregation)
      ? timeframe.aggregation
      : validAggregations[0],
    validDisplay = ['current', 'previous'].includes(validTimeframeType)
      ? validTimeValue
      : timeframe?.quarter
        ? `${currentYear} Q${timeframe.quarter}`
        : currentYear

  let newTimeframe: Timeframe = {
    ...timeframe,
    timeValue: validTimeValue,
    type: validTimeframeType,
    aggregation: validAggregation,
    display: validDisplay,
    ...(validTimeframeType === 'quarterly' ? { year: currentYear } : {}),
  }

  if (toggledTimeframeCondition) {
    newTimeframe = {
      ...timeframe,
      aggregation: 'month',
      display: currentYear,
      timeValue: 'year',
      type: 'quarterly',
      year: currentYear,
    }
  }

  return {
    ...usersTimeFrame,
    startDate: getTimeframeDates(newTimeframe, 'startDate'),
    endDate: getTimeframeDates(newTimeframe, 'endDate'),
    timeframe: newTimeframe,
  }
}

export const getDefaultTimeframeForLoyalty = (
  currentTimeFrame: TimeframeFilterProps,
  timeframe: Timeframe,
) => {
  // Loyalty data is only available at weekly aggregation
  let newUserTimeframe: TimeframeFilterProps = { ...currentTimeFrame }
  const isLoyaltySnSDatesInvalid =
    ['previous', 'current', 'historical', 'trailing'].includes(
      timeframe.type,
    ) && ['24hrs', '1d', 'day'].includes(timeframe.display.toLowerCase())
  if (isLoyaltySnSDatesInvalid) {
    const loyaltyTimeFrameData: Timeframe = ['day'].includes(
      timeframe.display.toLowerCase(),
    )
      ? {
          aggregation: 'week',
          display: 'Week',
          timeValue: 'week',
          type: timeframe.type === 'current' ? 'current' : 'previous',
        }
      : // This happens when the historical selected time is '24HRS' or '1D'
        {
          // Commenting for now until time period extension is enabled for loyalty. Once done uncomment the below code and remove the other week related logic
          aggregation: 'week',
          display: '7D',
          timeValue: 'day',
          type: 'trailing',
          value: 7,
        }

    const startDate = getTimeframeDates(loyaltyTimeFrameData, 'startDate'),
      endDate = getTimeframeDates(loyaltyTimeFrameData, 'endDate')
    newUserTimeframe = {
      ...currentTimeFrame,
      startDate: startDate,
      endDate: endDate,
      timeframe: {
        ...loyaltyTimeFrameData,
      },
    }
  } else {
    // Valid selection scenario
    newUserTimeframe = {
      ...currentTimeFrame,
      timeframe: {
        ...currentTimeFrame.timeframe,
        aggregation: 'week',
      },
    }
  }
  return newUserTimeframe
}

export const getDefaultTimeframeForLoyaltyLTV = (
  currentTimeFrame: TimeframeFilterProps,
  timeframe: Timeframe,
) => {
  // Loyalty data is only available at weekly aggregation
  let newUserTimeframe: TimeframeFilterProps = { ...currentTimeFrame }
  const isLoyaltyLTVDatesInvalid =
    (['previous', 'current', 'historical', 'trailing'].includes(
      timeframe.type,
    ) &&
      ['24hrs', '1d', '7d', '30d', 'day', 'week', 'month'].includes(
        timeframe.display.toLowerCase(),
      )) ||
    (timeframe?.value === 'custom' &&
      moment(currentTimeFrame.endDate).diff(
        currentTimeFrame.startDate,
        'days',
        // Min date range selected should be atleast 2 months
      ) < 61)
  if (isLoyaltyLTVDatesInvalid) {
    const loyaltyTimeFrameData: Timeframe = {
      aggregation: 'month',
      display: '12M',
      timeValue: 'month',
      type: 'trailing',
      value: 12,
    }

    const startDate = getTimeframeDates(loyaltyTimeFrameData, 'startDate'),
      endDate = getTimeframeDates(loyaltyTimeFrameData, 'endDate')
    newUserTimeframe = {
      ...currentTimeFrame,
      startDate: startDate,
      endDate: endDate,
      timeframe: {
        ...loyaltyTimeFrameData,
      },
    }
  } else {
    // Valid selection scenario
    newUserTimeframe = {
      ...currentTimeFrame,
      timeframe: {
        ...currentTimeFrame.timeframe,
        aggregation: 'month',
      },
    }
  }
  return newUserTimeframe
}

export const getDefaultTimeFrameForDestinyAutomationPreview = (
  currentTimeFrame: TimeframeFilterProps,
) => {
  let newUserTimeframe: TimeframeFilterProps = { ...currentTimeFrame }
  const previewTimeframe: Timeframe = {
    type: 'previous',
    display: 'Month',
    value: 1,
    timeValue: 'month',
    aggregation: 'week',
  }
  const startDate = getTimeframeDates(previewTimeframe, 'startDate'),
    endDate = getTimeframeDates(previewTimeframe, 'endDate')
  newUserTimeframe = {
    ...currentTimeFrame,
    startDate: startDate,
    endDate: endDate,
    timeframe: {
      ...previewTimeframe,
    },
  }

  return newUserTimeframe
}
export const getDefaultTimeframeForMarketShare = (
  currentTimeFrame: TimeframeFilterProps,
) => {
  let newUserTimeframe: TimeframeFilterProps = { ...currentTimeFrame }
  const marketShareTimeframe: Timeframe = {
    type: 'trailing',
    display: '6M',
    value: 6,
    timeValue: 'month',
    aggregation: 'month',
  }
  const startDate = getTimeframeDates(marketShareTimeframe, 'startDate'),
    endDate = getTimeframeDates(marketShareTimeframe, 'endDate')
  newUserTimeframe = {
    ...currentTimeFrame,
    startDate: startDate,
    endDate: endDate,
    timeframe: {
      ...currentTimeFrame.timeframe,
      ...marketShareTimeframe,
    },
  }

  return newUserTimeframe
}

///////////////////////////////////////////////////////////////////////////////////////////////////
// MARKETPLACE HELPERS
///////////////////////////////////////////////////////////////////////////////////////////////////

//add routes to disable non amazon marketplaces from global filter.
export const getDisabledNonAmazonMarketplaces = (
  locationPath: string,
  enableChinaForContent: boolean,
) => {
  const dashboardPaths = [
    '/insights/share/marketshare/*',
    '/insights/brand-health/*',
    '/advisory/market-analytics/*',
    '/loyalty/*',
    ...(enableChinaForContent ? [] : ['/content/*']),
    '/traffic/organic-traffic',
    '/traffic/destiny-automation/preview',
    `${productListingRoute}/:productId/marketplace/:marketId/listing/:listingId/loyalty/*`,
    `${productListingRoute}/:productId/marketplace/:marketId/listing/:listingId/content/details/retail-readiness`,
    // Disable Non Amazon Marketplaces on PROTECT > BUYBOX and SELLERS Tabs
    '/protect/buybox/*',
    '/protect/price/*', // Price Automations
    `${productListingRoute}/:productId/marketplace/:marketplaceId/listing/:listingId/share/*`,
    `/insights/share/product-details/:asin/:country_code/share/*`, // External Product Listing > Share Tab
    `/insights/share/external-listing/:asin/share/*`, //Second External Product Listing > Share Tab
  ]
  const isDashboardRoute = dashboardPaths.some((path) =>
    isMatchPath(path, locationPath),
  )
  return isDashboardRoute ?? false
}

export const getAvailableMarketplaces = ({
  params,
  apiController,
  selectedRegions,
  selectedMarketplaces,
  isLoyaltyMarketplaceRoute,
  isContentRoute,
  isShareAtAGlanceRoute,
  isMarketAnalyticsRoute,
  isPriceSegmentsRoute,
  isPriceProductsRoute,
  isBuyBoxRoute,
  isBrandHealthRoute,
  disableChinaForOrganicTraffic,
  isDestinyAutomationPreviewRoute,
}: GetAvailableMarketplacesProps): Promise<
  AvailableMarketplacesData | undefined
> => {
  return getGlobalMarketplaces(params, apiController.signal)
    .then((response) => {
      if (response?.data) {
        let {
          marketplaces: allBrandMarketplaces,
          regions: allBrandRegions,
        }: { marketplaces: Marketplace[]; regions: Region[] } = response.data

        // prevent error if marketplaces is `null` or `undefined`
        if (!allBrandMarketplaces) {
          allBrandMarketplaces = []
        }
        // prevent error if regions is `null` or `undefined`
        if (!allBrandRegions) {
          allBrandRegions = []
        }

        const rawMarketplaces = getNestedIds(allBrandMarketplaces)
        const allBrandsRegions = [
          0,
          ...(allBrandRegions?.map((nr) => nr.id) ?? []),
        ]
        const validRegionsForBrand = selectedRegions.filter((id) =>
          allBrandRegions?.some((nr) => nr.id === id),
        )

        // create a set of regions for selected brand:
        const possibleRegionsForBrand = new Set(
          selectedRegions?.length > 0
            ? selectedRegions.includes(0)
              ? allBrandsRegions
              : validRegionsForBrand.length > 0
                ? validRegionsForBrand
                : allBrandsRegions
            : allBrandsRegions,
        )

        // create a set of all marketplaces that are withing the selected regions:
        const filteredMarketplaces = allBrandMarketplaces?.reduce(
          (markets, mp) =>
            mp?.id &&
            (possibleRegionsForBrand.size === 0 ||
              possibleRegionsForBrand.has(mp.region_id))
              ? [...markets, mp.id]
              : [...markets],
          [] as MarketplaceId[],
        )

        // get all of the marketplaces that are within the matching markets:
        let matchingMarketplaces: Set<number> | null = null
        if (
          !selectedMarketplaces ||
          selectedMarketplaces.length < 1 ||
          selectedMarketplaces.includes(0)
        ) {
          matchingMarketplaces = new Set([0, ...filteredMarketplaces])
        } else {
          const matchingMarkets = selectedMarketplaces.filter((id) =>
            allBrandMarketplaces?.find((mp) => mp.id === id),
          )

          matchingMarketplaces =
            matchingMarkets?.length > 0
              ? new Set(matchingMarkets)
              : new Set([0, ...filteredMarketplaces])
        }

        // create array of selective distribution marketplaces:
        const sdMarketplaces = allBrandMarketplaces?.reduce(
          (markets, mp) =>
            mp?.is_selective_distribution ? [...markets, mp.id] : [...markets],
          [] as MarketplaceId[],
        )

        // get the marketplace codes for the selected marketplaces:
        const amazonMarketplaceCodes =
          // TODO: Remove below hardcoded ['US'] condition once we start supporting other than Amazon US marketplaces for SHARE, Advisory > MARKET ANALYTICS,
          // Insights > Share > Price Segments and Advisory > Price Segments
          (isShareAtAGlanceRoute ||
            isMarketAnalyticsRoute ||
            isPriceSegmentsRoute) &&
          allBrandMarketplaces?.[0]?.id === 1
            ? ['US']
            : allBrandMarketplaces?.reduce(
                (marketplaceCodes, market) => {
                  market.amazon &&
                    matchingMarketplaces?.has(market.id) &&
                    (marketplaceCodes = [
                      ...marketplaceCodes,
                      market.country_code,
                    ])
                  return marketplaceCodes
                },
                [] as Array<Marketplace['country_code']>,
              )

        // get the marketplace ids for the selected marketplaces:
        let amazonMarketplaceIdsData =
          // TODO: Remove below hardcoded [1] condition once we start supporting other than Amazon US marketplaces for SHARE, Advisory > MARKET ANALYTICS and (Insight and Advisory) > Price Segments
          (isShareAtAGlanceRoute ||
            isMarketAnalyticsRoute ||
            isPriceSegmentsRoute) &&
          allBrandMarketplaces?.[0].id === 1
            ? [1]
            : allBrandMarketplaces?.reduce(
                (marketplaceIds, market) => {
                  market.amazon &&
                    matchingMarketplaces?.has(market.id) &&
                    (marketplaceIds = [...marketplaceIds, market.id])
                  return marketplaceIds
                },
                [] as Array<Marketplace['id']>,
              )

        const allMarketplaces = getMarketplaces?.(allBrandMarketplaces) ?? []
        const amzMarketplaces = allMarketplaces.filter(
          (mp) => mp?.marketplace_name?.toLowerCase() === 'amazon',
        )

        // get the colors for the marketplaces
        const marketPlacesColors = allBrandMarketplaces.map((marketPlace) => {
          return {
            marketplaceName: marketPlace.marketplace_name.toLowerCase(),
            color: marketPlace.color,
          }
        }) as unknown as MarketplaceData['marketPlacesColors']

        /////////////////////////////////////////////////////////////////////////////////////////
        // ONLY USE AMAZON MARKETPLACES FOR LOYALTY, OPPORTUNITIES AND PROTECT > PRICE, BUY BOX & BRAND HEALTH ROUTES
        /////////////////////////////////////////////////////////////////////////////////////////
        if (
          isLoyaltyMarketplaceRoute ||
          isContentRoute ||
          isPriceProductsRoute ||
          isBuyBoxRoute ||
          isBrandHealthRoute ||
          isDestinyAutomationPreviewRoute
        ) {
          const amzOnlyMarketplaces = [
            ...(amzMarketplaces?.[0]?.subMarketplaces?.map((sbm) => sbm?.id) ??
              []),
          ]
          // are all amazon marketplaces included in the matchingMarketplaces?
          let selectedAllAmzMarketplaces = amzOnlyMarketplaces.every((amzMp) =>
            matchingMarketplaces?.has(amzMp),
          )

          let selectedAmzMarketplaces = [...matchingMarketplaces].filter((mp) =>
            amzOnlyMarketplaces.includes(mp),
          )

          // if no amazon marketplaces have been selected by the user, select all of them:
          if (selectedAmzMarketplaces.length === 0) {
            selectedAmzMarketplaces = amzOnlyMarketplaces.sort()
            selectedAllAmzMarketplaces = true
          }
          amazonMarketplaceIdsData = selectedAmzMarketplaces

          matchingMarketplaces = new Set(
            selectedAllAmzMarketplaces
              ? [0, ...selectedAmzMarketplaces]
              : [...selectedAmzMarketplaces],
          )

          const regionIds = allBrandMarketplaces
            ?.filter((el) => el.amazon)
            ?.map((el) => el?.region_id)
          allBrandRegions = allBrandRegions.filter((el) =>
            regionIds?.includes(el.id),
          )
        }

        /////////////////////////////////////////////////////////////////////////////////////////
        // ONLY USE AMAZON US MARKETPLACE & NORTH AMERICA REGION ON SHARE > AT A GLANCE, Advisory > Market Analytics,
        // and Insights > Shared > Price Segments and Advisory > Price Segments FOR INITIAL RELEASE
        // TODO: Remove below isShareAtAGlanceRoute, isMarketAnalyticsRoute and isPriceSegmentsRoute code once we start supporting all marketplaces and regions
        /////////////////////////////////////////////////////////////////////////////////////////
        if (
          isShareAtAGlanceRoute ||
          isMarketAnalyticsRoute ||
          isPriceSegmentsRoute
        ) {
          matchingMarketplaces = new Set([1])

          allBrandMarketplaces = allBrandMarketplaces?.filter(
            (marketplace) => marketplace?.id === 1,
          )
          allBrandRegions = allBrandRegions?.filter(
            (northRegion) => northRegion?.id === 1,
          )
        }
        //To be removed with toggle:china_data_organic_traffic_changes
        if (disableChinaForOrganicTraffic) {
          allBrandRegions = allBrandRegions?.filter(
            (region) => region?.id !== 3,
          )
        }

        /////////////////////////////////////////////////////////////////////////////////////////
        /////////////////////////////////////////////////////////////////////////////////////////
        allBrandMarketplaces.forEach((mp) => {
          if (mp.marketplace_name?.toLowerCase() === 'amazon') {
            // add the amazon marketplace to the amazon marketplace subMarketplaces:
            mp.subMarketplaces = amzMarketplaces[0]?.subMarketplaces?.map(
              (mp) => {
                if (mp.id === 1) {
                  return { ...mp, subMarketplaces: undefined }
                }
                return mp
              },
            )
          }
        })

        return {
          allBrandMarketplaces,
          allBrandRegions,
          amazonMarketplaceCodes,
          amazonMarketplaceIdsData,
          filteredMarketplaces,
          marketPlacesColors,
          matchingMarketplaces,
          possibleRegionsForBrand,
          rawMarketplaces,
          sdMarketplaces,
        }
      }
    })
    .catch((error) => {
      error.component = 'GlobalFilter.helpers.ts'
      throw error
    })
}

export const getMarketplaces = (responseMarketplaces: Marketplace[] = []) => {
  const amazonArr: Array<Marketplace> = [],
    otherArr: Array<Marketplace> = []
  responseMarketplaces.forEach((rm) => {
    if (rm.amazon) {
      amazonArr.push(rm)
    } else {
      otherArr.push(rm)
    }
  })
  const marketplacesArr = [
    ...(amazonArr.length > 0
      ? [
          {
            marketplace_name: 'Amazon',
            subMarketplaces: amazonArr,
            amazon: true,
          },
        ]
      : []),
    ...otherArr,
  ]
  return marketplacesArr
}

export const getNestedIds = (marketplaces: Marketplace[]): MarketplaceId[] => {
  const nestedIds: MarketplaceId[] = []
  marketplaces.forEach((marketplace) => {
    nestedIds.push(marketplace.id)
    if (marketplace?.subMarketplaces) {
      nestedIds.push(...getNestedIds(marketplace.subMarketplaces))
    }
  })
  return uniq(nestedIds)
}

// NOTE: method to create the displayed groups of marketplaces
export const getFilteredMarketplacesOrderByGroups = (
  enableSpecificChineseMarketplaces: boolean,
  marketplaces: Marketplace[],
  onlyAmazon?: boolean,
  onlyAmazonAndWalmart?: boolean,
) => {
  let amazonMarketPlacesRoot: Array<
    Marketplace | { marketplace_name: string; subMarketplaces: Marketplace[] }
  > = []
  let amazonMarketPlacesRootV1: Array<
    Marketplace | { marketplace_name: string; subMarketplaces: Marketplace[] }
  > = []
  let amazonWalmartMarketPlacesRoot: Array<
    Marketplace | { marketplace_name: string; subMarketplaces: Marketplace[] }
  > = []
  let allowedMarketPlacesRoot: Array<
    Marketplace | { marketplace_name: string; subMarketplaces: Marketplace[] }
  > = []
  let allowedMarketPlacesRootAmazon: Array<
    Marketplace | { marketplace_name: string; subMarketplaces: Marketplace[] }
  > = []
  // make unique array of group names
  const uniqueArrayOfGroupNames = new Set(
    marketplaces?.map((group) => group?.marketplace_group_name),
  )

  // filtered null group names for appending marketplaces at the end
  const filteredGroupedNullMarketplaces = marketplaces?.filter(
    (sel) => sel?.marketplace_group_name === null,
  )

  const excludeOtherMarketplacesInSort = ['B2B China']

  // iterate through array of group names to fetch their respective marketplaces
  uniqueArrayOfGroupNames.forEach((groupName) => {
    const filteredMarketplaces = marketplaces?.filter(
      (sel) => sel?.marketplace_group_name === groupName,
    )

    if (groupName) {
      if (
        groupName !== 'Others' &&
        !excludeOtherMarketplacesInSort.includes(groupName)
      ) {
        amazonMarketPlacesRoot.push({
          marketplace_name: groupName,
          subMarketplaces: filteredMarketplaces,
        })
      } else {
        // concat other market places
        amazonMarketPlacesRoot =
          amazonMarketPlacesRoot.concat(filteredMarketplaces)
      }
      if (onlyAmazon) {
        amazonMarketPlacesRootV1 = amazonMarketPlacesRoot?.filter(
          (amzMarketplace) =>
            amzMarketplace?.marketplace_name?.toLowerCase() === 'amazon',
        )
        if (enableSpecificChineseMarketplaces) {
          // considering chinese specific marketplaces
          const allowedMarketPlaces = amazonMarketPlacesRoot?.filter(
            (amzMarketplace) => {
              if (
                typeof amzMarketplace === 'object' &&
                !Object.prototype.hasOwnProperty.call(
                  amzMarketplace,
                  'subMarketplaces',
                )
              ) {
                return (
                  'region_id' in amzMarketplace &&
                  amzMarketplace?.region_id === 3 &&
                  allowedChineseMarketplaces.includes(amzMarketplace?.id)
                )
              } else {
                const regionIds = Array.from(
                  new Set(
                    amzMarketplace?.subMarketplaces?.map((mp) => mp?.region_id),
                  ),
                )
                if (!regionIds.includes(3)) {
                  return (
                    'region_id' in amzMarketplace &&
                    amzMarketplace?.region_id === 3 &&
                    allowedChineseMarketplaces.includes(amzMarketplace?.id)
                  )
                } else {
                  const updatedArr = amzMarketplace?.subMarketplaces?.filter(
                    (mp) => {
                      return (
                        'region_id' in mp &&
                        mp?.region_id === 3 &&
                        allowedChineseMarketplaces.includes(mp?.id)
                      )
                    },
                  )
                  return (
                    updatedArr &&
                    updatedArr.length > 0 && {
                      ...amzMarketplace,
                      subMarketplaces: updatedArr,
                    }
                  )
                }
              }
            },
          )
          allowedMarketPlacesRootAmazon = Array.from(
            new Set([...allowedMarketPlaces, ...amazonMarketPlacesRootV1]),
          )
        }
      }
      if (onlyAmazonAndWalmart) {
        amazonWalmartMarketPlacesRoot = amazonMarketPlacesRoot?.filter(
          (amzMarketplace) =>
            amzMarketplace?.marketplace_name?.toLowerCase() === 'amazon' ||
            amzMarketplace?.marketplace_name?.toLowerCase() === 'walmart',
        )
        if (enableSpecificChineseMarketplaces) {
          // considering chinese specific marketplaces
          const allowedMarketPlaces = amazonMarketPlacesRoot?.filter(
            (amzMarketplace) => {
              if (
                typeof amzMarketplace === 'object' &&
                !Object.prototype.hasOwnProperty.call(
                  amzMarketplace,
                  'subMarketplaces',
                )
              ) {
                return (
                  'region_id' in amzMarketplace &&
                  amzMarketplace?.region_id === 3 &&
                  allowedChineseMarketplaces.includes(amzMarketplace?.id)
                )
              } else {
                const regionIds = Array.from(
                  new Set(
                    amzMarketplace?.subMarketplaces?.map((mp) => mp?.region_id),
                  ),
                )
                if (!regionIds.includes(3)) {
                  return (
                    'region_id' in amzMarketplace &&
                    amzMarketplace?.region_id === 3 &&
                    allowedChineseMarketplaces.includes(amzMarketplace?.id)
                  )
                } else {
                  const updatedArr = amzMarketplace?.subMarketplaces?.filter(
                    (mp) => {
                      return (
                        'region_id' in mp &&
                        mp?.region_id === 3 &&
                        allowedChineseMarketplaces.includes(mp?.id)
                      )
                    },
                  )
                  return (
                    updatedArr &&
                    updatedArr.length > 0 && {
                      ...amzMarketplace,
                      subMarketplaces: updatedArr,
                    }
                  )
                }
              }
            },
          )
          allowedMarketPlacesRoot = Array.from(
            new Set([
              ...allowedMarketPlacesRoot,
              ...allowedMarketPlaces,
              ...amazonWalmartMarketPlacesRoot,
            ]),
          )
        }
      }
    }
  })

  return onlyAmazon
    ? enableSpecificChineseMarketplaces
      ? allowedMarketPlacesRootAmazon
      : amazonMarketPlacesRootV1
    : onlyAmazonAndWalmart
      ? enableSpecificChineseMarketplaces
        ? allowedMarketPlacesRoot
        : amazonWalmartMarketPlacesRoot
      : [...amazonMarketPlacesRoot.concat(filteredGroupedNullMarketplaces)]
}

type GetFilteredMarketplacesProps = {
  enableSpecificChineseMarketplaces: boolean
  allMarketplaces: Marketplace[]
  allRegions: Region[]
  selectedRegions: RegionId[]
  rawMarketplaces: MarketplaceId[]
  onlyAmazon?: boolean
  onlyAmazonAndWalmart?: boolean
}

export const getFilteredMarketplaces = ({
  enableSpecificChineseMarketplaces,
  allMarketplaces,
  allRegions,
  selectedRegions,
  rawMarketplaces,
  onlyAmazon,
  onlyAmazonAndWalmart,
}: GetFilteredMarketplacesProps) => {
  let _filteredMarketplaces: Array<
    | Marketplace
    | {
        marketplace_name: string
        subMarketplaces: Marketplace[]
      }
  > = []
  if ([0, allRegions?.length].includes(selectedRegions.length)) {
    const _marketplaces: Marketplace[] = allMarketplaces.filter((mp) =>
      rawMarketplaces.includes(mp.id),
    )
    _filteredMarketplaces = getFilteredMarketplacesOrderByGroups(
      enableSpecificChineseMarketplaces,
      _marketplaces,
      onlyAmazon,
      onlyAmazonAndWalmart,
    )
  } else {
    const _selectedMarketplaces = [
      ...allMarketplaces.filter((m) => selectedRegions.includes(m.region_id)),
    ]
    _filteredMarketplaces = getFilteredMarketplacesOrderByGroups(
      enableSpecificChineseMarketplaces,
      _selectedMarketplaces,
      onlyAmazon,
      onlyAmazonAndWalmart,
    )
  }
  return _filteredMarketplaces
}

///////////////////////////////////////////////////////////////////////////////////////////////////
// TAGS HELPERS
///////////////////////////////////////////////////////////////////////////////////////////////////
// Global Tags Visibility Condition
export function checkGlobalTagsDisabled(
  pathname: string,
  customer?: Customer | null,
) {
  const isAllBrands = customer?.id === 0

  const isTrafficChangeHistoryRoute = isMatchPath(
    changeHistoryModuleRoute,
    pathname,
  )

  // SELLER ROUTES
  const allowedSellerPaths = [
    '/protect/buybox/sellers',
    `${sellersCoreRoute}/:id/products`,
    `${sellersCoreRoute}/:id/products/all`,
    `${sellersCoreRoute}/:id/protect/compliance`,
    `${sellersCoreRoute}/:id/protect/buybox`,
    `${sellersCoreRoute}/:id/protect/at-a-glance`,
  ]
  const isProhibitedSellerRoute =
    isMatchPath(sellersCoreRoute, pathname, false) &&
    !allowedSellerPaths.some((path) => isMatchPath(path, pathname))

  // PRODUCT ROUTES
  const allowedProductPaths = [
    `${productCoreRoute}/overview`,
    `${productCoreRoute}/overview/all`,
    `${productListingRoute}/:productId/*`,
  ]
  const isProhibitedProductRoute =
    isMatchPath(productCoreRoute, pathname, false) &&
    !allowedProductPaths.some((path) => isMatchPath(path, pathname))

  // SETTINGS ROUTES
  const allowedSettingsPaths = [
    `${productCoreRoute}/overview`, // `/settings/overview/products/overview`
    `${productCoreRoute}/overview/all`, // `/settings/overview/products/overview/all`
    `${productListingRoute}/:productId/*`,
  ]
  const isProhibitedSettingsRoute =
    isMatchPath('/settings', pathname, false) &&
    !allowedSettingsPaths.some((path) => isMatchPath(path, pathname))

  // TRAFFIC ROUTES
  const allowedTrafficPaths = [
    '/traffic/reports/inventory-optimization',
    '/traffic/reports/products/overview',
    '/traffic/reports/products/performance',
    '/traffic/products',
    '/traffic/paid-traffic/products',
    '/traffic/at-a-glance',
    '/traffic/organic-traffic',
    '/traffic/destiny/insights',
    '/traffic/destiny/opportunities',
  ]
  const isProhibitedTrafficRoute =
    isMatchPath('/traffic', pathname, false) &&
    !allowedTrafficPaths.some((path) => isMatchPath(path, pathname))

  const isProhibitedProtectRoute = isMatchPath(
    '/protect/buybox/brands',
    pathname,
    false,
  )

  const prohibitedSharePaths = [
    '/insights/share/marketshare/*',
    `${productListingRoute}/:productId/marketplace/:marketplaceId/listing/:listingId/share/*`, // Product Listing > Share Tab
    `/insights/share/product-details/:asin/:country_code/share/*`, // External Product Listing > Share Tabs
    `/insights/share/external-listing/:asin/share/*`, //Second External Product Listing > Share Tab
  ]

  const prohibitedListingOrProductDetailsPaths = [
    `${productListingRoute}/:productId/marketplace/:marketplaceId/listing/:listingId/overview`,
    `${productListingRoute}/:productId/overview`,
  ]

  const isProhibitedListingOrProductDetailsPaths =
    prohibitedListingOrProductDetailsPaths.some((path) =>
      isMatchPath(path, pathname),
    )

  const isProhibitedShareRoute = prohibitedSharePaths.some((path) =>
    isMatchPath(path, pathname),
  )

  // CONTENT ROUTES
  const isProhibitedContentRoute = false // all content routes are allowed

  // GENERAL ROUTES (not exact path match)
  const prohibitedParentRoutes = [
    '/logistics',
    '/reports/attainment_reports',
    '/insights/attainment-reports',
    '/protect/selective-distribution',
    `${productListingRoute}/:id/marketplace/:id/listing/:id/content/details/conversion`,
    '/insights/digital-shelf',
    '/advisory/digital-shelf',
    '/advisory/market-analytics',
    '/advisory/price-segments',
    '/insights/share/price-segments',
    '/custom-service/presales',
  ]
  const isProhibitedParentRoute = prohibitedParentRoutes.some((route) =>
    pathname.includes(route),
  )

  const isProhibitedBrandHealthRoute = isMatchPath(
    '/insights/brand-health/*',
    pathname,
  )

  const disableTags =
    isAllBrands ||
    isProhibitedParentRoute ||
    isProhibitedProductRoute ||
    isProhibitedSellerRoute ||
    isProhibitedSettingsRoute ||
    isTrafficChangeHistoryRoute ||
    isProhibitedTrafficRoute ||
    isProhibitedContentRoute ||
    isProhibitedProtectRoute ||
    isProhibitedShareRoute ||
    isProhibitedListingOrProductDetailsPaths ||
    isProhibitedBrandHealthRoute

  return disableTags
}

///////////////////////////////////////////////////////////////////////////////////////////////////
// LINES OF BUSINESS HELPERS
///////////////////////////////////////////////////////////////////////////////////////////////////

export function checkGlobalLinesOfBusinessDisabled(
  pathname: string,
  lobFilterConversion?: boolean,
) {
  // TODO: Add routes that should disable the Lines of Business filter
  const isAllowedRoutes = [
    '/insights',
    ...insightsReportRoutes,
    '/traffic/*',
    '/content/conversion',
    ...(lobFilterConversion ? ['/content/at-a-glance'] : []),
    `${productListingRoute}/:productId/marketplace/:marketplaceId/listing/:listingId/overview`,
    `${productListingRoute}/:productId/overview`,
    '/custom-service/*',
  ].some((path) => isMatchPath(path, pathname))

  const isProhibitedRoutes = ['/traffic/automations/in-stock-protection'].some(
    (path) => isMatchPath(path, pathname),
  )

  return !isAllowedRoutes || isProhibitedRoutes
}

///////////////////////////////////////////////////////////////////////////////////////////////////
// CURRENCY HELPERS
///////////////////////////////////////////////////////////////////////////////////////////////////

// Return true if path matches to disable global currency from Global Filter.
export function CheckGlobalCurrencyDisabled(pathname: string) {
  const pathsThatDoNotShowCurrencyFilter = [
      changeHistoryModuleRoute, // isTrafficChangeHistoryRoute
      HISTORY_TAB_PATH, // Inventory Insights Route
    ],
    isPlaybookRulesPage = pathname.includes('/view-playbook-rules')

  const isPathThatDoesNotShowCurrencyFilter =
    pathsThatDoNotShowCurrencyFilter.includes(pathname) || isPlaybookRulesPage

  return isPathThatDoesNotShowCurrencyFilter
}

export const getInitialComparisonDates = (
  timeframe: CompareDisplayTimeframeType,
) => {
  const comparisonRangeString = timeframe?.compareDisplay
  const startDateStr = comparisonRangeString?.split(' - ')?.[0],
    endDateStr = comparisonRangeString?.split(' - ')?.[1]
  const currentDate = moment(),
    timeOptions = {
      hour: currentDate.hour(),
      minute: currentDate.minute(),
      second: currentDate.second(),
    }
  return {
    startDate:
      timeframe?.timeValue === 'hours'
        ? moment(startDateStr, 'MM/DD/YYYY').set(timeOptions).format()
        : moment(startDateStr, 'MM/DD/YYYY').startOf('day').format(),
    endDate:
      timeframe?.timeValue === 'hours'
        ? moment(endDateStr, 'MM/DD/YYYY').set(timeOptions).format()
        : moment(endDateStr, 'MM/DD/YYYY').endOf('day').format(),
  }
}

/** considering chinese specific marketplaces as only those will be listed for traffic and conversion module
798 - Jingdong
714 - Tmall CN
803 - Pinduoduo
837 - Tiktok CN
*/
export const allowedChineseMarketplaces = [798, 803, 714, 837]

/**
 * Helper function to filter marketplaces based on various conditions
 * @returns An array of filtered marketplace IDs
 */
export function getFilteredMarketplacesIds(
  isSelectiveDistribution: boolean,
  disabledNonAmazonMarketplaces: boolean,
  disableNonAmazonAndNonWalMarketplaces: boolean,
  enableSpecificChineseMarketplaces: boolean,
  isContentRoute: boolean,
  isShareAtAGlanceRoute: boolean,
  sdMarketplaceIds: number[],
  currentMarketplaces: number[],
  allAmazonMarketplaceIds: number[],
  specificMarketplacesIds: number[],
  combinedMarketplaceIds: number[],
) {
  if (isSelectiveDistribution) {
    return sdMarketplaceIds?.filter((mp: number) =>
      currentMarketplaces?.includes(mp),
    )
  }

  if (
    disabledNonAmazonMarketplaces ||
    (isContentRoute && !disabledNonAmazonMarketplaces)
  ) {
    if (isShareAtAGlanceRoute) {
      /**
       * NOTE: Hardcoded 1 marketplace is Amazon US that is only for the SHARE page as we are restricting all other marketplaces for initial launch
       * TODO: Remove hardcoded [1] once we start supporting other than Amazon US marketplaces
       */
      return [1]
    }

    if (enableSpecificChineseMarketplaces) {
      return [
        ...(allAmazonMarketplaceIds?.filter((mp: number) =>
          currentMarketplaces?.includes(mp),
        ) ?? []),
        ...(specificMarketplacesIds?.filter((mp: number) =>
          currentMarketplaces?.includes(mp),
        ) ?? []),
      ]
    }

    return allAmazonMarketplaceIds?.filter((mp: number) =>
      currentMarketplaces?.includes(mp),
    )
  }

  if (disableNonAmazonAndNonWalMarketplaces) {
    if (enableSpecificChineseMarketplaces) {
      return [
        ...(combinedMarketplaceIds?.filter((mp: number) =>
          currentMarketplaces?.includes(mp),
        ) ?? []),
        ...(specificMarketplacesIds?.filter((mp: number) =>
          currentMarketplaces?.includes(mp),
        ) ?? []),
      ]
    }

    return combinedMarketplaceIds?.filter((mp: number) =>
      currentMarketplaces?.includes(mp),
    )
  }

  return currentMarketplaces
}
