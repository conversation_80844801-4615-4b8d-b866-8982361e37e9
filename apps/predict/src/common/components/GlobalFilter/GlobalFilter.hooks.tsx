import { useContext, useEffect, useState } from 'react'
import { matchPath, useLocation } from 'react-router-dom'
import { isEqual } from 'lodash'
import { notEmpty, useToggle } from '@patterninc/react-ui'
import {
  isMatchPath,
  parseParams,
  productCoreRoute,
  productListingRoute,
  sellersCoreRoute,
  useTranslate,
} from '@predict-services'
import {
  HISTORY_TAB_PATH,
  INVENTORY_INSIGTHS_ITEMS_PATH,
  INVENTORY_INSIGTHS_LISTINGS_PATH,
} from 'src/modules/Logistics/LogisticsRoutes'
import { changeHistoryModuleRoute } from 'src/modules/Reports/components/Pages/TrafficChangeHistory/TrafficChangeHistory'
import type {
  BrandGroup,
  BrandGroupCustomer,
  CheckboxCategory,
  Currency,
  Customer,
  LinesOfBusinessProps,
  Marketplace,
  MarketplaceId,
  Region,
  Tags,
  Timeframe,
} from '@predict-types'

import { ThemeContext } from '../../../Context'
import { useCustomCategories } from '../../../modules/Settings/components/Pages/Products/Products/components/Pages/ProductDetails/Pages/ProductEdit/getCustomCategories'
import { checkGlobalCategoriesEnabled } from './CustomCategories'
import {
  checkGlobalLinesOfBusinessDisabled,
  checkGlobalTagsDisabled,
  copyOf,
  getValidTimeframeAggregations,
  insightsVendorAnalyticsRoute,
} from './GlobalFilter.helpers'

///////////////////////////////////////////////////////////////////////////////////////////////////
// ROUTES
///////////////////////////////////////////////////////////////////////////////////////////////////

export const useIsAttainmentRoute = () => {
  const { pathname } = useLocation()
  const attainmentPaths = [
    '/reports/attainment_reports',
    '/insights/attainment-reports',
  ]
  return attainmentPaths.some((path) => isMatchPath(path, pathname))
}

export const useIsInsightsDetailsRoutes = () => {
  const detailsPagePaths = [
    '/insights/share/marketshare/categories/:id/*',
    '/insights/share/marketshare/keywords/:id/*',
    '/insights/share/marketshare/brands/:id/*',
    '/insights/share/external-listing/:asin/*',
  ]
  const { pathname } = useLocation()

  return (
    detailsPagePaths.some((path) => isMatchPath(path, pathname)) ||
    pathname.includes('product-details') ||
    pathname.includes('range')
  )
}

export const useIsLoyaltySnSRoute = () => {
  const { pathname } = useLocation()
  const loyaltySnSPaths = [
    '/loyalty/SnS',
    `${productListingRoute}/:id/loyalty/SnS`,
    `${productListingRoute}/:id/marketplace/:id/listing/:id/loyalty/SnS`,
  ]
  return loyaltySnSPaths.some((path) => isMatchPath(path, pathname))
}

export const useIsLoyaltyLTVRoute = () => {
  const { pathname } = useLocation()
  const loyaltyLTVPaths = [
    '/loyalty/ltv',
    `${productListingRoute}/:id/loyalty/ltv`,
    `${productListingRoute}/:id/marketplace/:id/listing/:id/loyalty/ltv`,
  ]
  return loyaltyLTVPaths.some((path) => isMatchPath(path, pathname, false))
}

export const useIsDestinyAutomationPreviewRoute = () => {
  const { pathname } = useLocation()
  const destinyAutomationPreviewPaths = ['/traffic/destiny-automation/preview']
  return destinyAutomationPreviewPaths.some((path) =>
    isMatchPath(path, pathname, false),
  )
}

export const useIsRetailReadinessRoute = () => {
  const { pathname } = useLocation()
  const retailReadinessPaths = ['/content/retail-readiness']
  return retailReadinessPaths.some((path) => isMatchPath(path, pathname))
}

export const useIsMarketProductDetailsRoute = () => {
  const { pathname } = useLocation()
  const marketProductDetailsPaths = [
    `${productListingRoute}/:id/marketplace/:id/listing/:id/overview`,
    `${productListingRoute}/:id/marketplace/:id/listing/:id/details/overview`,
    `${productListingRoute}/:id/marketplace/:id/listing/:id/details/tags`,
    `${productListingRoute}/:id/marketplace/:id/listing/:id/protect/at-a-glance`,
    `${productListingRoute}/:id/marketplace/:id/listing/:id/protect/buybox/sellers`,
    `${productListingRoute}/:id/marketplace/:id/listing/:id/protect/buybox/suppression`,
    `${productListingRoute}/:id/marketplace/:id/listing/:id/sellers`,
    `${productListingRoute}/:id/marketplace/:id/listing/:id/protect`,
    `${productListingRoute}/:id/marketplace/:id/listing/:id/protect/buybox`,
    `${productListingRoute}/:id/marketplace/:id/listing/:id/protect/suppression`,
    `${productListingRoute}/:id/marketplace/:id/listing/:id/protect/attribution`,
    `${productListingRoute}/:id/marketplace/:id/listing/:id/protect/price_changes`,
    `${productListingRoute}/:id/marketplace/:id/listing/:id/protect/compliance`,
    `${productListingRoute}/:id/marketplace/:id/listing/:id/traffic/performance`,
    `${productListingRoute}/:id/marketplace/:id/listing/:id/content`,
    `${productListingRoute}/:id/marketplace/:id/listing/:id/content/details/rank-tracker`,
    `${productListingRoute}/:id/marketplace/:id/listing/:id/content/details/reviews`,
    `${productListingRoute}/:id/marketplace/:id/listing/:id/content/details/conversion`,
    `${productListingRoute}/:id/marketplace/:id/listing/:id/content/details/at-a-glance`,
    `${productListingRoute}/:id/overview`,
  ]
  return (
    marketProductDetailsPaths.some((path) => isMatchPath(path, pathname)) ||
    isMatchPath(
      '/settings/overview/products/:id/marketplace/:id/listing/:id',
      pathname,
      false,
    )
  )
}

export const useIsProtectRoute = () => {
  const { pathname } = useLocation()
  return isMatchPath('/protect', pathname, false)
}

export const useIsLoyaltyRoute = () => {
  const { pathname } = useLocation()
  const loyaltyPaths = [
    '/loyalty/ltv/product-insights',
    '/loyalty/ltv/overview',
    '/loyalty/SnS',
    '/loyalty/returns',
    '/loyalty/returns/brand/:id',
    '/loyalty/SnS/brand/:id',
    `${productListingRoute}/:productId/marketplace/:marketId/listing/:listingId/loyalty/returns`,
    `${productListingRoute}/:productId/marketplace/:marketId/listing/:listingId/loyalty/SnS`,
    `${productListingRoute}/:productId/marketplace/:marketId/listing/:listingId/content/details/retail-readiness`,
    `${productListingRoute}/:productId/marketplace/:marketId/listing/:listingId/loyalty/ltv`,
  ]
  return loyaltyPaths.some((path) => isMatchPath(path, pathname))
}

export const useIsTrafficRoute = () => {
  const { pathname } = useLocation()
  const trafficPaths = [
    '/traffic/*',
    '/settings/traffic/brand/:brandId/playbooks/:playbookId/view-playbook-rules',
    `${productListingRoute}/:productId/traffic/*`,
    `${productListingRoute}/:productId/marketplace/:marketplaceId/listing/:listingId/traffic/*`,
  ]
  return trafficPaths.some((path) => isMatchPath(path, pathname))
}
export const useIsContentRoute = () => {
  const { pathname } = useLocation()
  const contentPaths = [
    '/content/*',
    `${productListingRoute}/:productId/marketplace/:marketId/listing/:listingId/content/details/retail-readiness`,
  ]
  return contentPaths.some((path) => isMatchPath(path, pathname))
}
export const useIsOrganicTrafficRoute = () => {
  const { pathname } = useLocation()
  const organicTrafficPaths = [
    '/traffic/organic-traffic',
    `${productListingRoute}/:productId/marketplace/:marketId/listing/:listingId/traffic/organic-traffic`,
  ]
  return organicTrafficPaths.some((path) => isMatchPath(path, pathname))
}
export const useIsShareAtAGlanceRoute = () => {
  const { pathname } = useLocation()
  const shareAtAGlancePaths = [
    '/insights/share/marketshare/*',
    `${productListingRoute}/:productId/marketplace/:marketplaceId/listing/:listingId/share/*`, // Product Listing > Share Tab
    `/insights/share/product-details/:asin/:country_code/share/*`,
    `/insights/share/external-listing/:asin/share/*`, // Second External Product Listing > Share Tab
  ]
  return shareAtAGlancePaths.some((path) => isMatchPath(path, pathname))
}

export const useIsMarketShare = () => {
  const { pathname } = useLocation()
  const marketsharePaths = ['/insights/share/marketshare/*']
  return marketsharePaths.some((path) => isMatchPath(path, pathname, false))
}

export const useIsVendorAnalyticsRoute = () => {
  const { pathname } = useLocation()

  return !!matchPath(insightsVendorAnalyticsRoute, pathname)
}

export const useIsVATSupportedRoute = () => {
  const { pathname } = useLocation(),
    isOrganicTrafficRoute = useIsOrganicTrafficRoute()
  const VATSupportedPaths = [
    '/traffic/at-a-glance',
    '/traffic/brand-performance/overview',
    '/content/at-a-glance',
    '/content/conversion',
    '/content/reviews',
    '/content/retail-readiness',
    '/loyalty/ltv/overview',
    '/loyalty/ltv/product-insights',
    '/loyalty/SnS',
    '/loyalty/returns',
  ]
  return VATSupportedPaths.some(
    (path) => isMatchPath(path, pathname) || isOrganicTrafficRoute,
  )
}

export const useIsSellersRoute = () => {
  const { pathname } = useLocation()
  return [sellersCoreRoute].some((path) => isMatchPath(path, pathname, false))
}

export const useIsSellersDetailsRoute = () => {
  const { pathname } = useLocation()
  // Is a sellers route but not the main sellers route
  return (
    isMatchPath(sellersCoreRoute, pathname, false) &&
    !isMatchPath(sellersCoreRoute, pathname)
  )
}

export const useIsMarketAnalyticsRoute = () => {
  const { pathname } = useLocation()
  return isMatchPath('/advisory/market-analytics', pathname, false)
}

export const useIsInsightsRoute = () => {
  const location = useLocation()
  return isMatchPath('/insights', location.pathname, false)
}

/**
 * Check if the current route is the Insights > Price Segments or Advisory > Price Segments route
 */
export const useIsPriceSegmentsRoute = () => {
  const { pathname } = useLocation()
  const priceSegmentsPaths = [
    '/advisory/price-segments',
    '/insights/share/price-segments',
  ]
  return priceSegmentsPaths.some((path) => isMatchPath(path, pathname, false))
}

/**
 * returns true if the current route is Price Segments route in Marketshare or Market Analytics
 *
 *  Note: Relevant Price Segments routes can be added to this hook
 */
export const useIsMarketsharePriceSegmentsRoute = () => {
  const { pathname } = useLocation()
  const priceSegmentsPaths = [
    '/insights/share/marketshare/brands/:id/price-segments/*',
    '/advisory/market-analytics/brands/:id/price-segments/*',
    '/insights/share/marketshare/categories/:id/price-segments/*',
    '/advisory/market-analytics/categories/:id/price-segments/*',
    '/insights/share/marketshare/categories/:id/brands/:id/price-segments/*',
    '/advisory/market-analytics/categories/:id/brands/:id/price-segments/*',
  ]
  return priceSegmentsPaths.some((path) => isMatchPath(path, pathname, false))
}

export const useIsBrandHealthRoute = () => {
  const { pathname } = useLocation()
  const shareBrandHealthPaths = ['/insights/brand-health/*']
  return shareBrandHealthPaths.some((path) => isMatchPath(path, pathname))
}

export const useIsBuyBoxRoute = () => {
  const { pathname } = useLocation()
  const buyBoxPaths = ['/protect/buybox/*']
  return buyBoxPaths.some((path) => isMatchPath(path, pathname, false))
}

export const useIsPriceProductsRoute = () => {
  const { pathname } = useLocation()
  const priceProductsPaths = ['/protect/price/products']
  return priceProductsPaths.some((path) => isMatchPath(path, pathname, true))
}

export const useIsCustomerServiceRoute = () => {
  const { pathname } = useLocation()
  const customerServicePaths = ['/custom-service/*']
  return customerServicePaths.some((path) => isMatchPath(path, pathname, true))
}

///////////////////////////////////////////////////////////////////////////////////////////////////
// GENERAL GLOBAL FILTER HOOKS
///////////////////////////////////////////////////////////////////////////////////////////////////
export const useIsGlobalFilterDisabled = () => {
  const location = useLocation()
  const pathsThatDoNotShowGlobalFilter = ['/report/custom-reports']
  const isPathThatDoesNotShowGlobalFilter =
    pathsThatDoNotShowGlobalFilter.includes(location.pathname)
  return isPathThatDoesNotShowGlobalFilter
}

export type GlobalFilterVariables = {
  amazonMarketplaceIds: MarketplaceId[]
  brandGroup: BrandGroup
  currentMarketplaces: MarketplaceId[]
  currentRegions: Array<Region['id']>
  customCategories: CheckboxCategory[]
  customer: Partial<Customer>
  isVatAdjustmentTurnedOn: boolean
  isChurnToggleEnabled: boolean
  prevCustomer: Partial<Customer>
  rawMarketplaces: Marketplace[]
  regions: Region[]
  sdMarketplaceIds: MarketplaceId[]
  selectedCurrency: Partial<Currency>
  tags: Tags
  linesOfBusiness: LinesOfBusinessProps
  timeframe: Timeframe
}
export const useGlobalFilterVariables = (
  summaryVariables?: GlobalFilterVariables,
): GlobalFilterVariables => {
  // default values from ThemeContext
  const {
    allMarketplaces,
    allRegions,
    brandGroupCustomer,
    marketplaceIds,
    tags: selectedTags,
    linesOfBusiness: selectedLinesOfBusiness,
    timeframeGlobal,
  } = useContext(ThemeContext)

  let {
    amazonMarketplaceIds = [],
    selectedCurrency = { code: 'USD', symbol: '$' },
    customCategories = [],
    customer = {},
    isVatAdjustmentTurnedOn = false,
    isChurnToggleEnabled = true,
    prevCustomer = {},
    regions,
    sdMarketplaceIds = [],
  } = useContext(ThemeContext) as Partial<GlobalFilterVariables>

  let rawMarketplaces = copyOf(allMarketplaces) as Marketplace[],
    currentRegions = regions as unknown as Array<Region['id']>,
    timeframe = timeframeGlobal as Timeframe,
    brandGroup = brandGroupCustomer as BrandGroup,
    tags = selectedTags as Tags,
    linesOfBusiness = selectedLinesOfBusiness as LinesOfBusinessProps,
    currentMarketplaces = marketplaceIds as MarketplaceId[]
  regions = allRegions as Region[]

  // if summaryVariables are passed, override the ThemeContext variables
  if (summaryVariables) {
    amazonMarketplaceIds = summaryVariables.amazonMarketplaceIds
    brandGroup = summaryVariables.brandGroup
    currentMarketplaces = summaryVariables.currentMarketplaces
    currentRegions = summaryVariables.currentRegions
    customCategories = summaryVariables.customCategories
    customer = summaryVariables.customer
    isVatAdjustmentTurnedOn = summaryVariables.isVatAdjustmentTurnedOn
    isChurnToggleEnabled = summaryVariables.isChurnToggleEnabled
    prevCustomer = summaryVariables.prevCustomer
    rawMarketplaces = summaryVariables.rawMarketplaces
    regions = summaryVariables.regions
    sdMarketplaceIds = summaryVariables.sdMarketplaceIds
    selectedCurrency = summaryVariables.selectedCurrency
    tags = summaryVariables.tags
    linesOfBusiness = summaryVariables.linesOfBusiness
    timeframe = summaryVariables.timeframe
  }

  return {
    amazonMarketplaceIds,
    brandGroup,
    currentMarketplaces,
    currentRegions,
    customCategories,
    customer,
    isVatAdjustmentTurnedOn,
    isChurnToggleEnabled,
    prevCustomer,
    rawMarketplaces,
    regions,
    sdMarketplaceIds,
    selectedCurrency,
    tags,
    linesOfBusiness,
    timeframe,
  }
}

///////////////////////////////////////////////////////////////////////////////////////////////////
// TIMEFRAME HOOKS
///////////////////////////////////////////////////////////////////////////////////////////////////
export const useDisableTimeframe = () => {
  const { pathname } = useLocation()
  const disabledTimeframeRoutes = [
    `${productListingRoute}/:id/marketplace/:id/listing/:id/protect/compliance`,
  ]
  return disabledTimeframeRoutes.some((path) => isMatchPath(path, pathname))
}

export const useIsGlobalTimeframeEnabled = () => {
  const { pathname } = useLocation()

  const disabledRoutes = [
    // INSIGHTS
    '/insights/digital-shelf',
    '/insights/brand-health/*',
    // PRODUCTS
    `${productCoreRoute}/overview/uploads`,
    // SELLERS
    `${sellersCoreRoute}/:sellerId/contacts`,
    `${sellersCoreRoute}/:sellerId/notes`,
    `${sellersCoreRoute}/:sellerId/history`,
    '/traffic/brand/:brandId/playbooks/:playbookId/view-playbook-rules',
    // ADVISORY
    '/advisory/digital-shelf',
    // INVENTORY INSIGHTS
    INVENTORY_INSIGTHS_ITEMS_PATH,
    INVENTORY_INSIGTHS_LISTINGS_PATH,
  ]
  const isGlobalTimeFrameDisablePath = disabledRoutes.some((path) =>
    isMatchPath(path, pathname, false),
  )

  const disableInTheseModules = [
    '/reports',
    '/settings',
    '/support',
    '/profile',
    '/planning',
  ]
  const validRoutesInDisabledModules = [
    changeHistoryModuleRoute,
    `${productCoreRoute}/overview`,
    `${productCoreRoute}/overview/all`,
    `${productListingRoute}/:productId/*`,
    `${productListingRoute}/:productId/marketplace/:marketplaceId/listing/:listingId/*`,
    HISTORY_TAB_PATH,
  ]
  const isValidRouteInDisabledModules = disableInTheseModules.some((path) =>
    isMatchPath(path, pathname, false),
  )
    ? validRoutesInDisabledModules.some((path) => isMatchPath(path, pathname))
    : true

  return !isGlobalTimeFrameDisablePath && isValidRouteInDisabledModules
}

export const useValidTimeframeAggregations = () => {
  const location = useLocation()
  const { t: tf } = useTranslate('timeframe')

  const validTimeframeAggregations = getValidTimeframeAggregations(
    location.pathname,
    tf,
  )
  return validTimeframeAggregations
}

const validExactPaths = [
    '/insights',
    '/insights/insights-report',
    '/insights/insights-report/brand',
    '/insights/insights-report/product',
    '/insights/insights-report/marketplace',
    '/insights/insights-report/category',
    '/insights/1p-vendor-analytics/*',
    `${productListingRoute}/:id/protect/buybox`,
    `${productListingRoute}/:id/protect/suppression`,
    `${productListingRoute}/:id/marketplace/:id/listing/:id/protect/*`,
    `${productListingRoute}/:id/marketplace/:id/listing/:id/traffic/*`,
    `${productListingRoute}/:id/marketplace/:id/listing/:id/content/*`,
    `${productListingRoute}/:id/marketplace/:id/listing/:id/loyalty/*`,
    `${productListingRoute}/:id/traffic/playbooks`,
    `${productListingRoute}/:productId/marketplace/:marketplaceId/listing/:listingId/overview`,
    `${productListingRoute}/:productId/overview`,
    `${sellersCoreRoute}/:id/protect/buybox`,
    '/custom-service/*',
  ],
  validGeneralPaths = [
    '/loyalty',
    '/reports/custom',
    '/traffic',
    '/content',
    '/protect',
    productListingRoute,
  ]
export const useCustomComparisonDateRange = () => {
  const { pathname } = useLocation()
  const [
    shouldShowCustomComparisonDateRange,
    setShouldShowCustomComparisonDateRange,
  ] = useState(false)

  useEffect(() => {
    const isValidPath =
      validExactPaths.some((path) => isMatchPath(path, pathname)) ||
      validGeneralPaths.some((path) => isMatchPath(path, pathname, false))

    setShouldShowCustomComparisonDateRange(isValidPath)
  }, [pathname])

  return shouldShowCustomComparisonDateRange
}

///////////////////////////////////////////////////////////////////////////////////////////////////
// MARKETPLACES HOOKS
///////////////////////////////////////////////////////////////////////////////////////////////////
export const useIsGlobalMarketplacesEnabled = () => {
  const { search, pathname } = useLocation(),
    searchParams = parseParams(search)

  // If user drills down to Insights Report > Marketplace table view button then disable markeplace tab from global filter
  const isMarketplaceDrilledDown = !!(
    pathname === '/insights/insights-report' &&
    Number(searchParams['marketplace_id'])
  )
  const [globalMarketplacesEnabled, setGlobalMarketplacesEnabled] =
    useState(true)

  const isMarketProductDetailsRoute = useIsMarketProductDetailsRoute()

  const isSellersRoute = useIsSellersRoute(),
    allowedSellersPaths = [
      sellersCoreRoute,
      `${sellersCoreRoute}/master-sellers`,
    ],
    allowedSellersRoutes = allowedSellersPaths.some((path) =>
      isMatchPath(path, pathname),
    ),
    isAllowedSellersRoutes = isSellersRoute && allowedSellersRoutes

  const disabledMarketplacesModules = ['/profile'],
    isDisabledMarketplacesModule = disabledMarketplacesModules.some((path) =>
      isMatchPath(path, pathname, false),
    ),
    disabledMarketplacesRoutes = ['/reports/custom'],
    isDisabledMarketplacesRoute = disabledMarketplacesRoutes.some((path) =>
      isMatchPath(path, pathname),
    ),
    disabledInventoryInsightsRoutes = [
      HISTORY_TAB_PATH,
      INVENTORY_INSIGTHS_ITEMS_PATH,
      INVENTORY_INSIGTHS_LISTINGS_PATH,
    ],
    isDisabledInventoryInsightsRoute = disabledInventoryInsightsRoutes.some(
      (path) => isMatchPath(path, pathname),
    )

  useEffect(() => {
    setGlobalMarketplacesEnabled(
      isAllowedSellersRoutes ||
        (!isMarketProductDetailsRoute &&
          !isSellersRoute &&
          !isDisabledMarketplacesModule &&
          !isDisabledMarketplacesRoute &&
          !isDisabledInventoryInsightsRoute),
    )
  }, [
    isSellersRoute,
    isAllowedSellersRoutes,
    isMarketProductDetailsRoute,
    isDisabledMarketplacesRoute,
    isDisabledMarketplacesModule,
    isDisabledInventoryInsightsRoute,
  ])

  return globalMarketplacesEnabled && !isMarketplaceDrilledDown
}

///////////////////////////////////////////////////////////////////////////////////////////////////
// CURRENCY HOOKS
///////////////////////////////////////////////////////////////////////////////////////////////////
export const useIsGlobalCurrencyEnabled = () => {
  const location = useLocation()

  const pathsThatDoNotShowCurrencyFilter = [
      changeHistoryModuleRoute, // isTrafficChangeHistoryRoute
      HISTORY_TAB_PATH, // Inventory insights history
    ],
    playbookRulesPaths = [
      '/settings/traffic/brand/:brandId/playbooks/:playbookId/view-playbook-rules',
      '/traffic/brand/:brandId/playbooks/:playbookId/view-playbook-rules',
    ],
    isPlaybookRulesRoute = playbookRulesPaths.some((path) =>
      isMatchPath(path, location.pathname, false),
    )

  const isMarketsharePriceSegmentsRoute = useIsMarketsharePriceSegmentsRoute()
  const isPriceSegmentsRoute = useIsPriceSegmentsRoute()
  const isBrandHealthRoute = useIsBrandHealthRoute()

  const isPathThatDoesNotShowCurrencyFilter =
    pathsThatDoNotShowCurrencyFilter.includes(location.pathname) ||
    isPlaybookRulesRoute ||
    isMarketsharePriceSegmentsRoute ||
    isPriceSegmentsRoute ||
    isBrandHealthRoute

  return isPathThatDoesNotShowCurrencyFilter
}

///////////////////////////////////////////////////////////////////////////////////////////////////
// TAGS HOOKS
///////////////////////////////////////////////////////////////////////////////////////////////////
export const useIsGlobalTagsEnabled = () => {
  const [globalTagsDisabled, setGlobalTagsDisabled] = useState(false)
  const location = useLocation()
  const { customer: c, brandGroupCustomer: bgc } = useContext(ThemeContext)
  const customer = c as unknown as Customer // current context allows for undefined vendor_id
  const brandGroupCustomer = bgc as BrandGroupCustomer | null
  const isAutoBrandGroupSelected =
    brandGroupCustomer?.grouping_category === 'auto_brand_groups'
  const isBrandGroupSelected = !!brandGroupCustomer

  useEffect(() => {
    /**
     * Enabled tags for brands and auto brand groups
     * Disabled tags for brand groups and All Brands
     */
    if (!isBrandGroupSelected || isAutoBrandGroupSelected) {
      setGlobalTagsDisabled(
        checkGlobalTagsDisabled(
          location.pathname,
          isAutoBrandGroupSelected ? null : customer,
        ),
      )
    } else {
      setGlobalTagsDisabled(true)
    }
  }, [customer, isAutoBrandGroupSelected, isBrandGroupSelected, location])

  return globalTagsDisabled
}

///////////////////////////////////////////////////////////////////////////////////////////////////
// LINES OF BUSINESS HOOKS
///////////////////////////////////////////////////////////////////////////////////////////////////

export const useIsGlobalLinesOfBusinessEnabled = () => {
  const lobFilterConversion = useToggle('lob_filter_conversion')
  const [globalLinesOfBusinessDisabled, setGlobalLinesOfBusinessDisabled] =
    useState(false)
  const location = useLocation()
  useEffect(() => {
    setGlobalLinesOfBusinessDisabled(
      checkGlobalLinesOfBusinessDisabled(
        location.pathname,
        lobFilterConversion,
      ),
    )
  }, [location.pathname, lobFilterConversion])

  return globalLinesOfBusinessDisabled
}

///////////////////////////////////////////////////////////////////////////////////////////////////
// CATEGORIES HOOKS
///////////////////////////////////////////////////////////////////////////////////////////////////
export const useIsGlobalCategoriesEnabled = () => {
  const [globalCategoriesEnabled, setGlobalCategoriesEnabled] = useState(false)
  const location = useLocation()
  const { brandGroupCustomer, customer } = useContext(ThemeContext)
  const multipleBrandsSelected = customer?.id === 0 || !!brandGroupCustomer
  const { search: searchLocation, pathname } = useLocation(),
    searchParams = parseParams(searchLocation)
  // If user drills down to Insights Report > Category table view button then disable categories tab from global filter
  const isCategoryDrilledDown = !!(
    pathname === '/insights/insights-report' &&
    Number(searchParams['category_id'])
  )

  useEffect(() => {
    setGlobalCategoriesEnabled(checkGlobalCategoriesEnabled(location.pathname))
  }, [location])

  return (
    globalCategoriesEnabled && !multipleBrandsSelected && !isCategoryDrilledDown
  )
}

const allCategory = {
  id: -1,
  name: 'All Categories',
  checked: true,
  root_category_id: 0,
  children: [],
  customer_id: 0,
}

export const useFetchCustomCategories = (): {
  loading: boolean
  allCategories: CheckboxCategory[]
} => {
  const { brandGroupCustomer, customer } = useContext(ThemeContext)
  const [allCategories, setAllCategories] = useState<CheckboxCategory[]>([])

  const { data: customCategoriesData, isLoading } = useCustomCategories(
    customer?.id,
  )

  useEffect(() => {
    // If categories for the current customer are not yet loaded, load them
    const categoriesLoaded = notEmpty(allCategories)
    const hasCustomerId =
      categoriesLoaded && notEmpty(allCategories[0]?.customer_id)
    const customerIdMismatch =
      hasCustomerId && allCategories[0].customer_id !== customer?.id
    const isBrandGroup = customer?.id === 0 || !!brandGroupCustomer
    const loadCategories =
      !isBrandGroup &&
      (!categoriesLoaded || !hasCustomerId || customerIdMismatch)

    if (isBrandGroup) {
      setAllCategories((prev) => {
        return isEqual(prev, [allCategory]) ? prev : [allCategory]
      })
    } else if (loadCategories && customCategoriesData) {
      const categories = (customCategoriesData.categories ??
        []) as CheckboxCategory[]
      const _setAllToSelected = (categories: CheckboxCategory[]) => {
        categories.forEach((category) => {
          category.checked = true
          if (category.children) {
            _setAllToSelected(category.children)
          }
        })
      }
      _setAllToSelected(categories)
      const includeAllCategory = [
        {
          ...allCategory,
          children: categories.map((category) => ({
            ...category,
            parent_category_id: -1,
          })),
          customer_id: customer?.id,
        },
      ]
      setAllCategories((prev) => {
        return isEqual(prev, includeAllCategory) ? prev : includeAllCategory
      })
    }
  }, [allCategories, brandGroupCustomer, customer?.id, customCategoriesData])

  return { loading: isLoading, allCategories }
}
