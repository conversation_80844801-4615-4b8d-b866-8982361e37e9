/**
 *  Please be familiar with the documentation in the README.md file before making changes to this file
 *  or any of the related global filter files.
 **/

import React, {
  useCallback,
  useContext,
  useEffect,
  useMemo,
  useRef,
  useState,
} from 'react'
import { useLocation } from 'react-router-dom'
import { useBrandChange, useIndexedDB } from '@predict-hooks'
import { useQuery } from '@tanstack/react-query'
import { isEqual, uniq } from 'lodash'
import moment from 'moment'
import {
  FormFooter,
  getApiUrlPrefix,
  SideDrawer,
  useToggle,
} from '@patterninc/react-ui'
import {
  isMatchPath,
  productListingRoute,
  SecureAxios,
  tryLocalStorageParse,
  useTranslate,
} from '@predict-services'
import type {
  BrandGroupCustomer,
  CategoriesFilter,
  ChurnProps,
  CurrencyFilter,
  GetAvailableMarketplacesProps as GAMProps,
  LinesOfBusinessProps,
  LOBResponseProps,
  Marketplace,
  MarketplaceData,
  MarketplacesFilterDisplayState,
  Region,
  SelectedLinesOfBusinessProps,
  StateType,
  Tags,
  TimeframeFilterProps,
  VatProps,
} from '@predict-types'
import PlanningGlobalFilter from 'src/modules/Planning/PlanningGlobalFilter'

import { ThemeContext } from '../../../Context'
import { useUser } from '../../../context/user-context'
import { updateCurrency as updateCurrencySetting } from '../../services/CurrenciesService'
import {
  areAnyCategoriesSelected,
  findAllCheckedCategories,
  verifyParentsOfSelectedCategories,
} from './CustomCategories'
import FilterSummary from './FilterSummary'
import { defaultCurrency } from './GlobalCurrency'
import {
  checkCurrentTimeframeIsDisabled,
  copyOf,
  getAvailableMarketplaces,
  getDefaultTimeframeForAttainment,
  getDefaultTimeFrameForDestinyAutomationPreview,
  getDefaultTimeframeForLoyalty,
  getDefaultTimeframeForLoyaltyLTV,
  getDefaultTimeframeForMarketShare,
  getDisabledTimeframeOptions,
  getFilteredMarketplaces,
  getFilteredMarketplacesOrderByGroups,
  getFullCurrencyList,
  getNestedIds,
  getValidTimeframe,
  insightsReportRoutes,
  useCheckForValidLocationAndState,
} from './GlobalFilter.helpers'
import {
  useFetchCustomCategories,
  useIsAttainmentRoute,
  useIsBrandHealthRoute,
  useIsBuyBoxRoute,
  useIsContentRoute,
  useIsCustomerServiceRoute,
  useIsGlobalCurrencyEnabled,
  useIsGlobalMarketplacesEnabled,
  useIsGlobalTagsEnabled,
  useIsGlobalTimeframeEnabled,
  useIsLoyaltyLTVRoute,
  useIsLoyaltyRoute,
  useIsLoyaltySnSRoute,
  useIsMarketAnalyticsRoute,
  useIsMarketShare,
  useIsMarketsharePriceSegmentsRoute,
  useIsOrganicTrafficRoute,
  useIsPriceProductsRoute,
  useIsPriceSegmentsRoute,
  useIsSellersDetailsRoute,
  useIsShareAtAGlanceRoute,
  useIsTrafficRoute,
  useIsVATSupportedRoute,
} from './GlobalFilter.hooks'
import GlobalFilterTabs from './Tabs'

///////////////////////////////////////////////////////////////////////////////////////////////////
// TYPES (specific to this file)
///////////////////////////////////////////////////////////////////////////////////////////////////
type MPFU = {
  apiController: GAMProps['apiController']
  brandGroupCustomer: BrandGroupCustomer | null
  customer: { id: number }
  marketplaces: MarketplacesFilterDisplayState
}

type DefaultProps = {
  categories: CategoriesFilter
  currentYear: string
  isAttainmentRoute: boolean
  isShareAtAGlanceRoute: boolean
  isPriceSegmentsRoute: boolean
  isMarketAnalyticsRoute: boolean
  isLoyaltyLTVRoute: boolean
  marketplaces: MarketplacesFilterDisplayState
  linesOfBusiness: LinesOfBusinessProps
}

///////////////////////////////////////////////////////////////////////////////////////////////////
// STATIC VARIABLE FOR GLOBAL FILTER
///////////////////////////////////////////////////////////////////////////////////////////////////
// NOTE: defaultState includes all the loaded categories and marketplaces (AND relevant toggle states)
const defaultState = ({
  categories,
  currentYear,
  isAttainmentRoute,
  isShareAtAGlanceRoute,
  isPriceSegmentsRoute,
  isMarketAnalyticsRoute,
  isLoyaltyLTVRoute,
  marketplaces,
  linesOfBusiness,
}: DefaultProps): StateType => ({
  timeframe: {
    startDate:
      isAttainmentRoute ||
      isPriceSegmentsRoute ||
      isMarketAnalyticsRoute ||
      isShareAtAGlanceRoute ||
      isLoyaltyLTVRoute
        ? moment().startOf('year').format()
        : moment().startOf('day'),
    endDate:
      isAttainmentRoute ||
      isPriceSegmentsRoute ||
      isMarketAnalyticsRoute ||
      isShareAtAGlanceRoute ||
      isLoyaltyLTVRoute
        ? moment().endOf('year').format()
        : moment().endOf('day'),
    timeframe:
      isShareAtAGlanceRoute ||
      isPriceSegmentsRoute ||
      isMarketAnalyticsRoute ||
      isLoyaltyLTVRoute
        ? {
            display: 'Year',
            timeValue: 'year',
            type: 'current',
            aggregation: 'month',
          }
        : {
            display: isAttainmentRoute ? currentYear : 'Day',
            timeValue: isAttainmentRoute ? 'year' : 'day',
            type: isAttainmentRoute ? 'quarterly' : 'current',
            aggregation: isAttainmentRoute ? 'week' : 'hour',
            ...(isAttainmentRoute ? { year: currentYear } : {}),
          },
    comparisonDateRanges: {
      firstRange_startDate: null,
      firstRange_endDate: null,
      secondRange_startDate: null,
      secondRange_endDate: null,
    },
    comparisonPeriodSelection: { value: 'previous_period' },
  },
  marketplaces: {
    ...marketplaces,
    filteredMarketplaces: marketplaces.allMarketplaces,
    rawMarketplaces: getNestedIds(marketplaces.allMarketplaces),
    selectedMarketplaces: [0].concat(
      getNestedIds(marketplaces.allMarketplaces),
    ),
    selectedRegions: [0].concat(
      marketplaces.allRegions.map((region) => region.id),
    ),
  },
  categories: {
    fullCategoryList: categories.fullCategoryList,
    categories: categories.categories,
    searchTerm: '',
  },
  currency: {
    currentCurrency: {
      id: 1,
      name: 'US Dollar',
      symbol: '$',
      code: 'USD',
      type: 'Currency',
      label: '$ — US Dollar',
    },
    allCurrencies: [],
  },
  tags: {
    selectedTags: [],
    tag_ids: [],
    matchAllTags: false,
    customerId: 0,
  },
  vat: {
    id: 1,
    text: 'Exclude',
    value: 'on',
  },
  churn: {
    id: 1,
    text: 'Include',
    value: 'on',
  },
  linesOfBusiness,
})

///////////////////////////////////////////////////////////////////////////////////////////////////
// GLOBAL FILTER STATE COMPONENT
///////////////////////////////////////////////////////////////////////////////////////////////////
const GlobalFilter = () => {
  ///////////////////////////////////////////////////////////////////////////////////////////////////
  // VARIABLES FROM CONTEXT (saved global filter state)
  ///////////////////////////////////////////////////////////////////////////////////////////////////
  const {
    allMarketplaces,
    allRegions,
    amazonMarketplaceCountryCodes,
    amazonMarketplaceIds,
    brandGroupCustomer: bgc,
    customCategories,
    customer,
    endDate,
    startDate,
    timeframe,
    filteredMarketplaces: fMarketplaces,
    firstRange_startDate,
    firstRange_endDate,
    isVatAdjustmentTurnedOn,
    isChurnTurnedOn,
    marketplaceIds,
    marketPlacesColors,
    regions: selectedRegions,
    secondRange_startDate,
    secondRange_endDate,
    selectedComparisonPeriod,
    selectedCurrency,
    sdMarketplaceIds,
    tags,
    linesOfBusiness,
    LOBOptions,
    setVatAdjustmentToggleState,
    setChurnToggleState,
    updateAllMarketplaces,
    updateAmazonMarketplaces,
    updateAllRegions,
    updateCurrencyList,
    updateCustomCategories,
    updateFilteredMarketplaces,
    updateFullCategoryList,
    updateMarketplaces: updateMarketplaceIds,
    updateMarketplacesColor,
    updateRegions,
    updateSDMarketplaces,
    updateSelectedComparisonPeriod,
    updateSelectedCurrency,
    updateTags,
    updateLinesOfBusiness,
    updateLinesOfBusinessOptions,
    updateTimeframe,
    setLimitedToAssignmentsToggleState,
    isLimitedToAssignmentsEnabled,
    setShow1pVendorAnalyticsTab,
  } = useContext(ThemeContext)
  const { user, setUser } = useUser()
  const { t: tf } = useTranslate('timeframe')

  const pathname = useLocation().pathname,
    isPlanningRoute = pathname.includes('planning/intersection')

  const brandGroupCustomer = bgc as BrandGroupCustomer | null

  // This is used to set custom active tab index in case we want to switch to a specific tab
  const [activeTabIndex, setActiveTabIndex] = useState<number | undefined>(
    undefined,
  )

  /////////////////////////////////////////////////////////////////////////////////////////////////
  // TEMP USER STATE (user's selections before clicking apply)
  /////////////////////////////////////////////////////////////////////////////////////////////////
  const [tempUserState, setTempUserState] = useState<StateType>({
    timeframe: {
      timeframe: timeframe,
      startDate: startDate,
      endDate: endDate,
      comparisonDateRanges: {
        firstRange_startDate,
        firstRange_endDate,
        secondRange_startDate,
        secondRange_endDate,
      },
      comparisonPeriodSelection: selectedComparisonPeriod,
    },
    marketplaces: {
      allMarketplaces: allMarketplaces,
      allRegions: allRegions,
      amazonMarketplaces: {
        amazonMarketplaceCodes: amazonMarketplaceCountryCodes,
        amazonMarketplaceIdsData: amazonMarketplaceIds,
      },
      filteredMarketplaces: fMarketplaces,
      marketPlacesColors: marketPlacesColors,
      rawMarketplaces: getNestedIds(allMarketplaces),
      selectedMarketplaces: marketplaceIds,
      selectedRegions: selectedRegions,
      sdMarketplaces: sdMarketplaceIds,
    },
    categories: {
      fullCategoryList: [],
      categories: customCategories,
      searchTerm: '',
    },
    currency: {
      currentCurrency: selectedCurrency,
      allCurrencies: [],
    },
    tags: tryLocalStorageParse('tags_global') ?? tags,
    vat: { value: isVatAdjustmentTurnedOn ? 'on' : 'off' },
    churn: { value: isChurnTurnedOn ? 'on' : 'off' },
    linesOfBusiness:
      tryLocalStorageParse('lines_of_business') ?? linesOfBusiness,
  })
  const chinaDataOrganicTrafficChanges = useToggle(
    'china_data_organic_traffic_changes',
  )
  const [filteredMarketplaces, setFilteredMarketplaces] = useState<
    Array<
      Marketplace | { marketplace_name: string; subMarketplaces: Marketplace[] }
    >
  >(tempUserState.marketplaces.filteredMarketplaces)
  const { setItem, getItem } = useIndexedDB({
    storeName: 'customCategories',
    dbVersion: 1,
    dbName: 'custom-categories-db',
  })

  /////////////////////////////////////////////////////////////////////////////////////////////////
  // ORIGINAL STATE (use for reset functionality)
  /////////////////////////////////////////////////////////////////////////////////////////////////
  const originalState = useRef<StateType>(tempUserState)

  /////////////////////////////////////////////////////////////////////////////////////////////////
  // ROUTES - DEFINITIONS && CHECKS
  /////////////////////////////////////////////////////////////////////////////////////////////////
  const location = useLocation()
  const isAttainmentRoute = useIsAttainmentRoute(),
    isTrafficRoute = useIsTrafficRoute(),
    isVATSupportedRoute = useIsVATSupportedRoute(),
    isContentRoute = useIsContentRoute()
  const isOrganicTrafficRoute = useIsOrganicTrafficRoute()
  const isChinaRegionHidden =
    !chinaDataOrganicTrafficChanges && isOrganicTrafficRoute
  const isCustomerServiceRoute = useIsCustomerServiceRoute()
  const enableChinaForOrganicTraffic = chinaDataOrganicTrafficChanges
    ? true
    : !isOrganicTrafficRoute
  const enableSpecificChineseMarketplaces =
    (isTrafficRoute && enableChinaForOrganicTraffic) ||
    isContentRoute ||
    isCustomerServiceRoute
  const isLoyaltyMarketplaceRoute = useIsLoyaltyRoute()
  const isDestinyAutomationPreviewRoute = pathname.includes(
    '/destiny-automation/preview',
  )

  const isShareAtAGlanceRoute = useIsShareAtAGlanceRoute(),
    isMarketshareRoute = useIsMarketShare(),
    showVatPaths = [
      '/insights',
      ...insightsReportRoutes,
      `${productListingRoute}/:id/marketplace/:id/listing/:id/overview`,
      '/protect/price/products',
    ],
    showVAT = showVatPaths.some((path) => isMatchPath(path, location.pathname)),
    isLoyaltySnSRoute = useIsLoyaltySnSRoute(),
    isLoyaltyLTVRoute = useIsLoyaltyLTVRoute()

  const isSellersDetailsRoute = useIsSellersDetailsRoute(),
    isPriceSegmentsRoute = useIsPriceSegmentsRoute(),
    isMarketAnalyticsRoute = useIsMarketAnalyticsRoute(),
    isBuyBoxRoute = useIsBuyBoxRoute(),
    isPriceProductsRoute = useIsPriceProductsRoute()

  const isBrandHealthRoute = useIsBrandHealthRoute()

  const isVATinGlobalFilterToggleEnabled =
    showVAT || isAttainmentRoute || isVATSupportedRoute

  const [hasRouteChanged, setHasRouteChanged] = useState(false)
  const prevLocation = useRef(location.pathname)

  const disabledTimeframeOptions = useMemo(
    () => getDisabledTimeframeOptions(location.pathname, tf),
    [location.pathname, tf],
  )

  const checkForMarketshareSubRoute = useCallback(
    (path: string) => {
      return (
        prevLocation.current.includes(path) &&
        (location.pathname.includes(path) ||
          location.pathname.includes('insights/share/product-details'))
      )
    },
    [prevLocation, location],
  )

  const checkForProductsDetailsRoute = useCallback(() => {
    return (
      prevLocation.current.includes('insights/share/product-details') &&
      (location.pathname.includes('/marketshare') ||
        location.pathname.includes('/market-analytics'))
    )
  }, [prevLocation, location])

  useEffect(() => {
    const loadCategories = async () => {
      try {
        const data = await getItem('full_category_list')
        if (data) {
          setTempUserState((prevState) => ({
            ...prevState,
            categories: {
              ...prevState.categories,
              fullCategoryList: data,
            },
          }))
        }
      } catch (error) {
        console.error(
          'Failed to load full_category_list from IndexedDB from GlobalFilter:',
          error,
        )
      }
    }

    loadCategories()
  }, [getItem])

  useEffect(() => {
    const isMarketshareSubRoute =
      checkForMarketshareSubRoute('/marketshare') ||
      checkForMarketshareSubRoute('/market-analytics') ||
      checkForProductsDetailsRoute()

    const isDifferentRoute = prevLocation.current !== location.pathname
    if (isDifferentRoute) {
      if (isMarketshareSubRoute) {
        setHasRouteChanged(false)
      } else {
        setHasRouteChanged(isDifferentRoute)
      }
      prevLocation.current = location.pathname
    }
  }, [
    prevLocation,
    location,
    checkForMarketshareSubRoute,
    checkForProductsDetailsRoute,
  ])

  const [prevCurrency, setPrevCurrency] = useState<CurrencyFilter | undefined>()

  const [defaultLOBSelection, setDefaultLOBSelection] =
    useState<LinesOfBusinessProps>({
      selectedTypes: LOBOptions,
      type_ids: LOBOptions?.length
        ? LOBOptions?.map((lob: { value: string }) => lob.value)
        : [],
    })

  ///////////////////////////////////////////////////////////////////////////////////////////////////
  // GENERAL FILTER ACTIONS
  ///////////////////////////////////////////////////////////////////////////////////////////////////
  const [sideDrawerOpen, setSideDrawerOpen] = useState(false)
  const closeFilterSideDrawer = () => {
    setSideDrawerOpen(false)
  }
  const [isWorkingOnInitialLoad, setIsWorkingOnInitialLoad] = useState(true)
  const [updateCustomerMarketplaces, setUpdateCustomerMarketplaces] =
    useState(false)
  const [updateCustomerCategories, setUpdateCustomerCategories] =
    useState(false)
  const [resetTags, setResetTags] = useState(false)
  const [resetLinesOfBusiness, setResetLinesOfBusiness] = useState(false)
  const isValidLocationAndState = useCheckForValidLocationAndState({
    brand: brandGroupCustomer,
    customer,
  })
  const isGlobalTimeframeEnabled = useIsGlobalTimeframeEnabled(),
    isGlobalMarketplacesEnabled = useIsGlobalMarketplacesEnabled(),
    isGlobalCurrencyEnabled = useIsGlobalCurrencyEnabled(),
    isGlobalTagsEnabled = useIsGlobalTagsEnabled(),
    showGlobalFilter =
      (isGlobalTimeframeEnabled ||
        isGlobalMarketplacesEnabled ||
        !isGlobalCurrencyEnabled ||
        !isGlobalTagsEnabled) &&
      isValidLocationAndState

  /** disable apply button if certain criteria are not met due to user's selections */
  const [disableApplyButton, setDisableApplyButton] = useState(false),
    [isLOBSelected, setIsLOBSelected] = useState(true)

  // NOTE: limitSelectionToggleState - the state for the `limit selection to assignment` toggle
  const [limitSelectionToggleState, setLimitSelectionToggleState] = useState(
    brandGroupCustomer?.grouping_category === 'auto_brand_groups',
  )

  // If the user changes limit selection to assignments toggle and reopen sidedrawer then below state will set the latest value of the toggle from the context
  useEffect(() => {
    setLimitSelectionToggleState(isLimitedToAssignmentsEnabled)
  }, [isLimitedToAssignmentsEnabled])

  ///////////////////////////////////////////////
  // If the user changes Customers, Brands or Brand Groups,
  // reset marketplaces and categories
  ///////////////////////////////////////////////
  useEffect(() => {
    // If the user changes CUSTOMERS trigger updates
    if (customerRef?.current !== customer.id) {
      setUpdateCustomerMarketplaces(true)
      setUpdateCustomerCategories(true)
    }
    customerRef.current = customer.id
  }, [customer])

  // Reset tags when brand is changed
  useBrandChange(() => {
    setResetTags(true)
    setResetLinesOfBusiness(true)
  })

  useEffect(() => {
    // If the user changes BRANDS trigger updates
    if (!isEqual(brandRef.current, brandGroupCustomer)) {
      setUpdateCustomerMarketplaces(true)
      setUpdateCustomerCategories(true)
      brandRef.current = brandGroupCustomer
    }
  }, [brandGroupCustomer])

  ///////////////////////////////////////////////

  ///////////////////////////////////////////////
  // Prevent updates to global filter on initial load
  // without this, the global filter will overwrite the
  // user's saved settings
  ///////////////////////////////////////////////
  useEffect(() => {
    if (isWorkingOnInitialLoad) {
      getFullCurrencyList().then((currencyList) => {
        updateCurrencyList(currencyList)
      })
      setIsWorkingOnInitialLoad(false)
    }
  }, [isWorkingOnInitialLoad, updateCurrencyList])

  /////////////////////////////////////////////////////////////////////////////////////////////////
  // TIMEFRAME
  /////////////////////////////////////////////////////////////////////////////////////////////////
  const usersSelectedState = useRef<StateType>(tempUserState)

  const updateFilterTimeframe = useCallback(
    (usersTimeframe: TimeframeFilterProps) => {
      setTempUserState((prevState) => {
        const needsUpdate = !isEqual(usersTimeframe, prevState.timeframe)
        const updatedState = {
          ...prevState,
          timeframe: {
            ...prevState.timeframe,
            ...usersTimeframe,
          },
        }
        usersSelectedState.current = updatedState
        return needsUpdate ? updatedState : prevState
      })
    },
    [],
  )

  const updateGlobalTimeframe = useCallback(
    (userTimeframe: TimeframeFilterProps) => {
      const {
        startDate,
        endDate,
        timeframe,
        comparisonDateRanges,
        comparisonPeriodSelection,
      } = userTimeframe
      updateTimeframe(startDate, endDate, timeframe, comparisonDateRanges)
      updateSelectedComparisonPeriod(comparisonPeriodSelection)
    },
    [updateSelectedComparisonPeriod, updateTimeframe],
  )

  /** EXCEPTIONS **/
  // If the user changes routes where the timeframe is auto-adjusted, restore the timeframe to the user's previous selection when the leave those routes
  const restoreTimeframe = useRef<TimeframeFilterProps | null>(null)

  // Validate the timeframe filter on all routes and automatically update it if necessary
  useEffect(() => {
    const isTimeframeGlobal = tryLocalStorageParse('timeframe_global')
    // Allowing the timeframe to be updated on initial load will prevent unsupported timeframe from being applied when user navigates to a specific route using URL
    if (!isTimeframeGlobal || hasRouteChanged || isWorkingOnInitialLoad) {
      /** this is the user's saved/applied global timeframe */
      const userTimeframe = originalState.current.timeframe

      const isSelectedTimeframeIsDisabled = checkCurrentTimeframeIsDisabled(
        location.pathname,
        userTimeframe.timeframe,
        tf,
      )

      let updatedTimeframeForRoute: TimeframeFilterProps

      /////////////////////////////////////////////////////////
      // Updates based on route
      if (isAttainmentRoute) {
        updatedTimeframeForRoute = getDefaultTimeframeForAttainment(
          userTimeframe,
          tf,
        )
      } else if (isLoyaltySnSRoute) {
        updatedTimeframeForRoute = getDefaultTimeframeForLoyalty(
          userTimeframe,
          timeframe,
        )
      } else if (isLoyaltyLTVRoute) {
        updatedTimeframeForRoute = getDefaultTimeframeForLoyaltyLTV(
          userTimeframe,
          timeframe,
        )
      } else if (
        isSelectedTimeframeIsDisabled &&
        (isMarketshareRoute || isMarketAnalyticsRoute || isShareAtAGlanceRoute)
      ) {
        updatedTimeframeForRoute =
          getDefaultTimeframeForMarketShare(userTimeframe)
      } else if (isDestinyAutomationPreviewRoute) {
        updatedTimeframeForRoute =
          getDefaultTimeFrameForDestinyAutomationPreview(userTimeframe)
      }
      // validate timeframe for all other routes
      else {
        updatedTimeframeForRoute = getValidTimeframe(
          userTimeframe,
          location.pathname,
          tf,
        )
      }
      /////////////////////////////////////////////////////////

      /////////////////////////////////////////////////////////
      // if the user has changed routes to Attainment, Loyalty SnS, or Retail Readiness save the previous timeframe selection
      if (
        [
          isAttainmentRoute,
          isLoyaltySnSRoute,
          isLoyaltyLTVRoute,
          isDestinyAutomationPreviewRoute,
          isShareAtAGlanceRoute,
          isMarketAnalyticsRoute,
          isPriceSegmentsRoute,
          isMarketshareRoute,
        ].includes(true) &&
        !restoreTimeframe.current
      ) {
        restoreTimeframe.current = {
          timeframe,
          startDate,
          endDate,
          comparisonDateRanges: {
            firstRange_startDate,
            firstRange_endDate,
            secondRange_startDate,
            secondRange_endDate,
          },
          comparisonPeriodSelection: selectedComparisonPeriod,
        }
        //
        // if the user has left Attainment, Loyalty SnS / LTV, or Retail Readiness restore the previous timeframe selection
      } else if (
        [
          isAttainmentRoute,
          isLoyaltySnSRoute,
          isLoyaltyLTVRoute,
          isDestinyAutomationPreviewRoute,
          isShareAtAGlanceRoute,
          isMarketAnalyticsRoute,
          isPriceSegmentsRoute,
          isMarketshareRoute,
        ].every((condition) => condition === false) &&
        restoreTimeframe.current
      ) {
        updatedTimeframeForRoute = { ...restoreTimeframe.current }
        restoreTimeframe.current = null
      }
      // if the user updates the timeframe selection while on Attainment, Loyalty SnS,  or Retail Readiness clear the previous timeframe selection
      //   -- clear the restoreTimeframe ref when the user `applies` their updated settings in the `apply` method below
      //
      ////////////////////////////////////////////////

      // VALIDATE IF THE TIMEFRAME NEEDS TO BE UPDATED //
      const shouldUpdateTimeframe =
        // this `userTimeframe` is the global state
        // and if the global state is not equal to the updated state
        // both temporary and global state should be updated
        !isEqual(updatedTimeframeForRoute, userTimeframe)

      if (shouldUpdateTimeframe) {
        updateFilterTimeframe(updatedTimeframeForRoute)
        updateGlobalTimeframe(updatedTimeframeForRoute)
        originalState.current.timeframe = updatedTimeframeForRoute
      }
      setHasRouteChanged(false)
    }
  }, [
    endDate,
    firstRange_endDate,
    firstRange_startDate,
    hasRouteChanged,
    isAttainmentRoute,
    isLoyaltyLTVRoute,
    isLoyaltySnSRoute,
    isWorkingOnInitialLoad,
    location.pathname,
    secondRange_endDate,
    secondRange_startDate,
    selectedComparisonPeriod,
    startDate,
    timeframe,
    updateFilterTimeframe,
    updateGlobalTimeframe,
    isShareAtAGlanceRoute,
    isMarketAnalyticsRoute,
    isPriceSegmentsRoute,
    isMarketshareRoute,
    disabledTimeframeOptions,
    tf,
    isDestinyAutomationPreviewRoute,
  ])

  /////////////////////////////////////////////////////////////////////////////////////////////////
  // MARKETPLACES
  /////////////////////////////////////////////////////////////////////////////////////////////////
  const updateMarketplaces = useCallback((marketplaceData: MarketplaceData) => {
    setTempUserState((prevState) => {
      return {
        ...prevState,
        marketplaces: {
          ...prevState.marketplaces,
          /** ALL MARKETPLACES */
          ...(isEqual(
            prevState.marketplaces.allMarketplaces,
            marketplaceData.allMarketplaces,
          )
            ? {}
            : {
                allMarketplaces: marketplaceData.allMarketplaces,
              }),
          /** ALL REGIONS */
          ...(isEqual(
            prevState.marketplaces.allRegions,
            marketplaceData.allRegions,
          )
            ? {}
            : { allRegions: marketplaceData.allRegions }),
          /** RAW MARKETPLACES */
          ...(isEqual(
            prevState.marketplaces.rawMarketplaces,
            marketplaceData.rawMarketplaces,
          )
            ? {}
            : { rawMarketplaces: marketplaceData.rawMarketplaces }),
          /** SELECTED MARKETPLACES */
          ...(isEqual(
            prevState.marketplaces.selectedMarketplaces,
            marketplaceData.selectedMarketplaces,
          )
            ? {}
            : { selectedMarketplaces: marketplaceData.selectedMarketplaces }),
          /** SELECTED REGIONS */
          ...(isEqual(
            prevState.marketplaces.selectedRegions,
            marketplaceData.selectedRegions,
          )
            ? {}
            : { selectedRegions: marketplaceData.selectedRegions }),
          /** SD MARKETPLACES */
          ...(isEqual(
            prevState.marketplaces.sdMarketplaces,
            marketplaceData.sdMarketplaces,
          )
            ? {}
            : { sdMarketplaces: marketplaceData.sdMarketplaces }),
          /** MARKETPLACE COLORS */
          ...(isEqual(
            prevState.marketplaces.marketPlacesColors,
            marketplaceData.marketPlacesColors,
          )
            ? {}
            : { marketPlacesColors: marketplaceData.marketPlacesColors }),
          /** AMAZON MARKETPLACES */
          ...(isEqual(
            prevState.marketplaces.amazonMarketplaces,
            marketplaceData.amazonMarketplaces,
          )
            ? {}
            : { amazonMarketplaces: marketplaceData.amazonMarketplaces }),
          /** FILTERED MARKETPLACES BY GROUPS */
          ...(isEqual(
            prevState.marketplaces.filteredMarketplaces,
            marketplaceData.filteredMarketplaces,
          )
            ? {}
            : { filteredMarketplaces: marketplaceData.filteredMarketplaces }),
        },
      }
    })
    setDisableApplyButton(marketplaceData?.selectedMarketplaces?.length === 0)
  }, [])

  /**
   * This function is used to get the supported regions for the selected brand and if the selected
   * regions are not supported for the selected brand then it will return all supported regions.
   */
  const getBrandSupportedRegionIds = useCallback(
    (allReg: Region[], selectedRegIds: number[]) => {
      const filtRegions = allReg.filter((region) =>
        selectedRegIds.includes(region.id),
      )
      const allSupportedRegIds = [0].concat(allReg.map((region) => region.id))
      const validSelectedRegIds =
        filtRegions.length === 0 || selectedRegIds.length > allReg.length
          ? allSupportedRegIds
          : selectedRegIds
      // e.g. If a brand group is selected with the region South America and if user changes a brand which does not
      // support South America region in this case it will return all supported regions for that selected brand (All Regions).
      return validSelectedRegIds
    },
    [],
  )

  const updateGlobalMarketplaces = useCallback(
    (marketplaces: MarketplacesFilterDisplayState) => {
      const {
        filteredMarketplaces,
        allMarketplaces,
        sdMarketplaces,
        selectedMarketplaces,
        selectedRegions,
        marketPlacesColors,
      } = marketplaces

      const _selectedAmazonMarketplaces: Marketplace[] = allMarketplaces.filter(
        (mp) => mp.amazon && selectedMarketplaces.includes(mp.id),
      )
      const _amazonMarketplaceCountryCodes = _selectedAmazonMarketplaces.map(
        (mPlace) => mPlace.country_code,
      )
      const _amazonMarketplaceIds = _selectedAmazonMarketplaces.map(
        (mPlace) => mPlace.id,
      )

      updateMarketplaceIds(selectedMarketplaces)
      updateFilteredMarketplaces(filteredMarketplaces)
      updateSDMarketplaces(sdMarketplaces)
      const validSelectedRegionIds = getBrandSupportedRegionIds(
        marketplaces.allRegions,
        selectedRegions,
      )

      updateRegions(validSelectedRegionIds)
      updateMarketplacesColor(marketPlacesColors)
      updateAmazonMarketplaces(
        _amazonMarketplaceCountryCodes,
        _amazonMarketplaceIds,
      )
      updateAllMarketplaces(allMarketplaces)
    },
    [
      updateAllMarketplaces,
      updateAmazonMarketplaces,
      updateFilteredMarketplaces,
      updateMarketplaceIds,
      updateMarketplacesColor,
      updateRegions,
      updateSDMarketplaces,
      getBrandSupportedRegionIds,
    ],
  )

  /**
   * Below useEffect is to make call checkMarketplacesForUser() to get the SHARE and Advisory Market Analytics and Price Segments specific marketplaces
   */
  useEffect(() => {
    const updateMarketplacesIfRequired = () => {
      if (
        isShareAtAGlanceRoute ||
        isMarketAnalyticsRoute ||
        isPriceSegmentsRoute
      ) {
        setUpdateCustomerMarketplaces(true)
      }
    }
    updateMarketplacesIfRequired()

    // Need to run this on unmount as well, so that marketplaces are updated when user changes the route.
    return () => updateMarketplacesIfRequired()
  }, [isMarketAnalyticsRoute, isPriceSegmentsRoute, isShareAtAGlanceRoute])

  /** EXCEPTIONS **/
  // If the user changes Brands and the available marketplaces change, the marketplace filter may be automatically updated
  const customerRef = useRef<number>(undefined)
  const brandRef = useRef<BrandGroupCustomer | null>(undefined)

  const checkMarketplacesForUser = useCallback(
    ({ apiController, brandGroupCustomer, customer, marketplaces }: MPFU) => {
      const params = {
        ...(brandGroupCustomer?.customer_ids?.length
          ? { customer_ids: brandGroupCustomer?.customer_ids }
          : { customer_id: customer.id }),
      }

      getAvailableMarketplaces({
        apiController,
        isShareAtAGlanceRoute,
        isLoyaltyMarketplaceRoute,
        isMarketAnalyticsRoute,
        isPriceSegmentsRoute,
        isBuyBoxRoute,
        isPriceProductsRoute,
        isDestinyAutomationPreviewRoute,
        params,
        selectedMarketplaces: marketplaces.selectedMarketplaces,
        selectedRegions: marketplaces.selectedRegions,
        isBrandHealthRoute,
        disableChinaForOrganicTraffic: isChinaRegionHidden,
      }).then((marketplaceData) => {
        if (marketplaceData) {
          const validSelectedRegionIds = getBrandSupportedRegionIds(
            marketplaceData.allBrandRegions,
            selectedRegions,
          )

          const updatedMarketplaces = {
            ...marketplaces,
            selectedRegions: validSelectedRegionIds,
            allMarketplaces: marketplaceData.allBrandMarketplaces,
            allRegions: marketplaceData.allBrandRegions,
            filteredMarketplaces: marketplaceData.allBrandMarketplaces,
            selectedMarketplaces: Array.from(
              marketplaceData.matchingMarketplaces,
            ),
            amazonMarketplaces: {
              /**
               * NOTE: Hardcoded 1 marketplace is Amazon US that is only for the SHARE page as we are restricting all other marketplaces for initial launch
               * TODO: Remove hardcoded amazonMarketplaceCodes, amazonMarketplaceIdsData values once we start supporting other than Amazon US marketplaces
               * for SHARE and Advisory > Market Analytics and (Advisory and Insights) > Price Segments
               */
              amazonMarketplaceCodes:
                (isShareAtAGlanceRoute ||
                  isMarketAnalyticsRoute ||
                  isPriceSegmentsRoute) &&
                marketplaceData.amazonMarketplaceCodes.length === 1 &&
                marketplaceData.amazonMarketplaceCodes[0] === 'US'
                  ? ['US']
                  : marketplaceData.amazonMarketplaceCodes,
              amazonMarketplaceIdsData:
                (isShareAtAGlanceRoute ||
                  isMarketAnalyticsRoute ||
                  isPriceSegmentsRoute) &&
                marketplaceData.amazonMarketplaceIdsData.length === 1 &&
                marketplaceData.amazonMarketplaceIdsData[0] === 1
                  ? [1]
                  : marketplaceData.amazonMarketplaceIdsData,
            },
            rawMarketplaces: marketplaceData.rawMarketplaces,
            sdMarketplaces: marketplaceData.sdMarketplaces,
            marketPlacesColors: marketplaceData.marketPlacesColors,
          }
          updateMarketplaces(updatedMarketplaces)
          updateGlobalMarketplaces(updatedMarketplaces)
        }
      })
    },
    [
      isBuyBoxRoute,
      isLoyaltyMarketplaceRoute,
      isMarketAnalyticsRoute,
      isPriceProductsRoute,
      isPriceSegmentsRoute,
      isShareAtAGlanceRoute,
      updateGlobalMarketplaces,
      updateMarketplaces,
      getBrandSupportedRegionIds,
      selectedRegions,
      isBrandHealthRoute,
      isChinaRegionHidden,
      isDestinyAutomationPreviewRoute,
    ],
  )

  /////////////////////////////////////////////////////////
  // If the marketplaces are changed by going to
  // Opportunities routes or Loyalty routes or Marketshare routes or Market Analytics routes or Price Segments routes or Buy Box routes or Protect > Price > Products route
  // restore the marketplaces to the user's previous selection when the leave those routes
  /////////////////////////////////////////////////////////
  const restoreMarketplaces = useRef<MarketplacesFilterDisplayState | null>(
    null,
  )

  useEffect(() => {
    // if the user has changed routes to Opportunities or Loyalty,
    // or if the user has gone to a Seller's details route
    // or if the user has navigated to Share > MarketShare or Advisory > Market Analytics or Insights > Share > Price Segments or Advisory > Price Segments
    // or if the user has gone to Protect > Buy Box route
    // or if the user has gone to Protect > Price > Products route
    // or if the user has gone to Insights > Brand Health route
    // save the current marketplace selection
    if (
      (isSellersDetailsRoute ||
        isLoyaltyMarketplaceRoute ||
        isDestinyAutomationPreviewRoute ||
        isContentRoute ||
        isShareAtAGlanceRoute ||
        isMarketAnalyticsRoute ||
        isPriceSegmentsRoute ||
        isBuyBoxRoute ||
        isPriceProductsRoute ||
        isBrandHealthRoute) &&
      !restoreMarketplaces.current
    ) {
      restoreMarketplaces.current = {
        ...tempUserState.marketplaces,
        filteredMarketplaces: fMarketplaces,
        sdMarketplaces: sdMarketplaceIds,
        selectedMarketplaces: marketplaceIds,
        selectedRegions,
        marketPlacesColors,
        amazonMarketplaces: {
          amazonMarketplaceCodes: amazonMarketplaceCountryCodes,
          amazonMarketplaceIdsData: amazonMarketplaceIds,
        },
      }

      // if the user has left Opportunities, restore the marketplace selection
    } else if (
      !isSellersDetailsRoute &&
      !isLoyaltyMarketplaceRoute &&
      !isDestinyAutomationPreviewRoute &&
      !isContentRoute &&
      !isShareAtAGlanceRoute &&
      !isMarketAnalyticsRoute &&
      !isPriceSegmentsRoute &&
      !isBuyBoxRoute &&
      !isPriceProductsRoute &&
      !isBrandHealthRoute &&
      restoreMarketplaces.current
    ) {
      const updatedMarketplaces = { ...restoreMarketplaces.current }
      updateMarketplaces(updatedMarketplaces)
      updateGlobalMarketplaces(updatedMarketplaces)
      restoreMarketplaces.current = null
    }
    // if the user updates the marketplace selection while on Opportunities,
    // clear the saved marketplace selection
    // Note: setting the restoreMarketplaces ref to `null` when the user `applies` their updated
    // settings in the `apply` method below
  }, [
    amazonMarketplaceCountryCodes,
    amazonMarketplaceIds,
    fMarketplaces,
    isBuyBoxRoute,
    isContentRoute,
    isLoyaltyMarketplaceRoute,
    isMarketAnalyticsRoute,
    isPriceProductsRoute,
    isPriceSegmentsRoute,
    isSellersDetailsRoute,
    isShareAtAGlanceRoute,
    marketPlacesColors,
    marketplaceIds,
    sdMarketplaceIds,
    selectedRegions,
    tempUserState.marketplaces,
    updateGlobalMarketplaces,
    updateMarketplaces,
    isBrandHealthRoute,
    isDestinyAutomationPreviewRoute,
  ])

  const checkMarketplaces = useRef({ customerId: 0, attempts: 0 })
  useEffect(() => {
    const apiController = new AbortController()
    if (checkMarketplaces.current.customerId === customer.id) {
      checkMarketplaces.current.attempts += 1
    } else {
      checkMarketplaces.current = { customerId: customer.id, attempts: 1 }
    }

    if (isWorkingOnInitialLoad) {
      // don't update the marketplace filter on initial load
      // (it is set when userTempState is initialized)
    } else if (
      updateCustomerMarketplaces ||
      (allMarketplaces.length === 0 && checkMarketplaces.current.attempts < 3)
    ) {
      checkMarketplacesForUser({
        apiController,
        brandGroupCustomer,
        customer,
        marketplaces: {
          allMarketplaces: allMarketplaces,
          allRegions: allRegions,
          amazonMarketplaces: {
            amazonMarketplaceCodes: amazonMarketplaceCountryCodes,
            amazonMarketplaceIdsData: amazonMarketplaceIds,
          },
          filteredMarketplaces: fMarketplaces,
          marketPlacesColors: marketPlacesColors,
          rawMarketplaces: getNestedIds(allMarketplaces),
          selectedMarketplaces: marketplaceIds,
          selectedRegions: selectedRegions,
          sdMarketplaces: sdMarketplaceIds,
        },
      })
      brandRef.current = brandGroupCustomer
      setUpdateCustomerMarketplaces(false)
      setHasRouteChanged(false)
    }
  }, [
    allMarketplaces,
    allRegions,
    amazonMarketplaceCountryCodes,
    amazonMarketplaceIds,
    brandGroupCustomer,
    checkMarketplacesForUser,
    customer,
    fMarketplaces,
    hasRouteChanged,
    isShareAtAGlanceRoute,
    isWorkingOnInitialLoad,
    marketPlacesColors,
    marketplaceIds,
    sdMarketplaceIds,
    selectedRegions,
    updateCustomerMarketplaces,
  ])

  /////////////////////////////////////////////////////////
  // If the marketplaceIds are updated by another component,
  // update the global filter state to match
  /////////////////////////////////////////////////////////
  useEffect(() => {
    setTempUserState((prevState) => {
      const updateUserState = !isEqual(
        prevState.marketplaces.selectedMarketplaces,
        marketplaceIds,
      )

      return updateUserState
        ? {
            ...prevState,
            marketplaces: {
              ...prevState.marketplaces,
              selectedMarketplaces: marketplaceIds,
            },
          }
        : prevState
    })
  }, [marketplaceIds])

  /////////////////////////////////////////////////////////
  // If the Tags are updated by another component,
  // update the global filter state to match
  /////////////////////////////////////////////////////////
  useEffect(() => {
    setTempUserState((prevState) => {
      const updateUserState = !isEqual(prevState.tags, tags)

      return updateUserState
        ? {
            ...prevState,
            tags: { ...tags },
          }
        : prevState
    })
  }, [marketplaceIds, tags])

  /////////////////////////////////////////////////////////////////////////////////////////////////
  // CUSTOM CATEGORIES
  /////////////////////////////////////////////////////////////////////////////////////////////////
  const resetCategorySearch = useCallback(() => {
    setTempUserState((prevState) => ({
      ...prevState,
      categories: {
        ...prevState.categories,
        searchTerm: '',
      },
    }))
  }, [])

  const updateCategories = useCallback(
    async (categoriesData: CategoriesFilter) => {
      const { categories, fullCategoryList, searchTerm } = categoriesData

      await setItem('full_category_list', JSON.stringify(fullCategoryList))
      setTempUserState((prevState) => ({
        ...prevState,
        categories: { categories, fullCategoryList, searchTerm },
      }))

      setDisableApplyButton(
        categories.length > 0 && !areAnyCategoriesSelected(categories),
      )
    },
    [setItem],
  )

  const updateGlobalCustomCategories = useCallback(
    (userCategories: CategoriesFilter) => {
      // submit all selected categories regardless of
      // search filter visibility (`categories` will only
      // show visibly selected categories)
      verifyParentsOfSelectedCategories(userCategories.fullCategoryList)
      const selectedCategories = uniq(
        findAllCheckedCategories(userCategories.fullCategoryList),
      )
      updateCustomCategories(selectedCategories)
      updateFullCategoryList(userCategories.fullCategoryList)
    },
    [updateCustomCategories, updateFullCategoryList],
  )

  const { allCategories } = useFetchCustomCategories()
  useEffect(() => {
    if (isWorkingOnInitialLoad) {
      // don't update the custom categories here on initial load
      // (it is set from localStorage when userTempState is initialized)
      setUpdateCustomerCategories(false)
    } else {
      const _allCategories = copyOf(allCategories)
      const noCategoriesInState =
        !tempUserState.categories.fullCategoryList?.[0]?.children ||
        tempUserState.categories.fullCategoryList?.[0]?.children.length === 0
      const isBrandGroup = !!brandGroupCustomer || customer.id === 0
      const categoriesFetched =
        // when allCategories has been fetched the customer_id will be set and will match the current customer
        _allCategories?.[0]?.customer_id === customer.id ||
        // there are no categories to fetch for any brand group or `all brands`
        isBrandGroup

      if (
        (updateCustomerCategories || noCategoriesInState) &&
        categoriesFetched
      ) {
        const newUpdatedCategories = {
          categories: _allCategories,
          fullCategoryList: _allCategories,
          searchTerm: '',
        }

        if (
          !isEqual(_allCategories, tempUserState.categories.fullCategoryList)
        ) {
          updateCategories(newUpdatedCategories)
          updateGlobalCustomCategories(newUpdatedCategories)
          setUpdateCustomerCategories(false)
        }
      }
    }
  }, [
    allCategories,
    brandGroupCustomer,
    customer.id,
    getItem,
    isWorkingOnInitialLoad,
    tempUserState.categories,
    tempUserState.categories.fullCategoryList,
    updateCategories,
    updateCustomerCategories,
    updateGlobalCustomCategories,
  ])

  /////////////////////////////////////////////////////////////////////////////////////////////////
  // CURRENCY
  /////////////////////////////////////////////////////////////////////////////////////////////////
  const globalCurrencyDisabled = useIsGlobalCurrencyEnabled()
  const isMarketsharePriceSegmentRoute = useIsMarketsharePriceSegmentsRoute()

  const updateCurrency = useCallback((currencyData: CurrencyFilter) => {
    setTempUserState((prevState) => {
      return {
        ...prevState,
        currency: currencyData,
      }
    })
  }, [])

  const updateGlobalCurrency = useCallback(
    (userCurrency: CurrencyFilter) => {
      //NOTE: currency calculations are done from the db value so that value must be updated first
      updateCurrencySetting(userCurrency.currentCurrency.id) // update the user's currency setting in the database
        .then(() => {
          const shouldUpdateUser =
            userCurrency.currentCurrency?.code !== user.current_currency?.code
          shouldUpdateUser &&
            setUser((prevUser) => ({
              ...prevUser,
              current_currency: userCurrency.currentCurrency,
            }))

          updateSelectedCurrency(userCurrency.currentCurrency) // update currency in the global filter context
        })
    },
    [setUser, updateSelectedCurrency, user.current_currency?.code],
  )

  // Updating the currency to defaultCurrency (USD) when the user navigates to Marketshare Price Segments and Brand Health routes
  // And changing the currency back to the previous currency when the user navigates away from Marketshare Price Segments and Brand Health  routes
  // This will be updated/ removed when price segments and brand health starts supporting more currencies
  useEffect(() => {
    if (!hasRouteChanged) return

    if (
      (isMarketsharePriceSegmentRoute ||
        isPriceSegmentsRoute ||
        isBrandHealthRoute) &&
      !prevCurrency
    ) {
      const currency = {
        ...tempUserState.currency,
        currentCurrency: defaultCurrency,
      }
      setPrevCurrency(tempUserState.currency)
      updateGlobalCurrency(currency)
      updateCurrency(currency)
    } else if (
      !isMarketsharePriceSegmentRoute &&
      !isPriceSegmentsRoute &&
      !isBrandHealthRoute &&
      prevCurrency
    ) {
      updateGlobalCurrency(prevCurrency)
      updateCurrency(prevCurrency)
      setPrevCurrency(undefined)
    }
  }, [
    hasRouteChanged,
    isBrandHealthRoute,
    isMarketsharePriceSegmentRoute,
    isPriceSegmentsRoute,
    prevCurrency,
    tempUserState.currency,
    updateCurrency,
    updateGlobalCurrency,
  ])

  /////////////////////////////////////////////////////////////////////////////////////////////////
  // TAGS
  /////////////////////////////////////////////////////////////////////////////////////////////////
  const updateTagsFilter = useCallback((tagData: Tags) => {
    setTempUserState((prevState) => ({
      ...prevState,
      tags: tagData,
    }))
  }, [])

  const updateGlobalTags = useCallback(
    (userTags: Tags) => {
      updateTags(userTags)
    },
    [updateTags],
  )

  useEffect(() => {
    if (resetTags) {
      const tags = { ...tempUserState.tags }
      tags.selectedTags = []
      tags.tag_ids = []
      updateTagsFilter(tags)
      updateGlobalTags(tags)
      setResetTags(false)
    }
  }, [
    resetTags,
    tempUserState,
    tempUserState.tags,
    updateGlobalTags,
    updateTagsFilter,
  ])

  /////////////////////////////////////////////////////////////////////////////////////////////////
  // Lines of Business
  /////////////////////////////////////////////////////////////////////////////////////////////////
  const updateLinesOfBusinessFilter = useCallback(
    (linesOfBusiness: LinesOfBusinessProps) => {
      setTempUserState((prevState) => ({
        ...prevState,
        linesOfBusiness: linesOfBusiness,
      }))
    },
    [],
  )

  const updateGlobalLinesOfBusiness = useCallback(
    (userLinesOfBusiness: LinesOfBusinessProps) => {
      updateLinesOfBusiness(userLinesOfBusiness)
    },
    [updateLinesOfBusiness],
  )

  const getAllBrandsAndBrandGroupsLOBs = useCallback(
    (lob: LOBResponseProps[]) => {
      const allBrandsLOB: LinesOfBusinessProps = {
        selectedTypes: [],
        type_ids: [],
      }

      lob?.forEach((dataItem) => {
        // In both case when sensitive is not present or sensitive is false we want to update the LOB for `All Brands` and brand groups
        if (!dataItem?.sensitive) {
          const { code, name, id, sensitive } = dataItem
          allBrandsLOB.selectedTypes.push({
            value: code,
            title: name,
            id,
            sensitive,
          })
          allBrandsLOB.type_ids.push(dataItem.code)
        }
      })
      return allBrandsLOB
    },
    [],
  )

  useEffect(() => {
    if (resetLinesOfBusiness) {
      const linesOfBusiness = { ...tempUserState.linesOfBusiness }
      updateLinesOfBusinessFilter(linesOfBusiness)
      updateGlobalLinesOfBusiness(linesOfBusiness)
      updateLinesOfBusinessOptions(linesOfBusiness.selectedTypes)
      setResetLinesOfBusiness(false)
      setIsLOBSelected(true)
    }
  }, [
    resetLinesOfBusiness,
    tempUserState.linesOfBusiness,
    updateGlobalLinesOfBusiness,
    updateLinesOfBusinessFilter,
    updateLinesOfBusinessOptions,
  ])

  useEffect(() => {
    if (!isWorkingOnInitialLoad) {
      let selectedLOBs = {
        selectedTypes: [],
        type_ids: [],
      }

      const storedLOBData = localStorage.getItem('lines_of_business')
      const parsedLOBData = storedLOBData ? JSON.parse(storedLOBData) : null

      if (
        (!brandGroupCustomer?.id &&
          parsedLOBData?.customerId === customer?.id) ||
        brandGroupCustomer?.id === parsedLOBData?.customerId
      ) {
        selectedLOBs = parsedLOBData?.lobs
      }
      updateLinesOfBusinessFilter(selectedLOBs)
      updateGlobalLinesOfBusiness(selectedLOBs)
      updateLinesOfBusinessOptions(selectedLOBs?.selectedTypes)
    }
  }, [
    brandGroupCustomer?.id,
    customer,
    isWorkingOnInitialLoad,
    updateGlobalLinesOfBusiness,
    updateLinesOfBusinessFilter,
    updateLinesOfBusinessOptions,
  ])

  /////////////////////////////////////////////////////////////////////////////////////////////////
  // VAT ADJUSTMENT
  /////////////////////////////////////////////////////////////////////////////////////////////////
  const updateVatAdjustment = useCallback((vatAdjustment: VatProps) => {
    setTempUserState((prevState) => ({
      ...prevState,
      vat: {
        ...prevState.vat,
        ...vatAdjustment,
      },
    }))
  }, [])

  const updateGlobalVatAdjustment = (userVat: VatProps) => {
    setVatAdjustmentToggleState(userVat.value === 'on')
  }

  /////////////////////////////////////////////////////////////////////////////////////////////////
  // CHURN TOGGLE
  /////////////////////////////////////////////////////////////////////////////////////////////////
  const updateChurn = useCallback((churn: ChurnProps) => {
    setTempUserState((prevState) => ({
      ...prevState,
      churn: {
        ...prevState.churn,
        ...churn,
      },
    }))
  }, [])

  const updateGlobalChurn = (userChurn: ChurnProps) => {
    setChurnToggleState(userChurn.value === 'on')
  }

  /////////////////////////////////////////////////////////////////////////////////////////////////
  // ADDITIONAL FILTER ACTIONS - STATE UPDATES
  /////////////////////////////////////////////////////////////////////////////////////////////////
  const updateGlobalValues = () => {
    updateGlobalTimeframe(tempUserState.timeframe)
    updateGlobalMarketplaces(tempUserState.marketplaces)

    const allSelectedCategories = {
      fullCategoryList: allCategories,
      categories: customCategories,
      searchTerm: '',
    }

    // This change is intended to prevent all APIs from failing with a 431 error when all categories are selected, as the previous state (tempUserState.categories) was sending all category IDs in the request."
    const finalCategories = tempUserState.categories.fullCategoryList?.[0]
      ?.checked
      ? allSelectedCategories
      : tempUserState.categories

    updateGlobalCustomCategories(finalCategories)
    updateGlobalCurrency(tempUserState.currency)
    updateGlobalTags(tempUserState.tags)
    updateLinesOfBusiness(tempUserState.linesOfBusiness)
    updateGlobalVatAdjustment(tempUserState.vat)
    updateGlobalChurn(tempUserState.churn)
  }

  const applyFilter = () => {
    // update global filter context with user's selected state
    updateGlobalValues()

    // reset refs for new global state
    originalState.current = tempUserState
    restoreMarketplaces.current = null
    restoreTimeframe.current = null
    resetCategorySearch()

    // Update limit selection to assignment toggle value in context
    setLimitedToAssignmentsToggleState(limitSelectionToggleState)
    // close the global filter side drawer
    closeFilterSideDrawer()
  }

  const cancelFilter = useCallback(() => {
    closeFilterSideDrawer()
    // reset to state before user made changes
    setTempUserState(originalState.current)
  }, [])

  const resetFilter = () => {
    // reset to default state...
    // include all loaded categories and marketplaces
    // and reset the timeframe to the current year
    // and include relevant toggle states
    const currentYear = moment().format('YYYY')
    setTempUserState((prevState) =>
      defaultState({
        currentYear,
        isAttainmentRoute,
        isShareAtAGlanceRoute,
        isPriceSegmentsRoute,
        isMarketAnalyticsRoute,
        isLoyaltyLTVRoute,
        marketplaces: prevState.marketplaces,
        categories: {
          ...prevState.categories,
          categories: allCategories,
          fullCategoryList: allCategories,
        },
        linesOfBusiness: defaultLOBSelection,
      }),
    )
    /**
     * Explicitly calling updateFullCategoryList(allCategories) is necessary to keep the fullCategoryList context in sync when the reset button is clicked.
     * This is because fullCategoryList is now stored in IndexedDB instead of localStorage so the old flow is changed.
     * Previously, we could set the default state (tempUserState.categories.fullCategoryList) from localStorage,
     * but with IndexedDB, this is no longer possible since its data is retrieved asynchronously (as a Promise),
     * and React state cannot be initialized with a Promise.
     */
    updateFullCategoryList(allCategories)
    setDisableApplyButton(false)
    setIsLOBSelected(true)
  }

  useEffect(() => {
    const _filteredMarketplaces = getFilteredMarketplaces({
      enableSpecificChineseMarketplaces,
      allMarketplaces,
      allRegions,
      selectedRegions: tempUserState.marketplaces.selectedRegions,
      rawMarketplaces: tempUserState.marketplaces.rawMarketplaces,
      onlyAmazonAndWalmart: isTrafficRoute && !isOrganicTrafficRoute,
      onlyAmazon:
        isLoyaltyMarketplaceRoute ||
        isContentRoute ||
        isBrandHealthRoute ||
        isBuyBoxRoute ||
        isPriceProductsRoute ||
        isCustomerServiceRoute ||
        isOrganicTrafficRoute ||
        isDestinyAutomationPreviewRoute,
    })
    setFilteredMarketplaces(_filteredMarketplaces)
  }, [
    allMarketplaces,
    allRegions,
    isBuyBoxRoute,
    isContentRoute,
    isDestinyAutomationPreviewRoute,
    isLoyaltyMarketplaceRoute,
    isPriceProductsRoute,
    isTrafficRoute,
    isCustomerServiceRoute,
    tempUserState.marketplaces.rawMarketplaces,
    tempUserState.marketplaces.selectedRegions,
    enableSpecificChineseMarketplaces,
    isBrandHealthRoute,
    isOrganicTrafficRoute,
  ])

  const params = useMemo(() => {
    return {
      ...(brandGroupCustomer?.customer_ids?.length
        ? { customer_ids: brandGroupCustomer?.customer_ids }
        : { customer_id: customer.id }),
    }
  }, [brandGroupCustomer?.customer_ids, customer.id])

  useQuery({
    queryKey: ['availableMarketplaces', params, location.pathname],
    queryFn: ({ signal }) => {
      return getAvailableMarketplaces({
        params,
        apiController: { signal: signal as AbortSignal, abort: () => null },
        selectedRegions: tempUserState.marketplaces.selectedRegions,
        selectedMarketplaces: tempUserState.marketplaces.selectedMarketplaces,
        isLoyaltyMarketplaceRoute,
        isShareAtAGlanceRoute,
        isMarketAnalyticsRoute,
        isPriceSegmentsRoute,
        isBuyBoxRoute,
        isPriceProductsRoute,
        isBrandHealthRoute,
        isDestinyAutomationPreviewRoute,
        disableChinaForOrganicTraffic: isChinaRegionHidden,
      }).then((data) => {
        if (data) {
          const {
            allBrandMarketplaces,
            allBrandRegions,
            amazonMarketplaceCodes,
            amazonMarketplaceIdsData,
            filteredMarketplaces,
            marketPlacesColors,
            matchingMarketplaces,
            rawMarketplaces,
            sdMarketplaces,
          } = data
          const newlyFilteredMarketplacesByGroups =
            getFilteredMarketplacesOrderByGroups(
              enableSpecificChineseMarketplaces,
              allBrandMarketplaces.filter((mp) =>
                filteredMarketplaces.includes(mp.id),
              ),
              isLoyaltyMarketplaceRoute ||
                isContentRoute ||
                isBuyBoxRoute ||
                isPriceProductsRoute ||
                isCustomerServiceRoute ||
                isBrandHealthRoute ||
                isDestinyAutomationPreviewRoute,
              isTrafficRoute,
            )

          const selectedMarketplaces = Array.from(matchingMarketplaces)
          // set all the derived values to component state:
          setFilteredMarketplaces(newlyFilteredMarketplacesByGroups)
          updateMarketplaces({
            allRegions: allBrandRegions,
            allMarketplaces: allBrandMarketplaces,
            amazonMarketplaces: {
              amazonMarketplaceCodes,
              amazonMarketplaceIdsData,
            },
            filteredMarketplaces: newlyFilteredMarketplacesByGroups,
            marketPlacesColors,
            rawMarketplaces: rawMarketplaces,
            sdMarketplaces,
            selectedMarketplaces,
            selectedRegions: getBrandSupportedRegionIds(
              allBrandRegions,
              selectedRegions,
            ),
          })

          // update the global context with the new selectedMarketplaces values
          updateMarketplaceIds(selectedMarketplaces)

          // update the global context with the new `all` values, only if we aren't in custom reports:
          updateAllMarketplaces(allBrandMarketplaces)
          updateAllRegions(allBrandRegions)
          updateAmazonMarketplaces(
            amazonMarketplaceCodes,
            amazonMarketplaceIdsData,
          )
          return data
        }
      })
    },
    enabled:
      /////////////////////////////////////////////////////////////////////////////////////////////////
      // NOTE: Do not call this API on the custom reports page as it has this API inside of useGetAllBrandMarketplacesRegions()
      /////////////////////////////////////////////////////////////////////////////////////////////////
      !isWorkingOnInitialLoad && !location.pathname.includes('/reports/custom'),
  })

  const { data: LOBdata, status: LOBdataStatus } = useQuery({
    queryKey: [
      'lob_filter_options_data',
      customer.id,
      brandGroupCustomer?.customer_ids,
    ],
    queryFn: ({ signal }) =>
      SecureAxios.get(`${getApiUrlPrefix('iserve')}/api/v7/lines_of_business`, {
        signal,
        params: {
          ...(brandGroupCustomer?.customer_ids?.length
            ? { customer_ids: brandGroupCustomer?.customer_ids }
            : { customer_ids: customer.id }),
        },
      }).then((response) => {
        const is1pSupportedBrand = response?.data?.some?.(
          (lob: LOBResponseProps) => lob.code === '1p',
        )
        setShow1pVendorAnalyticsTab(is1pSupportedBrand)
        const filteredData =
          response.data?.map((lob: LOBResponseProps) => {
            return {
              value: lob.code,
              title: lob.name,
              id: lob.id,
              sensitive: lob.sensitive,
            }
          }) ?? []
        const lob: LinesOfBusinessProps = {
          selectedTypes: filteredData,
          type_ids: filteredData.map(
            (el: SelectedLinesOfBusinessProps) => el.value,
          ),
        }

        let updatedLOB =
          customer?.id === 0 || !!brandGroupCustomer
            ? getAllBrandsAndBrandGroupsLOBs(response.data)
            : lob
        setDefaultLOBSelection(updatedLOB)

        const storedLOBData = localStorage.getItem('lines_of_business')
        const parsedLOBData = storedLOBData ? JSON.parse(storedLOBData) : null

        if (
          ((!brandGroupCustomer?.id &&
            parsedLOBData?.customerId === customer?.id) ||
            brandGroupCustomer?.id === parsedLOBData?.customerId) &&
          parsedLOBData?.lobs?.selectedTypes?.length > 0
        ) {
          updatedLOB = parsedLOBData?.lobs
        }

        originalState.current.linesOfBusiness = updatedLOB

        updateLinesOfBusiness(updatedLOB)
        updateGlobalLinesOfBusiness(updatedLOB)
        updateLinesOfBusinessFilter(updatedLOB)
        updateLinesOfBusinessOptions(lob?.selectedTypes)

        return response.data
      }),
  })

  /////////////////////////////////////////////////////////////////////////////////////////////////
  // DISPLAY GLOBAL FILTER
  /////////////////////////////////////////////////////////////////////////////////////////////////
  return (
    <div id='global_filter_container'>
      {/******************* GLOBAL FILTER SUMMARY AND SIDEDRAWER TOGGLE *******************/}
      {showGlobalFilter && (
        <FilterSummary
          setSideDrawerOpen={setSideDrawerOpen}
          isAllCategoryChecked={
            tempUserState?.categories?.fullCategoryList?.[0]?.checked
          }
          isRenderedFromGlobalFilter={true}
          setActiveTabIndex={setActiveTabIndex}
        />
      )}

      {isPlanningRoute && <PlanningGlobalFilter />}
      {/*************************** GLOBAL FILTER - SIDEDRAWER ****************************/}
      <SideDrawer
        isOpen={sideDrawerOpen}
        closeCallout={() => {
          setSideDrawerOpen(false)
        }}
        size='md'
        headerContent='Filters'
        noContentPadding
        footerContent={
          <FormFooter
            cancelButtonProps={{
              onClick: () => {
                cancelFilter()
              },
            }}
            saveButtonProps={{
              children: 'Apply',
              onClick: () => {
                applyFilter()
              },
              disabled: disableApplyButton || !isLOBSelected,
            }}
            resetButtonProps={{
              onClick: () => {
                resetFilter()
              },
            }}
          />
        }
      >
        {/************************** GLOBAL FILTER - FILTER TABS **************************/}
        <GlobalFilterTabs
          closeFilterSideDrawer={() => setSideDrawerOpen(false)}
          displayState={tempUserState}
          limitSelectionToggleState={limitSelectionToggleState}
          resetTabIndex={sideDrawerOpen}
          setDisableApplyButton={setDisableApplyButton}
          setLimitSelectionToggleState={setLimitSelectionToggleState}
          toggles={{ globalCurrencyDisabled, isVATinGlobalFilterToggleEnabled }}
          updateCategories={updateCategories}
          updateCurrency={updateCurrency}
          updateMarketplaces={updateMarketplaces}
          updateTags={updateTagsFilter}
          updateLinesOfBusiness={updateLinesOfBusinessFilter}
          updateGlobalLinesOfBusiness={updateGlobalLinesOfBusiness}
          updateLinesOfBusinessOptions={updateLinesOfBusinessOptions}
          updateTimeframe={updateFilterTimeframe}
          updateVat={updateVatAdjustment}
          updateChurn={updateChurn}
          filteredMarketplaces={filteredMarketplaces}
          setLOBSelected={setIsLOBSelected}
          LOBdata={{ LOBdata, LOBdataStatus }}
          activeTabIndex={activeTabIndex}
          setActiveTabIndex={setActiveTabIndex}
        />
      </SideDrawer>
    </div>
  )
}

export default GlobalFilter
