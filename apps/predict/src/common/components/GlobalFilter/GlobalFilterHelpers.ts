import { useContext, useMemo } from 'react'
import { uniq } from 'lodash'
import { ThemeContext } from 'src/Context'
import { type CheckboxCategory } from '@predict-types'

export const getCustomCategories = (
  categories: Array<CheckboxCategory>,
  allCategories: Array<CheckboxCategory>,
): Array<number> => {
  if (
    categories?.length === 0 ||
    (categories?.[0]?.name === 'All Categories' && categories?.[0]?.checked) ||
    allCategories?.length === 0 ||
    allCategories?.[0]?.checked
  ) {
    // custom categories not loaded yet, default to all categories
    return []
  }

  let allCategoriesSelected = true
  const customCategoryIds: Array<number> = [],
    allCategoryIds: Array<number> = []

  const getIds = (
    category: Array<CheckboxCategory>,
    categoryIdArray: Array<number>,
  ) => {
    category.forEach((cat) => {
      if (cat?.checked) {
        categoryIdArray.push(cat.id)
      } else {
        allCategoriesSelected = false
      }

      if (cat?.children) {
        getIds(cat.children, categoryIdArray)
      }
    })
  }

  getIds(categories, customCategoryIds)
  getIds(allCategories, allCategoryIds)

  // NOTE: need unique values and remove pseudo categories (id <= 0)
  const uniqCategoryIds = uniq(customCategoryIds).filter((c) => c > 0)
  const uniqAllCategoryIds = uniq(allCategoryIds).filter((c) => c > 0)

  if (uniqCategoryIds.length !== uniqAllCategoryIds.length) {
    allCategoriesSelected = false
  }
  return allCategoriesSelected ? [] : uniqCategoryIds
}

export const useGetCustomCategoryIds = () => {
  const { customCategories, fullCategoryList } = useContext(ThemeContext)

  return useMemo(
    () => getCustomCategories(customCategories, fullCategoryList),
    [customCategories, fullCategoryList],
  )
}
