import React from 'react'
import { useLocation } from 'react-router-dom'
import {
  Checkbox,
  Icon,
  Switch,
  Tooltip,
  useToggle,
} from '@patterninc/react-ui'
import { isMatchPath, productListingRoute } from '@predict-services'

import styles from './_global-marketplaces.module.scss'
import {
  useIsBuyBoxRoute,
  useIsContentRoute,
  useIsCustomerServiceRoute,
  useIsOrganicTrafficRoute,
  useIsPriceProductsRoute,
  useIsShareAtAGlanceRoute,
  useIsTrafficRoute,
} from './GlobalFilter.hooks'
import { allowedChineseMarketplaces } from './GlobalFilter.helpers'
import type { MarketplaceGroup, MPHProps } from './MarketplacesFilter'
import { type Marketplace } from './globalFilterTypes'

// Define the function overloads
export interface InputHandler {
  (
    type: MPHProps['type'],
    checkboxValue: MPHProps['checkboxValue'],
    marketplaceValue?: MPHProps['marketplaceValue'],
  ): void
  (
    type: string,
    checkboxValue: boolean,
    value: { id: number; subMarketplaces: { id: number }[] },
  ): void
}

export interface GlobalMarketplacesProps {
  marketplaces: Array<Marketplace | MarketplaceGroup>
  selectedMarkets: number[]
  inputHandler: InputHandler
  disableNonAmazonMarketplaces?: boolean
  disableNonAmazonAndNonWalMarketplaces?: boolean
  isSelectiveDistribution?: boolean
  isKeyWordRankingRoute?: boolean
  isProductRankingsRoute?: boolean
  isAutoBrandGroupEnabled?: boolean
  toggleState?: boolean
  setToggleState?: React.Dispatch<React.SetStateAction<boolean>> | null
}

const GlobalMarketplaces = ({
  marketplaces,
  selectedMarkets,
  inputHandler,
  disableNonAmazonMarketplaces,
  disableNonAmazonAndNonWalMarketplaces,
  isSelectiveDistribution,
  isKeyWordRankingRoute,
  isProductRankingsRoute,
  isAutoBrandGroupEnabled = false,
  toggleState = false,
  setToggleState,
}: GlobalMarketplacesProps) => {
  const chinaDataOrganicTrafficChanges = useToggle(
    'china_data_organic_traffic_changes',
  )
  const isTrafficRoute = useIsTrafficRoute()
  const isOrganicTrafficRoute = useIsOrganicTrafficRoute()
  const isContentModule = useIsContentRoute()
  const isCustomerServiceRoute = useIsCustomerServiceRoute()
  const enableChinaForOrganicTraffic = chinaDataOrganicTrafficChanges
    ? true
    : !isOrganicTrafficRoute
  const enableSpecificChineseMarketplaces =
    (isTrafficRoute && enableChinaForOrganicTraffic) ||
    isContentModule ||
    isCustomerServiceRoute
  const checkAll = selectedMarkets.includes(0)
  const { pathname } = useLocation()

  const isLoyaltyRoute = [
    '/loyalty/*',
    `${productListingRoute}/:productId/marketplace/:marketId/listing/:listingId/loyalty/*`,
  ].some((path) => isMatchPath(path, pathname))

  const isShareAtAGlanceRoute = useIsShareAtAGlanceRoute(),
    isBuyBoxRoute = useIsBuyBoxRoute(),
    isPriceProductsRoute = useIsPriceProductsRoute()

  const checkAllMarkets = (markets: Marketplace[] = []): boolean => {
    return !markets.some((market) =>
      market.id
        ? !selectedMarkets.includes(market.id)
        : !checkAllSubMarkets(market.subMarketplaces),
    )
  }

  const checkAllSubMarkets = (subMarkets: Marketplace[] = []): boolean => {
    let allSelected = true
    subMarkets.forEach((market) => {
      if (market.id && !selectedMarkets.includes(market.id)) {
        allSelected = false
        return
      }
    })
    return allSelected
  }

  const getMarketIcon = (market: Marketplace) => {
    const content =
      market.marketplace_type === 'sales' ? 'Sales Only' : 'Monitoring Only'
    return (
      <Tooltip position='right' tooltipContent={content}>
        <Icon icon='info' iconSize='16px' color='dark-blue' />
      </Tooltip>
    )
  }

  const appendUsToMarketplace = (
    marketplaceId: number,
    marketplaceName: string,
  ): string => {
    // Added Logic for appending US text at the end of to selected marketplace name
    return `${marketplaceName}${[1, 2].includes(marketplaceId) ? ' US' : ''}`
  }

  const marektplaceId = isKeyWordRankingRoute
    ? Number(sessionStorage.getItem('keywordRankingMarketplaceId'))
    : Number(sessionStorage.getItem('productRankingsMarketplaceId'))

  return (
    <>
      <div className={styles.globalMarketplaces}>
        {!isShareAtAGlanceRoute &&
          !isLoyaltyRoute &&
          enableChinaForOrganicTraffic &&
          !isBuyBoxRoute &&
          !isPriceProductsRoute && (
            <Checkbox
              name={'checkbox-all-marketplaces'}
              stateName='all'
              checked={
                checkAll || checkAllMarkets(marketplaces as Marketplace[])
              }
              callout={(name, value) => inputHandler(name as 'all', value)}
              label={'All Marketplaces'}
              labelClass='checkbox-label'
              customClass={`${styles.topLevel} ${styles.level} ${
                isSelectiveDistribution ? 'disabled' : ''
              }`}
              {...(isSelectiveDistribution ||
              isKeyWordRankingRoute ||
              isProductRankingsRoute ||
              (toggleState && isAutoBrandGroupEnabled)
                ? { disabled: true }
                : {})}
            />
          )}
        {marketplaces?.map(
          (marketplaceData: Marketplace | MarketplaceGroup) => {
            const marketplace = marketplaceData as Marketplace
            return (
              <span
                className={`${styles.level} ${
                  marketplace?.marketplace_type &&
                  marketplace?.marketplace_type !== 'both'
                    ? styles.withIcon
                    : ''
                }`}
                key={`sub-market-${marketplace?.marketplace_name}`}
              >
                <Checkbox
                  name={`region-${marketplace?.marketplace_name}`}
                  stateName='marketplace'
                  checked={
                    checkAll ||
                    (marketplace?.id
                      ? selectedMarkets.includes(marketplace?.id)
                      : checkAllSubMarkets(marketplace?.subMarketplaces))
                  }
                  callout={(name, value) =>
                    inputHandler(name as 'marketplace', value, marketplace)
                  }
                  label={marketplace?.marketplace_name}
                  labelClass='checkbox-label'
                  customClass={
                    (isSelectiveDistribution &&
                      !marketplace?.is_selective_distribution) ||
                    ((disableNonAmazonMarketplaces || isContentModule) &&
                      marketplace?.marketplace_name !== 'Amazon')
                      ? 'disabled'
                      : ''
                  }
                  disabled={
                    enableSpecificChineseMarketplaces
                      ? ((toggleState && isAutoBrandGroupEnabled) ||
                          (isSelectiveDistribution &&
                            !marketplace?.is_selective_distribution) ||
                          isKeyWordRankingRoute ||
                          isProductRankingsRoute ||
                          ((disableNonAmazonMarketplaces || isContentModule) &&
                            marketplace?.marketplace_name !== 'Amazon') ||
                          (disableNonAmazonAndNonWalMarketplaces &&
                            !marketplace?.amazon &&
                            marketplace?.marketplace_name !== 'Amazon' &&
                            marketplace?.marketplace_name !== 'Walmart')) &&
                        !allowedChineseMarketplaces.includes(marketplace?.id)
                      : (toggleState && isAutoBrandGroupEnabled) ||
                        (isSelectiveDistribution &&
                          !marketplace?.is_selective_distribution) ||
                        isKeyWordRankingRoute ||
                        isProductRankingsRoute ||
                        (disableNonAmazonMarketplaces &&
                          marketplace?.marketplace_name !== 'Amazon') ||
                        (disableNonAmazonAndNonWalMarketplaces &&
                          !marketplace?.amazon &&
                          marketplace?.marketplace_name !== 'Amazon' &&
                          marketplace?.marketplace_name !== 'Walmart')
                  }
                />
                {marketplace?.marketplace_type &&
                  marketplace?.marketplace_type !== 'both' &&
                  getMarketIcon(marketplace)}
                {marketplace?.subMarketplaces?.map((sub) => (
                  <span
                    className={`${styles.level} ${
                      sub.marketplace_type && sub.marketplace_type !== 'both'
                        ? styles.withIcon
                        : ''
                    }`}
                    key={`sub-market-${sub.marketplace_name}`}
                  >
                    <Checkbox
                      name={`region-${sub.marketplace_name}`}
                      stateName='sub-marketplace'
                      checked={
                        checkAll ||
                        !!(sub?.id && selectedMarkets.includes(sub?.id))
                      }
                      callout={(name, value) =>
                        inputHandler(name as 'sub-marketplace', value, sub)
                      }
                      label={appendUsToMarketplace(
                        sub.id,
                        sub.marketplace_name,
                      )}
                      labelClass='checkbox-label'
                      disabled={
                        enableSpecificChineseMarketplaces
                          ? ((toggleState && isAutoBrandGroupEnabled) ||
                              (isSelectiveDistribution &&
                                !sub.is_selective_distribution) ||
                              ((isKeyWordRankingRoute ||
                                isProductRankingsRoute) &&
                                marektplaceId !== sub.id) ||
                              ((disableNonAmazonMarketplaces ||
                                isContentModule) &&
                                !sub.amazon) ||
                              (disableNonAmazonAndNonWalMarketplaces &&
                                !sub.amazon &&
                                sub.marketplace_name !== 'Walmart' &&
                                sub.marketplace_name !== 'Amazon')) &&
                            !allowedChineseMarketplaces.includes(sub?.id)
                          : (toggleState && isAutoBrandGroupEnabled) ||
                            (isSelectiveDistribution &&
                              !sub.is_selective_distribution) ||
                            ((isKeyWordRankingRoute ||
                              isProductRankingsRoute) &&
                              marektplaceId !== sub.id) ||
                            (disableNonAmazonMarketplaces && !sub.amazon) ||
                            (disableNonAmazonAndNonWalMarketplaces &&
                              !sub.amazon &&
                              sub.marketplace_name !== 'Walmart' &&
                              sub.marketplace_name !== 'Amazon')
                      }
                      customClass={
                        isSelectiveDistribution &&
                        !sub.is_selective_distribution
                          ? 'disabled'
                          : ''
                      }
                    />
                    {sub.marketplace_type &&
                      sub.marketplace_type !== 'both' &&
                      getMarketIcon(sub)}
                  </span>
                ))}
              </span>
            )
          },
        )}
      </div>
      {isAutoBrandGroupEnabled && (
        <div className='flex align-items-center bdrt bdrc-light-gray pat-py-2'>
          <Switch
            callout={() => {
              setToggleState?.(!toggleState)
            }}
            checked={toggleState}
          />
          <span className='pat-ml-2 fs-10'>Limit selection to assignments</span>
        </div>
      )}
    </>
  )
}

export default GlobalMarketplaces
