import React, { useMemo } from 'react'
import { MultiSelect } from '@patterninc/react-ui'
import { c } from '@predict-services'
import type { Region, RegionId } from '@predict-types'

interface GlobalRegionsProps {
  regions: Region[]
  selectedRegions: RegionId[]
  handleRegionChange: (
    regionName: Region['region_name'],
    isChecked: boolean,
  ) => void
  handleRegionCheckAll: (isAllChecked: boolean) => void
  isSelectiveDistribution: boolean
}

const GlobalRegions = ({
  regions,
  selectedRegions,
  handleRegionChange,
  handleRegionCheckAll,
  isSelectiveDistribution,
}: GlobalRegionsProps) => {
  const regionsChecked = useMemo(
      () =>
        regions
          ?.filter((r) => selectedRegions.includes(r.id))
          .map((r) => r.region_name),
      [regions, selectedRegions],
    ),
    regionsList = regions.map((r) => ({
      ...r,
      customClass:
        isSelectiveDistribution && !r.is_selective_distribution
          ? 'disabled'
          : '',
    }))

  const compareRegions = (newRegions: string[], currentRegions: string[]) => {
    const added =
      newRegions.find((item) => !currentRegions.includes(item)) || null
    const removed =
      currentRegions.find((item) => !newRegions.includes(item)) || null
    return { added, removed }
  }

  return (
    <div className='global-regions' style={{ maxWidth: 'calc(100vw - 32px)' }}>
      <MultiSelect
        formLabelProps={{ label: c('region') }}
        options={regionsList}
        selectedOptions={regionsList.filter((r) =>
          regionsChecked.includes(r.region_name),
        )}
        selectPlaceholder={c('selectRegions')}
        callout={(selectedList) => {
          if (selectedList.length === 0) {
            handleRegionCheckAll(false)
            return
          } else if (selectedList.length === regionsList.length) {
            handleRegionCheckAll(true)
            return
          }
          const { added, removed } = compareRegions(
              selectedList.map((r) => r.region_name),
              regionsChecked,
            ),
            regionName = added || removed
          if (regionName) {
            handleRegionChange(regionName, !!added)
          }
        }}
        labelKey={'region_name'}
      />
    </div>
  )
}

export default GlobalRegions
