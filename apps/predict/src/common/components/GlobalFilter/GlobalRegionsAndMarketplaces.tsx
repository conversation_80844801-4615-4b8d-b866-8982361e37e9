import React from 'react'
import { Alert, useToggle } from '@patterninc/react-ui'

import { useIsAutoBrandGroupEnabled } from '../../hooks'
import GlobalMarketplaces, { type InputHandler } from './GlobalMarketplaces'
import GlobalRegions from './GlobalRegions'
import { useIsOrganicTrafficRoute } from './GlobalFilter.hooks'
import type { GlobalRegionsAndMarketplacesProps } from './MarketplacesFilter'

const GlobalRegionsAndMarketplaces = ({
  disableNonAmazonMarketplaces,
  disableNonAmazonAndNonWalMarketplaces,
  filteredMarketplaces,
  handleRegionChange,
  handleRegionCheckAll,
  isKeyWordRankingRoute,
  isProductRankingsRoute,
  isSelectiveDistribution,
  marketplaceHandler,
  marketplaces,
  regions,
  selectedAmazonIds,
  selectedMarketplaces,
  selectedRegions,
  setToggleState,
  showLabel = true,
  toggleState,
}: GlobalRegionsAndMarketplacesProps) => {
  const isOrganicTrafficRoute = useIsOrganicTrafficRoute()
  const chinaDataOrganicTrafficChanges = useToggle(
    'china_data_organic_traffic_changes',
  )
  const alertMsg = isKeyWordRankingRoute
    ? sessionStorage.getItem('keywordRankingMarketplaceLabel')
    : sessionStorage.getItem('productRankingsMarketplaceLabel')
  const isAllAmazonMarketplacesUnchecked = chinaDataOrganicTrafficChanges
    ? disableNonAmazonMarketplaces &&
      !isOrganicTrafficRoute &&
      selectedAmazonIds?.length === 0
    : disableNonAmazonMarketplaces && selectedAmazonIds?.length === 0
  const isAutoBrandGroupEnabled = useIsAutoBrandGroupEnabled()

  return (
    <div className='marketplaces-filter-column'>
      {showLabel && (
        <label className='fw-semi-bold fc-purple pat-mb-2'>
          Regions and Marketplaces
        </label>
      )}
      {(selectedMarketplaces.length === 0 ||
        isSelectiveDistribution ||
        (chinaDataOrganicTrafficChanges
          ? disableNonAmazonMarketplaces && !isOrganicTrafficRoute
          : disableNonAmazonMarketplaces)) && (
        <div
          className={`fs-12 pat-mb-4 bdr bdrr-4 pat-p-2 ${
            selectedMarketplaces.length === 0 ||
            isAllAmazonMarketplacesUnchecked
              ? 'red bdrc-red bgc-light-red'
              : 'blue bdrc-blue info-background'
          }`}
        >
          {isAllAmazonMarketplacesUnchecked
            ? 'Please select at least one amazon marketplace.'
            : selectedMarketplaces.length === 0
              ? 'Please select at least one marketplace.'
              : disableNonAmazonMarketplaces
                ? 'All Non-Amazon marketplaces are disabled in this module.'
                : 'All Non-EU marketplaces are disabled on this module.'}
        </div>
      )}
      {(isKeyWordRankingRoute || isProductRankingsRoute) && (
        <Alert
          type='info'
          text={`This page is based on ${alertMsg} data`}
          customClass='pat-mb-5'
        />
      )}
      {regions && (
        <GlobalRegions
          regions={regions}
          selectedRegions={selectedRegions}
          handleRegionChange={handleRegionChange}
          handleRegionCheckAll={handleRegionCheckAll}
          isSelectiveDistribution={isSelectiveDistribution}
          // TODO: values not used in GlobalRegions.js; remove them?
          // isKeyWordRankingRoute={isKeyWordRankingRoute}
          // isProductRankingsRoute={isProductRankingsRoute}
          // isAutoBrandGroupEnabled={isAutoBrandGroupEnabled}
          // toggleState={toggleState}
        />
      )}
      {marketplaces && (
        <GlobalMarketplaces
          marketplaces={filteredMarketplaces}
          selectedMarkets={selectedMarketplaces}
          inputHandler={marketplaceHandler as InputHandler}
          isSelectiveDistribution={isSelectiveDistribution}
          disableNonAmazonMarketplaces={disableNonAmazonMarketplaces}
          disableNonAmazonAndNonWalMarketplaces={
            disableNonAmazonAndNonWalMarketplaces
          }
          isKeyWordRankingRoute={isKeyWordRankingRoute}
          isProductRankingsRoute={isProductRankingsRoute}
          isAutoBrandGroupEnabled={isAutoBrandGroupEnabled}
          toggleState={toggleState}
          setToggleState={setToggleState}
        />
      )}
    </div>
  )
}

export default GlobalRegionsAndMarketplaces
