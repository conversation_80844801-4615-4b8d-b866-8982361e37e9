import React, { useEffect } from 'react'
import type { ReactNode } from 'react'
import { useQuery } from '@tanstack/react-query'
import {
  type FormLabelProps,
  getApiUrlPrefix,
  MultiSelect,
  Switch,
} from '@patterninc/react-ui'
import { c } from '@predict-services'
import type { Tag, Tags } from '@predict-types'

import SecureAxios from '../../services/SecureAxios'
import styles from './_global-tags.module.scss'

export interface TagRecord {
  created_at: Date
  created_by_username: string
  id: number
  ids_customer: number[]
  ids_master_product: number[]
  master_product_count: number
  lower: string
  name: string
  updated_at: Date
}

const GlobalTags = ({
  customerId,
  disabled = false,
  ignoreMatchAll = false,
  onChange,
  selectedAbgCustomerIds,
  tags,
  formLabelProps,
}: {
  customerId: number
  disabled?: boolean
  ignoreMatchAll?: boolean
  onChange: (tagsState: Tags) => void
  selectedAbgCustomerIds: number[]
  tags: Tags
  formLabelProps?: FormLabelProps
}): ReactNode => {
  const params = {
    ...(selectedAbgCustomerIds?.length
      ? { customer_ids: selectedAbgCustomerIds }
      : { customer_id: customerId }),
    sort: 'name:asc:lowercase',
  }

  const { data: tagData, isLoading } = useQuery({
      queryKey: [params],
      queryFn: async ({ signal }) => {
        try {
          const response = await SecureAxios.get(
            `${getApiUrlPrefix('iserve')}/api/v3/tags`,
            {
              params,
              signal,
            },
          )
          return response?.data?.data?.length
            ? response.data.data.filter(
                (el: TagRecord) => el.ids_master_product.length > 0,
              )
            : []
        } catch (error) {
          console.error('Error fetching tags:', error)
          throw error
        }
      },
    }),
    tagOptions = tagData ? tagData : []

  const handleTagChange = (selectedTags: string[]) => {
    const selectedTagsData = tagOptions.filter((el: TagRecord) =>
      selectedTags.includes(el.name),
    )
    onChange({
      ...tags,
      selectedTags: selectedTagsData,
      tag_ids: selectedTagsData.map((el: Tag) => el.id),
      customerId,
    })
  }

  const handleToggleChange = (value: boolean) => {
    onChange({
      ...tags,
      matchAllTags: value,
    })
  }

  useEffect(() => {
    if (!tags || (tags && tags.customerId && tags.customerId !== customerId)) {
      onChange({
        selectedTags: [],
        tag_ids: [],
        matchAllTags: null,
        customerId,
      })
    }
  }, [tags, customerId, onChange])

  return !disabled ? (
    <div className='pat-mt-2.5'>
      <MultiSelect
        appendTo='parent'
        formLabelProps={formLabelProps ?? { label: c('tags') }}
        selectPlaceholder={c('selectTags')}
        options={tagOptions.map((tag: TagRecord) => {
          return { id: tag.id, tagName: tag.name }
        })}
        selectedOptions={tags.selectedTags.map((tag: Tag) => {
          return { id: tag.id, tagName: tag.name }
        })}
        callout={(selectedList) => {
          handleTagChange(selectedList.map((tag) => tag.tagName))
        }}
        emptyStateProps={{ primaryText: c('noTagsFound') }}
        labelKey={'tagName'}
        loading={isLoading}
      />

      {!ignoreMatchAll && tags?.selectedTags?.length > 1 && (
        <div className={`${styles.switchContainer} pat-mt-1 pat-pt-1`}>
          <div className='pat-mr-1'>
            <Switch
              checked={tags.matchAllTags ?? false}
              callout={handleToggleChange}
            />
          </div>
          <span className={`${styles.globalTagsToggleLabel} pat-ml-1`}>
            Results must have ALL selected tags
          </span>
        </div>
      )}
    </div>
  ) : null
}

export default GlobalTags
