import React from 'react'
import { useLocation } from 'react-router-dom'
import { Timeframe } from '@patterninc/react-ui'
import type { Timeframe as TimeframeType } from '@predict-types'

import {
  checkOnlyCoreRoutes,
  checkOnlyTrafficRoutes,
  isContentRoutes,
  isLoyaltyRoutes,
} from './GlobalFilter.helpers'

export interface GlobalTimeframeProps {
  comparisonDateRanges: NonNullable<
    React.ComponentProps<typeof Timeframe>['comparisonDateRange']
  >
  customAggregations: Record<string, unknown>
  disabledTimeframeOptions: NonNullable<
    React.ComponentProps<typeof Timeframe>['disabledTimeframeOptions']
  >
  disableTimeFrameColumn: boolean
  endDateCopy: NonNullable<React.ComponentProps<typeof Timeframe>['endDate']>
  isComparisonDateRangeEnabled: boolean
  historicalTimeframes: NonNullable<
    React.ComponentProps<typeof Timeframe>['historicalTimeframes']
  >
  showAggregations: boolean
  showHistoricTimeFrameDateRange: boolean
  showHistoricTimeFrameDateRangeOnMobile: boolean
  showLabel?: boolean
  startDateCopy: NonNullable<
    React.ComponentProps<typeof Timeframe>['startDate']
  >
  timeframeCopy: TimeframeType
  // Using 'any' type here because the Timeframe component's callback signature
  // doesn't align with our implementation.
  timeframeCallout: (...args: any) => void
  updateToValidFilterState: (...args: any) => void
  // Using 'any' type since we don't send all required props in this type
  alertProps: any
}

const GlobalTimeframe = ({
  alertProps,
  comparisonDateRanges,
  customAggregations,
  disabledTimeframeOptions,
  disableTimeFrameColumn,
  endDateCopy,
  isComparisonDateRangeEnabled,
  historicalTimeframes,
  showAggregations,
  showHistoricTimeFrameDateRange,
  showHistoricTimeFrameDateRangeOnMobile,
  showLabel = true,
  startDateCopy,
  timeframeCallout,
  timeframeCopy,
  updateToValidFilterState,
}: GlobalTimeframeProps) => {
  const location = useLocation(),
    // TODO: Remove isOnlyCoreRoutes check once we start supporting new timeframes in Traffic, Content and Loyalty modules.
    isOnlyCoreRoutes = checkOnlyCoreRoutes(location.pathname),
    isOnlyTrafficRoute = checkOnlyTrafficRoutes(location.pathname),
    loyaltyTimeExtentionEnabled = isLoyaltyRoutes(location?.pathname),
    isContentRoute = isContentRoutes(location?.pathname),
    enableTimePeriodExtensionforCoreModules =
      isOnlyCoreRoutes ||
      isOnlyTrafficRoute ||
      loyaltyTimeExtentionEnabled ||
      isContentRoute
  return (
    <div className='timeframe-filter-column'>
      {disableTimeFrameColumn && (
        <div className='fs-12 pat-mb-4 bdr bdrr-4 pat-p-2 blue fc-blue bdrc-blue info-background'>
          Global Date Picker is disabled for this screen since it only accepts a
          single date at this time
        </div>
      )}
      {showLabel && (
        <label className='fw-bold fs-12 pat-mb-2'>Default Timeframe</label>
      )}
      <div className='timeframe-filter-wrapper timeframe-date-display'>
        <Timeframe
          alertProps={alertProps}
          callout={timeframeCallout}
          comparisonDateRange={comparisonDateRanges}
          currentTimeframe
          customAggregations={customAggregations}
          disabledTimeframeOptions={disabledTimeframeOptions}
          disableTimeFrameFilter={disableTimeFrameColumn}
          endDate={endDateCopy}
          getComparisonDateRange={isComparisonDateRangeEnabled}
          hideCustomDateSearch={true}
          historicalTimeframes={historicalTimeframes}
          trailingTimeframe={enableTimePeriodExtensionforCoreModules}
          previousTimeframe={enableTimePeriodExtensionforCoreModules}
          quarterlyTimeframe
          showHistoricTimeFrameDateRange={showHistoricTimeFrameDateRange}
          showHistoricTimeFrameDateRangeOnMobile={
            showHistoricTimeFrameDateRangeOnMobile
          }
          startDate={startDateCopy}
          timeframe={timeframeCopy}
          updateToValidFilterState={updateToValidFilterState}
          useTimeframeAggregations={showAggregations}
          currentPath={location.pathname}
        />
      </div>
    </div>
  )
}

export default GlobalTimeframe
