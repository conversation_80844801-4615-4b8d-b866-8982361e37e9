import React, { useMemo, useState } from 'react'
import { MultiSelect } from '@patterninc/react-ui'
import { c } from '@predict-services'

import styles from './_global-lines-of-business.module.scss'
import {
  type LinesOfBusinessProps,
  type LOBdataProps,
  type LOBResponseProps,
  type SelectedLinesOfBusinessProps,
} from './globalFilterTypes'

type LinesOfBusinessFilterProps = {
  updateLinesOfBusiness: (linesOfBusiness: LinesOfBusinessProps) => void
  displayState: LinesOfBusinessProps
  setLOBSelected: (lobSelected: boolean) => void
  updateGlobalLinesOfBusiness: (linesOfBusiness: LinesOfBusinessProps) => void
  updateLinesOfBusinessOptions: (
    linesOfBusiness: SelectedLinesOfBusinessProps,
  ) => void
  LOBdata: LOBdataProps
}

const LinesOfBusinessFilter = ({
  updateLinesOfBusiness,
  displayState: linesOfBusiness,
  setLOBSelected,
  LOBdata,
}: LinesOfBusinessFilterProps) => {
  const [selectedTypes, setSelectedTypes] = useState<
    SelectedLinesOfBusinessProps[]
  >([])

  const handleOptionChange = (
    selectedTypes: SelectedLinesOfBusinessProps[],
  ) => {
    setSelectedTypes(selectedTypes)
    setLOBSelected?.(selectedTypes?.length !== 0)
    updateLinesOfBusiness({
      ...linesOfBusiness,
      selectedTypes: selectedTypes,
      type_ids: selectedTypes.map(
        (el: SelectedLinesOfBusinessProps) => el.value,
      ),
    })
  }

  const isNonPattern3PStatus = LOBdata?.LOBdataStatus === 'success'

  const updateLOBData = useMemo(() => {
    return (
      LOBdata?.LOBdata?.map((lob: LOBResponseProps) => {
        return {
          value: lob.code,
          title: lob.name,
          id: lob.id,
          sensitive: lob.sensitive,
        }
      }) ?? []
    )
  }, [LOBdata])

  return (
    <div className='pat-mt-4'>
      <MultiSelect
        labelKey='title'
        formLabelProps={{
          label: c('linesOfBusiness'),
        }}
        options={updateLOBData?.length ? updateLOBData : []}
        callout={(selectedList: SelectedLinesOfBusinessProps[]) =>
          handleOptionChange(selectedList)
        }
        selectedOptions={linesOfBusiness?.selectedTypes || []}
        loading={LOBdata?.LOBdataStatus === 'pending'}
        searchBarProps={{
          placeholder: c('searchByLinesOfBusinessName'),
        }}
        emptyStateProps={{ primaryText: c('noLinesOfBusinessFound') }}
        exposed
      />
      {selectedTypes.length === 0 &&
        isNonPattern3PStatus &&
        linesOfBusiness.selectedTypes?.length === 0 && (
          <div className={styles.globalLOBErrorWrap}>
            {c('pleaseSelectAtLeastOneLinesOfBusiness')}
          </div>
        )}
    </div>
  )
}

export default LinesOfBusinessFilter
