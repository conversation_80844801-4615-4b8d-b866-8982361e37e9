import React from 'react'
import { useLocation } from 'react-router-dom'
import { isEqual, uniq } from 'lodash'
import { isMatchPath } from '@predict-services'
import { useToggle } from '@patterninc/react-ui'
import type {
  Marketplace,
  MarketplaceData,
  MarketplaceId,
  MarketplacesFilterDisplayState,
  Region,
  RegionId,
  SubMarketplace,
} from '@predict-types'

import {
  getDisabledNonAmazonMarketplaces,
  getFilteredMarketplacesOrderByGroups,
} from './GlobalFilter.helpers'
import {
  useIsContentRoute,
  useIsCustomerServiceRoute,
  useIsOrganicTrafficRoute,
  useIsTrafficRoute,
} from './GlobalFilter.hooks'
import GlobalRegionsAndMarketplaces from './GlobalRegionsAndMarketplaces'

export type MPHProps = {
  type: 'all' | 'marketplace' | 'sub-marketplace'
  checkboxValue: boolean
  marketplaceValue?: Marketplace | SubMarketplace
}

type MarketplacesFilterProps = {
  displayState: MarketplacesFilterDisplayState
  updateMarketplaces: (marketplaceData: MarketplaceData) => void
  limitSelectionToggleState?: boolean
  setLimitSelectionToggleState?: React.Dispatch<React.SetStateAction<boolean>>
  getAllMarketplaces?: boolean
  filteredMarketplaces?: Array<
    Marketplace | { marketplace_name: string; subMarketplaces: Marketplace[] }
  >
}

export type MarketplaceGroup = {
  marketplace_name: string
  subMarketplaces: Marketplace[]
}

export interface GlobalRegionsAndMarketplacesProps {
  disableNonAmazonMarketplaces: boolean
  disableNonAmazonAndNonWalMarketplaces: boolean
  filteredMarketplaces: Array<Marketplace | MarketplaceGroup>
  handleRegionChange: (
    regionName: Region['region_name'],
    isChecked: boolean,
  ) => void
  handleRegionCheckAll: (isAllChecked: boolean) => void
  isKeyWordRankingRoute: boolean
  isProductRankingsRoute: boolean
  isSelectiveDistribution: boolean
  marketplaceHandler: (
    type: MPHProps['type'],
    checkboxValue: MPHProps['checkboxValue'],
    marketplaceValue: MPHProps['marketplaceValue'],
  ) => void
  marketplaces: Marketplace[]
  regions: Region[]
  selectedAmazonIds: MarketplaceId[]
  selectedMarketplaces: MarketplaceId[]
  selectedRegions: RegionId[]
  setToggleState?: React.Dispatch<React.SetStateAction<boolean>>
  showLabel?: boolean
  toggleState?: boolean
}

///////////////////////////////////////////////////////////////////////////////////////////////////
// MARKETPLACE FILTER
///////////////////////////////////////////////////////////////////////////////////////////////////
const MarketplacesFilter = ({
  displayState,
  updateMarketplaces,
  limitSelectionToggleState,
  setLimitSelectionToggleState,
  filteredMarketplaces,
}: MarketplacesFilterProps) => {
  const chinaDataOrganicTrafficChanges = useToggle(
    'china_data_organic_traffic_changes',
  )
  const isTrafficRoute = useIsTrafficRoute()
  const isOrganicTrafficRoute = useIsOrganicTrafficRoute()
  const isContentRoute = useIsContentRoute()
  const isCustomerServiceRoute = useIsCustomerServiceRoute()
  const enableChinaForOrganicTraffic = chinaDataOrganicTrafficChanges
    ? true
    : !isOrganicTrafficRoute
  const enableSpecificChineseMarketplaces =
    (isTrafficRoute && enableChinaForOrganicTraffic) ||
    isContentRoute ||
    isCustomerServiceRoute
  const {
    allMarketplaces,
    allRegions,
    rawMarketplaces,
    selectedMarketplaces,
    selectedRegions,
  } = displayState

  const marketplaceData = React.useMemo(
    () => ({
      allMarketplaces: displayState.allMarketplaces,
      allRegions: displayState.allRegions,
      amazonMarketplaces: displayState.amazonMarketplaces,
      filteredMarketplaces: displayState.filteredMarketplaces,
      marketPlacesColors: displayState.marketPlacesColors,
      rawMarketplaces: displayState.rawMarketplaces,
      sdMarketplaces: displayState.sdMarketplaces,
      selectedMarketplaces: displayState.selectedMarketplaces,
      selectedRegions: displayState.selectedRegions,
    }),
    [displayState],
  )

  /////////////////////////////////////////////////////////////////////////////////////////////////
  // ROUTES
  /////////////////////////////////////////////////////////////////////////////////////////////////
  const { pathname } = useLocation()

  const isSelectiveDistribution = pathname?.includes('selective-distribution'),
    disabledNonAmazonMarketplaces = getDisabledNonAmazonMarketplaces(
      pathname,
      enableSpecificChineseMarketplaces,
    ),
    disableNonAmazonAndNonWalMarketplaces = useIsTrafficRoute(),
    isKeyWordRankingRoute = isMatchPath(
      '/content/rank-tracker/products/:marketProductId/ranked-keywords',
      pathname,
    ),
    isProductRankingsRoute = isMatchPath(
      '/content/rank-tracker/keywords/:keyword/ranked-products',
      pathname,
    )

  /////////////////////////////////////////////////////////////////////////////////////////////////
  // MARKETPLACE METHODS
  /////////////////////////////////////////////////////////////////////////////////////////////////

  const marketplaceHandler = (
    type: MPHProps['type'],
    checkboxValue: MPHProps['checkboxValue'],
    marketplaceValue: MPHProps['marketplaceValue'],
  ) => {
    let currentlySelectedMarketplaces: MarketplaceId[] = []
    if (type === 'all') {
      const allMarkets = getAllFilteredMarkets(allMarketplaces, selectedRegions)
      currentlySelectedMarketplaces = checkboxValue ? [0, ...allMarkets] : []
      updateMarketplaces({
        ...marketplaceData,
        selectedMarketplaces: currentlySelectedMarketplaces,
      })
    } else {
      // if currentSelection is not `All` then remove `0` from the current selection
      const currentSelection = new Set(selectedMarketplaces)
      if (currentSelection.has(0)) {
        currentSelection.delete(0)
      }
      if (marketplaceValue?.id) {
        // add or remove the marketplace category from the current selection
        currentSelection[checkboxValue ? 'add' : 'delete'](marketplaceValue.id)
      } else {
        // add or remove the individual marketplace from the current selection
        marketplaceValue?.subMarketplaces?.forEach((subMarket) => {
          currentSelection[checkboxValue ? 'add' : 'delete'](subMarket.id)
        })
      }
      if (
        (selectedRegions.length > 0 &&
          currentSelection.size ===
            allMarketplaces?.filter((mp) =>
              selectedRegions.includes(mp.region_id),
            )?.length) ||
        currentSelection.size === rawMarketplaces?.length
      ) {
        currentSelection.add(0)
      }
      currentlySelectedMarketplaces = Array.from(currentSelection)

      const selectedAmazonIds =
        selectedAmazonMarketplacesIds(currentlySelectedMarketplaces) ?? []
      const selectedAmazonCountryCodes =
        selectedAmazonMarketsCountryCodes(currentlySelectedMarketplaces) ?? []

      if (!isEqual(selectedMarketplaces, currentlySelectedMarketplaces)) {
        updateMarketplaces({
          ...marketplaceData,
          selectedMarketplaces: currentlySelectedMarketplaces,
          amazonMarketplaces: {
            amazonMarketplaceCodes: selectedAmazonCountryCodes,
            amazonMarketplaceIdsData: selectedAmazonIds,
          },
        })
      }
    }
  }

  const amazonMarketplace = allMarketplaces?.find(
    (mp) => mp.marketplace_name === 'Amazon',
  )

  let amazonMarketplaces: Marketplace[] = []

  if (amazonMarketplace) {
    amazonMarketplaces = [
      amazonMarketplace,
      ...(amazonMarketplace?.subMarketplaces || []),
    ]
  }

  const selectedAmazonMarketsCountryCodes = (marketplaces: MarketplaceId[]) => {
    return amazonMarketplaces
      ?.filter((amazonMarket) => marketplaces.includes(amazonMarket.id))
      ?.map((market) => market?.country_code)
  }

  const selectedAmazonMarketplacesIds = (marketplaces: MarketplaceId[]) => {
    return amazonMarketplaces
      ?.filter((amazonMarket) => marketplaces.includes(amazonMarket.id))
      ?.map((market) => market?.id)
  }

  const handleRegionChange = (
    regionName: Region['region_name'],
    isChecked: boolean,
  ) => {
    const regionUpdatedId = allRegions.find(
      (region) => region.region_name === regionName,
    )?.id
    if (regionUpdatedId) {
      const newRegions = new Set(selectedRegions)

      newRegions[isChecked ? 'add' : 'delete'](regionUpdatedId)

      // if all regions are selected then add `0` to the selected regions
      newRegions[
        isChecked && newRegions.size === allRegions.length ? 'add' : 'delete'
      ](0)

      // filter marketplaces on the basis of updated regions
      let newMarketplaces = new Set(selectedMarketplaces),
        availableMarketplaces = 0

      newMarketplaces.delete(0)
      if (newRegions.size > 0 || isChecked) {
        allMarketplaces.forEach((mp) => {
          if (newRegions.has(mp.region_id)) {
            availableMarketplaces++
            newMarketplaces.add(mp.id)
          } else {
            newMarketplaces.delete(mp.id)
          }
        })

        if (availableMarketplaces === newMarketplaces.size) {
          newMarketplaces.add(0)
        }
      } else if (newRegions.size === 0) {
        newMarketplaces = new Set()
      }

      const filteredMarketplaces = getFilteredMarketplacesOrderByGroups(
        enableSpecificChineseMarketplaces,
        [
          ...(availableMarketplaces === newMarketplaces.size ? [0] : []),
          ...getAllFilteredMarkets(allMarketplaces, Array.from(newRegions)),
        ]
          .map((marketplaceId) =>
            allMarketplaces.find(
              (marketplace) => marketplace.id === marketplaceId,
            ),
          )
          .filter(
            (marketplace): marketplace is Marketplace =>
              marketplace !== undefined,
          ),
      )

      updateMarketplaces({
        ...marketplaceData,
        selectedMarketplaces: Array.from(newMarketplaces),
        selectedRegions: Array.from(newRegions),
        filteredMarketplaces,
      })
    }
  }

  const handleRegionCheckAll = (isAllChecked: boolean) => {
    let newMarketplaces: Array<MarketplaceId> = [],
      newRegions: Array<RegionId> = []
    if (isAllChecked) {
      newMarketplaces = uniq([0, ...allMarketplaces.map((item) => item.id)])
      newRegions = uniq([0, ...allRegions.map((region) => region.id)])
    }
    updateMarketplaces({
      ...marketplaceData,
      selectedMarketplaces: newMarketplaces,
      selectedRegions: newRegions,
    })
  }

  const selectedAmazonIds = selectedAmazonMarketplacesIds(selectedMarketplaces)

  const globalRegAndMarketplacesProps: GlobalRegionsAndMarketplacesProps = {
    disableNonAmazonMarketplaces: disabledNonAmazonMarketplaces,
    disableNonAmazonAndNonWalMarketplaces:
      disableNonAmazonAndNonWalMarketplaces,
    filteredMarketplaces: filteredMarketplaces ?? [],
    handleRegionChange: handleRegionChange,
    handleRegionCheckAll: handleRegionCheckAll,
    isKeyWordRankingRoute: isKeyWordRankingRoute,
    isProductRankingsRoute: isProductRankingsRoute,
    isSelectiveDistribution: isSelectiveDistribution,
    marketplaceHandler: marketplaceHandler,
    marketplaces: allMarketplaces,
    regions: allRegions,
    selectedAmazonIds: selectedAmazonIds,
    selectedMarketplaces: selectedMarketplaces,
    selectedRegions: selectedRegions,
    setToggleState: setLimitSelectionToggleState,
    toggleState: limitSelectionToggleState,
  }

  return <GlobalRegionsAndMarketplaces {...globalRegAndMarketplacesProps} />
}

export default MarketplacesFilter

export const getAllFilteredMarkets = (
  rawMarketData: Marketplace[],
  selectedRegionsIds: number[],
): MarketplaceId[] => {
  return rawMarketData?.reduce(
    (markets, marketplace) =>
      marketplace?.id &&
      (selectedRegionsIds.length === 0 ||
        selectedRegionsIds.includes(marketplace.region_id))
        ? [...markets, marketplace.id]
        : [...markets],
    [] as MarketplaceId[],
  )
}
