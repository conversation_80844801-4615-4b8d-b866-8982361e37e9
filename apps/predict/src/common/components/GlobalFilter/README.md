# The Global Filter

The Global Filter has been divided into several components in order to clarify responsibility of function. The responsibilities include state management, summary display, filter display and filter component interface. Each component's responsibility is listed below.

## Global Filter

This Global Filter component is set up to only be concerned with updating and maintaining the state of the global filter. It is not concerned with how the global filter is displayed or how the user interacts with it.

The Global Filter component has 4 different types of state:

1. **Global Filter State (context)**: this is the state that is used by all of the components
2. **User State (TempUserState)**: this is the state with the user's selections before clicking apply
3. **Transition State (Original State)**: this is the saved state when the user changes to specific routes and is used to restore the user's previous selections when they leave those routes
4. **Default State**: this is the state that is used to reset the global filter or when the user first logs into the application

Note, the Global Filter file is not directly responsible for displaying the filter, HOWEVER, it is responsible for showing the filter summary and toggling the filter side drawer (to open the filter).

- The `FilterSummary` component shows the filter summary in the page `Header` and contains all the logic for displaying the filter summary
- The `Tabs` component is responsible for displaying the filter tabs (filter sections) and contains all the logic for determining which tabs to display
- There are 2 helper files that are used within the Global Filter component setup:
  - `GlobalFilter.helpers.ts` - contains all the helper functions for the Global Filter
  - `GlobalFilter.hooks.ts` - contains all the hooks that are used within the Global Filter

The information that is needed to be passed from this component to the `FilterSummary` and `Tabs` components includes only 3 things:

1. The `displayState`: this is the visible state that is used by all of the filter components
2. The `update` functions: these are the functions that are used to update the user's filter state
3. The `toggles`: these are the toggles that are used to help determine which tabs to display

This component is divided into multiple sections:

1. TOGGLES: where all the toggles are defined
2. STATE VARIABLES, divided to:
   - VARIABLES FROM CONTEXT: where all the global variables are deconstructed from the global filter context [Global State]
   - TEMP USER STATE: where the user's selections are stored (before clicking apply) [User State]
   - ORIGINAL STATE: where the user's selections are stored when they change to specific routes
3. GENERAL FILTER FUNCTIONS, divided to:
   - ROUTES: where the any route specific variables are defined
   - GENERAL FILTER ACTIONS: the general filter actions are defined
4. SPECIFIC FILTER GROUP FUNCTIONS, divided to:
   - TIMEFRAME: timeframe filter related functions
   - MARKETPLACES: marketplace filter related functions
   - CUSTOM CATEGORIES: custom categories filter related functions
   - CURRENCY: currency filter related functions
   - TAGS: tags filter related functions
   - VAT ADJUSTMENT: vat adjustment filter related functions
5. ADDITIONAL FILTER ACTIONS: state update methods (apply, cancel & reset)
6. RENDER: where the filter summary toggle and global filter components are rendered

## Tabs

The Tabs module is responsible for determining and displaying the appropriate filter groups within the global filter, according to the toggles prop delivered from its parent component and the current page being viewed by the user.

If a filter group is validated to be displayed, it's included in the tab list.

Every filter group receives specific data needed for display, along with the selected filter group and an updater function for modifying user selections. Note that toggles aren't sent to the filter groups; each group maintains its own unique toggles which function within their own filtering processes.

## Filter Summary

This module is responsible for the display of a summarized version of the user's current selections from the global filter on the header component of the application.

The summary component generates its values from the saved global filter values for all filters. It only receives a single prop used to close the filter.

## Helper Files

### Hooks

The hooks file is a helper file that only includes custom hooks.

### Helpers

The helpers file contains functions used by various global filter components. While there are many helper functions within this file there are 3 main functions that are utilized quite heavily, especially for the Timeframe filter.

- _getDisabledTimeframeOptions_ - this method determines the various timeframes that are disabled on any given route. If the timeframe is included in this object it will _NOT_ be selectable.
- _getValidTimeframeAggregations_ - this method provides the valid timeframe/aggregation combinations that are available on any given route. The aggregations in this list WILL be selectable.
- _getValidTimeframe_ - this method is checked on nearly every route. It determines if the currently saved Timeframe is valid for the current route. If it is valid there are no changes. If the timeframe is not valid it returns a new valid timeframe (within the same timeframe type, if possible) and/or a valid aggregation for the valid timeframe. There are a couple of specific methods that are implemented on specific routes because the conversion is to be done in a specific manner instead of the general manner handled here.

## Types

Because the context file is still a .js file it does not have reliable type information. To help with the conversion and use of types in the rest of the typed files a temporary _contextTypes.ts_ file has been created to help with some of the typing. The file is not currently a complete set of types for the context variables but can be used in transition, until the context file has been converted to typescript.

## Interfacing Files

Most of the global filter components are files that are actually housed and maintained in the general React-UI library. Because of this we utilize some interface files to massage the needed variables into the correct format for these files.

### Timeframe Filter

The TimeframeFilter.tsx file is responsible for determining which timeframe types to display, which timeframes are enabled/disabled, which aggregation combinations are valid and handle users selection. It creates a valid params object for the main GlobalFilter library component and handles the selection object to pass valid data for the user's updated state.

### Marketplace Filter

The MarketplaceFilter.tsx file is responsible for organizing and displaying all of the global marketplaces and regions. It creates a valid params object for the main GlobalMarketplaces library component and handles the selection objects for regions and marketplaces and passes valid data for the user's updated state.

### Categories Filter

This filter is unique to Predict and does not have a library component. So this filter component handles the display and selection of custom categories for all brands.

### Currency Filter

The CurrencyFilter.tsx file is responsible for fetching and displaying all global currencies as well as handling the selection of any currency.

### Tags Filter

The TagsFilter.tsx file is responsible for fetching and displaying tags for a selected brand. It also handles the selection of tags.

### VAT Toggle

While not a general filter, this component handles the toggle that determines if the VAT is applied to the sales values or not.
