/**
 *  Please be familiar with the documentation in the README.md file before making changes to this file
 *  or any of the related global filter files.
 **/

import React, { useContext, useEffect } from 'react'
import { Tabs } from '@patterninc/react-ui'
import { nonLOBGroupCategories, ThemeContext } from 'src/Context'
import type {
  CategoriesFilter,
  ChurnProps,
  CurrencyFilter,
  LinesOfBusinessProps,
  LOBdataProps,
  Marketplace,
  MarketplaceData,
  SelectedLinesOfBusinessProps,
  StateType,
  Tags,
  TimeframeFilterProps,
  VatProps,
} from '@predict-types'

import styles from './_globalFilter.module.scss'
import ChurnToggle from './ChurnToggle'
import CurrencyFilterContainer from './CurrencyFilter'
import CustomCategories from './CustomCategories'
import {
  useIsAttainmentRoute,
  useIsGlobalCategoriesEnabled,
  useIsGlobalCurrencyEnabled,
  useIsGlobalLinesOfBusinessEnabled,
  useIsGlobalMarketplacesEnabled,
  useIsGlobalTagsEnabled,
  useIsGlobalTimeframeEnabled,
} from './GlobalFilter.hooks'
import LinesOfBusinessFilter from './LinesOfBusinessFilter'
import MarketplacesFilter from './MarketplacesFilter'
import TagsFilter from './TagsFilter'
import TimeframeFilter from './TimeframeFilter'
import VatAdjustmentToggle from './VatAdjustmentToggle'

type GlobalFilterTabsProps = {
  closeFilterSideDrawer?: () => void
  displayState: StateType
  limitSelectionToggleState?: boolean
  resetTabIndex: boolean
  setDisableApplyButton?: React.Dispatch<React.SetStateAction<boolean>>
  setLimitSelectionToggleState?: React.Dispatch<React.SetStateAction<boolean>>
  toggles: Record<string, boolean>
  updateCategories: (categoriesData: CategoriesFilter) => void
  updateCurrency: (currencyData: CurrencyFilter) => void
  updateMarketplaces: (marketplaceData: MarketplaceData) => void
  updateTags: (tagsState: Tags) => void
  updateLinesOfBusiness: (linesOfBusiness: LinesOfBusinessProps) => void
  updateGlobalLinesOfBusiness: (linesOfBusiness: LinesOfBusinessProps) => void
  updateLinesOfBusinessOptions: (
    linesOfBusiness: SelectedLinesOfBusinessProps,
  ) => void
  updateTimeframe: (timeframe: TimeframeFilterProps) => void
  updateVat: (vat: VatProps) => void
  updateChurn: (churn: ChurnProps) => void
  filteredMarketplaces: Array<
    Marketplace | { marketplace_name: string; subMarketplaces: Marketplace[] }
  >
  setLOBSelected: (lobSelected: boolean) => void
  LOBdata: LOBdataProps
  activeTabIndex?: number
  setActiveTabIndex?: React.Dispatch<React.SetStateAction<number | undefined>>
}

const GlobalFilterTabs = ({
  closeFilterSideDrawer,
  displayState,
  limitSelectionToggleState,
  resetTabIndex,
  setDisableApplyButton,
  setLimitSelectionToggleState,
  toggles,
  updateCategories,
  updateCurrency,
  updateMarketplaces,
  updateTags,
  updateLinesOfBusiness,
  updateGlobalLinesOfBusiness,
  updateLinesOfBusinessOptions,
  updateTimeframe,
  updateVat,
  updateChurn,
  filteredMarketplaces,
  setLOBSelected,
  LOBdata,
  activeTabIndex,
  setActiveTabIndex,
}: GlobalFilterTabsProps) => {
  const isAttainmentRoute = useIsAttainmentRoute()

  const { customer } = useContext(ThemeContext),
    isAllBrandSelected = customer?.id === 0

  const [activeTab, setActiveTab] = React.useState(activeTabIndex ?? 0)
  const globalMarketplacesEnabled = useIsGlobalMarketplacesEnabled(),
    globalCurrencyDisabled = useIsGlobalCurrencyEnabled(),
    globalTagsDisabled = useIsGlobalTagsEnabled(),
    globalLinesOfBusinessDisabled = useIsGlobalLinesOfBusinessEnabled(),
    globalCategoriesEnabled = useIsGlobalCategoriesEnabled(),
    globalTimeframeEnabled = useIsGlobalTimeframeEnabled()

  const unsupportedLOBs = nonLOBGroupCategories.includes(
    customer?.grouping_category,
  )

  const getFilterTabs = () => {
    let filterTabIndex = -1
    const filterTabs = []
    if (globalTimeframeEnabled) {
      filterTabs.push({
        id: ++filterTabIndex,
        tabName: 'Timeframe',
        content: (
          <TimeframeFilter
            displayState={displayState.timeframe}
            updateTimeframe={updateTimeframe}
          />
        ),
      })
    }

    if (globalMarketplacesEnabled) {
      filterTabs.push({
        id: ++filterTabIndex,
        tabName: 'Marketplaces',
        content: (
          <MarketplacesFilter
            displayState={displayState.marketplaces}
            updateMarketplaces={updateMarketplaces}
            limitSelectionToggleState={limitSelectionToggleState}
            setLimitSelectionToggleState={setLimitSelectionToggleState}
            filteredMarketplaces={filteredMarketplaces}
          />
        ),
      })
    }

    if (globalCategoriesEnabled) {
      filterTabs.push({
        id: ++filterTabIndex,
        tabName: 'Category',
        content: (
          <CustomCategories
            categoriesData={displayState.categories}
            closeFilterSideDrawer={closeFilterSideDrawer}
            setCustomCategories={updateCategories}
            setDisableApplyButton={setDisableApplyButton}
          />
        ),
      })
    }

    if (!(globalCurrencyDisabled && globalTagsDisabled)) {
      filterTabs.push({
        id: ++filterTabIndex,
        tabName: 'More',
        content: (
          <div className='global-filter-column'>
            <label className='fw-semi-bold fc-purple pat-mb-2'>
              Additional Filters
            </label>
            {!globalCurrencyDisabled && (
              <CurrencyFilterContainer
                displayState={displayState.currency}
                updateCurrency={updateCurrency}
              />
            )}

            {!globalTagsDisabled && (
              <TagsFilter
                displayState={displayState.tags}
                updateTags={updateTags}
              />
            )}

            {toggles?.isVATinGlobalFilterToggleEnabled && (
              <VatAdjustmentToggle
                optionSelected={displayState.vat}
                updateVatSelection={updateVat}
              />
            )}

            {isAllBrandSelected && isAttainmentRoute && (
              <ChurnToggle
                optionSelected={displayState.churn}
                updateChurnSelection={updateChurn}
              />
            )}

            {!globalLinesOfBusinessDisabled && !unsupportedLOBs && (
              <LinesOfBusinessFilter
                updateLinesOfBusiness={updateLinesOfBusiness}
                displayState={displayState.linesOfBusiness}
                setLOBSelected={setLOBSelected}
                updateGlobalLinesOfBusiness={updateGlobalLinesOfBusiness}
                updateLinesOfBusinessOptions={updateLinesOfBusinessOptions}
                LOBdata={LOBdata}
              />
            )}
          </div>
        ),
      })
    }

    return filterTabs
  }

  // Reset tab index to 0 when filter closes
  useEffect(() => {
    if (setActiveTabIndex) {
      setActiveTabIndex?.((prev) => activeTabIndex ?? prev)
      // To sync activeTab with activeTabIndex on reset
      setActiveTab((prev) => activeTabIndex ?? prev)
    } else {
      setActiveTab((prev) => (resetTabIndex ? 0 : prev))
    }
    return () => {
      setActiveTabIndex?.(0)
    }
  }, [activeTabIndex, resetTabIndex, setActiveTabIndex])

  return (
    <div
      id='global_filter_main_body_tab_container'
      className={styles.globalFilterContainer}
    >
      <Tabs
        customId='global-filter-tabs'
        customClass={styles.tabs}
        active={activeTab}
        equalWidth
        callout={(tab) => setActiveTab(tab)}
        tabs={getFilterTabs()}
      />
    </div>
  )
}

export default GlobalFilterTabs
