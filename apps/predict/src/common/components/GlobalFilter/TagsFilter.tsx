import React, { useEffect } from 'react'
import { useLocation } from 'react-router-dom'
import type { BrandGroupCustomer, Customer, Tags } from '@predict-types'

import { ThemeContext } from '../../../Context'
import { checkGlobalTagsDisabled } from './GlobalFilter.helpers'
import GlobalTags from './GlobalTags'

type TagsFilterProps = {
  displayState: Tags
  updateTags: (tagsState: Tags) => void
}

const TagsFilter = ({ displayState, updateTags }: TagsFilterProps) => {
  const [globalTagsDisabled, setGlobalTagsDisabled] = React.useState(false)
  const location = useLocation()
  const { brandGroupCustomer, customer } = React.useContext(ThemeContext),
    bgc = brandGroupCustomer as unknown as BrandGroupCustomer,
    isBrandGroupSelected = !!brandGroupCustomer
  const isAutoBrandGroupSelected =
    bgc?.grouping_category === 'auto_brand_groups'

  const globalTagsProps = {
    customerId: customer.id,
    disabled: globalTagsDisabled,
    onChange: updateTags,
    selectedAbgCustomerIds: isAutoBrandGroupSelected ? bgc?.customer_ids : [],
    tags: displayState,
  }

  useEffect(() => {
    /**
     * Enabled tags for brands and auto brand groups
     * Disabled tags for brand groups and All Brands
     */
    if (!isBrandGroupSelected || isAutoBrandGroupSelected) {
      setGlobalTagsDisabled(
        checkGlobalTagsDisabled(
          location.pathname,
          isAutoBrandGroupSelected ? null : (customer as unknown as Customer),
        ),
      )
    } else {
      setGlobalTagsDisabled(true)
    }
  }, [customer, isAutoBrandGroupSelected, isBrandGroupSelected, location])

  return <GlobalTags key={customer.id} {...globalTagsProps} />
}

export default TagsFilter
