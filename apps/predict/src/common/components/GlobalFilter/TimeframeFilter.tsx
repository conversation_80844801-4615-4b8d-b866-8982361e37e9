import React, { useCallback, useEffect, useMemo, useState } from 'react'
import { useLocation } from 'react-router-dom'
import { isEqual } from 'lodash'
import moment, { type Moment } from 'moment'
import {
  type DisabledTimeframeOptionsProps,
  getComparisonDates,
  historicalTimeframes,
  newHistoricalTimeframes,
  useMediaQuery,
} from '@patterninc/react-ui'
import {
  type CompareWith,
  type TimeframeType,
} from '@patterninc/react-ui/dist/components/HeaderMetric/HeaderMetricHelpers'
import {
  isMatchPath,
  productListingRoute,
  useTranslate,
} from '@predict-services'
import type { DateType, Timeframe } from '@predict-types'

import { useAlertProps } from '../../../modules/Loyalty/LoyaltyHooks'
import ComparisonPeriodToggle from './ComparisonPeriodToggle'
import {
  checkOnlyCoreRoutes,
  checkOnlyTrafficRoutes,
  copyOf,
  getDefaultTimeframeForAttainment,
  getDisabledTimeframeOptions,
  getValidAggregation,
  getValidAggregationsForHistoricalTimeframe,
  insightsReportRoutes,
  insightsVendorAnalyticsRoute,
  isContentRoutes,
  isLoyaltyRoutes,
  renderAggregations,
} from './GlobalFilter.helpers'
import {
  useCustomComparisonDateRange,
  useDisableTimeframe,
  useIsAttainmentRoute,
  useIsLoyaltySnSRoute,
  useIsMarketAnalyticsRoute,
  useIsPriceSegmentsRoute,
  useIsShareAtAGlanceRoute,
  useValidTimeframeAggregations,
} from './GlobalFilter.hooks'
import GlobalTimeframe from './GlobalTimeframe'
import type {
  StateType,
  TimeframeFilterProps as TimeframeProps,
} from './globalFilterTypes'

type TimeframeFilterProps = {
  displayState: StateType['timeframe']
  updateTimeframe: (timeframeProps: TimeframeProps) => void
}

type UpdateAggregationProps = {
  timeframe: TimeframeProps
  aggregation: string
}

type UpdateRangeProps = {
  startDateOrAggregation: {
    startDate: DateType
    aggregation: string
  }
  endDate: DateType
  timeframe: TimeframeProps['timeframe']
  comparisonDateRanges: {
    firstRange_startDate: Moment
    firstRange_endDate: Moment
    secondRange_startDate?: Moment
    secondRange_endDate?: Moment
  }
}

type GetRangeProps = {
  startDate: string
  endDate: string
  timeframe: TimeframeProps['timeframe']
  comparisonDateRanges: UpdateRangeProps['comparisonDateRanges']
}

const TimeframeFilter = ({
  displayState,
  updateTimeframe,
}: TimeframeFilterProps) => {
  const location = useLocation()
  const { timeframe, startDate, endDate } = displayState
  const screenIsMdMax = useMediaQuery({ type: 'max', breakpoint: 'md' })

  ///////////////////////////////////////////////////////////////////////////////////////////////////
  // TOGGLES
  ///////////////////////////////////////////////////////////////////////////////////////////////////
  const comparisonPeriodTogglePaths = [
      '/insights',
      ...insightsReportRoutes,
      '/insights/share/marketshare/*',
      '/insights/share/price-segments/*',
      '/protect/buybox/sellers',
      '/protect/buybox/brands',
      '/protect/buybox/suppression',
      '/protect/buybox/products',
      insightsVendorAnalyticsRoute,
    ],
    otherComparisonPeriodTogglePaths = [
      '/traffic',
      '/loyalty',
      '/content',
      '/reports/custom',
      `${productListingRoute}/:id/marketplace/:id/listing/:id/content/details/at-a-glance`,
      `${productListingRoute}/:id/marketplace/:id/listing/:id/content/details/conversion`,
      '/advisory/price-segments',
      '/advisory/market-analytics',
      '/protect/price/products',
      `${productListingRoute}/:productId/marketplace/:marketplaceId/listing/:listingId/protect/price`,
      `${productListingRoute}/:productId/protect/price`,
      '/protect/price/automations/*',
      `${productListingRoute}/:productId/marketplace/:marketplaceId/listing/:listingId/share/*`,
      `/insights/share/product-details/:asin/:country_code/share/*`,
      `/insights/share/external-listing/:asin/share/*`,
      `${productListingRoute}/:productId/protect/buybox`,
      `${productListingRoute}/:productId/marketplace/:marketplaceId/listing/:listingId/protect/buybox`,
      `${productListingRoute}/:productId/protect/suppression`,
      `${productListingRoute}/:productId/marketplace/:marketplaceId/listing/:listingId/protect/suppression`,
      `${productListingRoute}/:productId/marketplace/:marketplaceId/listing/:listingId/overview`,
      `${productListingRoute}/:productId/overview`,
    ]
  const showComparisonPeriodToggle =
    comparisonPeriodTogglePaths.some((path) =>
      isMatchPath(path, location.pathname),
    ) ||
    otherComparisonPeriodTogglePaths.some((path) =>
      isMatchPath(path, location.pathname, false),
    )

  /////////////////////////////////////////////////////////////////////////////////////////////////
  // ROUTE DEFINITIONS
  /////////////////////////////////////////////////////////////////////////////////////////////////
  const isAttainmentRoute = useIsAttainmentRoute()
  const isLoyaltySnSRoute = useIsLoyaltySnSRoute()
  const isProtectBuyboxRoutes = location.pathname?.includes('protect')
  const isPriceSegmentsRoute = useIsPriceSegmentsRoute()
  const isShareAtAGlanceRoute = useIsShareAtAGlanceRoute()
  const isMarketAnalyticsRoute = useIsMarketAnalyticsRoute()
  const showAggregations = renderAggregations(location.pathname)
  const routeRef = React.useRef(location.pathname)

  /////////////////////////////////////////////////////////////////////////////////////////////////
  // TIMEFRAME
  /////////////////////////////////////////////////////////////////////////////////////////////////
  const alertProps = useAlertProps(displayState.startDate, displayState.endDate)
  const disableTimeFrameColumn = useDisableTimeframe()
  const validTimeframeAggregations = useValidTimeframeAggregations()
  const [disableCompareWith, setDisableCompareWith] = useState(
    displayState?.timeframe?.value === 'custom',
  )
  const { t: tf } = useTranslate('timeframe')

  const setComparisonPeriodSelection = (selection: { value: string }) => {
    updateTimeframe({
      ...displayState,
      comparisonPeriodSelection: selection,
    })
  }

  const [disabledTimeframeOptions, setDisabledTimeframeOptions] =
    React.useState<DisabledTimeframeOptionsProps>({
      current: [],
      previous: [],
      trailing: [],
      historical: [],
      quarterly: [],
    })

  useEffect(() => {
    const newDisabledTimeframeOptions = getDisabledTimeframeOptions(
      location.pathname,
      tf,
    )
    setDisabledTimeframeOptions((prevOptions) => {
      return JSON.stringify(prevOptions) !==
        JSON.stringify(newDisabledTimeframeOptions)
        ? newDisabledTimeframeOptions
        : prevOptions
    })
  }, [location.pathname, tf])

  const isCoreRoute = checkOnlyCoreRoutes(location.pathname)
  const isTrafficRoute = checkOnlyTrafficRoutes(location.pathname),
    isLoyaltyRoute = isLoyaltyRoutes(location.pathname),
    isContentRoute = isContentRoutes(location.pathname)
  const validTimeframeOption = (
    isCoreRoute || isTrafficRoute || isLoyaltyRoute || isContentRoute
      ? newHistoricalTimeframes
      : historicalTimeframes
  ).filter(
    (option) => !disabledTimeframeOptions?.historical?.includes(option.display),
  )[0]

  const validHistoricalTimeFrame = useMemo<Timeframe>(() => {
    const { historical: validHistoricalAggregations } =
      validTimeframeAggregations

    const validValue =
      typeof validTimeframeOption?.value === 'number'
        ? validTimeframeOption.value
        : validTimeframeOption?.value === 'custom'
          ? 'custom'
          : 30

    return {
      type:
        (isCoreRoute || isTrafficRoute || isLoyaltyRoute || isContentRoute) &&
        (timeframe?.type === 'trailing' || timeframe?.type === 'historical')
          ? 'trailing'
          : ('historical' as Timeframe['type']),
      display: validTimeframeOption?.display ?? '30D',
      timeValue: validTimeframeOption?.timeValue ?? 'day',
      value: validValue,
      aggregation: getValidAggregationsForHistoricalTimeframe({
        pathname: location.pathname,
        timeframe,
        value: typeof validValue === 'number' ? validValue : 30,
        startDate,
        endDate,
        customAggregations: validHistoricalAggregations,
        timePeriodExtension: true,
      })[0],
    }
  }, [
    endDate,
    isCoreRoute,
    isLoyaltyRoute,
    isTrafficRoute,
    location.pathname,
    startDate,
    timeframe,
    validTimeframeAggregations,
    validTimeframeOption?.display,
    validTimeframeOption?.timeValue,
    validTimeframeOption?.value,
    isContentRoute,
  ])

  /////////////////////////////////////////////////////////////////////////////////////////////////
  // Update Aggregation AND Date Range selection
  /////////////////////////////////////////////////////////////////////////////////////////////////
  const comparisonPeriodRef = React.useRef(
    displayState.comparisonPeriodSelection,
  )

  const updateAggregation = useCallback(
    ({ timeframe, aggregation }: UpdateAggregationProps) => {
      // only update the aggregation if it has changed
      timeframe.startDate = displayState.startDate
      timeframe.endDate = displayState.endDate
      timeframe.timeframe = {
        ...timeframe.timeframe,
        aggregation: aggregation,
      }

      // if the comparison period has changed, update the comparison date ranges
      if (comparisonPeriodRef.current !== timeframe.comparisonPeriodSelection) {
        comparisonPeriodRef.current = timeframe.comparisonPeriodSelection

        if (displayState.comparisonPeriodSelection.value !== 'custom') {
          const correctedTimeframe = { ...timeframe.timeframe } as TimeframeType
          const currentDate = moment(),
            timeOptions = {
              hour: currentDate.hour(),
              minute: currentDate.minute(),
              second: currentDate.second(),
            }

          const comparisonDates = getComparisonDates({
            startDate: moment(displayState.startDate).set(timeOptions).format(),
            endDate: moment(displayState.endDate).set(timeOptions).format(),
            timeframe: correctedTimeframe,
            compareWith: displayState.comparisonPeriodSelection
              .value as CompareWith,
          })
          timeframe.comparisonDateRanges = {
            firstRange_startDate: displayState.startDate,
            firstRange_endDate: displayState.endDate,
            secondRange_startDate:
              correctedTimeframe?.timeValue === 'hours'
                ? comparisonDates?.startDate
                : moment(comparisonDates?.startDate).startOf('day').format(),
            secondRange_endDate:
              correctedTimeframe?.timeValue === 'hours'
                ? comparisonDates?.endDate
                : moment(comparisonDates?.endDate).endOf('day').format(),
          }
        }
        return timeframe
      }
    },
    [
      displayState.comparisonPeriodSelection,
      displayState.endDate,
      displayState.startDate,
    ],
  )

  const getStandardDateRange = useCallback(
    ({
      startDate,
      endDate,
      timeframe,
    }: Omit<GetRangeProps, 'comparisonDateRanges'>) => {
      const correctedTimeframe = { ...timeframe } as TimeframeType
      const standardDateRange = getComparisonDates({
        startDate,
        endDate,
        timeframe: correctedTimeframe,
        compareWith: displayState.comparisonPeriodSelection
          .value as CompareWith,
      })
      return standardDateRange
    },
    [displayState.comparisonPeriodSelection.value],
  )

  const getCustomDateRange = useCallback(
    ({
      comparisonDateRanges,
      startDate,
      endDate,
      timeframe,
    }: GetRangeProps) => {
      // use custom comparison date ranges if they are defined
      if (
        comparisonDateRanges?.secondRange_startDate &&
        comparisonDateRanges?.secondRange_endDate
      ) {
        return {
          startDate: comparisonDateRanges?.secondRange_startDate,
          endDate: comparisonDateRanges?.secondRange_endDate,
        }
      } else {
        // otherwise calculate the comparison date ranges based on the user's selected timeframe
        const correctedTimeframe = { ...timeframe } as TimeframeType
        const calculatedCustomDateRange = getComparisonDates({
          startDate,
          endDate,
          timeframe: correctedTimeframe,
          compareWith: displayState.comparisonPeriodSelection
            .value as CompareWith,
        })
        return calculatedCustomDateRange
      }
    },
    [displayState.comparisonPeriodSelection.value],
  )

  const updateRange = useCallback(
    ({
      startDateOrAggregation,
      endDate,
      timeframe,
      comparisonDateRanges,
    }: UpdateRangeProps) => {
      const validStartDate = (
          startDateOrAggregation
            ? timeframe?.timeValue === 'hours'
              ? moment(startDateOrAggregation?.toString())
              : moment(startDateOrAggregation?.toString()).startOf('day')
            : moment().startOf('day')
        ) as DateType,
        validEndDate = endDate
          ? timeframe?.timeValue === 'hours'
            ? moment(endDate)
            : moment(endDate).endOf('day')
          : moment().endOf('day'),
        aggregation = getValidAggregation(
          timeframe,
          validTimeframeAggregations,
        ),
        validTimeframe = {
          ...timeframe,
          ...(comparisonDateRanges?.secondRange_startDate
            ? {
                compareDisplay: `${moment(
                  comparisonDateRanges?.secondRange_startDate,
                )?.format(
                  'L',
                )} - ${moment(comparisonDateRanges?.secondRange_endDate)?.format('L')}`,
              }
            : {}),
          ...(aggregation ? { aggregation } : {}),
        }

      setDisableCompareWith(timeframe.value === 'custom')

      const secondDateRange = (validTimeframe?.value
        ? getCustomDateRange({
            comparisonDateRanges,
            startDate: moment(validStartDate).format('L'),
            endDate: moment(validEndDate).format('L'),
            timeframe: validTimeframe,
          })
        : getStandardDateRange({
            startDate: moment(validStartDate).format('L'),
            endDate: moment(validEndDate).format('L'),
            timeframe: validTimeframe,
          })) ?? { startDate: '', endDate: '' }

      return {
        ...timeframe,
        startDate: validStartDate,
        endDate: validEndDate,
        timeframe: validTimeframe,
        comparisonDateRanges: {
          firstRange_startDate: moment(validStartDate).format(), // Removed format('L') as API expects ISO format
          firstRange_endDate: moment(validEndDate).format(),
          secondRange_startDate: moment(secondDateRange.startDate).format(),
          secondRange_endDate: moment(secondDateRange.endDate).format(),
        },
      }
    },
    [getCustomDateRange, getStandardDateRange, validTimeframeAggregations],
  )

  /** Method to update all of the timeframe variables that the user has selected */
  const timeframeCallout = useCallback(
    (
      calloutType: 'UPDATE_AGGREGATION' | 'UPDATE_RANGE',
      startDateOrAggregation: {
        startDate: DateType
        aggregation: string
      },
      endDate: DateType,
      localTimeframe: TimeframeProps['timeframe'],
      comparisonDateRanges: {
        firstRange_startDate: Moment
        firstRange_endDate: Moment
        secondRange_startDate?: Moment
        secondRange_endDate?: Moment
      },
    ) => {
      const updatedTimeframe: TimeframeProps = {
        ...displayState,
        comparisonPeriodSelection: displayState.comparisonPeriodSelection,
        ...(calloutType === 'UPDATE_AGGREGATION'
          ? updateAggregation({
              timeframe: copyOf(displayState),
              aggregation: startDateOrAggregation.aggregation,
            })
          : updateRange({
              startDateOrAggregation,
              endDate,
              timeframe: localTimeframe ?? displayState.timeframe,
              comparisonDateRanges,
            })),
      }

      !isEqual(displayState, updatedTimeframe) &&
        updateTimeframe(updatedTimeframe)
    },
    [displayState, updateAggregation, updateRange, updateTimeframe],
  )

  const updateValidFilterState = (
    calloutType: string,
    validOption: { aggregation: string },
  ) => {
    if (calloutType === 'UPDATE_AGGREGATION') {
      const updatedTimeframe = {
        ...displayState,
        timeframe: {
          ...displayState.timeframe,
          aggregation: validOption.aggregation,
        },
      }
      const hasChanged = !isEqual(displayState, updatedTimeframe)

      hasChanged && updateTimeframe(updatedTimeframe)
    }
  }

  /////////////////////////////////////////////////////////////////////////////////////////////////
  // EXCEPTIONS TO THE DEFAULT TIMEFRAME
  /////////////////////////////////////////////////////////////////////////////////////////////////

  // Disabling Trailing 24HRS on Protect module as we don't support time at hourly grain that do not have sales data.
  useEffect(() => {
    if (
      isProtectBuyboxRoutes &&
      timeframe.type === 'trailing' &&
      disabledTimeframeOptions[timeframe.type]?.includes(timeframe.display)
    ) {
      const updatedTimeframe = {
          ...displayState,
          timeframe: {
            ...displayState.timeframe,
            ...validHistoricalTimeFrame,
          },
        },
        hasChanged = !isEqual(displayState, updatedTimeframe)

      hasChanged && updateTimeframe(updatedTimeframe)
    }
  }, [
    disabledTimeframeOptions,
    displayState,
    isProtectBuyboxRoutes,
    timeframe.display,
    timeframe.type,
    updateTimeframe,
    validHistoricalTimeFrame,
  ])

  useEffect(() => {
    if (routeRef.current !== location.pathname) {
      let updatedTimeframe = copyOf(displayState)
      if (isAttainmentRoute) {
        updatedTimeframe = getDefaultTimeframeForAttainment(displayState, tf)
      }

      const shouldUpdateTimeframe = !isEqual(displayState, updatedTimeframe)
      if (shouldUpdateTimeframe) {
        updateTimeframe(updatedTimeframe)
      }

      routeRef.current = location.pathname
    }
  }, [displayState, isAttainmentRoute, location.pathname, updateTimeframe, tf])

  /////////////////////////////////////////////////////////////////////////////////////////////////
  // TIMEFRAME DISPLAY PROPS
  /////////////////////////////////////////////////////////////////////////////////////////////////
  const globalTimeframeProps = {
    /** new alert for Loyalty route (requires all timeframes to be Sunday - Saturday) */
    alertProps: isLoyaltySnSRoute ? alertProps : undefined,
    /** the callout action when anything is clicked on the timeframe display */
    timeframeCallout: timeframeCallout,
    /** StartDate */
    startDateCopy: displayState.startDate as string,
    /** EndDate */
    endDateCopy: displayState.endDate as string,
    /** User's Currently Selected Timeframe */
    timeframeCopy: displayState.timeframe,
    /** used as a default timeframe to ensure that a valid timeframe can be selected */
    historicalTimeframes:
      isCoreRoute || isTrafficRoute || isLoyaltyRoute || isContentRoute
        ? newHistoricalTimeframes
        : historicalTimeframes,
    disableTimeFrameColumn: disableTimeFrameColumn,
    /** should show the various aggregations for the selected timeframe (boolean) */
    showAggregations: showAggregations,
    /** the valid aggregation options for the user's selected timeframe */
    customAggregations: validTimeframeAggregations,
    showHistoricTimeFrameDateRange: screenIsMdMax
      ? false
      : !(
          isAttainmentRoute ||
          isPriceSegmentsRoute ||
          isShareAtAGlanceRoute ||
          isMarketAnalyticsRoute
        ),
    /** show the compare date range inputs for Custom date ranges */
    isComparisonDateRangeEnabled: useCustomComparisonDateRange(),
    /** Disabled Custom date picker for attainment report only */
    showHistoricTimeFrameDateRangeOnMobile:
      screenIsMdMax &&
      !(isAttainmentRoute || isPriceSegmentsRoute || isMarketAnalyticsRoute),
    comparisonDateRanges: {
      firstRange_endDate:
        displayState.comparisonDateRanges.firstRange_endDate ?? '',
      firstRange_startDate:
        displayState.comparisonDateRanges.firstRange_startDate ?? '',
      secondRange_endDate:
        displayState.comparisonDateRanges.secondRange_endDate ?? '',
      secondRange_startDate:
        displayState.comparisonDateRanges.secondRange_startDate ?? '',
    },
    // Pass disabled custom options based on path.
    // Add path in helper function for which you would like to disabled timeframe options
    disabledTimeframeOptions: disabledTimeframeOptions,
    updateToValidFilterState: updateValidFilterState,
    showLabel: screenIsMdMax ? false : true,
  }

  return (
    <div>
      <GlobalTimeframe {...globalTimeframeProps} />
      {showComparisonPeriodToggle && (
        <ComparisonPeriodToggle
          optionSelected={displayState.comparisonPeriodSelection}
          setOptionSelected={setComparisonPeriodSelection}
          disableCompareWith={disableCompareWith}
        />
      )}
    </div>
  )
}

export default TimeframeFilter
