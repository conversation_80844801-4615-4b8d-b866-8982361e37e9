import React from 'react'
import { <PERSON><PERSON>abel, Icon, Picker } from '@patterninc/react-ui'
import { c } from '@predict-services'

type OptionSelectedProps = {
  value: string
}

type VatProps = {
  id?: number
  text: 'Exclude' | 'Include'
  value: 'on' | 'off'
}

type VatAdjustmentToggleProps = {
  optionSelected: OptionSelectedProps | VatProps
  updateVatSelection?: (vat: VatProps) => void
}

const vatAdjustmentOptions = [
  {
    id: 0,
    text: 'Exclude',
    value: 'on',
  },
  {
    id: 1,
    text: 'Include',
    value: 'off',
  },
]

const VatAdjustmentToggle = ({
  optionSelected,
  updateVatSelection,
}: VatAdjustmentToggleProps): React.JSX.Element => {
  return (
    <div className='pat-mt-2.5'>
      <FormLabel
        label={c('valueAddedTaxVAT')}
        tooltip={{
          tooltipContent: (
            <p>
              {c('vatTooltip1')}
              <span className='vat-icon-placement'>
                <Icon icon='vat' color='dark-purple' />
              </span>
              {c('vatTooltip2')}
            </p>
          ),
          position: 'top',
        }}
      />
      <div
        style={{
          marginTop: '6px',
        }}
      >
        <Picker
          options={vatAdjustmentOptions}
          state={optionSelected}
          stateName='value'
          callout={(_, value: string) => {
            const selection = vatAdjustmentOptions.find(
              (vat) => vat.value === value,
            ) as VatProps
            updateVatSelection?.(selection)
          }}
        />
      </div>
    </div>
  )
}

export default VatAdjustmentToggle
