@use '../../scss/base/mixins' as *;
@use '@patterninc/react-ui/dist/variables' as variables;

.global-filter.filters-container {
  // These styles were deprecated from the Filter in the library due to the SideDrawer implementation. Moving them over here to help style the Global Filter. Once the Global Filter moves into a SideDrawer, a lot of styles from this file will be deprecated as well.
  height: 100%;
  .popover {
    .dropdown-box {
      max-width: unset;
      .dropdown {
        max-height: unset;
        margin: 0;
        padding: 0;
      }
    }
    .popover-toggle {
      .selected-text {
        width: 100%;
        font-size: var(--font-size-12);
      }
    }
  }
  .filter-wrapper {
    .filter-header {
      padding: 16px;
      @include border-radius(4px 4px 0px 0px);
      background: var(--lighter-gray);
      border-bottom: 1px solid var(--light-gray);
      font-weight: var(--font-weight-semibold);
      color: var(--dark-purple);
      font-size: var(--font-size-12);
      display: flex;
      justify-content: space-between;
      align-items: center;
      line-height: normal;
    }
    .filter-body {
      padding: 16px;
      // Reduced the padding to prevent horizontal scrolling on the global filters on mobile view.
      ul[class*='global-timeframe'] {
        button[class^='button'] {
          padding: 16px 12px;
        }
      }
    }
    .footer {
      border-top: 1px solid var(--light-gray);
      padding: 16px;
    }
  }
  //////////////////////////////////////////////////////////

  .selected-text {
    white-space: nowrap;
    display: flex;
    align-items: center;
    font-weight: var(--font-weight-regular);
  }
  .selected-filters-display {
    display: flex;
    align-items: center;
    text-transform: uppercase;
    .divider {
      width: 1px;
      background: var(--purple);
      height: 12px;
      margin: 0 8px;
      display: inline-block;
    }
  }
  .global-filter-popover-wrapper.filter-body {
    display: flex;
    gap: 16px;
    position: relative;
    height: 100%;
    // Gradient overlay
    .gradient-bg {
      position: absolute;
      left: 0;
      right: 0;
      height: 35px;
      bottom: 0;
      z-index: 1;
      background: linear-gradient(0deg, #fff 36%, hsla(0, 0%, 100%, 0));
    }
    .global-filter-column {
      // Added min width for each filter column for consistency
      min-width: 200px;
      border-right: 1px solid var(--light-gray);
      padding-right: 16px;
      &:last-child {
        border: none;
        padding: 0;
      }
      .timeframe-date-display {
        padding: 0;
        height: auto;
      }
      .info-background {
        background-color: #ecfaff;
      }
      @media screen and (max-width: variables.$breakpoint-md) {
        max-width: calc(100vw - 32px);
      }
    }

    .global-filter-column .global-currency .dropdown-box {
      width: 100%;
      .dropdown {
        padding: 10px;
      }
    }
  }

  // Regions
  .global-regions {
    .select-box {
      border: 1px solid var(--medium-gray);
      padding: 8px;
      color: var(--dark-purple);
    }

    .select-box-dropdown {
      height: auto;
    }
  }

  // Marketplaces
  .global-marketplaces {
    overflow: auto;
    .level {
      display: block;
      font-size: var(--font-size-12);
      padding-left: 20px;
      position: relative;
      &.top-level {
        padding-left: 0;
      }
      &.with-icon {
        display: flex;
        justify-content: space-between;
        align-items: center;
      }
      & {
        margin: 10px 0;
      }
    }
    .shield,
    .sellers {
      svg {
        width: 16px;
        height: 16px;
        path {
          fill: var(--purple);
        }
      }
    }
    .l {
      position: absolute;
      // This vertically centers the icon next to the checkbox. Could not use 8px grid on this value.
      top: 10px;
      left: 5px;
    }
    &.without-message {
      height: 317px;
    }
    &.with-message {
      height: 253px;
    }
  }

  // Currency
  #global-currency-search {
    margin-bottom: 16px;
  }
  .global-currency {
    .global-currency-list {
      height: 300px;
      overflow: auto;
    }
    .global-currency-list-item {
      display: block;
      padding: 8px;
      cursor: pointer;
      &:hover {
        background: var(--light-blue);
      }
      &:last-child {
        margin-bottom: 8px;
      }
    }
    input[type='radio'] {
      visibility: hidden;
      position: absolute;
    }
    label.global-currency-label {
      font-weight: var(--font-weight-regular);
      margin: 0;
      cursor: pointer;
      &.active {
        font-weight: var(--font-weight-bold);
        position: relative;
        padding-bottom: 4px;
        &:after {
          content: '';
          width: 110%;
          position: absolute;
          height: 4px;
          bottom: 0;
          left: 0;
          border-radius: 4px;
        }
      }
    }
  }
}

.timeframe-filter-column {
  display: flex;
  flex-direction: column;
  .timeframe-date-display {
    flex: 1 1 auto;
    display: flex;
    flex-direction: column;
    min-width: 280px; //to make design stable.
  }
}

.marketplaces-filter-column {
  display: flex;
  flex-direction: column;
}

// overriding for padding for mobile layout sidedrawer
.filters-container
  .filter-wrapper
  .filter-body.global-filter-responsive-layout {
  padding: 0;
}

.global-filter.filters-container {
  // updated styles of global filters as per design
  .filter-wrapper {
    .filter-header.global-filter-layout-header {
      padding: 12px;
    }
    .filter-body.global-filter-popover-wrapper.global-filter-layout
      .global-filter-column {
      [data-timeframe-section-header='timeframe-body'] {
        padding: 0 4px;
      }
      // explicitly added !important to win against library class
      .global-currency .global-regions {
        .dropdown-option-value {
          font-size: var(--font-size-12) !important;
        }
      }
    }
  }

  // applying loader style
  .filter-wrapper
    .filter-body.global-filter-popover-wrapper.filter-body.global-filter-layout.global-filter-loader-container {
    grid-template-columns: 230px 230px 210px;
  }

  // style for footer buttons at bottom
  &.responsive-global-filter
    .filter-wrapper
    .footer.global-filter-responsive-layout {
    transform: translate(-28px);
    width: calc(100% + -20px);
    margin-top: 24px;
    position: absolute;
    bottom: 0;
  }

  /**
  * updated styled for scrollbar on mobile
  */
  &.responsive-global-filter
    .filter-wrapper
    .filter-body.global-filter-responsive-layout {
    overflow: auto;
  }

  @supports (-webkit-touch-callout: none) {
    .marketplaceContent {
      overflow: unset;
      div[class^='tabContent'] {
        overflow: scroll;
        height: calc(100vh - 280px);
      }
    }
  }
}

/**
* Responsive - Global filter.
* height/width changes are added based on the few responsive testing
*/
.global-filter.filters-container.responsive-global-filter {
  .filter-wrapper {
    min-width: 0px;
    height: 100%;
  }
  .dropdown-box.mobile-popover.right {
    right: -74px;
  }
  .dropdown-box.banner-display.mobile-popover.right {
    right: 0;
  }
  .filter-header {
    height: 30px;
  }
  .filter-body {
    display: grid;
    grid-template-columns: none;

    .global-regions .select-box-dropdown {
      height: auto;
      max-width: none;
    }
    .global-marketplaces.without-message {
      height: calc(var(--100vh) - 290px);
    }
    .global-marketplaces.with-message {
      height: calc(var(--100vh) - 318px);
    }

    .popover {
      .dropdown-box.select-box-dropdown {
        // reducing 2px to ensure the box-shadows are shown properly, it's for an absolute element
        max-width: calc(100vw - 2px);
      }
    }
  }
}

.global-marketplaces-tooltip {
  overflow: auto;
  min-width: 250px;
  .level {
    display: block;
    font-size: var(--font-size-12);
    padding-left: 20px;
    position: relative;
    &.top-level {
      padding-left: 0;
      padding-bottom: 0;
    }
    &.with-icon {
      display: flex;
      justify-content: space-between;
      align-items: center;
      min-height: 4px;
    }
  }
  .l {
    position: absolute;
    left: 5px;
  }
}

.tags-dropdown {
  position: relative;
  .popover-toggle {
    .select-box {
      border: 1px solid var(--medium-gray);
    }
  }
  .checkbox {
    min-width: 16px;
  }
}

.vat-icon-placement {
  display: inline-block;
  margin-left: 4px;
  margin-right: 4px;
}

@supports (-webkit-touch-callout: none) {
  .globalFilterMobileFooter {
    div[class*='footer'] {
      bottom: -8px;
    }
  }
}
.side-drawer-global-filter {
  font-size: 14px;
  line-height: 20px;
}
