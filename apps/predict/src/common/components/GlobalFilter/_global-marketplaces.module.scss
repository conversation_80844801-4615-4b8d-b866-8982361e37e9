@use '@patterninc/react-ui/dist/variables' as *;

.searchBarContainer {
  width: 99%;
}

.globalMarketplaces {
  height: 100%;
  overflow: auto;
  flex-grow: 1; // height adjustment in case of conditional filters
}

.level {
  display: block;
  font-size: var(--font-size-12);
  padding-left: 24px;
  position: relative;
  margin: 10px 0;
}

.topLevel {
  padding-left: 0;
}

.l {
  position: absolute;
  // This vertically centers the icon next to the checkbox. Could not use 8px grid on this value.
  top: 10px;
  left: 5px;
}

.withIcon {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.categoriesFilterContainer {
  display: flex;
  flex-direction: column;
}
