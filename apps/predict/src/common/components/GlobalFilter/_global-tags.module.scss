@use '@patterninc/react-ui/dist/variables';

.globalTags {
  width: 100%;
  min-width: 200px !important;
}

.dropdownBox {
  margin: 0px;
  width: 100%;
  border: unset;

  @media only screen and (min-width: variables.$breakpoint-md) {
    max-height: 120px !important;
  }
}

.globalTagsToggleLabel {
  font-weight: var(--font-weight-regular);
  font-size: var(--font-size-12);
}

.switchContainer {
  display: flex;
  align-items: flex-start;
  justify-content: flex-start;
}
