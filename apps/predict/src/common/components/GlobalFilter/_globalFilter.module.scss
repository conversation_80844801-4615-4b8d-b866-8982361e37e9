@use '@patterninc/react-ui/dist/variables';

.divider {
  width: 1px;
  background-color: var(--purple);
  height: 12px;
  display: inline-block;
}

.globalFilterContainer {
  font-size: var(--font-size-14);
  height: 100%;
  line-height: 20px;
  padding: 0 16px;
}

.tabs {
  @media only screen and (max-width: variables.$breakpoint-md) {
    max-width: 100%;
    display: flex;
  }
}

.tabs > li > button {
  @media only screen and (max-width: variables.$breakpoint-md) {
    padding: 8px;
    padding-left: 16px;
  }
}
