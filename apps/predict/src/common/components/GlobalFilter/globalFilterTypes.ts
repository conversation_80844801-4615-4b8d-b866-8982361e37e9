import type { DateType } from '../../types'

// Originally from GlobalFilter.helpers.ts
export type AvailableMarketplacesData = {
  allBrandMarketplaces: Marketplace[]
  allBrandRegions: Region[]
  amazonMarketplaceCodes: string[]
  amazonMarketplaceIdsData: number[]
  filteredMarketplaces: number[]
  marketPlacesColors: MarketplaceData['marketPlacesColors']
  matchingMarketplaces: Set<number>
  possibleRegionsForBrand: Set<number>
  rawMarketplaces: number[]
  sdMarketplaces: number[]
}

// Originally from contextTypes.ts
export type Brand = {
  id: number
  customer_key: string
  customer_name: string
  customer_desc: string
  created_at: string
  updated_at: string
  process_days: number
  discount: null | number
  settings: {
    buybox_tracking: boolean
    default_marketplaces: unknown | null
    display_period: unknown | null
    emails: boolean
    inventory_collection: boolean
    only_authorized_sellers: boolean
    refresh_schedule: null | unknown
    show_compliant_volume: boolean
  }
  exclusive_start_date: string
  customer_state_id: number | null
  stripe_customer_id: null | number
  billing_email: string
  vendor_id: number
  customer_type: string
  admin_control: boolean
  threepn: boolean
  law_firm_id: null | number
  logo: {
    url: null | string
    thumb: {
      url: null | string
    }
  }
  initial_signing_value: string
  expansion_value: null | string
  opportunity_source: null | unknown
  sales_person_id: null | number
  financial_analyst_id: null | number
  accepted_customer_names: string[]
  partner_code: string
  adminczar_org_unit_id: null | number
  partner_name: null | string
  lines_of_business: string[]
}

// Originally from contextTypes.ts
export type BrandGroup = {
  id: number
  name: string
  is_my_brand_group: boolean
  customer_ids: number[]
  vendor_ids: number[]
  brand_count: number
  brand_data: {
    user_brand_group_id: number
    customer_id: number
    customer_name: string
    vendor_id: number | null
  }[]
  customer_name: string
  grouping_category: 'brand_groups' | 'auto_brand_groups'
  itemIndex: number
} | null

// Originally from contextTypes.ts
export type BrandGroupCustomer = {
  grouping_category: 'auto_brand_groups' | string
  customer_ids: number[]
  id: number
}

// Originally defined in GlobalFilter.tsx
export type CategoriesFilter = {
  fullCategoryList: CheckboxCategory[]
  categories: CheckboxCategory[] // selected categories
  searchTerm: string
}

export type Category = {
  id: number
  name: string
  children: Category[]
  root_category_id: number
  parent_category_id?: number
}

export type CheckboxCategory = Category & {
  checked?: boolean
  customer_id?: number
}

// Originally defined in GlobalFilter.tsx
export type ChurnProps = {
  id?: number
  text?: 'Exclude' | 'Include'
  value: 'off' | 'on'
}

// Originally from contextTypes.ts
export type Currency = {
  id: number
  name: string
  symbol: string
  code: string
  type: string
  label: string
  created_at?: string
  updated_at?: string
}

// Originally defined in GlobalFilter.tsx
export type CurrencyFilter = {
  currentCurrency: Currency
  allCurrencies: Currency[]
}

// Originally from sharedTypes.ts (checkout Content > Content.tsx Customer type)
export type Customer = {
  id: number
  customer_key: string
  customer_name: string
  customer_desc: string
  created_at: string
  updated_at: string
  process_days: number
  discount?: number | null
  settings: Record<string, unknown>
  exclusive_start_date: string
  customer_state_id: number | null
  stripe_customer_id?: number | null
  billing_email?: string
  vendor_id: number
  customer_type: string
  admin_control: boolean
  threepn: boolean
  law_firm_id?: number | null
  logo: {
    url: string | null
    thumb: {
      url: string | null
    }
  }
}

// Originally from contextTypes.ts
export type CustomTimeAggregation =
  | '24HRS'
  | '1D'
  | '7D'
  | '30D'
  | '1W'
  | '3M'
  | '6M'
  | '12M'
  | '1Y'
  | 'custom'

// Originally from contextTypes.ts
export type CustomerWithLOB = {
  id: number
  customer_key: string
  customer_name: string
  lines_of_business: string[]
  grouping_category: string
}

// Originally from contextTypes.ts
export type DateFormat = {
  format: string
  date_format_id: number
  type: string
  label: string
}

// Originally from GlobalFilter.helpers.ts
export type GetAvailableMarketplacesProps = {
  /** this is an object of a customer_id or an array of customer_ids */
  params: Record<string, unknown>
  apiController: AbortController
  selectedRegions: RegionId[]
  selectedMarketplaces: MarketplaceId[]
  isLoyaltyMarketplaceRoute?: boolean
  isContentRoute?: boolean
  isShareAtAGlanceRoute?: boolean
  isMarketAnalyticsRoute?: boolean
  isPriceSegmentsRoute?: boolean
  isBuyBoxRoute?: boolean
  isPriceProductsRoute?: boolean
  isBrandHealthRoute?: boolean
  disableChinaForOrganicTraffic?: boolean
  isDestinyAutomationPreviewRoute?: boolean
}

// Originally from contextTypes.ts
export type Language = {
  name: string
  value: 'en' | 'zh'
  label: string
}

// Originally defined in GlobalFilter.tsx
export type LinesOfBusinessProps = {
  type_ids: string[]
  selectedTypes: SelectedLinesOfBusinessProps[]
}

// Originally defined in GlobalFilter.tsx
export type LOBdataProps = {
  LOBdata: LOBResponseProps[]
  LOBdataStatus: 'success' | 'pending' | 'error'
}

// Originally defined in GlobalFilter.tsx
export type LOBResponseProps = {
  id: number
  name: string
  code: string
  sensitive?: boolean
}

export type Marketplace = {
  // are any of these optional?
  amazon: boolean
  color: string // is this a list of specific colors?
  country_code: string
  country_id: number
  id: number
  is_selective_distribution: boolean
  marketplace_group_name: string
  marketplace_name: string
  marketplace_type: string // is this a list of specific types? 'both' | '?'
  region_id: RegionId
  subMarketplaces?: Marketplace[]
}

export type MarketplaceId = Marketplace['id']

// Originally defined in GlobalFilter.tsx
export type MarketplaceColor = Array<{
  marketplaceName: Marketplace['marketplace_name']
  color: Marketplace['color']
}>

// Originally defined in GlobalFilter.tsx
export type MarketplaceData = {
  allMarketplaces: Marketplace[]
  allRegions: Region[]
  amazonMarketplaces: {
    amazonMarketplaceCodes: Array<Marketplace['country_code']>
    amazonMarketplaceIdsData: Array<Marketplace['id']>
  }
  filteredMarketplaces: Array<
    Marketplace | { marketplace_name: string; subMarketplaces: Marketplace[] }
  >
  marketPlacesColors: Array<MarketplaceColor>
  rawMarketplaces: MarketplaceId[]
  sdMarketplaces: MarketplaceId[]
  selectedMarketplaces?: MarketplaceId[]
  selectedRegions: RegionId[]
}

// Originally defined in GlobalFilter.tsx
export type MarketplacesFilterDisplayState = MarketplaceData & {
  amazonMarketplaces: {
    amazonMarketplaceCodes: Array<Marketplace['country_code']>
    amazonMarketplaceIdsData: Array<Marketplace['id']>
  }
  filteredMarketplaces: Array<
    Marketplace | { marketplace_name: string; subMarketplaces: Marketplace[] }
  >
  marketPlacesColors: Array<MarketplaceColor>
  rawMarketplaces: MarketplaceId[]
  selectedMarketplaces: MarketplaceId[]
  selectedRegions: RegionId[]
  sdMarketplaces: MarketplaceId[]
}

// Originally from contextTypes.ts
export type OnepCustomer = {
  id: number
  customer_name: string
}

export type Region = {
  id: number
  region_name: string
  is_selective_distribution: boolean
}

export type RegionId = Region['id']

// Originally defined in GlobalFilter.tsx
export type SelectedLinesOfBusinessProps = {
  value: string
  title: string
  id?: number
  sensitive?: boolean
}

// Originally defined in GlobalFilter.tsx
export type StateType = {
  timeframe: TimeframeFilterProps
  marketplaces: MarketplacesFilterDisplayState
  categories: CategoriesFilter
  currency: CurrencyFilter
  tags: Tags
  vat: VatProps
  churn: ChurnProps
  linesOfBusiness: LinesOfBusinessProps
}

export type SubMarketplace = Marketplace & {
  parent_id: number
}

// Originally from tags-types.ts
export type Tag = {
  id: number
  lower: string
  name: string
  created_at?: string
  created_by_username?: string
  ids_customers?: number[]
  ids_master_products?: number[]
  master_product_count?: number
  updated_at?: string
}

// Originally from tags-types.ts
export type Tags = {
  selectedTags: Tag[]
  tag_ids: number[]
  matchAllTags: boolean | undefined | null
  customerId: number
}

// Originally from tags-types.ts
export type TagsDropdownProps = {
  tagOptions: Tag[]
  selectedTags: Tag[]
  clickText: string
  callout: (selected: Tag[]) => void
}

// Originally from contextTypes.ts
export type Timeframe = {
  aggregation: 'day' | 'week' | 'month' | 'year' | string
  display: '1D' | '1W' | '30D' | 'month' | '3M' | '6M' | '1Y' | string
  timeValue: 'hours' | 'day' | 'week' | 'month' | 'year' | 'custom' | string
  type: 'current' | 'previous' | 'trailing' | 'historical' | 'quarterly'
  value?: 'custom' | number
  year?: string
  quarter?: number
}

// Originally defined in GlobalFilter.tsx
export type TimeframeFilterProps = {
  startDate: DateType
  endDate: DateType
  timeframe: Timeframe
  comparisonDateRanges: {
    firstRange_startDate: DateType
    firstRange_endDate: DateType
    secondRange_startDate?: DateType
    secondRange_endDate?: DateType
  }
  comparisonPeriodSelection: { value: string }
}

// Originally from contextTypes.ts
export type Timezone = {
  id: number
  name: string
  formatted_offset: string
  utc_offset: number
  abbreviation: string
  iana_name: string
}

// Originally from contextTypes.ts
export type User = {
  error: string | null
  id: number
  username: string
  settings: Record<string, unknown>
  email: string
  enabled: boolean
  registration_time: Date | null
  email_verified: boolean
  group_id: number
  job_title: string
  first_name: string
  last_name: string
  law_firm_id: number | null
  user_type_id: number
  all_brands: boolean
  type: string
  group_name: string
  customer: Brand
  customers: CustomerWithLOB[]
  current_currency: Currency
  current_timezone: Timezone
  brand_groups: BrandGroup[]
  brand_assignment_details: unknown[] | null
  onep_customers: OnepCustomer[]
  language: Language
  default_date_format: DateFormat
  auth_roles: string[]
  roles: string[]
  default_currency: Currency
  img?: string | null
  title?: string | null
  phone?: number | null
  password?: string | null
  region?: string | null
  user_type?: string | null
  group?: string | null
  boss?: string | null
}

// Originally defined in GlobalFilter.tsx
export type VatProps = {
  id?: number
  text?: 'Exclude' | 'Include'
  value: 'on' | 'off'
}

// Originally defined in sharedTypes.ts
export type GlobalFilterParamsType = {
  startDate: string
  endDate: string
  timeframe: Timeframe
  customer: Partial<Customer>
  marketplaceIds: Array<number>
  isVatAdjustmentTurnedOn: boolean
  areMarketplacesFiltered: boolean
  selectedComparisonPeriod: {
    value: string
  }
  amazonMarketplaceIds: Array<number>
}
