import React, { useContext, useEffect, useRef, useState } from 'react'
import { useQuery } from '@tanstack/react-query'
import {
  Button,
  getApiUrlPrefix,
  Icon,
  ListLoading,
  notEmpty,
  SearchBar,
  SideDrawer,
} from '@patterninc/react-ui'
import { haveRoles, SecureAxios } from '@predict-services'

import { ThemeContext } from '../../../Context'
import { useUser } from '../../../context/user-context'
import styles from './_global-search.module.scss'
import ContactsSearchList from './Lists/ContactsSearchList'
import MasterSellersSearch from './Lists/MasterSellersSearch'
import ProductSearchList from './Lists/ProductSearchList'
import SellersSearchList from './Lists/SellersSearchList'
import { useMerchants } from '../../hooks/useMerchantQueries'

const GlobalSearch = (): React.JSX.Element => {
  const [open, setOpen] = useState(false),
    [search, setSearch] = useState('')

  const openDrawer = () => {
    setOpen(true)
  }

  const closeDrawer = () => {
    setOpen(false)
    setSearch('')
  }

  return (
    <>
      <Button onClick={openDrawer}>
        <Icon icon='magnifier' iconSize='16px' />
      </Button>
      <SideDrawer
        isOpen={open}
        closeCallout={closeDrawer}
        headerContent='Search'
      >
        <SearchContent
          closeDrawer={closeDrawer}
          search={search}
          setSearch={setSearch}
          isOpen={open}
        />
      </SideDrawer>
    </>
  )
}

export default GlobalSearch

const SearchContent = ({
  closeDrawer,
  search,
  setSearch,
  isOpen,
}: {
  closeDrawer: () => void
  search: string
  setSearch: (searchTerm: string) => void
  isOpen: boolean
}) => {
  const { brandGroupCustomer, customer } = useContext(ThemeContext),
    //@ts-expect-error Context is still a .js file so typescript is throwing an error with this value that comes from Context.
    brandGroupCustomerIds: number[] = brandGroupCustomer?.customer_ids,
    { user } = useUser(),
    canAccessMasterSeller =
      customer.id === 0 &&
      user.all_brands &&
      haveRoles(user, ['read_master_sellers'])

  const params = {
      query: search?.trim(),
      customer_ids: brandGroupCustomerIds?.length
        ? brandGroupCustomerIds
        : customer.id !== 0
          ? [customer.id]
          : undefined,
      per_page: 20,
      page: 1,
      sort: 'is_active:desc',
    },
    apiRoot = `${getApiUrlPrefix('iserve')}/api/v3/search`

  //// Products ////
  const productsApi = `${apiRoot}/master_products`,
    { data: pData, status: productsStatus } = useQuery({
      queryKey: [params, productsApi],
      queryFn: ({ signal }) => {
        return SecureAxios.get(productsApi, {
          params,
          signal,
        })
      },
      enabled: notEmpty(search),
    }),
    productsData = productsStatus === 'success' ? pData?.data?.data : []
  ///////////////////////////////////

  //// Sellers ////
  const { data: sData, status: sellersStatus } = useMerchants({
      ...params,
      ...(canAccessMasterSeller
        ? {
            search,
            sort: 'is_active:desc,seller_name:asc',
          }
        : {}),
    }),
    sellersData = sellersStatus === 'success' ? sData?.data?.data : []
  ///////////////////////////////////

  //// Contacts ////
  const contactsApi = `${apiRoot}/contacts`,
    { data: cData, status: contactsStatus } = useQuery({
      queryKey: [params, contactsApi],
      queryFn: ({ signal }) => {
        return SecureAxios.get(contactsApi, {
          params,
          signal,
        })
      },
      enabled: notEmpty(search),
    }),
    contactsData = contactsStatus === 'success' ? cData?.data?.data : []
  ///////////////////////////////////

  const loading =
    productsStatus === 'pending' ||
    sellersStatus === 'pending' ||
    contactsStatus === 'pending'

  const searchBarRef = useRef<HTMLInputElement>(null),
    searchBarTimeout = useRef<ReturnType<typeof setTimeout>>(undefined)

  useEffect(() => {
    if (isOpen && searchBarRef.current) {
      searchBarTimeout.current = setTimeout(() => {
        searchBarRef.current?.focus()
      }, 250)
    }
    return () => {
      searchBarTimeout.current = undefined
    }
  }, [isOpen])

  return (
    <div className='flex flex-direction-column pat-gap-4'>
      <SearchBar
        value={search}
        onChange={setSearch}
        placeholder='Search Products, Sellers, & Contacts'
        debounce={250}
        ref={searchBarRef}
      />
      {notEmpty(search) ? (
        !loading ? (
          <div className='flex flex-direction-column pat-gap-2'>
            <GroupHeader text='Products' />
            <div className='pat-mb-2'>
              <ProductSearchList products={productsData} close={closeDrawer} />
            </div>
            <GroupHeader text='Sellers' />
            <div className='pat-mb-2'>
              {canAccessMasterSeller ? (
                <MasterSellersSearch
                  sellers={sellersData}
                  close={closeDrawer}
                />
              ) : (
                <SellersSearchList sellers={sellersData} close={closeDrawer} />
              )}
            </div>
            <GroupHeader text='Contacts' />
            <ContactsSearchList contacts={contactsData} close={closeDrawer} />
          </div>
        ) : (
          <ListLoading longList />
        )
      ) : null}
    </div>
  )
}

const GroupHeader = ({ text }: { text: string }) => {
  return <div className={styles.groupHeader}>{text}</div>
}
