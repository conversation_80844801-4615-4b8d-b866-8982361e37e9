import React, { useContext } from 'react'
import { Link } from 'react-router-dom'
import { EmptyState, Tag, Tooltip } from '@patterninc/react-ui'
import { c, sellersCoreRoute } from '@predict-services'

import { ThemeContext } from '../../../../Context'

type ContactsSearchListProps = {
  contacts: Array<{
    contact_sellers: Array<{
      id: number
      seller_name: string
    }>
    customer_id: number
    id: number
    is_active: boolean
    name: string
  }>
  close: () => void
}

const ContactsSearchList = ({
  contacts,
  close,
}: ContactsSearchListProps): React.JSX.Element => {
  const { customer } = useContext(ThemeContext)

  return contacts.length > 0 ? (
    <>
      {contacts.map((contact, i) => (
        <Link
          to={{
            pathname:
              customer.id === 0
                ? `/b/${contact.customer_id}/sellers/${contact.contact_sellers[0].id}/contacts/${contact.id}`
                : `${sellersCoreRoute}/${contact.contact_sellers[0].id}/contacts/${contact.id}`,
          }}
          state={{
            fromGlobalSearch: true,
          }}
          key={contact.name}
          onClick={close}
        >
          <div
            className={`flex justify-content-between align-items-center pat-gap-4 fs-12 pat-p-2 ${
              i !== contacts.length - 1 ? 'bdrb bdrc-light-gray' : ''
            }`}
          >
            <div className='flex align-items-center pat-gap-2'>
              <img src='/images/contact_profile.svg' alt={contact.name} />
              <div className='flex flex-direction-column pat-gap-1'>
                <span className='fw-semi-bold'>{contact.name}</span>
                {contact.contact_sellers.length > 1 ? (
                  <Tooltip
                    tooltipContent={
                      <div>
                        {contact.contact_sellers.map((cs) => (
                          <span key={cs.seller_name}>{cs.seller_name}</span>
                        ))}
                      </div>
                    }
                    position='left'
                  >
                    {c('sellerAmount', {
                      count: contact.contact_sellers.length,
                    })}
                  </Tooltip>
                ) : (
                  <span>{contact.contact_sellers[0].seller_name}</span>
                )}
              </div>
            </div>
            <Tag color={contact.is_active ? 'green' : 'gray'}>
              {contact.is_active ? 'Active' : 'Inactive'}
            </Tag>
          </div>
        </Link>
      ))}
    </>
  ) : (
    <EmptyState primaryText='No Contacts Matching this Search' />
  )
}

export default ContactsSearchList
