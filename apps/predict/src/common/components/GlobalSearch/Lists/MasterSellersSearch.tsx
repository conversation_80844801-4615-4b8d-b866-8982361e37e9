import React from 'react'
import { <PERSON> } from 'react-router-dom'
import { EmptyState, MdashCheck, Tag, TrimText } from '@patterninc/react-ui'
import { sellersCoreRoute } from '@predict-services'

import SellersSearchList, { type SellersType } from './SellersSearchList'

type MasterSellersSearchProps = {
  sellers: Array<{
    seller_details: SellersType
    id: number
    is_active: boolean
    seller_name: string
    marketplace_name: string
    merchant_id: string
  }>
  close: () => void
}

const MasterSellersSearch = ({
  sellers,
  close,
}: MasterSellersSearchProps): React.JSX.Element => {
  return sellers.length > 0 ? (
    <>
      {sellers.map((e) => (
        <div
          key={`master-seller-search-${e.id}`}
          className='bdrb bdrc-light-gray'
        >
          <Link
            to={`${sellersCoreRoute}/master-sellers/${e.id}`}
            onClick={close}
          >
            <div className='flex justify-content-between pat-gap-4 align-items-center pat-gap-4 fs-12 pat-p-2 bdrb bdrc-light-gray'>
              <div className='flex align-items-center pat-gap-2'>
                <img src='/images/seller_profile.svg' alt={e.seller_name} />
                <div className='flex flex-direction-column pat-gap-1'>
                  <span className='fw-semi-bold'>
                    <TrimText text={e.seller_name || ''} limit={20} />
                  </span>
                  <div>
                    <span>{e.marketplace_name}</span>
                  </div>
                  <span className='flex pat-gap-1 fc-purple fs-10'>
                    <span>ID:</span>
                    <MdashCheck check={!!e.merchant_id}>
                      {e.merchant_id}
                    </MdashCheck>
                  </span>
                </div>
              </div>
              <Tag color={e.is_active ? 'green' : 'gray'}>
                {e.is_active ? 'Active' : 'Inactive'}
              </Tag>
            </div>
          </Link>
          <SellersSearchList
            close={close}
            sellers={e.seller_details}
            hasMasterSeller
          />
        </div>
      ))}
    </>
  ) : (
    <EmptyState primaryText='No Master Sellers Matching this Search' />
  )
}

export default MasterSellersSearch
