import React, { useContext } from 'react'
import { NumericFormat } from 'react-number-format'
import { Link } from 'react-router-dom'
import {
  EmptyState,
  LabelAndData,
  PrimaryTableCell,
} from '@patterninc/react-ui'
import { productListingRoute } from '@predict-services'

import { ThemeContext } from '../../../../Context'

type ProductSearchListProps = {
  products: Array<{
    customer_id: number
    id: number
    img_url: string
    is_active: boolean
    map_price: string
    name: string
    primary_tag: string
  }>
  close: () => void
}

const ProductSearchList = ({
  products,
  close,
}: ProductSearchListProps): React.JSX.Element => {
  const { customer } = useContext(ThemeContext)

  return products.length > 0 ? (
    <>
      {products.map((product, i) => (
        <Link
          to={{
            pathname:
              customer.id === 0
                ? `/b/${product.customer_id}/products/${product.id}/overview`
                : `${productListingRoute}/${product.id}/overview`,
          }}
          state={{ fromGlobalSearch: true }}
          key={product.name}
          onClick={close}
        >
          <div
            className={`flex justify-content-between pat-gap-4 pat-pr-2 ${
              i !== products.length - 1 ? 'bdrb bdrc-light-gray' : ''
            }`}
          >
            <PrimaryTableCell
              title={product.name}
              titleProp='name'
              sortBy={{ prop: product.name }}
              uniqId={{
                id: product.primary_tag,
                idLabel: 'ID',
                idName: 'id',
              }}
              imageProps={{
                url: product.img_url,
                alt: product.name,
              }}
              tags={[
                {
                  children: product.is_active ? 'Active' : 'Inactive',
                  color: product.is_active ? 'green' : 'gray',
                },
              ]}
            />
            <LabelAndData
              labelClass='pat-pt-2'
              label='Price'
              data={
                <NumericFormat
                  value={product.map_price}
                  thousandSeparator={true}
                  fixedDecimalScale={true}
                  decimalScale={2}
                  displayType='text'
                />
              }
              check={!!product.map_price}
            />
          </div>
        </Link>
      ))}
    </>
  ) : (
    <EmptyState primaryText='No Products Matching this Search' />
  )
}

export default ProductSearchList
