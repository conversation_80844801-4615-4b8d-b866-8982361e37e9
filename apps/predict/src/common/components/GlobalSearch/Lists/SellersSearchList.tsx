import React, { useContext } from 'react'
import { Link } from 'react-router-dom'
import {
  EmptyState,
  Icon,
  MdashCheck,
  Tag,
  TrimText,
} from '@patterninc/react-ui'
import { sellersCoreRoute } from '@predict-services'

import { ThemeContext } from '../../../../Context'

export type SellersType = Array<{
  customer_id: number
  customer_name: string
  id: number
  is_active: boolean
  seller_name: string
  text_merchant_id: string
  marketplace_name: string
  merchant_id: string
}>

type SellersSearchListProps = {
  sellers: SellersType
  close: () => void
  hasMasterSeller?: boolean
}

const SellersSearchList = ({
  sellers,
  close,
  hasMasterSeller,
}: SellersSearchListProps): React.JSX.Element => {
  const { customer } = useContext(ThemeContext)

  return sellers?.length > 0 ? (
    <>
      {sellers.map((seller, i) => (
        <Link
          to={{
            pathname:
              customer.id === 0
                ? `/b/${seller.customer_id}/sellers/${seller.id}/products`
                : `${sellersCoreRoute}/${seller.id}/products`,
          }}
          state={{
            fromGlobalSearch: true,
          }}
          key={seller.seller_name}
          onClick={close}
        >
          <div
            className={`flex justify-content-between pat-gap-4 align-items-center pat-gap-4 fs-12 pat-p-2 ${
              i !== sellers.length - 1 ? 'bdrb bdrc-light-gray' : ''
            }`}
          >
            <div className='flex pat-gap-2 align-items-center'>
              <div className='flex align-items-center pat-gap-2'>
                {hasMasterSeller ? (
                  <Icon icon='l' className='pat-ml-2' iconSize='8px' />
                ) : null}
                <img
                  src='/images/seller_profile.svg'
                  alt={seller.seller_name}
                />
              </div>
              <div className='flex flex-direction-column pat-gap-1'>
                <span className='fw-semi-bold'>
                  <TrimText text={seller.seller_name || ''} limit={20} />
                </span>
                <div>
                  <span>{seller.marketplace_name}</span>
                </div>
                <span className='flex pat-gap-1 fc-purple fs-10'>
                  {hasMasterSeller ? (
                    <>
                      <span>Customer:</span>
                      <MdashCheck check={!!seller.customer_name}>
                        {seller.customer_name}
                      </MdashCheck>
                    </>
                  ) : (
                    <>
                      <span>ID:</span>
                      <MdashCheck check={!!seller.text_merchant_id}>
                        {seller.text_merchant_id}
                      </MdashCheck>
                    </>
                  )}
                </span>
              </div>
            </div>
            <Tag color={seller.is_active ? 'green' : 'gray'}>
              {seller.is_active ? 'Active' : 'Inactive'}
            </Tag>
          </div>
        </Link>
      ))}
    </>
  ) : (
    <EmptyState primaryText='No Sellers Matching this Search' />
  )
}

export default SellersSearchList
