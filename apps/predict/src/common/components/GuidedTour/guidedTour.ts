import { useEffect, useState } from 'react'
import { useQuery } from '@tanstack/react-query'
import { getUserSettingEndpoint, SecureAxios } from '@predict-services'

import { GuidedTourAPI } from '../Tables/GuidedTourHelper'

export const useGuidedTourStatus = () => {
  const [showTour, setShowTour] = useState(false)

  // Start: Guided Tour API for User Settings status
  const guidedTourKey = 'main-page:guided-tour:loyalty-release-live',
    { endpoint: guidedTourAPIUserSettingUrl } =
      getUserSettingEndpoint(guidedTourKey)
  const { status: guidedTourAPIStatus, data: guidedTourAPIResponse } = useQuery(
    {
      queryKey: [guidedTourAPIUserSettingUrl],
      queryFn: ({ signal }) =>
        SecureAxios.get(guidedTourAPIUserSettingUrl, { signal }),
    },
  )
  const guidedTourData =
    guidedTourAPIStatus === 'success' && guidedTourAPIResponse?.data
  useEffect(() => {
    if (guidedTourAPIStatus === 'success') {
      if (guidedTourData?.viewed) {
        setShowTour(false)
      } else {
        setShowTour(true)
      }
    }
  }, [guidedTourAPIStatus, guidedTourData?.viewed])

  const onCloseGuidedTour = () => {
    setShowTour(false)
    GuidedTourAPI({
      api: guidedTourAPIUserSettingUrl,
    })
  }
  // End: Guided Tour API for User Settings status
  return [showTour, onCloseGuidedTour]
}
