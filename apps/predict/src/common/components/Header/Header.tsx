import React, { useContext } from 'react'
import { useNavigate } from 'react-router-dom'
import {
  Breadcrumbs,
  type BreadcrumbType,
  envColorMap,
  envName,
  Tag,
  type TagColorList,
  useIsMobileView,
  useMediaQuery,
} from '@patterninc/react-ui'

import { getCurrentEnv } from '../../../App'
import { ThemeContext } from '../../../Context'
import BrandSelectionDropDown from '../BrandSelectionDropDown/BrandSelectionDropDown'
import { useShowBrandSelector } from '../BrandSelectionDropDown/brandSelector.helper'
import NewGlobalFilter from '../GlobalFilter/GlobalFilter'
import GlobalSearch from '../GlobalSearch/GlobalSearch'
import Notifications from '../Notifications/Notifications'

type HeaderProps = {
  breadcrumbs: Array<BreadcrumbType>
  refetchCustomer: boolean
  updateRefetchCustomer: (flag: boolean) => void
}

const Header: React.FC<HeaderProps> = ({
  breadcrumbs,
  refetchCustomer,
  updateRefetchCustomer,
}) => {
  const navigate = useNavigate(),
    { breadcrumbCallout } = useContext(ThemeContext),
    showBrandSelector = useShowBrandSelector(),
    screenIsMdLarge = useMediaQuery({ type: 'min', breakpoint: 'md' }),
    screenIsMobile = useIsMobileView(),
    environment = getCurrentEnv()

  return (
    <div className='header-container'>
      <div className='header' id='header'>
        <div className='header-left-section'>
          <Breadcrumbs
            breadcrumbs={breadcrumbs}
            textUnderlineCharacterLimit={55}
            callout={(breadcrumb) => {
              navigate(breadcrumb.link)
              breadcrumbCallout(breadcrumb)
            }}
          />
          {screenIsMobile && environment !== 'production' ? (
            <Tag color={envColorMap[environment] as TagColorList}>
              {envName[environment]}
            </Tag>
          ) : null}
        </div>

        {/** only show these filters/notification in desktop (more than md screen) as in mobile Ui it is handled in sidebar header */}
        {screenIsMdLarge && (
          <div className='header-right-section global-header'>
            <NewGlobalFilter />

            {showBrandSelector ? (
              <BrandSelectionDropDown
                refetchCustomer={refetchCustomer}
                updateRefetchCustomer={updateRefetchCustomer}
              />
            ) : (
              <div className='flex justify-content-between'>
                {/* Needed for legacy styling */}
              </div>
            )}

            <Notifications />
            <GlobalSearch />
          </div>
        )}
      </div>
    </div>
  )
}

export default Header
