@use '../../scss/base/mixins' as *;
@use '@patterninc/react-ui/dist/variables' as variables;

.header-container {
  @include prefix(position, sticky);
  @media only screen and (max-width: variables.$breakpoint-sm-max) {
    @include box-shadow(0, 3px, 6px);
  }
  & {
    position: -webkit-sticky;
    top: 0;
    background: var(--white);
    z-index: 99;
  }
  .header {
    .header-left-section {
      display: flex;
      justify-content: space-between;
      align-items: center;
      gap: 16px;
      width: 100%;
    }
    .header-right-section {
      display: grid;
      gap: 16px;
      grid-template-columns: 1fr auto auto;
      &.currency-header {
        grid-template-columns: 40px 1fr 20px auto;
      }
      &.global-header {
        grid-template-columns: repeat(4, auto);
      }
      & {
        align-items: center;
        position: relative;
      }

      @media only screen and (max-width: variables.$breakpoint-sm-max) {
        grid-template-columns: 1fr 20px;
      }
    }
    .company {
      &.read-only {
        pointer-events: none;
        .carat {
          display: none;
        }
      }
      @media only screen and (max-width: variables.$breakpoint-sm-max) {
        display: none;
      }
    }
    .right-side-options {
      align-items: center;
      .popover {
        margin-right: 16px;
      }
    }
    .notifications {
      @media only screen and (max-width: variables.$breakpoint-sm-max) {
        display: none;
      }
    }
  }
}
