import { useEffect, useRef, useState } from 'react'

export default function useElementHeight(initHeight: number) {
  const eleRef = useRef<HTMLDivElement | null>(null)
  const [eleHeight, setEleHeight] = useState(initHeight)

  useEffect(() => {
    if (eleRef.current) {
      const height = (eleRef.current || {}).clientHeight
      setEleHeight(height)
    }
  }, [])

  return { eleHeight, eleRef }
}
