import React, { useContext, useEffect, useMemo, useRef } from 'react'
import html2canvas from 'html2canvas'
import { type IconProps, type MenuActionProps } from '@patterninc/react-ui'
import {
  c,
  type DownloadOptionsType,
  tryLocalStorageParse,
} from '@predict-services'
import { ThemeContext } from 'src/Context'
import type { Marketplace, Timeframe } from '@predict-types'
import { useLocation } from 'react-router-dom'

import ButtonWithOptions from '../Button/ButtonWithOptions'
import { useVatFilterToggleEnabled } from '../GlobalFilter/FilterSummary'

export type ImageHelperProps = {
  setIsSquare?: React.Dispatch<React.SetStateAction<boolean>>
}

export type LocalTimeframe = Pick<Timeframe, 'type' | 'aggregation' | 'display'>

export type ImageMetaData = {
  brandName?: string
  categories?: string
  marketplaces?: string
  tags?: string
  timeframe?: LocalTimeframe
}

export type PngImageProps = {
  children: React.ReactElement

  /**
   * @description Wraps any component to enable image capture functionality.
   * Optional timeframe: To overlay timeframe information, add an "id" to the target element and pass it via the replaceId prop.
   * Optional image size: The image can be either Square or Landscape, with Landscape as the default.
   * Optional file name: a fileName prop can be passed in to set the name of the file to download.
   * @usage
   * ```jsx
   * <PngImage pngProps={{ isImageCapture, setIsImageCapture }}>
   *   <YourComponent />
   * </PngImage>
   * ```
   * @required isImageCapture, setIsImageCapture (useState hook values to trigger and reset image capture)
   */
  pngProps: {
    /**
     * @description Determines if the chart image is being captured for download --
     * when true, the chart will immediately be captured
     */
    isImageCapture: boolean
    /**
     * @description method to reset the isImageCapture state to false
     */
    setIsImageCapture: React.Dispatch<React.SetStateAction<boolean>>
    /**
     * @description Determines if the chart is square or landscape
     * @default undefined - landscape image width of 1200px
     */
    isSquare?: boolean
    /**
     * @description (optional) customSquareImageWidth is used to set the width of the square image
     * @default undefined - 600px
     */
    customSquareImageWidth?: string
    /**
     * @description The id of the element to overlay with the timeframe information
     * @default undefined - no timeframe overlay will be displayed
     */
    replaceId?: string
    /**
     * @description (optional) name of the file to download;
     * defaults to {customer_name}_{number of marketplaces}_{timeframe}.png
     * @default undefined - {customer_name}_{number of marketplaces}_{timeframe}_{square or landscape}.png
     */
    fileName?: string
    /**
     * @description (optional) ImageMetaData is used to override the global filters data;
     * this may be done in full or partially. The default (if no value(s) is/are provided)
     * will be pulled from the global filters.
     */
    ImageMetaData?: ImageMetaData
    /**
     * @description (optional) isTwoMetricHeader is used if the header is a two metric header;
     * when this is true, the timeframe will be displayed in the footer with the other filter metadata
     */
    isTwoMetricHeader?: boolean
    /**
     * @description (optional) overlayProps is used to pass in additional styles for the overlay
     * @default undefined - no additional styles will be applied
     * @usage
     * ```jsx
     * {background: 'var(--white)'}
     * ```
     */
    overlayProps?: { [key: string]: string }
    /**
     * @description (optional) Use to display the product name in the footer
     */
    productName?: string
  }
}

export type Category = {
  checked?: boolean | undefined
  id?: number | undefined
  path_name: string
  children: Category[]
}

const PngImage = ({ children, pngProps }: PngImageProps) => {
  // validate the two required props are passed into the component
  if (!Object.prototype.hasOwnProperty.call(pngProps, 'isImageCapture')) {
    throw new Error('isImageCapture prop is required to trigger image capture')
  }
  if (!Object.prototype.hasOwnProperty.call(pngProps, 'setIsImageCapture')) {
    throw new Error(
      'setIsImageCapture method is required to reset the isImageCapture state',
    )
  }

  const overrideImageMetaData = pngProps?.ImageMetaData || {},
    {
      brandName: overrideBrandName,
      categories: overrideCategories,
      marketplaces: overrideMarketplaces,
      tags: overrideTags,
      timeframe: overrideTimeframe,
    } = overrideImageMetaData

  const { pathname } = useLocation()
  const isVatFilterToggleEnabled = useVatFilterToggleEnabled(pathname)
  const defaultFileName = useGetDefaultFileName(pngProps?.isSquare)
  const brandName = useGetBrandName(overrideBrandName)
  const marketplaces = useGetMarketplaces(overrideMarketplaces)
  const categories = getCategories(overrideCategories)
  const tags = getTags(overrideTags)

  // TODO: Use VAT filter supported routes defined in the global filter to display vat tag in the image.
  // Ensure the display reflects the VAT filter based on the routes configured in the global filter.
  const vat = getVat(isVatFilterToggleEnabled)

  const timeframe = useGetTimeframe(overrideTimeframe).toUpperCase()
  const captureRef = useRef<HTMLDivElement>(null)
  const containerStyles = useMemo(
    () =>
      pngProps.isImageCapture
        ? {
            width: pngProps.isSquare
              ? (pngProps.customSquareImageWidth ?? '600px')
              : '100%',
            fontFamily: '"Wix Madefor Display", sans-serif',
          }
        : {},
    [
      pngProps.isImageCapture,
      pngProps.isSquare,
      pngProps.customSquareImageWidth,
    ],
  )
  const shouldUseBlankOverlay = !!(
    pngProps.isTwoMetricHeader && pngProps.isSquare
  )

  useEffect(() => {
    if (captureRef.current && pngProps.isImageCapture) {
      const overlayElement = captureRef.current?.querySelector(
        `#${pngProps.replaceId}`,
      ) as HTMLDivElement

      // setTimeout is to allow the chart animation to run and adjust to the resized image frame
      setTimeout(
        () => {
          // add image only elements to the DOM
          const container = overlayTimeframe(
            timeframe,
            shouldUseBlankOverlay,
            pngProps?.overlayProps ?? {},
            overlayElement,
          )

          // convert the chart to a png then download
          html2canvas(captureRef.current as HTMLElement).then((canvas) => {
            const link = document.createElement('a')
            link.download = `${pngProps.fileName || defaultFileName}.png`
            link.href = canvas.toDataURL()
            link.click()

            // clean-up DOM from image only element updates
            pngProps.setIsImageCapture(false)
            if (overlayElement && container) {
              if (overlayElement.hasAttribute('data-png-modified')) {
                // The overlayElement has been modified and needs to be reset
                overlayElement.style.removeProperty('max-width')
                overlayElement.style.removeProperty('overflow')
                overlayElement.removeAttribute('data-png-modified')
              }
              if (overlayElement.hasAttribute('data-hide-container')) {
                // The overlayElement has been hidden and needs to be reset
                overlayElement.style.removeProperty('display')
                overlayElement.removeAttribute('data-hide-container')
              }
              removeOverlay(overlayElement, container)
            }
          })
        },
        250, // 250ms is to allow the user time to move the mouse away from the button before the image capture
      )
    }
  }, [defaultFileName, pngProps, shouldUseBlankOverlay, timeframe])

  return (
    <div id='png_image_wrapper' ref={captureRef} style={containerStyles}>
      {React.cloneElement(children)}
      {pngProps.isImageCapture && (
        <>
          {/* NOTE: Only display the product name in this footer if the name has been provided (on product detail pages only)) */}
          {pngProps.productName && (
            <div className='pat-px-2 pat-pb-2 fc-purple fs-12'>{`* ${c('productName')}: ${pngProps.productName}`}</div>
          )}
          {/* NOTE: Only display the timeframe in this footer if the chart is a two metric header and is square (not enough space for the timeframe in the header) */}
          {shouldUseBlankOverlay && (
            <div className='pat-px-2 pat-pb-2 fc-purple fs-12'>{`* ${c('timeframe')}: ${timeframe}`}</div>
          )}
          <div className='pat-px-2 pat-pb-2 fc-purple fs-12'>{`* ${c('brand')}: ${brandName}`}</div>
          <div className='pat-px-2 pat-pb-2 fc-purple fs-12'>{`* ${c('marketplaces')}: ${marketplaces}`}</div>
          {categories && (
            <div className='pat-px-2 pat-pb-2 fc-purple fs-12'>{`* ${c('categories')}: ${categories}`}</div>
          )}
          {tags && (
            <div className='pat-px-2 pat-pb-2 fc-purple fs-12'>{`* ${c('Tags')}: ${tags}`}</div>
          )}
          {vat && (
            <div className='pat-px-2 pat-pb-2 fc-purple fs-12'>{`* ${c('vat')} ${vat}`}</div>
          )}
        </>
      )}
    </div>
  )
}

export default PngImage

///////////////////////////////////////////////////////////////////////////////
// Helper functions
///////////////////////////////////////////////////////////////////////////////

const useGetDefaultFileName = (isSquare?: boolean): string => {
  const { marketplaceIds, customer } = useContext(ThemeContext)
  const timeframe = useGetTimeframe()

  return `${customer.customer_name}_${marketplaceIds.length} ${c('marketplaces')}_${timeframe}_${isSquare ? 'square' : 'landscape'}`
}

const useGetMarketplaces = (defaultMarketplaces?: string): string => {
  const allMarketplaces = tryLocalStorageParse('marketplaces_all')
  const { marketplaceIds } = useContext(ThemeContext)

  const marketplaces = marketplaceIds
    .map((marketplaceId: number) => {
      const marketplace = allMarketplaces?.find(
        (marketplace: Marketplace) => marketplace.id === marketplaceId,
      )
      return marketplace?.marketplace_name
    })
    .filter(Boolean) // Filter out undefined or null values

  return defaultMarketplaces ?? marketplaces.join(', ')
}

const useGetBrandName = (defaultBrandName?: string): string => {
  const { brandGroupCustomer, customer } = useContext(ThemeContext)
  const brandGroup =
    brandGroupCustomer !== null
      ? (brandGroupCustomer as { customer_name: string })
      : null
  const globalCustomer = brandGroup?.customer_name ?? customer?.customer_name
  return defaultBrandName ?? globalCustomer
}

const getCategories = (defaultCategories?: string): string | null => {
  if (defaultCategories) return defaultCategories
  const allCategories = tryLocalStorageParse('custom_categories') as Category[]
  const selectedCategories: string[] = []

  if (allCategories) {
    if (allCategories?.[0]?.id === -1 && allCategories?.[0]?.checked)
      return null

    const getSelectedCategory = (category: Category) => {
      if (category.children.length > 0) {
        category.children.forEach(getSelectedCategory)
      } else if (category.checked) {
        selectedCategories.push(category.path_name)
      }
    }
    allCategories?.forEach(getSelectedCategory)
  }

  return selectedCategories.join(', ')
}

const getTags = (defaultTags?: string): string | null => {
  if (defaultTags) return defaultTags
  const { selectedTags, matchAllTags } = tryLocalStorageParse(
    'tags_global',
  ) ?? {
    selectedTags: [],
    matchAllTags: false,
  }
  if (matchAllTags) return null
  return selectedTags.map((tag: { name: string }) => tag.name).join(', ')
}

const getVat = (isVatToggleEnabled: boolean): string | null => {
  if (!isVatToggleEnabled) return null
  const vatEnabled = Boolean(tryLocalStorageParse('is_vat_adjustment_enabled'))
  return vatEnabled ? c('excluded') : c('included')
}

const useGetTimeframe = (defaultTimeframe?: LocalTimeframe): string => {
  const { timeframe: globalTimeframe } = useContext(ThemeContext)
  const timeframe = defaultTimeframe ?? globalTimeframe
  return timeframe.aggregation
    ? `${timeframe.type === 'previous' ? `${c('previous')} ` : ''}${
        timeframe.display
      } - ${c('by')} ${timeframe?.aggregation}`
    : `${timeframe.type === 'previous' ? `${c('previous')} ` : ''}${timeframe.display}`
}

const overlayTimeframe = (
  timeframeString: string,
  isBlankOverlay: boolean,
  overlayProps: { [key: string]: string },
  overLayElement?: HTMLDivElement,
) => {
  if (!overLayElement) {
    return
  }
  // This overlay is designed to cover the existing settings and download buttons with the timeframe information
  const maxWidth = 300
  const containerWidth = overLayElement.getBoundingClientRect().width
  const containerHeight = overLayElement.getBoundingClientRect().height
  if (containerWidth > 350) {
    // The underlying container is too large for the square image capture so we're modifying the width
    // for the overlay to fit the image capture size and avoid most of the text overflow.
    overLayElement.style.maxWidth = `${maxWidth}px`
    overLayElement.style.overflow = 'hidden'
    overLayElement.setAttribute('data-png-modified', 'true')
  }

  // Create the main overlay container div element
  const container = document.createElement('div')

  // Create a content container for the calendar SVG
  const svgContainer = document.createElement('div')
  svgContainer.style.width = '20px'
  svgContainer.style.height = '20px'
  svgContainer.className = 'calendar_svg__st0 pat-pr-2'
  svgContainer.innerHTML = calendarSvg // Set the SVG content

  // Create the text node for the timeframe string
  const text = document.createTextNode(timeframeString)

  if (isBlankOverlay) {
    overLayElement.style.display = 'none'
    overLayElement.setAttribute('data-hide-container', 'true')
  } else {
    // place the SVG and text in the overlay container div
    container.appendChild(svgContainer)
    container.appendChild(text)
  }

  // Set the position and size of the overlay container div
  container.style.position = 'absolute'
  container.style.top = '0'
  container.style.left = `${isBlankOverlay || containerWidth - maxWidth > 0 ? '0' : containerWidth - maxWidth}px`
  container.style.width = `${isBlankOverlay ? containerWidth : Math.max(maxWidth, containerWidth)}px`
  container.style.maxWidth = `${maxWidth}px`
  container.style.height = `${Math.max(20, containerHeight)}px`

  // place overlay container over existing download buttons
  container.style.zIndex = '999'
  container.style.background = overlayProps?.background
    ? overlayProps.background
    : 'var(--faint-gray)'

  // display styles for content of container
  container.className = 'flex gap-2 justify-end'
  container.style.alignItems = 'center'
  container.style.paddingRight = '8px'

  // Append the overlay container to the anchor element (download button container)
  overLayElement.style.position = 'relative'
  overLayElement?.appendChild(container)

  // Return the main container so it can be removed from the DOM later
  return container
}

const removeOverlay = (
  captureRef: HTMLDivElement,
  container: HTMLDivElement,
) => {
  captureRef?.removeChild(container)
}

const calendarSvg = `
  <svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 96 96'>
    <path d='M90 96H6c-3.3 0-6-2.7-6-6V18c0-3.3 2.7-6 6-6h20V2c0-1.1.9-2 2-2s2 .9 2 2v10h36V2c0-1.1.9-2 2-2s2 .9 2 2v10h20c3.3 0 6 2.7 6 6v72c0 3.3-2.7 6-6 6m2-78c0-1.1-.9-2-2-2H70v6c0 1.1-.9 2-2 2s-2-.9-2-2v-6H30v6c0 1.1-.9 2-2 2s-2-.9-2-2v-6H6c-1.1 0-2 .9-2 2v72c0 1.1.9 2 2 2h84c1.1 0 2-.9 2-2zm0 17H4v4h88z' />
  </svg>`

export type CsvDownloadHelperOptions = {
  pngProps: {
    enabledSquareImg?: boolean
    enabledLandscapeImg?: boolean
    setIsImageCapture: React.Dispatch<React.SetStateAction<boolean>>
    setIsSquare?: React.Dispatch<React.SetStateAction<boolean>>
    DOWNLOAD_BUTTON_ID: string
  }
  anythingToDownload: boolean
  csvDownloadOptions: MenuActionProps[] | DownloadOptionsType
}

export const CsvButtonHelper = ({
  pngProps,
  anythingToDownload,
  csvDownloadOptions,
}: CsvDownloadHelperOptions) => {
  const [isLoading, setIsLoading] = React.useState(false)
  const {
      setIsImageCapture,
      setIsSquare,
      DOWNLOAD_BUTTON_ID,
      enabledSquareImg = true,
      enabledLandscapeImg = true,
    } = pngProps,
    hasCsvDownloadOptions =
      Array.isArray(csvDownloadOptions) && csvDownloadOptions.length > 0

  return (
    <ButtonWithOptions
      id={DOWNLOAD_BUTTON_ID}
      tippyPlacement='bottom'
      loadingStatus={isLoading}
      options={[
        // Section header
        ...(anythingToDownload && hasCsvDownloadOptions
          ? [
              {
                text: c('downloadAs'),
                callout: () => null,
              },
            ]
          : []),
        // Conditionally include CSV download options
        ...(anythingToDownload && hasCsvDownloadOptions
          ? csvDownloadOptions.map(
              (csvOption) =>
                ({
                  csv: csvOption,
                  handleCsvAction: (value: boolean) => {
                    setIsLoading(value)
                  },
                }) as MenuActionProps,
            )
          : []),

        // Section divider && header
        {
          hasDivider: anythingToDownload,
          text: c('downloadChartAs'),
          callout: () => null,
        },

        // The new image capture options
        ...(enabledSquareImg
          ? [
              {
                text: c('squareImage'),
                icon: 'frame' as IconProps['icon'],
                callout: () => {
                  setIsSquare?.(true)
                  setIsImageCapture(true)
                },
              },
            ]
          : []),
        ...(enabledLandscapeImg
          ? [
              {
                text: c('landscapeImage'),
                icon: 'fullScreen' as IconProps['icon'],
                callout: () => {
                  setIsSquare?.(false)
                  setIsImageCapture(true)
                },
              },
            ]
          : []),
      ]}
    />
  )
}
