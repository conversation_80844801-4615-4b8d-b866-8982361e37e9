import { type ReactNode } from 'react'
import type { BreadcrumbType as LibraryBreadcrumbType } from '@patterninc/react-ui'
import type moment from 'moment'

import type {
  BrandGroup,
  CheckboxCategory,
  Currency,
  Customer,
  LinesOfBusinessProps,
  LOBResponseProps,
  Marketplace,
  MarketplaceColor,
  MarketplaceId,
  Region,
  RegionId,
  Tags,
  Timeframe,
  User,
} from '../components/GlobalFilter/globalFilterTypes'

// Common Types
export type DateType = moment.Moment | string | null | undefined
export type ComparisonPeriodType = {
  value: string
}
export type BreadcrumbChangeType = 'rootLevel' | 'tab'
export type ComparisonDateRangesType = {
  firstRange_startDate?: DateType
  firstRange_endDate?: DateType
  secondRange_startDate?: DateType
  secondRange_endDate?: DateType
}
export type FilteredLOBType = string[] | number[]

// Props for the ThemeProvider component
export interface ThemeProviderProps {
  children: ReactNode
  setUser?: (user: User) => void
}

// Main Context State interface
export interface ThemeContextState {
  page: string
  isVatAdjustmentTurnedOn: boolean
  isChurnTurnedOn: boolean
  show1pVendorAnalyticsTab: boolean
  isLimitedToAssignmentsEnabled: boolean
  tags: Tags
  linesOfBusiness: LinesOfBusinessProps
  LOBOptions: LOBResponseProps[]
  areLOBsFiltered: boolean
  filteredLOB: FilteredLOBType
  prevCustomer: Customer
  customer: Customer
  customCategories: CheckboxCategory[]
  fullCategoryList: CheckboxCategory[]
  startDate: DateType
  endDate: DateType
  timeframe: Timeframe & { compareDisplay?: string }
  firstRange_startDate: DateType
  firstRange_endDate: DateType
  secondRange_startDate: DateType
  secondRange_endDate: DateType
  selectedComparisonPeriod: ComparisonPeriodType
  marketplaceIds: MarketplaceId[]
  sdMarketplaceIds: MarketplaceId[]
  amazonMarketplaceCountryCodes: string[]
  amazonMarketplaceIds: MarketplaceId[]
  regions: RegionId[]
  marketPlacesColors: MarketplaceColor[]
  currencyList: Currency[]
  selectedCurrency: Currency
  prevDefaultCurrency: Currency | null
  brandGroupCustomer: BrandGroup | null
  filteredMarketplaces: Marketplace[]
  allMarketplaceIds: MarketplaceId[]
  allMarketplaces: Marketplace[]
  allRegions: Region[]
  hideGlobalBrandSelector: boolean
  areAllSelectedMarketplacesChinese: boolean
  areMarketplacesFiltered: boolean
  breadcrumbs: BreadcrumbType[]
  redirectUpByLevel?: number
  redirectToRoot?: boolean
  id?: number
}

// Context Methods interface
export interface ThemeContextMethods {
  changeProduct: () => void
  finishTask: () => void
  navigateFromBreadcrumbs: (index: number, link: string) => void
  removeBrandGroupState: () => void
  setVatAdjustmentToggleState: (vatAdjustment: boolean) => void
  setChurnToggleState: (churn: boolean) => void
  setShow1pVendorAnalyticsTab: (show: boolean) => void
  setLimitedToAssignmentsToggleState: (isSelectionEnabled: boolean) => void
  updateSelectedComparisonPeriod: (period: ComparisonPeriodType) => void
  setSelectedComparisonPeriod: (period: ComparisonPeriodType) => void
  updateAllMarketplaceIds: (allMarketplaceIds: MarketplaceId[]) => void
  updateAmazonMarketplaces: (
    amazonMarketplaces: string[],
    selectedAmazonMarketplaceIds: MarketplaceId[],
  ) => void
  updateBrandGroupState: (brandCustomer: BrandGroup) => void
  updateCurrencyList: (currencyList: Currency[]) => void
  updateCustomCategories: (customCategories: CheckboxCategory[]) => void
  updateFullCategoryList: (fullCategoryList: CheckboxCategory[]) => void
  updateSelectedCurrency: (currency: Currency) => void
  updateCustomer: (
    stateAttr: string,
    value?: Customer,
    vendorId?: number,
    callback?: () => void,
  ) => void
  updateCustomerVendorId: (stateAttr: string, vendorId: number) => Promise<void>
  updateFilteredMarketplaces: (filteredMarketplaces: Marketplace[]) => void
  updateMarketplaces: (markets?: MarketplaceId[]) => void
  updateAllMarketplaces: (allMarketplaces: Marketplace[]) => void
  updateMarketplacesColor: (marketsColorList: MarketplaceColor[]) => void
  updatePrevCustomer: (customer: Customer) => void
  updatePrevDefaultCurrency: (prevDefaultCurrency: Currency) => void
  updateRegions: (regions: RegionId[]) => void
  updateAllRegions: (allRegions: Region[]) => void
  updateSDMarketplaces: (markets: MarketplaceId[]) => void
  updateTags: (tags: Tags) => void
  updateLinesOfBusiness: (linesOfBusiness: LinesOfBusinessProps) => void
  updateLinesOfBusinessOptions: (LOBOptions: LOBResponseProps[]) => void
  updateTimeframe: (
    startDate: DateType,
    endDate: DateType,
    timeframe: Timeframe,
    comparisonDateRanges?: ComparisonDateRangesType,
  ) => void
  setHideGlobalBrandSelector: (hide: boolean) => void
  updateBreadcrumbs: (breadcrumb: BreadcrumbType, replace?: boolean) => void
  updateBreadcrumbsMultipleTabs: (breadcrumb: BreadcrumbType) => void
  breadcrumbCallout: (breadcrumb: BreadcrumbType) => void
}

// Combined Context interface
export interface ThemeContextType
  extends ThemeContextState,
    ThemeContextMethods {
  // Legacy properties that need to be maintained for backwards compatibility
  endDateGlobal: DateType
  startDateGlobal: DateType
  timeframeGlobal: Timeframe
}

export interface BreadcrumbType extends LibraryBreadcrumbType {
  changeType?: BreadcrumbChangeType
  showIcon?: boolean
  pagename?: string
  search?: string
  tabType?: 'campaign' | 'brand' | string
}
