import React from 'react'
import type { ReactNode } from 'react'

import { AuthProvider } from './auth-context'
import { UserProvider } from './user-context'

interface AppProvidersProps {
  children: ReactNode
}

const AppProviders = ({ children }: AppProvidersProps): React.JSX.Element => {
  return (
    <AuthProvider>
      <UserProvider>{children}</UserProvider>
    </AuthProvider>
  )
}

export default AppProviders
