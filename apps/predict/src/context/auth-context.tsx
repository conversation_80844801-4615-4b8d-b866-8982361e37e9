import { type User } from '@predict-types'
import React, { useState } from 'react'
import {
  getUser,
  handleAuthentication,
  isAuthenticated,
  login,
  loginAsImpersonateUser,
  logout,
  resetImpersonateUser,
} from 'src/Auth/Auth'

interface AuthContextType {
  user: User
  setUser: React.Dispatch<React.SetStateAction<User>>
  login: typeof login
  logout: typeof logout
  handleAuthentication: typeof handleAuthentication
  isAuthenticated: typeof isAuthenticated
  getUser: typeof getUser
  resetImpersonateUser: typeof resetImpersonateUser
  loginAsImpersonateUser: typeof loginAsImpersonateUser
}

interface AuthProviderProps {
  children: React.ReactNode
  [key: string]: unknown
}

const AuthContext = React.createContext<AuthContextType | undefined>(undefined)

const AuthProvider = (props: AuthProviderProps) => {
  const [user, setUser] = useState<User>(null as unknown as User) // Need to look into a better way to initialize this

  return (
    <AuthContext.Provider
      value={{
        user,
        setUser,
        login,
        logout,
        handleAuthentication,
        isAuthenticated,
        getUser,
        resetImpersonateUser,
        loginAsImpersonateUser,
      }}
      {...props}
    />
  )
}

const useAuth = (): AuthContextType => {
  const context = React.useContext(AuthContext)
  if (context === undefined) {
    throw new Error(`useAuth must be used within a AuthProvider`)
  }
  return context
}

export { AuthProvider, useAuth }
