import React, { useEffect } from 'react'
import type { ReactNode } from 'react'
import {
  changeLanguageTo,
  LOCAL_STORAGE_LANGUAGE_SETTING_KEY,
} from '@predict-services'
import moment from 'moment'
import { type User } from '@predict-types'

import { useAuth } from './auth-context'

interface UserContextType {
  user: User
  setUser: React.Dispatch<React.SetStateAction<User>>
  [key: string]: unknown
}

interface UserProviderProps {
  children: ReactNode
  [key: string]: unknown
}

const UserContext = React.createContext<UserContextType | undefined>(undefined)

const UserProvider = (props: UserProviderProps) => {
  const { user, setUser } = useAuth()

  useEffect(() => {
    if (user) {
      localStorage.setItem('user', JSON.stringify(user))
      localStorage.setItem(
        LOCAL_STORAGE_LANGUAGE_SETTING_KEY,
        user?.language?.value ?? 'en',
      )
      changeLanguageTo(user?.language?.value ?? 'en')
      if (user?.language?.value === 'zh') {
        moment.locale('zh-cn')
      } else {
        moment.locale('en') // Or 'en-gb' if you want British English
      }
    }
  }, [user])

  const providerValue: UserContextType = { user, setUser }
  return <UserContext.Provider value={{ ...providerValue }} {...props} />
}

const useUser = () => {
  const context = React.useContext(UserContext)
  if (context === undefined) {
    throw new Error(`useUser must be used within a UserProvider`)
  }
  return context
}

export { UserProvider, useUser }
