// [WIP] UserType needs to be updated with additional fields
export type UserType = {
  user: {
    email: string
    group_id: number
    group_name: string
    id: number
    name: string
    roles: string[]
    settings: {
      privacy_policy_accepted: boolean
      privacy_policy_updated_at: string
    }
    type: 'Pattern' | 'Customer'
    user_type_id: number
    username: string
    auth_roles: string[]
  }
  setUser: (user: UserType) => void
}
