import React, { useContext, useLayoutEffect } from 'react'
import {
  Navigate,
  NavLink,
  Route,
  Routes,
  useLocation,
  useNavigate,
} from 'react-router-dom'
import { RouterTabs } from '@patterninc/react-ui'
import { c, haveRoles, useRouteMatchTemp } from '@predict-services'

import { ThemeContext } from '../../Context'
import { useUser } from '../../context/user-context'
import DigitalShelfTabs from '../Insights/components/Pages/DigitalShelf/DigitalShelfTabs'
import { useIsDetailsPage } from './common/Advisory.hooks'
import { MarketAnalytics } from './components/Pages/Forensics/MarketAnalytics/MarketAnalytics'
import { PriceSegmentsRoutes } from './components/Pages/Forensics/PriceSegments/PriceSegmentsRoutes'
import { PriceSegmentsContextProvider } from './context/PriceSegmentsContext'

const AdvisoryRoutes = (): React.JSX.Element => {
  const { pathname } = useLocation()
  const { updateBreadcrumbs } = useContext(ThemeContext)
  const navigateTo = useNavigate()
  const { basePath } = useRouteMatchTemp()

  const navigate = (link: string) => {
    navigateTo(link)
  }

  // Not using toggle here, since we don't have timeline when My Product tab will be shown again
  const showMyProductsTab = false

  // To hide tabs when routing to
  // /advisory/digital-shelf/product-details or
  // /advisory/price-segments/segment/:segment_id/range/:range
  const isDetailsPage = useIsDetailsPage()

  const user = useUser().user

  //all the permissions
  const hasAdvisoryDigitalShelfPermission = haveRoles(user, [
      'read_advisory_digital_shelf',
    ]),
    hasMarketAnalyticsPermission = haveRoles(user, ['read_market_analytics']),
    hasPriceSegmentsPermission = haveRoles(user, ['read_price_segments'])

  useLayoutEffect(() => {
    if (!isDetailsPage) {
      updateBreadcrumbs({
        name: c('advisory'),
        link: pathname,
        changeType: 'rootLevel',
      })
    }
  }, [isDetailsPage, pathname, updateBreadcrumbs])

  const advisoryMobileTabsConfig = [
    ...(hasMarketAnalyticsPermission
      ? [
          {
            label: 'Market Analytics',
            link: `${basePath}/market-analytics`,
            linkV6: 'market-analytics',
          },
        ]
      : []),

    ...(hasPriceSegmentsPermission
      ? [
          {
            label: 'Price Segments',
            link: `${basePath}/price-segments`,
            linkV6: 'price-segments',
          },
        ]
      : []),

    ...(hasAdvisoryDigitalShelfPermission
      ? [
          {
            label: 'Digital Shelf',
            link: `${basePath}/digital-shelf`,
            linkV6: 'digital-shelf',
            subrows: [
              ...(showMyProductsTab
                ? [
                    {
                      label: 'My Products',
                      link: `${basePath}/digital-shelf/my-products`,
                      linkV6: 'digital-shelf/my-products',
                    },
                  ]
                : []),
              {
                label: 'Explore Products',
                link: `${basePath}/digital-shelf/explore-products`,
                linkV6: 'digital-shelf/explore-products',
              },
            ],
          },
        ]
      : []),
  ]

  return (
    <div className='page-container'>
      {!isDetailsPage && (
        <RouterTabs
          mobileConfig={advisoryMobileTabsConfig}
          navigate={navigate}
          currentPath={pathname}
        >
          {hasMarketAnalyticsPermission && (
            <NavLink to='market-analytics'>Market Analytics</NavLink>
          )}

          {hasPriceSegmentsPermission && (
            <NavLink to='price-segments'>Price Segments</NavLink>
          )}

          {hasAdvisoryDigitalShelfPermission && (
            <NavLink to='digital-shelf'>Digital Shelf</NavLink>
          )}
        </RouterTabs>
      )}

      <Routes>
        {/* MARKET ANALYTICS */}
        {hasMarketAnalyticsPermission && (
          <Route path='market-analytics/*' element={<MarketAnalytics />} />
        )}

        {/* PRICE SEGMENTS */}
        {hasPriceSegmentsPermission && (
          <Route
            path='price-segments/*'
            element={
              <PriceSegmentsContextProvider>
                <PriceSegmentsRoutes />
              </PriceSegmentsContextProvider>
            }
          />
        )}

        {/* DIGITAL SHELF */}
        {hasAdvisoryDigitalShelfPermission && (
          <Route
            path='digital-shelf/*'
            element={<DigitalShelfTabs rootPageName={'Advisory'} />}
          />
        )}

        {/* DEFAULT ROUTE */}
        <Route
          path='*'
          element={<Navigate to={advisoryMobileTabsConfig[0].link} />}
        />
      </Routes>
    </div>
  )
}

export default AdvisoryRoutes
