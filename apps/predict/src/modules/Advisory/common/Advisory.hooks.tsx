import { useLocation } from 'react-router-dom'
import { isMatchPath } from '@predict-services'

/**
 * This custom React hook determines if the current page is a details page based on the URL path.
 * It checks if the current path matches any of the following:
 * - /advisory/price-segments/segment/:segment_id/range/:range/*
 * - /advisory/market-analytics/categories/:id/*
 * - /advisory/market-analytics/brands/*
 * - /advisory/digital-shelf/product-details/*
 */
const paths = [
  '/advisory/digital-shelf/product-details/*',
  '/advisory/price-segments/segment/:segment_id/range/:range/*',
  '/advisory/market-analytics/categories/:id/*',
  '/advisory/market-analytics/brands/:id/*',
  '/advisory/market-analytics/keywords/:id/*',
]
export const useIsDetailsPage = () => {
  const { pathname } = useLocation()
  return paths.some((path) => isMatchPath(path, pathname))
}
