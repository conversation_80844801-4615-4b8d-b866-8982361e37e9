import { c } from '@predict-services'
import React, { lazy, Suspense, useContext, useEffect } from 'react'
import { Route, Routes, useLocation } from 'react-router-dom'
import { ThemeContext } from 'src/Context'
import { useIsDetailsPage } from 'src/modules/Advisory/common/Advisory.hooks'

const Marketshare = lazy(
  () =>
    import('src/modules/Insights/components/Pages/Share/MarketShare/Dashboard'),
)

export const MarketAnalytics = () => {
  const { pathname } = useLocation()

  const { updateBreadcrumbs } = useContext(ThemeContext)

  const isDetailsPage = useIsDetailsPage()

  useEffect(() => {
    if (!isDetailsPage) {
      updateBreadcrumbs({
        name: c('advisory'),
        link: pathname.startsWith('/advisory/market-analytics/categories')
          ? '/advisory/market-analytics/categories'
          : pathname.startsWith('/advisory/market-analytics/brands')
            ? '/advisory/market-analytics/brands'
            : pathname,
        changeType: 'rootLevel',
      })
    }
  }, [isDetailsPage, pathname, updateBreadcrumbs])

  return (
    <Routes>
      {/* All MarketShare detail routes are moved into MarketShare app*/}
      {/* MARKETSHARE */}
      <Route
        path='/*'
        element={
          <Suspense fallback={<div>Loading Market Analytics module...</div>}>
            <Marketshare />
          </Suspense>
        }
      />
    </Routes>
  )
}
