import React from 'react'
import { Stepper } from '@patterninc/react-ui'
import { useTranslate } from '@predict-services'

import {
  type BrandsListProps,
  type BrandsSearchBarProps,
  type SegmentType,
} from '../../../../../services/PriceSegmentsHelperService'
import styles from '../_price_segments.module.scss'
import { GeneralInfo } from './GeneralInfo'
import { SegmentBrands } from './SegmentBrands'
import SegmentCategories from './SegmentCategories'

type CreateOrEditSegmentProps = {
  stepCounter: number
  setStepCounter: React.Dispatch<React.SetStateAction<number>>
  generalInfo: SegmentType
  setGeneralInfo: React.Dispatch<React.SetStateAction<SegmentType>>
  brandsListMetadata: {
    brands: BrandsListProps[] | undefined
    status: string
    totalBrandsCount: number | undefined
  }
  setSelectedBrandIds: React.Dispatch<React.SetStateAction<Array<number>>>
  selectedBrandIds: Array<number>
  fetchNextPage: () => void
  hasNextPage?: boolean
} & BrandsSearchBarProps

export const CreateOrEditSegment = ({
  setStepCounter,
  stepCounter,
  generalInfo,
  setGeneralInfo,
  brandsListMetadata,
  brandsSearchText,
  checkAllSegBrands,
  fetchNextPage,
  selectedBrandIds,
  setBrandsSearchText,
  setCheckAllSegBrands,
  setSelectedBrandIds,
  setUncheckedBrandIds,
  uncheckedBrandIds,
  hasNextPage,
  isCategoryClearFiltersClicked,
  setIsCategoryClearFiltersClicked,
}: CreateOrEditSegmentProps): React.JSX.Element => {
  const { t } = useTranslate('advisory')
  return (
    <div className={styles.createOrEditContainer}>
      <Stepper
        steps={[t('generalInfo'), t('segmentCategories'), t('segmentBrands')]}
        currentStep={stepCounter}
        callout={(currentStep) => {
          setStepCounter(currentStep - 1)
        }}
        hideStepText
      />

      <div className='pat-mt-4'>
        {stepCounter === 1 && (
          <GeneralInfo
            generalInfo={generalInfo}
            setGeneralInfo={setGeneralInfo}
          />
        )}
        {stepCounter === 2 && (
          <SegmentCategories
            setGeneralInfo={setGeneralInfo}
            isCategoryClearFiltersClicked={isCategoryClearFiltersClicked}
            setIsCategoryClearFiltersClicked={setIsCategoryClearFiltersClicked}
          />
        )}
        {stepCounter === 3 && (
          <SegmentBrands
            brandsListMetadata={brandsListMetadata}
            setSelectedBrandIds={setSelectedBrandIds}
            selectedBrandIds={selectedBrandIds}
            fetchNextPage={fetchNextPage}
            hasNextPage={hasNextPage}
            brandsSearchText={brandsSearchText}
            setBrandsSearchText={setBrandsSearchText}
            setCheckAllSegBrands={setCheckAllSegBrands}
            checkAllSegBrands={checkAllSegBrands}
            setUncheckedBrandIds={setUncheckedBrandIds}
            uncheckedBrandIds={uncheckedBrandIds}
          />
        )}
      </div>
    </div>
  )
}
