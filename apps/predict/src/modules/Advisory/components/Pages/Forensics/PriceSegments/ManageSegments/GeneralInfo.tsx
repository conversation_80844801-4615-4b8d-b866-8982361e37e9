import React from 'react'
import { Icon, Switch, TextInput, Tooltip } from '@patterninc/react-ui'
import { useTranslate } from '@predict-services'

import { type SegmentType } from '../../../../../services/PriceSegmentsHelperService'

type GeneralInfoPropType = {
  generalInfo: SegmentType
  setGeneralInfo: React.Dispatch<React.SetStateAction<SegmentType>>
}

export const GeneralInfo = ({
  generalInfo,
  setGeneralInfo,
}: GeneralInfoPropType): React.JSX.Element => {
  const { t } = useTranslate('advisory')
  const segmentviewTooltipContent = (
    <>
      <span className='fw-semi-bold'>{t('defaultSegmentView')}</span>
      <div className='fs-12'>{t('defaultSegmentViewTooltip')}</div>
    </>
  )
  return (
    <>
      <TextInput
        labelText={t('segmentName')}
        value={generalInfo.name}
        callout={(_, value) =>
          setGeneralInfo((prevState) => ({
            ...prevState,
            name: value,
          }))
        }
        type='text'
        required
        autoFocus
      />
      <TextInput
        labelText={t('segmentDescription')}
        value={generalInfo.description}
        callout={(_, value) =>
          setGeneralInfo((prevState) => ({
            ...prevState,
            description: value,
          }))
        }
        type='textarea'
        containerClassName='pat-mt-4'
        required
      />
      <div className='flex align-items-center fs-12 pat-mt-4 pat-gap-2'>
        <Switch
          checked={generalInfo.is_default}
          callout={() =>
            setGeneralInfo((prevState) => ({
              ...prevState,
              is_default: !generalInfo.is_default,
            }))
          }
        />
        {t('setAsDefaultDashboardSegmentView')}
        <Tooltip
          tooltipContent={segmentviewTooltipContent}
          position='bottom'
          maxWidth='220px'
        >
          <Icon icon='info' iconSize='12px' color='dark-blue' />
        </Tooltip>
      </div>
    </>
  )
}
