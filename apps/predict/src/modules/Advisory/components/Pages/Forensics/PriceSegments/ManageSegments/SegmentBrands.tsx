import React, { useCallback, useEffect, useMemo, useState } from 'react'
import {
  abbreviateNumber,
  Alert,
  type ConfigItemType,
  SearchBar,
  StandardTable,
} from '@patterninc/react-ui'
import { c, useTranslate } from '@predict-services'
import { useUser } from 'src/context/user-context'
import {
  type BrandsListProps,
  type BrandsSearchBarProps,
} from 'src/modules/Advisory/services/PriceSegmentsHelperService'

type SegmentBrandsType = {
  brandsListMetadata: {
    brands: BrandsListProps[] | undefined
    status: string
    totalBrandsCount: number | undefined
  }
  setSelectedBrandIds: React.Dispatch<React.SetStateAction<Array<number>>>
  selectedBrandIds: Array<number>
  fetchNextPage: () => void
  hasNextPage?: boolean
} & BrandsSearchBarProps

export const SegmentBrands = ({
  brandsListMetadata,
  setSelectedBrandIds,
  fetchNextPage,
  hasNextPage,
  brandsSearchText,
  setBrandsSearchText,
  setCheckAllSegBrands,
  checkAllSegBrands,
  uncheckedBrandIds,
  setUncheckedBrandIds,
  selectedBrandIds,
}: SegmentBrandsType): React.JSX.Element => {
  const { user } = useUser()
  const [clonedBrands, setClonedBrands] = useState<
    BrandsListProps[] | undefined
  >([])
  const { status, brands, totalBrandsCount } = brandsListMetadata
  const { t } = useTranslate('advisory')
  const baseConfig = useMemo(
    () => [
      {
        name: 'name',
        label: c('brandName'),
        cell: {
          children: (brandList: BrandsListProps) => {
            return <div>{brandList?.name}</div>
          },
        },
        noSort: true,
      },
      {
        name: 'est_revenue',
        label: t('estRevenue'),
        cell: {
          children: (brandList: BrandsListProps) => {
            const formattedValue = `${
              user?.current_currency?.symbol
            }${abbreviateNumber(brandList?.est_revenue)?.toLocaleString()}`
            return <div>{formattedValue}</div>
          },
        },
        noSort: true,
      },
    ],
    [t, user?.current_currency?.symbol],
  )

  useEffect(() => {
    /**
     * Below logic is to handle the checkbox ALL check/uncheck logic.
     * Single checkbox change logic is handled in the handleCheckedBoxes().
     */
    const clonedBrands = brands
      ? brands?.map((brand) => {
          // Below logic is to handle check all checkbox
          if (checkAllSegBrands) {
            if (selectedBrandIds?.indexOf(brand.id) === -1) {
              setSelectedBrandIds((prev) => [...prev, brand?.id])
            }
            return {
              ...brand,
              checked: brand?.is_checked,
            }
          }
          // Below logic is to handle uncheck all checkbox
          else if (checkAllSegBrands !== undefined) {
            if (uncheckedBrandIds?.indexOf(brand.id) === -1) {
              setUncheckedBrandIds((prev) => [...prev, brand.id])

              return {
                ...brand,
                checked: !brand?.is_checked,
              }
            }
          }
          return brand
        })
      : []
    setClonedBrands(clonedBrands)
  }, [
    brands,
    checkAllSegBrands,
    selectedBrandIds,
    setSelectedBrandIds,
    setUncheckedBrandIds,
    uncheckedBrandIds,
  ])

  const [sort, setSort] = useState({
      prop: 'est_sale',
      flip: true,
    }),
    setStandardTableSortBy = (sortObj: {
      activeColumn: string
      direction: boolean
    }) => {
      setSort({
        prop: sortObj?.activeColumn,
        flip: sortObj?.direction,
      })
    }

  const handleCheckedBoxes = useCallback(
    (checkedBrands: BrandsListProps[], checkAll = false) => {
      const checkedBrandIds = checkedBrands?.map((brand) => brand.id)

      setSelectedBrandIds(checkedBrandIds)
      const selectedIds = new Set(checkedBrandIds)

      brands?.forEach((brand) => {
        if (checkAll && checkedBrands?.length > 0) {
          brand.is_checked = true
          setCheckAllSegBrands(true)
          setUncheckedBrandIds([])
        } else if (!checkAll && checkedBrands?.length === 0) {
          setCheckAllSegBrands(false)
        } else {
          // undefined is to identify single checkbox change
          setCheckAllSegBrands(undefined)

          if (selectedIds?.has(brand?.id)) {
            brand.is_checked = true

            // Remove from uncheckedBrandIds if brand id already exists
            if (uncheckedBrandIds?.includes(brand?.id)) {
              const uncheckedBrandIdsCopy = [...uncheckedBrandIds]
              const index = uncheckedBrandIdsCopy?.indexOf(brand?.id)

              uncheckedBrandIdsCopy.splice(index, 1)
              setUncheckedBrandIds(uncheckedBrandIdsCopy)
            }
          } else {
            brand.is_checked = false

            // Add to uncheckedBrandIds if brand id doesn't exist
            if (uncheckedBrandIds?.indexOf(brand?.id) === -1) {
              setUncheckedBrandIds((prev) => [...prev, brand?.id])
            }
          }
        }
      })
      setClonedBrands(brands)
    },
    [
      brands,
      setCheckAllSegBrands,
      setSelectedBrandIds,
      setUncheckedBrandIds,
      uncheckedBrandIds,
    ],
  )

  return (
    <>
      <Alert
        type='info'
        customClass='pat-mb-4'
        text={t('displayingBrandsAvailableToCreateASegment', {
          count: totalBrandsCount ?? 0,
        })}
      />
      <SearchBar
        onChange={(val) => {
          setBrandsSearchText(val)
        }}
        value={brandsSearchText}
        autoFocus
        placeholder={c('searchBrands')}
      />
      <StandardTable
        data={clonedBrands || []}
        config={
          baseConfig as ConfigItemType<unknown, Record<string, unknown>>[]
        }
        hasCheckboxes
        hasData={!!brands?.length}
        hasMore={!!hasNextPage}
        getData={fetchNextPage}
        noDataFields={{
          primaryText: c('noBrandsFound'),
          secondaryText: t('weCouldNotFindAnyBrandsForTheSelectedCategories'),
        }}
        showGroups
        successStatus={status === 'success'}
        loading={status === 'loading'}
        tableId='segment_brands_table'
        sort={setStandardTableSortBy}
        dataKey='name'
        sortBy={sort}
        groups={[
          {
            check: (data) => !data?.is_checked,
            groupHeader: t('unselectedBrands'),
          },
          {
            check: (data) => data?.is_checked,
            groupHeader: () =>
              t('selectedAllBrnads', {
                value: checkAllSegBrands ? c('all') : c('brands'),
              }),
          },
        ]}
        handleCheckedBoxes={handleCheckedBoxes}
        isDefaultCheckedAll={checkAllSegBrands}
        customWidth='100%'
      />
    </>
  )
}
export default SegmentBrands
