import React, {
  use<PERSON><PERSON>back,
  useContext,
  useEffect,
  useMemo,
  useState,
} from 'react'
import { type NumericFormatProps } from 'react-number-format'
import { useQuery } from '@tanstack/react-query'
import {
  abbreviateNumber,
  type DateCheckState,
  getApiUrlPrefix,
  getFlattenNestedTreeList,
  hasValue,
  Icon,
  ListLoading,
  MdashCheck,
  SearchBar,
  Switch,
  Tooltip,
  TreeListBox,
  useIsMobileView,
} from '@patterninc/react-ui'
import { ThemeContext } from 'src/Context'
import { type SegmentType } from 'src/modules/Advisory/services/PriceSegmentsHelperService'

import {
  c,
  SecureAxios,
  useTranslate,
} from '../../../../../../../common/services'
import styles from '../_price_segments.module.scss'

type SegmentCategoriesProps = {
  setGeneralInfo: React.Dispatch<React.SetStateAction<SegmentType>>
  setIsCategoryClearFiltersClicked?: React.Dispatch<
    React.SetStateAction<boolean>
  >
  isCategoryClearFiltersClicked?: boolean
}

enum CheckboxState {
  UNCHECKED = 'UNCHECKED',
  CHECKED = 'CHECKED',
}

type GenericNode = {
  [key: string]: string | number | null | NumericFormatProps
}

const SegmentCategories = ({
  setGeneralInfo,
  isCategoryClearFiltersClicked,
  setIsCategoryClearFiltersClicked,
}: SegmentCategoriesProps): React.JSX.Element => {
  const { t } = useTranslate('advisory')
  const { t: tInsights } = useTranslate('insights')
  const { customer, selectedCurrency } = useContext(ThemeContext)
  const isMobileView = useIsMobileView()
  const categoriesNestedListURL = `${getApiUrlPrefix(
    'marketshare',
  )}/api/v3/categories`

  const [searchQuery, setSearchQuery] = useState('')
  const [onlyCustomerCategories, setOnlyCustomerCategories] = useState(false)

  const { data: categoriesData, status: respStatus } = useQuery({
    queryKey: [customer.id],
    queryFn: async ({ signal }) => {
      try {
        const response = await SecureAxios.get(categoriesNestedListURL, {
          params: {
            customer_id: customer.id,
            format: 'gzip',
          },
          signal,
        })
        return response?.data
      } catch (error) {
        console.error('Error fetching categories nested list:', error)
        throw error
      }
    },
    gcTime: 1000 * 60 * 30,
    staleTime: 1000 * 60 * 5,
  })

  const getIdentifierKeys = () => {
    return {
      displayKey: 'name',
      idKey: 'id',
      parentKey: 'parent_category_id',
    }
  }

  const flattenData = getFlattenNestedTreeList({
    nestedData: (categoriesData as GenericNode[]) ?? [],
    childrenKey: 'children',
    parentKey: getIdentifierKeys().parentKey,
    idKey: getIdentifierKeys().idKey,
  })

  const defaultItemStates = useMemo(
    () =>
      flattenData.map((i: GenericNode) => ({
        id: i[getIdentifierKeys().idKey] as string | number,
        state: CheckboxState.UNCHECKED,
      })),
    [flattenData],
  )

  const [itemStates, setItemStates] =
    useState<DateCheckState[]>(defaultItemStates)

  // TODO: To be updated in the react library to accept the value as specific type. Will remove 'any[]' after update.

  const extractLeafIds = useCallback((data: any[]) => {
    return data
      ?.filter((item) => item?.is_leaf_node && item?.state === 'CHECKED')
      ?.map((element) => element?.id)
  }, [])

  useEffect(() => {
    if (isCategoryClearFiltersClicked) {
      setItemStates(defaultItemStates)
      setIsCategoryClearFiltersClicked?.(false)
    }
  }, [
    defaultItemStates,
    isCategoryClearFiltersClicked,
    setIsCategoryClearFiltersClicked,
  ])

  const NestedListComponent = useCallback(() => {
    return (
      <div className='flex flex-direction-column pat-gap-2'>
        <SearchBar
          value={searchQuery}
          onChange={(inputText) => setSearchQuery(inputText)}
          debounce={300}
        />
        <div className='flex pat-gap-2'>
          <Switch
            checked={onlyCustomerCategories}
            callout={(checked) => setOnlyCustomerCategories(checked)}
          />
          <div className='fs-12'>{tInsights('hideCategoriesIDontPlayIn')}</div>
          <Tooltip
            position='top'
            tooltipContent={tInsights('hideCategoriesIDontPlayInTooltip')}
          >
            <Icon icon='info' iconSize='12px' color='dark-blue' />
          </Tooltip>
        </div>
        <TreeListBox
          data={(flattenData as GenericNode[]) ?? []}
          search={searchQuery}
          hasData={
            Array.isArray(categoriesData) &&
            categoriesData?.length > 0 &&
            respStatus === 'success'
          }
          isLoading={respStatus === 'pending'}
          tableConfig={[
            {
              label: t('categoriesSubcategories'),
              dataKey: 'name',
              children: (data) => <span>{data.name as string}</span>,
            },
            {
              label: c('estSales'),
              dataKey: 'est_revenue',
              children: (data) => {
                return (
                  <MdashCheck check={hasValue(data.est_revenue)}>
                    {`${selectedCurrency?.symbol}${abbreviateNumber(data?.est_revenue as number)}`}
                  </MdashCheck>
                )
              },
            },
          ]}
          onChange={(value) => {
            setGeneralInfo((prevState) => ({
              ...prevState,
              category_ids: extractLeafIds(value),
            }))
          }}
          identifierKeys={getIdentifierKeys()}
          // adjust heigh and width based on mobile version
          customHeight={640}
          customWidth={isMobileView ? 360 : 560}
          setItemStates={setItemStates}
          itemStates={itemStates}
        />
      </div>
    )
  }, [
    categoriesData,
    extractLeafIds,
    flattenData,
    isMobileView,
    itemStates,
    onlyCustomerCategories,
    respStatus,
    searchQuery,
    selectedCurrency?.symbol,
    setGeneralInfo,
    t,
    tInsights,
  ])

  return (
    <>
      <div className='fs-12 pat-mb-2 flex justify-content-between'>
        <span>{t('segmentsCategories')}</span>
        <span className={styles.orderedByText}>{t('orderedByEstSale')}</span>
      </div>
      {respStatus === 'success' ? (
        NestedListComponent()
      ) : (
        // TODO: Remove listloading once we have our library component latest version. It handles the loading itself.
        <ListLoading longList={true} numberOfRows={10} />
      )}
    </>
  )
}

export default SegmentCategories
