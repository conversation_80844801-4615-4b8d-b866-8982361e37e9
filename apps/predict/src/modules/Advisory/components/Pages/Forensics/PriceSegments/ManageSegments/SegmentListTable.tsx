import React, { useMemo } from 'react'
import { useMutation } from '@tanstack/react-query'
import {
  Button,
  ButtonGroup,
  getApiUrlPrefix,
  Icon,
  StandardTable,
  toast,
  Tooltip,
} from '@patterninc/react-ui'

import {
  c,
  SecureAxios,
  useTranslate,
} from '../../../../../../../common/services'
import { useUser } from '../../../../../../../context/user-context'
import { type SegmentType } from '../../../../../services/PriceSegmentsHelperService'
import styles from '../_price_segments.module.scss'

type SegmentListTableProps = {
  segmentList: SegmentType[]
  isLoading: boolean
  setDefaultChanged: (value: boolean) => void
  setGeneralInfo: (value: SegmentType) => void
  setIsEditOrCreateSideDrawerOpen: (value: boolean) => void
  setIsShareSegmentSideDrawerOpen: (value: boolean) => void
  setSharedSegment: (value: SegmentType) => void
  setIsCreateSegment: (value: boolean) => void
  setStepCounter: (value: number) => void
  setIsActionPerformed: (value: boolean) => void
  setIsDeletingSegment: (value: boolean) => void
}
export const SegmentListTable = ({
  segmentList,
  isLoading,
  setDefaultChanged,
  setIsShareSegmentSideDrawerOpen,
  setSharedSegment,
  setIsCreateSegment,
  setIsActionPerformed,
  setIsDeletingSegment,
}: SegmentListTableProps): React.JSX.Element => {
  const { t } = useTranslate('advisory')
  const { user } = useUser(),
    customer = user.customer

  // urls
  const segmentApiEndPoint = `${getApiUrlPrefix('marketshare')}/api/v3/segments`

  const { mutate: updateSegment } = useMutation({
    //Refactor direct API calls to use React Query hooks for better state management and efficiency.
    mutationFn: async ({
      segment,
      is_shared,
    }: {
      segment: SegmentType
      is_shared: boolean
    }) => {
      return SecureAxios.put(
        `${segmentApiEndPoint}/${segment?.id}`,
        {
          customer_id: customer.id,
          created_by_user_id: segment.created_by_user_id,
          is_default: true,
          update_set_default: true,
          user_id: user.id,
        },
        {
          params: {
            ...(is_shared ? { is_shared: is_shared } : {}),
          },
        },
      )
    },
    onSuccess: () => {
      setDefaultChanged(true)
      toast({ type: 'success', message: 'Segment Updated Successfully' })
    },
    onError: () => {
      toast({
        type: 'error',
        message: t('thereWasAProblemCreatingtheSegment'),
      })
    },
  })

  const { mutate: makeACopy } = useMutation({
    mutationFn: async (segment: SegmentType) => {
      return SecureAxios.post(`${segmentApiEndPoint}/${segment?.id}/copy`, {
        customer_id: customer?.id,
        user_id: user?.id,
        created_by_user_id: user?.id,
        description: segment?.description,
        is_default: false,
        name: `Copy of ${segment?.name}`,
      })
    },
    onMutate: () => {
      setIsActionPerformed(true)
      setIsCreateSegment(true)
    },
    onSuccess: () => {
      setDefaultChanged(true)
      setIsActionPerformed(false)
      setIsCreateSegment(false)
      toast({ type: 'success', message: t('segmentCreatedSuccessfully') })
    },
    onError: () => {
      setIsActionPerformed(false)
      setIsCreateSegment(false)
      toast({
        type: 'error',
        message: t('thereWasAProblemCreatingtheSegment'),
      })
    },
  })

  const { mutate: deleteSegment } = useMutation({
    mutationFn: async (segment: SegmentType) => {
      return SecureAxios.delete(`${segmentApiEndPoint}/${segment?.id}`, {
        params: {
          user_id: user.id,
          ...(segment?.created_by_user_id !== user.id
            ? { is_shared: true }
            : {}),
        },
      })
    },
    onMutate: () => {
      setIsActionPerformed(true)
      setIsDeletingSegment(true)
    },
    onSuccess: () => {
      setDefaultChanged(true)
      setIsDeletingSegment(false)
      setIsActionPerformed(false)
      toast({ type: 'success', message: t('segmentDeletedSuccessfully') })
    },
    onError: () => {
      setIsDeletingSegment(false)
      setIsActionPerformed(false)
      toast({
        type: 'error',
        message: t('thereWasaProblemDeletingtheSegment'),
      })
    },
  })

  const config = useMemo(() => {
    return [
      {
        name: 'name',
        label: t('savedSegments'),
        noSort: true,
        cell: {
          children: (segment: SegmentType) => {
            const isSharedSegment =
              segment?.created_by_user !== 'System' &&
              segment?.created_by_user_id !== user.id
            return (
              <div
                key={segment.id}
                className='flex align-items-center justify-content-between pat-gap-4'
              >
                <div
                  className={
                    isSharedSegment
                      ? 'flex flex-direction-column pat-gap-1'
                      : ''
                  }
                >
                  <Tooltip
                    position='bottom'
                    tooltipContent={segment.description}
                  >
                    {segment.name}
                  </Tooltip>
                  {isSharedSegment && (
                    <div className='fc-purple'>{c('shared')}</div>
                  )}
                </div>
              </div>
            )
          },
        },
      },
      {
        name: 'created_by',
        label: c('createdBy'),
        noSort: true,
        cell: {
          children: (segment: SegmentType) => {
            return <div key={segment.id}>{segment.created_by_user}</div>
          },
        },
      },
      {
        name: 'is_default',
        label: t('defaultView'),
        noSort: true,
        className: `${styles.segmentListHeaderContainer}`,
        cell: {
          children: (segment: SegmentType) => {
            return (
              <div key={segment.id} className='flex justify-content-center'>
                <Icon
                  icon='check'
                  color={segment.is_default ? 'dark-green' : 'purple'}
                />
              </div>
            )
          },
        },
      },
      {
        name: '',
        label: '',
        noSort: true,
        cell: {
          children: (segment: SegmentType) => {
            return (
              <div key={segment.id}>
                {segment.created_by_user === 'System' ? (
                  <Button
                    styleType='secondary'
                    disabled={segment.is_default}
                    onClick={() => {
                      updateSegment({ segment, is_shared: false })
                    }}
                  >
                    {c('setAsDefault')}
                  </Button>
                ) : (
                  <ButtonGroup
                    buttons={[
                      {
                        actions: [
                          {
                            text: t('useAsDefaultSegment'),
                            disabled: {
                              value: segment.is_default,
                            },
                            callout: () => {
                              updateSegment({
                                segment,
                                is_shared:
                                  segment?.created_by_user_id !== user.id,
                              })
                            },
                          },
                          ...(segment?.created_by_user_id === user.id
                            ? [
                                {
                                  text: t('shareSegment'),
                                  callout: () => {
                                    setSharedSegment(segment)
                                    setIsShareSegmentSideDrawerOpen(true)
                                  },
                                },
                              ]
                            : []),
                          {
                            text: c('makeaCopy'),
                            callout: () => {
                              makeACopy(segment)
                            },
                          },
                        ],
                      },
                      //  TODO: Un-comment this after initail release of v1(marketshare) for consistency.
                      // {
                      //   icon: 'pencil',
                      //   tooltip: {
                      //     tooltipContent: null,
                      //   },
                      //   onClick: () => {
                      //     setStepCounter(1)
                      //     setGeneralInfo(segment)
                      //     setIsEditOrCreateSideDrawerOpen(true)
                      //     setIsCreateSegment(false)
                      //   },
                      //   disabled: segment?.created_by_user_id !== user.id,
                      // },
                      {
                        icon: 'trash',
                        destructive: true,
                        as: 'confirmation',
                        confirmation: {
                          header: c('areYouSure'),
                          body: t(
                            'thisWillDeleteTheSegmentWouldYouLikeContinue',
                          ),
                          type: 'red',
                          confirmCallout: () => {
                            deleteSegment(segment)
                          },
                        },
                      },
                    ]}
                  />
                )}
              </div>
            )
          },
        },
      },
    ]
  }, [
    deleteSegment,
    makeACopy,
    setIsShareSegmentSideDrawerOpen,
    setSharedSegment,
    t,
    updateSegment,
    user.id,
  ])

  return (
    <StandardTable
      data={segmentList}
      config={config}
      dataKey='id'
      getData={() => undefined}
      hasData={segmentList?.length > 0}
      hasMore={false}
      loading={isLoading}
      noDataFields={{
        primaryText: c('noDataAvailable'),
        secondaryText: c('pleaseTryAgain'),
      }}
      sort={() => undefined}
      sortBy={{
        prop: '',
        flip: false,
      }}
      tableId='segment_list_table'
      successStatus
      customHeight='auto'
      customWidth='100%'
    />
  )
}
