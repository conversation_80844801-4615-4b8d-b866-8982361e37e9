import React, { useCallback } from 'react'
import { type QueryStatus } from '@tanstack/react-query'
import {
  Alert,
  Cell,
  CustomTable,
  FormLabel,
  MdashCheck,
  Row,
  SectionHeader,
  Switch,
  TableCheckbox,
} from '@patterninc/react-ui'
import { c, useTranslate } from '@predict-services'
import {
  type SegmentType,
  type ShareSegmentUserType,
} from 'src/modules/Advisory/services/PriceSegmentsHelperService'

import styles from './_share_segment.module.scss'
import { UserSearchBox } from './UserSearchBox'

type ShareSegmentContentProps = {
  checkAll: boolean
  setCheckAll: React.Dispatch<React.SetStateAction<boolean>>
  displayState: ShareSegmentUserType[]
  setDisplayState: React.Dispatch<React.SetStateAction<ShareSegmentUserType[]>>
  checkedBoxes: ShareSegmentUserType[]
  setCheckedBoxes: React.Dispatch<React.SetStateAction<ShareSegmentUserType[]>>
  sharedWithUsersListAPIStatus: QueryStatus
  sharedWithUsersListAPILoading: boolean
  sharedSegment: SegmentType
}

export const ShareSegmentContent = ({
  checkAll,
  setCheckAll,
  displayState,
  setDisplayState,
  checkedBoxes,
  setCheckedBoxes,
  sharedWithUsersListAPIStatus,
  sharedWithUsersListAPILoading,
  sharedSegment,
}: ShareSegmentContentProps): React.JSX.Element => {
  const { t } = useTranslate('advisory')
  const handleCheck = useCallback(
    (value: boolean, dataObj: ShareSegmentUserType, name?: string) => {
      let stateIndex: number
      if (name !== 'all') {
        stateIndex = displayState?.findIndex(
          (element: ShareSegmentUserType) =>
            element?.user_id === dataObj?.user_id,
        )
      }
      if (value) {
        if (
          name === 'all' ||
          checkedBoxes?.length === displayState?.length - 1
        ) {
          displayState?.length > 0 && setCheckAll(true)
          const updatedCheckedBoxes = displayState?.map((item) => ({
            ...(dataObj?.user_id === item?.user_id
              ? { ...dataObj, is_shared: true }
              : { ...item, is_shared: true }),
          }))

          setCheckedBoxes(updatedCheckedBoxes)
          setDisplayState(updatedCheckedBoxes)
        } else {
          setCheckedBoxes((prevState) => {
            return [
              ...prevState,
              typeof dataObj === 'object'
                ? { ...dataObj, is_shared: value }
                : dataObj,
            ]
          })
          setDisplayState((displayState) => {
            if (stateIndex >= 0 && displayState) {
              return [
                ...displayState.slice(0, stateIndex),
                { ...dataObj, is_shared: value },
                ...displayState.slice(stateIndex + 1),
              ]
            }
            return displayState ?? []
          })
        }
      } else {
        setCheckAll(false)
        if (name === 'all') {
          setCheckedBoxes([])
          setDisplayState((displayState) => {
            return displayState
              ? displayState.map((item) => ({
                  ...item,
                  is_shared: false,
                  is_default: false,
                }))
              : []
          })
        } else {
          const checkedBoxIndex = checkedBoxes?.findIndex(
            (element: ShareSegmentUserType) =>
              element?.user_id === dataObj?.user_id,
          )
          setCheckedBoxes([
            ...(checkedBoxes?.slice(0, checkedBoxIndex) ?? []),
            ...(checkedBoxes?.slice(checkedBoxIndex + 1) ?? []),
          ])
          setDisplayState((displayState) => {
            if (stateIndex >= 0 && displayState) {
              return [
                ...displayState.slice(0, stateIndex),
                { ...dataObj, is_shared: value, is_default: false },
                ...displayState.slice(stateIndex + 1),
              ]
            }
            return displayState ?? []
          })
        }
      }
    },
    [checkedBoxes, displayState, setCheckAll, setCheckedBoxes, setDisplayState],
  )

  const handleToggle = useCallback(
    (value: boolean, dataObj: ShareSegmentUserType) => {
      if (value && !dataObj.is_shared) {
        handleCheck(true, { ...dataObj, is_default: value })
      } else {
        setCheckedBoxes((prevState) => {
          const index = prevState.findIndex(
            (element: ShareSegmentUserType) =>
              element.user_id === dataObj.user_id,
          )
          return index >= 0
            ? [
                ...prevState.slice(0, index),
                { ...dataObj, is_default: value },
                ...prevState.slice(index + 1),
              ]
            : prevState
        })
        setDisplayState((prevState) => {
          const index = prevState?.findIndex(
            (element: ShareSegmentUserType) =>
              element.user_id === dataObj.user_id,
          )

          if (index >= 0 && prevState) {
            return [
              ...prevState.slice(0, index),
              { ...dataObj, is_default: value },
              ...prevState.slice(index + 1),
            ]
          }

          return prevState ?? []
        })
      }
    },
    [handleCheck, setCheckedBoxes, setDisplayState],
  )

  const handleAddButton = useCallback(
    (user: ShareSegmentUserType) => {
      const isUserPresent =
        displayState?.length > 0 &&
        displayState?.some(
          (u: ShareSegmentUserType) => u?.user_id === user.user_id,
        )
      // Only update states if the user is not already present or state is empty
      if (!isUserPresent) {
        user = {
          ...user,
          is_shared: true,
          is_default: false,
          segment_id: sharedSegment?.id,
        }
        setDisplayState((prevState) => [user, ...prevState])
        setCheckedBoxes((prevState) => {
          return [...prevState, user]
        })
      }
    },
    [displayState, setCheckedBoxes, setDisplayState, sharedSegment?.id],
  )

  return (
    <>
      <Alert
        type='info'
        text={t(
          'onceSegmentIsShareditWillAppearInSelectedUsersMarketShareDashboard',
        )}
        customClass='pat-mb-4'
      />
      <FormLabel label={c('searchUsers')} />
      <UserSearchBox
        handleAddButton={handleAddButton}
        sharedSegment={sharedSegment}
      />
      <SectionHeader title={c('sharedWith')} />
      <CustomTable
        hasData={!!displayState?.length}
        successStatus={sharedWithUsersListAPIStatus === 'success'}
        loading={sharedWithUsersListAPILoading}
        // Below two props as hard coded since we are not doing pagination as of now.
        hasMore={false}
        getData={() => undefined}
        tableId='share-segment-user-list-table'
        noDataFields={{
          icon: 'femaleUser',
          primaryText: t('thisSegmentHasntBeenSharedYet'),
          secondaryText: t('shareSegmentContentSecondaryText'),
        }}
        sort={() => undefined}
        sortBy={{
          prop: '',
          flip: false,
        }}
        customWidth='100%'
        customHeaders={
          <>
            <TableCheckbox
              name='checkbox-all-users'
              checked={checkAll}
              handleCheck={() =>
                handleCheck(
                  !checkAll,
                  // passing dummy object, since for check all data object is not required
                  {
                    user_id: 0,
                    full_user_name: '',
                    is_default: false,
                    is_shared: false,
                    segment_id: 0,
                    username: '',
                  },
                  'all',
                )
              }
            />
            <Cell className={styles.headerCell}>
              <span>{t('userName')}</span>
            </Cell>
            <Cell className={styles.headerCell}>
              <span>{c('setAsDefault')}</span>
            </Cell>
          </>
        }
      >
        {/* ---------------------- Data Rows ---------------------- */}
        {displayState?.length &&
          displayState?.map((user) => {
            return (
              <Row key={user.user_id}>
                <TableCheckbox
                  name={`checkbox-user-${user.user_id}`}
                  checked={user.is_shared}
                  handleCheck={() => handleCheck(!user.is_shared, user)}
                />
                <Cell>
                  <MdashCheck check={!!user.full_user_name}>
                    <span>{user.full_user_name}</span>
                  </MdashCheck>
                </Cell>
                <Cell>
                  <div className='flex justify-content-end'>
                    <Switch
                      checked={user.is_default}
                      callout={() => handleToggle(!user.is_default, user)}
                    />
                  </div>
                </Cell>
              </Row>
            )
          })}
      </CustomTable>
    </>
  )
}
