import React, { useCallback, useState } from 'react'
import { useMutation, useQuery } from '@tanstack/react-query'
import {
  Button,
  EmptyState,
  getApiUrlPrefix,
  SideDrawer,
  toast,
} from '@patterninc/react-ui'

import {
  c,
  SecureAxios,
  useTranslate,
} from '../../../../../../../common/services'
import {
  defaultSegment,
  type SegmentType,
  type ShareSegmentUserType,
} from '../../../../../services/PriceSegmentsHelperService'
import { ShareSegmentContent } from './ShareSegmentContent'

type ShareSegmentSideDrawerProps = {
  isShareSegmentSideDrawerOpen: boolean
  setIsShareSegmentSideDrawerOpen: React.Dispatch<React.SetStateAction<boolean>>
  sharedSegment: SegmentType
  setSharedSegment: React.Dispatch<React.SetStateAction<SegmentType>>
}

export const ShareSegmentSideDrawer = ({
  isShareSegmentSideDrawerOpen,
  setIsShareSegmentSideDrawerOpen,
  sharedSegment,
  setSharedSegment,
}: ShareSegmentSideDrawerProps): React.JSX.Element => {
  const { t } = useTranslate('advisory')

  const sharedSegmentCloseCallout = useCallback(() => {
    setCheckAll(true)
    setCheckedBoxes([])
    setDisplayState([])
    setSharedSegment(defaultSegment)
    setIsShareSegmentSideDrawerOpen(!isShareSegmentSideDrawerOpen)
  }, [
    isShareSegmentSideDrawerOpen,
    setIsShareSegmentSideDrawerOpen,
    setSharedSegment,
  ])

  const [checkAll, setCheckAll] = useState<boolean>(true)
  const [checkedBoxes, setCheckedBoxes] = useState<ShareSegmentUserType[]>([])
  const [displayState, setDisplayState] = useState<ShareSegmentUserType[]>([])
  const sharedWithUsersListAPI = `${getApiUrlPrefix(
    'marketshare',
  )}/api/v3/segments`

  const {
    status: sharedWithUsersListAPIStatus,
    isLoading: sharedWithUsersListAPILoading,
  } = useQuery({
    queryKey: [sharedWithUsersListAPI, sharedSegment.id],
    queryFn: async ({ signal }) => {
      try {
        const response = await SecureAxios.get(
          `${sharedWithUsersListAPI}/${sharedSegment.id}/assigned_users`,
          {
            params: {
              segment_id: sharedSegment.id,
            },
            signal,
          },
        )
        const userData = response?.data?.data
        setDisplayState(userData)
        setCheckedBoxes(userData)
        return userData
      } catch (error) {
        console.error('Error fetching shared users list:', error)
        throw error
      }
    },

    // To prevent refetching in background, on-focus, etc.
    staleTime: Infinity,
    refetchOnWindowFocus: false,
    enabled: !!sharedSegment.id,
    gcTime: 0,
  })

  const SharedWithUsersApiParams = {
    users: checkedBoxes,
  }
  const { mutate: shareSegmentAPICall, status: shareSegmentAPIStatus } =
    useMutation({
      mutationFn: async () => {
        const response = await SecureAxios.put(
          `${sharedWithUsersListAPI}/${sharedSegment.id}/share`,
          SharedWithUsersApiParams,
          { headers: { 'Content-Type': 'application/json' } },
        )
        return response.data
      },
      onSuccess: () => {
        toast({
          type: 'success',
          message: t('segmentSharedSuccessfullyWithSelectedUsers'),
          config: {
            autoClose: 3000,
          },
        })
        sharedSegmentCloseCallout()
      },
      onError: () => {
        toast({
          type: 'error',
          message: t('thereWasaProblemInSharingTheSegment'),
          config: {
            autoClose: 3000,
          },
        })
      },
    })

  return (
    <SideDrawer
      isOpen={isShareSegmentSideDrawerOpen}
      closeCallout={sharedSegmentCloseCallout}
      headerContent={t('shareSegmentName', { name: sharedSegment.name })}
      size='md'
      layerPosition={2}
      footerContent={
        <div className='flex justify-content-end align-items-center pat-gap-4'>
          <Button onClick={sharedSegmentCloseCallout}>{c('close')}</Button>

          <div>
            {checkedBoxes.length < displayState.length ? (
              <Button
                styleType='primary-green'
                as='confirmation'
                confirmation={{
                  body: t(
                    'unselectedUsersWillNoLongerHaveAccessToThisSegmentPleaseConfirmtoProceed',
                  ),
                  cancelButtonText: c('cancel'),
                  confirmButtonText: t('iUnderstandContinue'),
                  confirmCallout: shareSegmentAPICall, // will be implemented once API is ready
                  header: t('removeTheseUsers'),
                  type: 'blue',
                }}
                disabled={
                  !displayState.length || shareSegmentAPIStatus === 'pending'
                }
              >
                {c('saveChanges')}
              </Button>
            ) : (
              <Button
                styleType='primary-green'
                disabled={
                  !displayState.length || shareSegmentAPIStatus === 'pending'
                }
                onClick={() => shareSegmentAPICall()}
              >
                {c('saveChanges')}
              </Button>
            )}
          </div>
        </div>
      }
    >
      {shareSegmentAPIStatus === 'pending' ? (
        <EmptyState
          primaryText={c('hangOnForOneMoment')}
          secondaryText={t('sharingTheSegmentWithSelectedUsers')}
        />
      ) : (
        <ShareSegmentContent
          checkAll={checkAll}
          setCheckAll={setCheckAll}
          displayState={displayState}
          setDisplayState={setDisplayState}
          checkedBoxes={checkedBoxes}
          setCheckedBoxes={setCheckedBoxes}
          sharedWithUsersListAPIStatus={sharedWithUsersListAPIStatus}
          sharedWithUsersListAPILoading={sharedWithUsersListAPILoading}
          sharedSegment={sharedSegment}
        />
      )}
    </SideDrawer>
  )
}
