import React, { useEffect, useMemo, useState } from 'react'
import { useQuery } from '@tanstack/react-query'
import {
  Button,
  EmptyState,
  getApiUrlPrefix,
  ListLoading,
  SearchBar,
} from '@patterninc/react-ui'
import { useUser } from 'src/context/user-context'

import { SecureAxios } from '../../../../../../../common/services'
import {
  type SegmentType,
  type ShareSegmentUserType,
} from '../../../../../services/PriceSegmentsHelperService'
import styles from './_share_segment.module.scss'

type UserSearchBoxProps = {
  sharedSegment: SegmentType
  handleAddButton: (user: ShareSegmentUserType) => void
}

type SearchListProps = {
  userSearchResponse: ShareSegmentUserType[]
  isLoading: boolean
  handleAddButton: (user: ShareSegmentUserType) => void
}

type SearchListRowProps = {
  user: ShareSegmentUserType
  handleAddButton: (user: ShareSegmentUserType) => void
}

export const UserSearchBox = ({
  handleAddButton,
  sharedSegment,
}: UserSearchBoxProps): React.JSX.Element => {
  const [searchedUser, setSearchedUser] = useState<string>('')
  const searchUsersAPIUrl = `${getApiUrlPrefix('marketshare')}/api/v3/segments`
  const { user } = useUser()

  const { data, status, isLoading } = useQuery({
    queryKey: [searchUsersAPIUrl, sharedSegment.id, searchedUser],
    queryFn: async ({ signal }) => {
      try {
        const response = await SecureAxios.get(
          `${searchUsersAPIUrl}/${sharedSegment.id}/users`,
          {
            params: {
              page: 1,
              per_page: 10,
              segment_id: sharedSegment.id,
              user_id: user?.id,
              ...(searchedUser ? { query: searchedUser } : {}),
            },
            signal,
          },
        )
        return response?.data
      } catch (error) {
        console.error('Error searching users:', error)
        throw error
      }
    },
    enabled: !!searchedUser,
  })

  const searchResult: ShareSegmentUserType[] = useMemo(() => {
    if (status === 'success' && data?.data) {
      return data?.data
    }
    return []
  }, [data, status])

  // clearing the search text when unmounting
  useEffect(() => {
    return () => {
      setSearchedUser('')
    }
  }, [])

  return (
    <div
      className={`${styles.searchBoxContainer} ${
        searchedUser && styles.border
      }`}
    >
      <SearchBar
        value={searchedUser}
        placeholder='Search Users'
        onChange={(inputText) => {
          setSearchedUser(inputText)
        }}
      />
      {searchedUser && (
        <SearchList
          isLoading={isLoading}
          userSearchResponse={searchResult}
          handleAddButton={handleAddButton}
        />
      )}
    </div>
  )
}

const SearchList = ({
  userSearchResponse,
  isLoading,
  handleAddButton,
}: SearchListProps): React.JSX.Element => {
  return (
    <div className={styles.searchResultsContainer}>
      {isLoading ? (
        <ListLoading noSlideInUp />
      ) : userSearchResponse.length > 0 ? (
        userSearchResponse.map((user) => {
          return (
            <SearchListRow
              key={user.user_id}
              user={user}
              handleAddButton={handleAddButton}
            />
          )
        })
      ) : (
        <EmptyState primaryText='No Users Found' />
      )}
    </div>
  )
}

const SearchListRow = ({
  user,
  handleAddButton,
}: SearchListRowProps): React.JSX.Element => {
  return (
    <div className={styles.searchListRow}>
      <div className={styles.username}>
        <div>
          <span>{user?.full_user_name}</span>
        </div>
      </div>
      <div className={styles.verticalDivider} />
      <div className={styles.addButton}>
        <Button
          as='button'
          styleType='text-blue'
          onClick={() => handleAddButton(user)}
        >
          Add
        </Button>
      </div>
    </div>
  )
}
