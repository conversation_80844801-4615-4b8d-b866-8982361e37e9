.searchBoxContainer {
  border: 1px solid var(--medium-purple);
  border-radius: 4px;
  padding: 8px;
  margin-bottom: 16px;
}

.searchResultsContainer {
  display: flex;
  flex-direction: column;
  margin-top: 8px;

  .searchListRow {
    display: flex;
    flex-direction: row;
    height: 36px; // As per mock-up

    .username {
      display: flex;
      align-items: center;
      font-size: 12px;
      padding: 8px;
      width: 100%;
    }

    .verticalDivider {
      background: var(--light-gray);
      width: 1px;
    }

    .addButton {
      display: flex;
      align-items: center;
      padding: 8px 16px;
    }
  }
}

.headerCell {
  text-transform: uppercase;
  font-weight: var(--font-weight-semibold);
  color: var(--purple);
}

.searchBoxContainer {
  border: 1px solid var(--white);
  border-radius: 4px;
  padding: 8px;
  margin-bottom: 16px;
  transition: border-color 0.3s ease;
}

.border {
  border: 1px solid var(--medium-purple);
}
