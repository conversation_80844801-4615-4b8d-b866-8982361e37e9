import React, { useContext, useEffect, useMemo } from 'react'
import { useLocation, useParams } from 'react-router-dom'
import { useQuery } from '@tanstack/react-query'
import { parseParams, SecureAxios } from '@predict-services'

import { ThemeContext } from '../../../../../../Context'
import { PriceSegmentContext } from '../../../../context/PriceSegmentsContext'
import { getFetchBrandsAPI } from '../../../../services/PriceSegmentsHelperService'
import { PriceSegmentsProductsTable } from './PriceSegmentsProductsTable'

type PriceSegmentBrandProductsProps = {
  currency: {
    code: string
    symbol: string
  }
  priceRangeMin: string
  priceRangeMax: string
  segmentId: string
}

export const PriceSegmentBrandProducts = ({
  currency,
  priceRangeMin,
  priceRangeMax,
  segmentId,
}: PriceSegmentBrandProductsProps) => {
  const { updateBreadcrumbs } = useContext(ThemeContext)
  const { selectedBrand } = useContext(PriceSegmentContext)

  const { pathname, search } = useLocation()

  const brand_id = useParams<{ brand_id: string }>().brand_id as string

  const brandNameFromUrl = parseParams(search).brand_name
  const brand = useMemo(
    () => ({ id: brand_id, name: brandNameFromUrl }),
    [brandNameFromUrl, brand_id],
  )
  const { data, status } = useQuery({
    queryKey: [segmentId, brand_id, selectedBrand],
    queryFn: ({ signal }) => {
      const params = {
        search_for: brand_id,
      }
      // If no brand is selected and no brand data is available,
      // it means we're either redirected from the Price Segment Main Page
      // with a specific brand selected or redirected from the brands tables
      // with brand data stored in local storage.
      // In this case, we don't need to make an API call.
      if (!selectedBrand.id && !brand) {
        return SecureAxios.get(getFetchBrandsAPI(segmentId), { params, signal })
      }
      return null
    },
    enabled: !selectedBrand.id && !brand,
  })

  const brandName = useMemo(() => {
    if (selectedBrand.id) return selectedBrand.name
    else if (brand && brand.id === brand_id) return brand.name
    else if (
      status === 'success' &&
      data?.data &&
      data?.data?.data.length > 0
    ) {
      const brandData = data?.data?.data[0]
      return brandData.id === brand_id ? brandData.name : undefined
    }
  }, [brand, brand_id, data?.data, selectedBrand, status])
  useEffect(() => {
    if (pathname.includes('brand-products')) {
      updateBreadcrumbs({
        name: brandName,
        link: pathname,
      })
    }
  }, [brandName, pathname, updateBreadcrumbs])

  return (
    <PriceSegmentsProductsTable
      currency={currency}
      priceRangeMin={priceRangeMin}
      priceRangeMax={priceRangeMax}
      segmentId={segmentId}
      brandId={brand_id}
    />
  )
}
