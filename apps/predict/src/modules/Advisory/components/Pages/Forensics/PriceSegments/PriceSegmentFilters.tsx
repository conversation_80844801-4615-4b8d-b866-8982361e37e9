import React, {
  useCallback,
  useContext,
  useEffect,
  useRef,
  useState,
} from 'react'
import {
  Button,
  Slider,
  TextInput,
  type TextInputProps,
  toast,
  toastify,
  usePrevious,
} from '@patterninc/react-ui'
import { c, useTranslate } from '@predict-services'
import { useUser } from 'src/context/user-context'
import { PriceSegmentContext } from 'src/modules/Advisory/context/PriceSegmentsContext'
import { type PriceRangeLimitType } from 'src/modules/Advisory/services/PriceSegmentsHelperService'

import styles from './_chart_slider.module.scss'
import { PriceSegmentBrandSelector } from './PriceSegmentsBrandSelector'

export const PriceSegmentFilters = () => {
  const { t } = useTranslate('advisory')
  const {
      priceRangeSliderValue,
      updatePriceRangeSliderValue,
      updateSelectedBrand,
      priceRangeLimit,
      updatePriceRangeLimit,
      selectedSegment,
    } = useContext(PriceSegmentContext),
    [searchTermBrandSelector, setSearchTermBrandSelector] = useState('')

  const { current_currency: currency, customer } = useUser().user

  const previousCustomerId = usePrevious(customer?.id)
  const previousSegmentId = usePrevious(selectedSegment?.id)

  const [limit, setLimit] = useState<PriceRangeLimitType>(priceRangeLimit)

  const numberFormat: TextInputProps['numberFormat'] = {
    type: 'currency',
    currencySymbol: currency?.symbol,
  }

  const prevToastID = useRef<string | number>(undefined)

  const setDefaults = useCallback(() => {
    //in future the slider value should update upon the currency object that we get from backend
    updatePriceRangeSliderValue(5)
    updateSelectedBrand({ id: 0, name: 'Select Brand' })
    setSearchTermBrandSelector('')
    setLimit({ min: null, max: null })
  }, [updatePriceRangeSliderValue, updateSelectedBrand])

  const generateError = useCallback(() => {
    if (limit.max !== null && limit.min !== null) {
      if (limit.max < limit.min) {
        return t('maxPriceCannotBeLessThanMinPrice')
      } else if (limit.max === limit.min) {
        return t('maxpriceAndMinPriceCannotBeEqual')
      } else if (limit.max - limit.min < priceRangeSliderValue) {
        return t(
          'differenceOfMaxandminPriceShouldBeGreaterThanPriceRangeIncrement',
        )
      }
    }

    if (limit.max !== null && limit.max === 0) {
      return t('maxPriceCannotBeZero')
    }

    return ''
  }, [limit.max, limit.min, priceRangeSliderValue, t])

  useEffect(() => {
    const error = generateError()
    if (error) {
      toastify.dismiss(prevToastID.current)
      prevToastID.current = toast({
        type: 'error',
        message: error,
        config: {
          autoClose: 3000,
        },
      })
    }

    if (!error) {
      updatePriceRangeLimit({ min: limit.min, max: limit.max })
    }
  }, [generateError, limit.max, limit.min, updatePriceRangeLimit])

  useEffect(() => {
    if (
      (previousCustomerId && previousCustomerId !== customer?.id) ||
      (previousSegmentId && previousSegmentId !== selectedSegment?.id)
    ) {
      setDefaults()
    }
  }, [
    customer?.id,
    previousCustomerId,
    selectedSegment?.id,
    previousSegmentId,
    setDefaults,
  ])

  return (
    <div className={styles.sliderWidth}>
      <div className='flex flex-direction-column pat-p-4 pat-gap-4'>
        <div>
          <Slider
            label={t('priceRangeIncrement')}
            value={priceRangeSliderValue}
            updateValue={updatePriceRangeSliderValue}
            // hardcoding min, max for now as we only support USD. Will change this to the values from API response in future.
            min={1}
            max={100}
            debounce={500}
          />
        </div>
        <div className='flex justify-content-between pat-gap-4'>
          <TextInput
            labelText={t('minPrice')}
            type='number'
            onlyWholeNumbers
            numberFormat={numberFormat}
            placeholder='None'
            value={limit.min !== null ? limit.min : ''}
            callout={(_, value) =>
              setLimit((prev) => {
                return {
                  ...prev,
                  min: value !== '' ? Math.abs(Number(value)) : null,
                }
              })
            }
            debounce={500}
            fullWidth
          />
          <TextInput
            labelText={t('maxPrice')}
            type='number'
            onlyWholeNumbers
            numberFormat={numberFormat}
            placeholder='None'
            value={limit.max !== null ? limit.max : ''}
            callout={(_, value) =>
              setLimit((prev) => {
                return {
                  ...prev,
                  max: value !== '' ? Math.abs(Number(value)) : null,
                }
              })
            }
            debounce={500}
            fullWidth
          />
        </div>
        <PriceSegmentBrandSelector
          searchTermBrandSelector={searchTermBrandSelector}
          setSearchTermBrandSelector={setSearchTermBrandSelector}
        />
      </div>
      <div className={styles.footer}>
        <div className='pat-p-2'>
          {/* reset button */}
          <Button styleType='text-red' onClick={setDefaults}>
            {c('reset')}
          </Button>
        </div>
      </div>
    </div>
  )
}
