import React, { useCallback, useContext, useState } from 'react'
import { PriceSegmentContext } from 'src/modules/Advisory/context/PriceSegmentsContext'
import SegmentBar from 'src/modules/Insights/components/Pages/Share/MarketShare/AtAGlance/SegmentBar'
import { type SegmentProps } from 'src/modules/Insights/components/Pages/Share/MarketShare/AtAGlance/AtAGlanceTypes'
import { EmptyState, type IconStringList } from '@patterninc/react-ui'
import { haveRoles, useTranslate } from '@predict-services'
import { useUser } from 'src/context/user-context'
import { type QueryStatus } from '@tanstack/react-query'

import styles from './_chart_slider.module.scss'
import { PriceSegmentFilters } from './PriceSegmentFilters'
import { PriceSegmentsCharts } from './PriceSegmentsCharts'
import { PriceSegmentsTable } from './PriceSegmentsTable'

export const PriceSegments = (): React.JSX.Element => {
  const {
    csvExportProps,
    selectedSegment,
    updateSelectedSegment,
    updateIsSegmentLoading,
  } = useContext(PriceSegmentContext)
  const { user } = useUser()
  const { t } = useTranslate('segments')

  // To open/close segment creation side drawer
  const [segmentsSideDrawerOpen, setSegmentsSideDrawerOpen] = useState(false)
  const [showNoSegmentExp, setShowSegmentExp] = useState(false)

  const hasManageSegmentsPermission = haveRoles(user, ['write_manage_segment'])

  const noSegmentExperienceInfo = {
    secondaryText: t('clickButtonCreateNewCustomSegment'),
    icon: 'info' as IconStringList,
    buttonProps: {
      children: t('createANewSegment'),
      onClick: () => {
        setSegmentsSideDrawerOpen(true)
      },
    },
  }

  const noSegmentExperienceInfoWithNoSegmentPermission = {
    secondaryText: t('pleaseReachOutToYourPatternContactToMoveForward'),
    icon: 'info' as IconStringList,
  }

  const handleSegmentListFetchStatus = useCallback(
    (status: QueryStatus, noSegmentsFound: boolean) => {
      updateIsSegmentLoading(status === 'pending')
      setShowSegmentExp(noSegmentsFound)
    },
    [updateIsSegmentLoading],
  )

  return (
    <div className={styles.pageContainer}>
      <SegmentBar
        selectedSegment={selectedSegment as SegmentProps}
        setSelectedSegment={
          updateSelectedSegment as (segment: SegmentProps) => void
        }
        isSideDrawerOpen={segmentsSideDrawerOpen}
        setSideDrawerOpen={setSegmentsSideDrawerOpen}
        segmentListFetchStatusCallback={handleSegmentListFetchStatus}
        chartCSVExport={csvExportProps}
      />
      {showNoSegmentExp ? (
        <EmptyState
          primaryText={
            hasManageSegmentsPermission
              ? t('noAvailableSegment')
              : t('noSegmentExistsForThisCustomer')
          }
          {...(hasManageSegmentsPermission
            ? noSegmentExperienceInfo
            : noSegmentExperienceInfoWithNoSegmentPermission)}
        />
      ) : (
        <>
          <div className={styles.chartAndSliderContainer}>
            <PriceSegmentsCharts />
            <PriceSegmentFilters />
          </div>
          <PriceSegmentsTable />
        </>
      )}
    </div>
  )
}
