import React, { useContext, useMemo } from 'react'
import { useInfiniteQuery } from '@tanstack/react-query'
import { Select } from '@patterninc/react-ui'

import { SecureAxios, useTranslate } from '../../../../../../common/services'
import { PriceSegmentContext } from '../../../../context/PriceSegmentsContext'
import { getFetchBrandsAPI } from '../../../../services/PriceSegmentsHelperService'

type PriceSegmentBrandSelectorPropType = {
  searchTermBrandSelector: string
  setSearchTermBrandSelector: (value: string) => void
}

export const PriceSegmentBrandSelector = ({
  searchTermBrandSelector,
  setSearchTermBrandSelector,
}: PriceSegmentBrandSelectorPropType): React.JSX.Element => {
  const { t } = useTranslate('advisory')
  const { selectedBrand, updateSelectedBrand, selectedSegment } =
    useContext(PriceSegmentContext)

  const params = useMemo(() => {
    return {
      per_page: 20,
      ...(searchTermBrandSelector && { search_for: searchTermBrandSelector }),
    }
  }, [searchTermBrandSelector])

  const { data, status, isLoading, hasNextPage, fetchNextPage } =
    useInfiniteQuery({
      queryKey: [selectedSegment, params],
      queryFn: async ({ pageParam = 1, signal }) => {
        try {
          const response = await SecureAxios.get(
            getFetchBrandsAPI(selectedSegment.id),
            {
              signal,
              params: { page: pageParam, ...params },
            },
          )
          return response
        } catch (error) {
          console.error('Error fetching brands:', error)
          throw error
        }
      },
      initialPageParam: 1,
      getNextPageParam: (previousPage) => {
        return previousPage?.data?.pagination?.last_page
          ? undefined
          : previousPage?.data?.pagination?.next_page
      },
      enabled: !!selectedSegment.id,
    })

  //memoize api response
  const apiResponse = useMemo(() => {
    if (status === 'success' && data?.pages) {
      const flatData = data.pages.flatMap((page) => {
        return page?.data.data
      })
      return [...flatData]
    }
  }, [data?.pages, status])

  return (
    <Select
      options={apiResponse ?? []}
      labelKeyName='name'
      optionKeyName='id'
      selectedItem={selectedBrand}
      onChange={(option) => {
        updateSelectedBrand(option)
      }}
      searchBarProps={{
        showSearchBar: true,
        onChange: (value) => {
          setSearchTermBrandSelector(value)
        },
      }}
      loading={isLoading}
      scrollProps={{
        hasMore: hasNextPage,
        getData: fetchNextPage,
      }}
      noOptionsMessage={t('noBrandData')}
    />
  )
}
