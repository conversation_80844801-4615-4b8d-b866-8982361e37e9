import React, { useCallback, useContext, useMemo, useState } from 'react'
import { Link } from 'react-router-dom'
import { useInfiniteQuery } from '@tanstack/react-query'
import {
  Button,
  CsvExport,
  SearchBar,
  type SortColumnProps,
  standardSortParams,
  StandardTable,
} from '@patterninc/react-ui'

import {
  c,
  type DownloadOptionsType,
  SecureAxios,
  useTranslate,
} from '../../../../../../common/services'
import { csvToastCallout } from '../../../../../../common/services/CsvHelpers'
import { ThemeContext } from '../../../../../../Context'
import {
  getAllBrandsTableAPI,
  priceRangeLabel,
  PriceSegmentsCommonHeaders,
  type PriceSegmentsCommonObject,
} from '../../../../services/PriceSegmentsHelperService'

type PriceSegmentsBrandsTableProps = {
  currency: {
    code: string
    symbol: string
  }
  priceRangeMin: string
  priceRangeMax: string
  segmentId: string
}

export const PriceSegmentsBrandsTable = ({
  currency,
  priceRangeMin,
  priceRangeMax,
  segmentId,
}: PriceSegmentsBrandsTableProps): React.JSX.Element => {
  const { t } = useTranslate('advisory')
  const { timeframe, startDate, endDate, selectedComparisonPeriod } =
    useContext(ThemeContext)

  const [sortBy, setSortBy] = useState({
    prop: 'sales_current_period',
    flip: false,
  })

  // Callback function to set sort column from the StandardTable
  const sort: SortColumnProps['sorter'] = (sortObj) => {
    setSortBy({
      prop: sortObj.activeColumn,
      flip: sortObj.direction,
    })
  }

  const [searchTerm, setSearchTerm] = useState('')

  const searchInputHandler = (inputText: string) => {
    setSearchTerm(inputText)
  }

  const priceRangeString = useMemo(() => {
    return priceRangeLabel(currency, priceRangeMin, priceRangeMax)
  }, [currency, priceRangeMax, priceRangeMin])

  const config = [
    { ...PriceSegmentsCommonHeaders.brand, mainColumn: true },
    {
      ...PriceSegmentsCommonHeaders.sales,
      tooltip: {
        content: t('brandsSalesColumnTooltip'),
      },
    },
    {
      ...PriceSegmentsCommonHeaders.pct_of_total_sales,
      tooltip: {
        content: t('totalSalesTooltip'),
      },
    },
    {
      ...PriceSegmentsCommonHeaders.units,
      tooltip: {
        content: t('unitsTooltip'),
      },
    },
    {
      ...PriceSegmentsCommonHeaders.pct_of_total_units,
      tooltip: {
        content: t('totalUnitsTooltip'),
      },
    },
    {
      name: '',
      label: '',
      noSort: true,
      isButton: true,
      cell: {
        children: (data: PriceSegmentsCommonObject) => {
          const params = new URLSearchParams()
          params.append('brand_name', data?.brand_name)
          const link = `brand-products/${data?.brand_id}?${params.toString()}`
          return (
            <Button as='link' routerComponent={Link} to={link}>
              {c('viewProducts')}
            </Button>
          )
        },
      },
    },
  ]

  const tableParams = useMemo(() => {
    return {
      price_range_min: priceRangeMin,
      price_range_max: priceRangeMax,
      currency_code: currency.code,
      sort: standardSortParams(sortBy, ['brand_name']),
      ...(searchTerm !== '' && { search_for: searchTerm }),
      start_date: startDate,
      end_date: endDate,
      timeframe: timeframe.type,
      comparison_period: selectedComparisonPeriod.value,
    }
  }, [
    priceRangeMin,
    priceRangeMax,
    currency.code,
    sortBy,
    searchTerm,
    startDate,
    endDate,
    timeframe.type,
    selectedComparisonPeriod.value,
  ])

  const {
    data: paginatedData,
    fetchNextPage,
    hasNextPage,
    status,
    isLoading,
  } = useInfiniteQuery({
    queryKey: ['price_segments_brands_table', tableParams, segmentId],
    queryFn: ({ pageParam = 1, signal }) => {
      const params = {
        page: pageParam,
        per_page: 20,
        ...tableParams,
      }
      return SecureAxios.get(getAllBrandsTableAPI(segmentId), {
        params,
        signal,
      })
    },
    initialPageParam: 1,
    getNextPageParam: (previousResp) => {
      return previousResp?.data?.pagination?.last_page
        ? undefined
        : previousResp?.data?.pagination?.next_page
    },
  })

  const tableData: PriceSegmentsCommonObject[] = useMemo(() => {
    if (status === 'success' && paginatedData?.pages) {
      const data = paginatedData.pages.flatMap((page) => {
        return page?.data.data
      })

      return [...data]
    }

    return []
  }, [paginatedData?.pages, status])

  // To get the total brand count
  const brandsCount = useMemo(() => {
    if (status === 'success' && paginatedData?.pages) {
      return paginatedData?.pages[0]?.data?.pagination?.count
    }

    return 0
  }, [paginatedData?.pages, status])

  // CSV Download
  const exportCSV = useCallback(
    (params: unknown) => {
      return SecureAxios.get(getAllBrandsTableAPI(segmentId), { params }).then(
        (response) => response.data,
      )
    },
    [segmentId],
  )

  const csvDownloadOptions: DownloadOptionsType = [
    {
      linkName: 'Price Segments Brands Report',
      csvName: t('priceSegmentsBrandsReport'),
      csvFormat: {
        asyncDownload: true,
        api: exportCSV,
        params: {
          ...tableParams,
          async: true,
        },
        callout: csvToastCallout(),
      },
    },
  ]

  return (
    <>
      <div className=' flex flex-direction-column pat-gap-1 pat-pt-4'>
        <div className='fs-16 fw-medium'>
          {t('priceSegRange', { range: priceRangeString })}
        </div>
        <div className='fs-12 fw-regular'>
          {t('priceRangeTooltipForBrand', {
            priceRangeString: priceRangeString,
          })}
        </div>
      </div>
      <div className='flex pat-py-4 justify-content-between align-items-center'>
        <SearchBar
          value={searchTerm}
          onChange={searchInputHandler}
          placeholder={t('searchBarPlaceHolderForBrands', {
            brandsCount: brandsCount,
          })}
        />
        {tableData.length > 0 && (
          <CsvExport
            csvDownloadOptions={csvDownloadOptions}
            initialDisplay
            show
          />
        )}
      </div>
      <StandardTable
        data={tableData}
        config={config}
        dataKey={'brand_id'}
        hasData={tableData.length > 0}
        hasMore={!!(status === 'success' && hasNextPage)}
        successStatus={status === 'success'}
        loading={isLoading}
        tableId={'price_segments_brands_table'}
        noDataFields={{
          icon: 'info',
          primaryText: c('noDataFound'),
          secondaryText: t('noDataDisplayForPriceSegment'),
        }}
        sort={sort}
        sortBy={sortBy}
        getData={fetchNextPage}
        stickyTableConfig={{ right: 1 }}
      />
    </>
  )
}
