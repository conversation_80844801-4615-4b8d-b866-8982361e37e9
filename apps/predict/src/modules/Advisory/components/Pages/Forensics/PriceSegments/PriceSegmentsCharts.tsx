import React, { useContext, useMemo } from 'react'
import { useQuery } from '@tanstack/react-query'
import {
  Area,
  Bar,
  CartesianGrid,
  ComposedChart,
  ResponsiveContainer,
  Tooltip,
  type TooltipProps,
  XAxis,
  YAxis,
} from 'recharts'
import {
  type NameType,
  type ValueType,
} from 'recharts/types/component/DefaultTooltipContent'
import {
  Ellipsis,
  EmptyState,
  GraphLoading,
  useWindowSize,
} from '@patterninc/react-ui'
import { useUser } from 'src/context/user-context'

import { abbreviateNumber } from '../../../../../../common/components/Charts/Graphs/GraphHelperService'
import { c, SecureAxios, useTranslate } from '../../../../../../common/services'
import { ThemeContext } from '../../../../../../Context'
import { PriceSegmentContext } from '../../../../context/PriceSegmentsContext'
import {
  formattedCurrencyLabel,
  getPriceSegmentsAPI,
  priceRangeLabel,
  type PriceSegmentsCommonObject,
} from '../../../../services/PriceSegmentsHelperService'
import styles from './_chart_slider.module.scss'

const CustomPriceSegmentTooltip = ({
  active,
  payload,
  label,
}: TooltipProps<ValueType, NameType>) => {
  const { t } = useTranslate('advisory')
  if (active && payload && payload.length > 0)
    return (
      <div className='basic-tooltip'>
        <div className='stat-box justify-content-between'>
          <div className='stat-container pat-my-2 flex-direction-column justify-content-between graph-legend'>
            <div className='top-section flex'>
              <div className='pat-pb-4 fc-dark-purple'>
                <div className='fs-12 fw-bold'>{`Price Range: ${label}`}</div>
              </div>
            </div>
            <div className={styles.metricContainer}>
              {/* header */}
              <div className={`${styles.rows} ${styles.header}`}>
                <span>{t('metric')}</span>
                <span>{t('total')}</span>
                <span>{t('%OfTotal')}</span>
              </div>
              {/* sales row */}
              <div className={`bdrb bdrc-medium-purple ${styles.rows}`}>
                <div className='flex'>
                  <div
                    className={`bgc-chart-light-4-blue ${styles.graphLegend}`}
                  />
                  <span className={`pat-px-2 ${styles.graphLegendValue}`}>
                    {t('estRevenue')}
                  </span>
                </div>
                <span className='fc-dark-purple fs-12 align-self-center'>
                  $
                  {abbreviateNumber(
                    payload[0].payload.sales_current_period.toFixed(2),
                  ).toLocaleString()}
                </span>
                <span className='fc-dark-purple fs-12 align-self-center'>
                  {payload[0].payload.pct_total_sales_current_period < 0.0001
                    ? '< 0.01%'
                    : `${(
                        payload[0].payload.pct_total_sales_current_period * 100
                      ).toFixed(2)}%`}
                </span>
              </div>
              {/* Units row */}
              <div className={`bdrb bdrc-medium-purple ${styles.rows}`}>
                <div className='flex'>
                  <div
                    className={`bgc-chart-standard-pink ${styles.graphLegend}`}
                  />
                  <span className={`pat-px-2 ${styles.graphLegendValue}`}>
                    {t('estUnits')}
                  </span>
                </div>
                <span className='fc-dark-purple fs-12 align-self-center'>
                  {abbreviateNumber(
                    payload[0].payload.units_current_period.toFixed(2),
                  ).toLocaleString()}
                </span>
                <span className='fc-dark-purple fs-12 align-self-center'>
                  {payload[0].payload.pct_total_units_current_period < 0.0001
                    ? '< 0.01%'
                    : `${(
                        payload[0].payload.pct_total_units_current_period * 100
                      ).toFixed(2)}%`}
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>
    )
  return null
}

export const PriceSegmentsCharts = (): React.JSX.Element => {
  const { t } = useTranslate('advisory')
  const {
    priceRangeSliderValue,
    selectedBrand,
    selectedSegment,
    isSegmentLoading,
    priceRangeLimit,
  } = useContext(PriceSegmentContext)

  const { timeframe, startDate, endDate, selectedComparisonPeriod } =
    useContext(ThemeContext)
  const currency = useUser().user?.current_currency
  const customer_id = useUser().user?.customer?.id
  const { width: windowWidth } = useWindowSize()
  const defaultBarSize = 10
  const increaseBarSize = 20

  // API Params
  const chartParams = useMemo(() => {
    return {
      price_range_increment: priceRangeSliderValue,
      currency_code: currency?.code,
      start_date: startDate,
      end_date: endDate,
      customer_id: customer_id,
      timeframe: timeframe.type,
      comparison_period: selectedComparisonPeriod.value,
      ...(selectedBrand.id !== 0 && { brand_id: selectedBrand.id }),
      ...(priceRangeLimit.max !== null &&
        priceRangeLimit.max !== 0 && {
          price_range_upper_limit: priceRangeLimit.max,
        }),
      price_range_lower_limit: priceRangeLimit.min ?? 0,
    }
  }, [
    priceRangeSliderValue,
    currency?.code,
    startDate,
    endDate,
    customer_id,
    timeframe.type,
    selectedComparisonPeriod.value,
    selectedBrand.id,
    priceRangeLimit.max,
    priceRangeLimit.min,
  ])

  const {
    data: apiResponse,
    isLoading,
    status,
  } = useQuery({
    queryKey: [chartParams, selectedSegment],
    queryFn: async ({ signal }) => {
      try {
        const response = await SecureAxios.get(
          getPriceSegmentsAPI(selectedSegment.id),
          {
            params: chartParams,
            signal,
          },
        )
        return response.data
      } catch (error) {
        console.error('Error fetching price segments chart data:', error)
        throw error
      }
    },
    enabled: !!selectedSegment.id,
  })

  const chartData = useMemo(() => {
    if (status === 'success' && apiResponse?.data) {
      return apiResponse?.data?.map((data: PriceSegmentsCommonObject) => {
        return {
          ...data,
          label: priceRangeLabel(
            {
              code: data.currency_code,
              symbol: data.currency_symbol,
            },
            data.price_range_min,
            data.price_range_max,
          ),
        }
      })
    }
  }, [apiResponse?.data, status])

  // getting max number of bars that can be adjusted with change in width of chart
  const numberOfBarsToAdjustedWidth = windowWidth ? windowWidth / 36 : 0

  const barSize =
    chartData && numberOfBarsToAdjustedWidth
      ? chartData.length > numberOfBarsToAdjustedWidth
        ? defaultBarSize
        : (numberOfBarsToAdjustedWidth + 1 - (chartData.length ?? 0)) *
          increaseBarSize
      : defaultBarSize

  return (
    <div className={styles.chartWidth}>
      <div className={styles.chartContainer}>
        {isLoading || isSegmentLoading ? (
          <GraphLoading height={300} />
        ) : apiResponse?.data?.length > 0 ? (
          <ResponsiveContainer height={300} width='100%'>
            <ComposedChart data={chartData}>
              <XAxis dataKey={'label'} fontSize={10} tickLine={false} />
              <YAxis
                yAxisId={'Est Revenue'}
                axisLine={false}
                fontSize={10}
                tickLine={false}
                tickFormatter={(tick) =>
                  formattedCurrencyLabel(
                    apiResponse?.currency,
                    abbreviateNumber(tick),
                  )
                }
              />
              <YAxis
                yAxisId={'Est Units'}
                orientation='right'
                axisLine={false}
                fontSize={10}
                tickLine={false}
                tickFormatter={(tick) =>
                  abbreviateNumber(tick).toLocaleString()
                }
              />
              <Area
                yAxisId={'Est Revenue'}
                dataKey='sales_current_period'
                fill='var(--chart-light-4-blue)'
                fillOpacity={1}
                stroke='none'
              />
              <CartesianGrid vertical={false} stroke='var(--medium-purple)' />
              <Bar
                yAxisId={'Est Units'}
                dataKey='units_current_period'
                barSize={barSize}
                fill='var(--chart-standard-pink)'
              />
              <Tooltip content={CustomPriceSegmentTooltip} />
            </ComposedChart>
          </ResponsiveContainer>
        ) : (
          <div className={styles.chartEmptyState}>
            <EmptyState
              primaryText={c('noDataFound')}
              secondaryText={t('thereIsNoDataToDisplayForthisSegment')}
            />
          </div>
        )}
        {/* custom legend code */}
        <div className='graph-legend flex pat-py-4 justify-content-center'>
          <div className={`legend-single-stat no-stat flex`}>
            <div className='line bgc-chart-light-4-blue' />
            <div className='text'>
              {isLoading || isSegmentLoading ? (
                <span>
                  {c('fetchingData')}
                  <Ellipsis />
                </span>
              ) : apiResponse?.data ? (
                <span>{t('estRevenue')}</span>
              ) : (
                <span>{t('noData')}</span>
              )}
            </div>
          </div>
          <div className={`legend-single-stat no-stat flex`}>
            <div className='line bgc-chart-standard-pink' />
            <div className='text'>
              {isLoading || isSegmentLoading ? (
                <span>
                  {c('fetchingData')}
                  <Ellipsis />
                </span>
              ) : apiResponse?.data ? (
                <span>{t('estUnits')}</span>
              ) : (
                <span>{t('noData')}</span>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
