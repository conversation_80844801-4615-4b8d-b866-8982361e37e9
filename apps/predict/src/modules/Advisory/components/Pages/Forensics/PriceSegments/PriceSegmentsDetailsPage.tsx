import React, { useContext, useEffect } from 'react'
import {
  Navigate,
  Route,
  Routes,
  useLocation,
  useParams,
} from 'react-router-dom'
import { Tabs } from '@patterninc/react-ui'
import { c, useTranslate } from '@predict-services'
import { useUser } from 'src/context/user-context'

import { useBrandChangeRedirect } from '../../../../../../common/hooks/useBrandChange'
import { ThemeContext } from '../../../../../../Context'
import { priceRangeLabel } from '../../../../services/PriceSegmentsHelperService'
import { PriceSegmentBrandProducts } from './PriceSegmentBrandProducts'
import { PriceSegmentsBrandsTable } from './PriceSegmentsBrandsTable'
import { PriceSegmentsProductsTable } from './PriceSegmentsProductsTable'

export const PriceSegmentsDetailsPage = () => {
  const { t } = useTranslate('advisory')
  const { updateBreadcrumbs } = useContext(ThemeContext)
  const currency = useUser().user?.current_currency
  const pathname = useLocation().pathname

  const { segment_id = '', range = '' } = useParams<{
    segment_id: string
    range: string
  }>()

  // extracting the min and max range value from param range which looks like "range_min-range_max"
  const [range_min, range_max] = range?.split('-') ?? []

  useEffect(() => {
    if (!pathname.includes('brand-products')) {
      updateBreadcrumbs({
        name: `${priceRangeLabel(currency, range_min, range_max)} ${t('priceRange')}`,
        link: pathname,
      })
    }
  }, [currency, pathname, range_max, range_min, t, updateBreadcrumbs])

  // Will Redirect back to Price Segment Main page, if brand is changed from Brand Selector on this page/ route
  useBrandChangeRedirect()

  return (
    <Routes>
      <Route
        path='brand-products/:brand_id/*'
        element={
          <PriceSegmentBrandProducts
            currency={currency}
            priceRangeMin={range_min}
            priceRangeMax={range_max}
            segmentId={segment_id}
          />
        }
      />
      <Route
        path=''
        element={
          <Tabs
            tabs={[
              {
                id: 0,
                tabName: c('brands'),
                content: (
                  <PriceSegmentsBrandsTable
                    currency={currency}
                    priceRangeMin={range_min}
                    priceRangeMax={range_max}
                    segmentId={segment_id}
                  />
                ),
              },
              {
                id: 1,
                tabName: c('products'),
                content: (
                  <PriceSegmentsProductsTable
                    currency={currency}
                    priceRangeMin={range_min}
                    priceRangeMax={range_max}
                    segmentId={segment_id}
                  />
                ),
              },
            ]}
          />
        }
      />
      <Route path='*' element={<Navigate to={'/advisory'} replace />} />
    </Routes>
  )
}
