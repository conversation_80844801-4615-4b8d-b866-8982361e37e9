import React, { useContext, useEffect, useMemo, useState } from 'react'
import { useInfiniteQuery, useQuery } from '@tanstack/react-query'
import {
  Button,
  CsvExport,
  EmptyState,
  getApiUrlPrefix,
  Pill,
  Select,
  SideDrawer,
  toast,
  Tooltip,
  useIsMobileView,
} from '@patterninc/react-ui'
import { c, SecureAxios, useTranslate } from '@predict-services'

import { useUser } from '../../../../../../context/user-context'
import { PriceSegmentContext } from '../../../../context/PriceSegmentsContext'
import {
  type BrandsListProps,
  defaultSegment,
  type SegmentType,
  type SelectedBrandProps,
} from '../../../../services/PriceSegmentsHelperService'
import styles from './_price_segments.module.scss'
import { CreateOrEditSegment } from './ManageSegments/CreateOrEditSegment'
import { SegmentListTable } from './ManageSegments/SegmentListTable'
import { ShareSegmentSideDrawer } from './ManageSegments/ShareSegmentSideDrawer'
import { SegmentPillsListTooltip } from './SegmentPillsListTooltip'

type ApiResponse = {
  data: BrandsListProps[] | undefined
  pagination: {
    next_page: number
    last_page: number
    count: string
  }
}

export const PriceSegmentsHeader = (): React.JSX.Element => {
  const { t } = useTranslate('advisory')
  const isMobileView = useIsMobileView()

  const segmentsURL = `${getApiUrlPrefix('marketshare')}/api/v3/segments`
  const { user } = useUser()
  const customer = user.customer
  const { selectedSegment, updateSelectedSegment, updateIsSegmentLoading } =
      useContext(PriceSegmentContext),
    [isSideDrawerOpen, setIsSideDrawerOpen] = useState(false),
    [isEditOrCreateSideDrawerOpen, setIsEditOrCreateSideDrawerOpen] =
      useState(false),
    [isCreateSegment, setIsCreateSegment] = useState(false),
    [isDeletingSegment, setIsDeletingSegment] = useState(false),
    [isActionPerformed, setIsActionPerformed] = useState(false),
    [defaultChanged, setDefaultChanged] = useState(false),
    [stepCounter, setStepCounter] = useState(1),
    [generalInfo, setGeneralInfo] = useState(defaultSegment),
    [selectedBrandsList] = useState<SelectedBrandProps>({
      selectedBrandNames: generalInfo.brands,
      selectedbrandIds: generalInfo.brand_ids,
    }),
    [isShareSegmentSideDrawerOpen, setIsShareSegmentSideDrawerOpen] =
      useState(false)
  const [sharedSegment, setSharedSegment] =
    useState<SegmentType>(defaultSegment)

  const [checkAllSegBrands, setCheckAllSegBrands] = useState(true)
  const [uncheckedBrandIds, setUncheckedBrandIds] = useState<number[]>([])
  const [selectedBrandIds, setSelectedBrandIds] = useState<Array<number>>([])
  const [brandsSearchText, setBrandsSearchText] = useState('')
  const [isCategoryClearFiltersClicked, setIsCategoryClearFiltersClicked] =
    useState(false)
  const isLastSelectAllChecked = localStorage.getItem('lastSelectAllChecked')

  // api url to fetch brands
  const brandsURL = `${getApiUrlPrefix('marketshare')}/api/v3/brands/filter`
  const isAdvisoryUser = user.group_name === 'Advisory' // this is a hot fix. later this will be replaced by a permission from BE (write_manage_segment)

  //parameters for api
  const apiParams = useMemo(() => {
    return {
      customer_id: customer.id,
      user_id: user.id,
    }
  }, [customer, user])

  //api call to get segments
  const { data, isLoading, isFetching } = useQuery({
    queryKey: [apiParams, defaultChanged],
    queryFn: ({ signal }) =>
      SecureAxios.get(segmentsURL, { signal, params: apiParams }).then(
        (resp) => {
          setDefaultChanged(false)
          return resp?.data
        },
      ),
  })
  //api call to get category and brand data
  const {
    data: categoriesAndBrandsApiResponse,
    isLoading: categoriesAndBrandsApiIsLoading,
  } = useQuery({
    queryKey: [selectedSegment?.id, defaultChanged],
    queryFn: async ({ signal }) => {
      try {
        const response = await SecureAxios.get(
          `${getApiUrlPrefix('marketshare')}/api/v3/segments/${selectedSegment?.id}`,
          {
            signal,
            params: {
              page: 1,
              per_page: 20,
            },
          },
        )
        setDefaultChanged(false)
        return response?.data
      } catch (error) {
        console.error('Error fetching categories and brands:', error)
        throw error
      }
    },
    enabled: !!selectedSegment?.id,
  })

  // api call to get brands
  const {
    status,
    error,
    data: brandsListApiResponse,
    fetchNextPage: fetchBrandsNextPage,
    hasNextPage: hasBrandsNextPage,
  } = useInfiniteQuery({
    queryKey: [
      brandsURL,
      user.current_currency.code,
      stepCounter,
      generalInfo.category_ids,
      brandsSearchText,
    ],
    queryFn: async ({ pageParam = 1 }) => {
      if (stepCounter !== 3) {
        return {
          data: [],
          pagination: { next_page: 0, last_page: 0, count: '0' },
        }
      }
      try {
        const body = {
          category_ids: generalInfo?.category_ids,
          page: pageParam,
          per_page: 20,
          currency_code: user?.current_currency?.code,
          ...(brandsSearchText ? { search_term: brandsSearchText } : {}),
        }
        const response = await SecureAxios.post(brandsURL, body)
        return response?.data
      } catch (error) {
        console.error('Error fetching brands list:', error)
        throw error
      }
    },
    initialPageParam: 1,
    getNextPageParam: (previousPage: ApiResponse) => {
      return previousPage?.pagination?.last_page
        ? undefined
        : previousPage?.pagination?.next_page
    },
  })

  const brandListMetaData = useMemo(() => {
    const totalBrandsCount = brandsListApiResponse
      ? parseInt(brandsListApiResponse?.pages[0]?.pagination.count)
      : 0
    const brandListData: BrandsListProps[] = []

    brandsListApiResponse?.pages?.forEach((page) => {
      return page?.data?.forEach((brand: BrandsListProps) => {
        /**
         * The current logic of the brand list checkbox is the default select all the brands and API is sending is_checked: true for all brands so when user uncheck all brands,
         * select few brands and then scroll down to lazy load more brands then below if code executes to make the rest of all upcoming brands default unchecked.
         */
        if (
          isLastSelectAllChecked === 'false' &&
          !selectedBrandIds.includes(brand.id) &&
          uncheckedBrandIds.length > 0
        ) {
          brand.is_checked = false
        }
        brandListData.push(brand)
      })
    })
    return {
      brands: brandListData,
      status,
      fetchNextPage: fetchBrandsNextPage,
      hasNextPage: hasBrandsNextPage,
      error,
      totalBrandsCount: totalBrandsCount,
    }
  }, [
    brandsListApiResponse,
    error,
    fetchBrandsNextPage,
    hasBrandsNextPage,
    isLastSelectAllChecked,
    selectedBrandIds,
    status,
    uncheckedBrandIds.length,
  ])

  const checkedBrandLength =
    brandListMetaData?.brands?.length - uncheckedBrandIds?.length
  const uncheckedBrandLength =
    brandListMetaData?.brands?.length - selectedBrandIds?.length

  //api params for segments api url
  const createOrUpdateSegApiParams = useMemo(() => {
    return {
      customer_id: user?.customer?.id,
      name: generalInfo?.name?.toString(),
      description: generalInfo?.description?.toString(),
      is_default: generalInfo?.is_default,
      created_by_user_id: user?.id,
      brand_ids: selectedBrandsList?.selectedbrandIds,
      category_ids: generalInfo?.category_ids,
      ...(checkAllSegBrands &&
      (uncheckedBrandLength === brandListMetaData?.brands?.length ||
        checkedBrandLength === brandListMetaData?.brands?.length)
        ? { check_all: true }
        : checkedBrandLength < uncheckedBrandLength ||
            checkedBrandLength === uncheckedBrandLength
          ? { selected_brands: selectedBrandIds }
          : uncheckedBrandLength < checkedBrandLength
            ? { unselected_brands: uncheckedBrandIds }
            : {}),
    }
  }, [
    brandListMetaData?.brands?.length,
    checkAllSegBrands,
    checkedBrandLength,
    generalInfo?.category_ids,
    generalInfo?.description,
    generalInfo?.is_default,
    generalInfo?.name,
    selectedBrandIds,
    selectedBrandsList?.selectedbrandIds,
    uncheckedBrandIds,
    uncheckedBrandLength,
    user?.customer?.id,
    user?.id,
  ])

  // handler for next button in SideBar for Create or Edit
  const nextButtonHandler = () => {
    setStepCounter((prev) => prev + 1)

    if (stepCounter === 3) {
      setIsActionPerformed(true)
      setIsEditOrCreateSideDrawerOpen(false)
      setStepCounter(1)
      setIsSideDrawerOpen(true)
      if (isCreateSegment) {
        SecureAxios.post(segmentsURL, createOrUpdateSegApiParams)
          .then(() => {
            setIsActionPerformed(false)
            toast({ message: t('segmentCreatedSuccessfully'), type: 'success' })
            setDefaultChanged(true)
          })
          .catch(() => {
            setIsActionPerformed(false)
            toast({
              message: t('thereWasAProblemCreatingtheSegment'),
              type: 'error',
            })
          })
      } else {
        SecureAxios.put(
          `${segmentsURL}/${generalInfo?.id}`,
          createOrUpdateSegApiParams,
        )
          .then(() => {
            setDefaultChanged(true)
            setIsActionPerformed(false)
            toast({ message: t('segmentUpdatedSuccessfully'), type: 'success' })
            setDefaultChanged(true)
          })
          .catch(() => {
            setIsActionPerformed(false)
            toast({
              message: t('thereWasaProblemUpdatingtheSegment'),
              type: 'error',
            })
          })
      }
    }
  }

  //memoize api response
  const segmentList = useMemo(() => {
    return data?.data
  }, [data?.data])

  const categoriesAndBrandsData = useMemo(() => {
    return categoriesAndBrandsApiResponse?.data[0]
  }, [categoriesAndBrandsApiResponse?.data])

  //set selected segment
  useEffect(() => {
    const presentSegment = segmentList?.filter(
      (element: SegmentType) => element?.id === selectedSegment?.id,
    )
    if (presentSegment?.length === 0 && segmentList) {
      updateSelectedSegment(segmentList[0])
    }
  }, [segmentList, selectedSegment, updateSelectedSegment])

  //to set selectedSegment to default segment while data is loading. otherwise it shows previous selectedSegment until new data is fetched
  useEffect(() => {
    if (isLoading) updateSelectedSegment(defaultSegment)
  }, [isLoading, updateSelectedSegment])

  //to set isSegmentLoading, this is going to be used in chart's and table's component along with their own isLoading states to display LoadState
  useEffect(() => {
    updateIsSegmentLoading(isLoading)
  }, [isLoading, updateIsSegmentLoading])

  //memoized function to check if save/edit button is disabled
  const isSaveOrNextBtnDisabled = useMemo(() => {
    if (stepCounter === 3) {
      return (
        selectedBrandIds?.length === 0 ||
        generalInfo?.category_ids?.length === 0
      )
    }

    if (stepCounter === 2) {
      return generalInfo?.category_ids?.length === 0
    }

    return !generalInfo?.name
  }, [
    generalInfo?.category_ids?.length,
    generalInfo?.name,
    selectedBrandIds?.length,
    stepCounter,
  ])

  return (
    <div className={styles.segmentBoxContainer}>
      <div className={styles.segmentsListAndTags}>
        <div
          className={`${isMobileView ? 'pat-py-2' : 'flex align-items-center'}`}
        >
          {!isMobileView && <span className='fs-14'>{t('segment')}</span>}
          {/* To display Segment List */}
          <span className={`${styles.segmentDropDown}`}>
            <Select
              {...(isMobileView ? { labelProps: { label: t('segment') } } : {})}
              searchBarProps={{
                showSearchBar: true,
              }}
              options={segmentList ?? []}
              optionKeyName='id'
              labelKeyName='name'
              selectedItem={selectedSegment ?? defaultSegment}
              onChange={(option: SegmentType) => {
                if (option?.id) {
                  updateSelectedSegment?.(option)
                }
              }}
              loading={isLoading}
            />
          </span>
        </div>
        {/* to show no. of categories and brands */}
        {/* tooltip for categories*/}
        <div className='flex justify-content-between'>
          <span className='flex flex-direction-row pat-gap-4'>
            <Tooltip
              position='bottom'
              tooltipContent={
                <SegmentPillsListTooltip
                  data={categoriesAndBrandsData?.categories ?? []}
                  label={t('categoriesAndBrandName', {
                    name: categoriesAndBrandsData?.name,
                  })}
                  dataLimit={10}
                  totalCount={categoriesAndBrandsData?.category_count}
                />
              }
            >
              <div className='flex align-items-center pat-gap-1'>
                <span className='fc-dark-purple fs-12'>{t('categories')}</span>
                <Pill
                  color='blue'
                  number={
                    categoriesAndBrandsData?.name
                      ? categoriesAndBrandsData?.category_count
                      : 0
                  }
                  loading={categoriesAndBrandsApiIsLoading || isLoading}
                />
              </div>
            </Tooltip>

            {/* tooltip fo brands*/}
            <Tooltip
              position='bottom'
              tooltipContent={
                <SegmentPillsListTooltip
                  data={categoriesAndBrandsData?.brands ?? []}
                  label={t('categoriesAndBrand', {
                    name: categoriesAndBrandsData?.name,
                  })}
                  dataLimit={10}
                  totalCount={categoriesAndBrandsData?.brand_count}
                />
              }
            >
              <div className='flex align-items-center pat-gap-1'>
                <span className='fc-dark-purple fs-12'>{c('brands')}:</span>
                <Pill
                  color='blue'
                  number={
                    categoriesAndBrandsData?.name
                      ? categoriesAndBrandsData?.brand_count
                      : 0
                  }
                  loading={categoriesAndBrandsApiIsLoading || isLoading}
                />
              </div>
            </Tooltip>
          </span>
          {isMobileView && !isAdvisoryUser && (
            <SegmentHeaderButtons setIsSideDrawerOpen={setIsSideDrawerOpen} />
          )}
        </div>
      </div>
      {!isMobileView && !isAdvisoryUser && (
        <SegmentHeaderButtons setIsSideDrawerOpen={setIsSideDrawerOpen} />
      )}

      {/* MANAGE SAVED SEGMENTS SIDEBAR */}
      <SideDrawer
        isOpen={isSideDrawerOpen}
        closeCallout={() => setIsSideDrawerOpen(false)}
        headerContent={t('manageSavedSegments')}
        footerContent={
          <div className='flex justify-content-end align-items-center pat-gap-4'>
            <Button
              as='button'
              styleType='secondary'
              onClick={() => setIsSideDrawerOpen(false)}
            >
              {c('close')}
            </Button>
            <Button
              styleType='primary-green'
              onClick={() => {
                setStepCounter(1)
                setGeneralInfo(defaultSegment)
                setIsEditOrCreateSideDrawerOpen(true)
                setIsCreateSegment(true)
              }}
            >
              {t('createNewSegment')}
            </Button>
          </div>
        }
        size='md'
      >
        {isActionPerformed ? (
          <EmptyState
            primaryText={c('hangOnForOneMoment')}
            secondaryText={
              isDeletingSegment
                ? t('deletingtheSegment')
                : isCreateSegment
                  ? t('creatingtheSegment')
                  : t('updatingtheSegment')
            }
          />
        ) : (
          <SegmentListTable
            segmentList={segmentList}
            isLoading={isFetching}
            setDefaultChanged={setDefaultChanged}
            setGeneralInfo={setGeneralInfo}
            setIsEditOrCreateSideDrawerOpen={setIsEditOrCreateSideDrawerOpen}
            setIsShareSegmentSideDrawerOpen={setIsShareSegmentSideDrawerOpen}
            setSharedSegment={setSharedSegment}
            setIsCreateSegment={setIsCreateSegment}
            setStepCounter={setStepCounter}
            setIsActionPerformed={setIsActionPerformed}
            setIsDeletingSegment={setIsDeletingSegment}
          />
        )}
      </SideDrawer>

      {/* Create Or Edit Segments SideDrawer */}
      <SideDrawer
        isOpen={isEditOrCreateSideDrawerOpen}
        closeCallout={() => setIsEditOrCreateSideDrawerOpen(false)}
        headerContent={isCreateSegment ? t('createSegments') : t('editSegment')}
        size='md'
        layerPosition={2}
        footerContent={
          <div className='flex justify-content-between align-items-center'>
            <Button
              styleType='text-blue'
              disabled={false}
              onClick={() => {
                setGeneralInfo(defaultSegment)
                setIsCategoryClearFiltersClicked(true)
              }}
            >
              {t('clearFilters')}
            </Button>

            <div>
              <Button
                as='button'
                className='pat-mr-4'
                onClick={() => {
                  setIsEditOrCreateSideDrawerOpen(false)
                }}
              >
                {c('cancel')}
              </Button>
              <Button
                as='button'
                styleType='primary-green'
                onClick={nextButtonHandler}
                disabled={isSaveOrNextBtnDisabled}
              >
                {stepCounter === 3
                  ? isCreateSegment
                    ? t('createSegments')
                    : c('save')
                  : c('next')}
              </Button>
            </div>
          </div>
        }
      >
        <CreateOrEditSegment
          generalInfo={generalInfo}
          setGeneralInfo={setGeneralInfo}
          stepCounter={stepCounter}
          setStepCounter={setStepCounter}
          brandsListMetadata={brandListMetaData}
          brandsSearchText={brandsSearchText}
          fetchNextPage={fetchBrandsNextPage}
          selectedBrandIds={selectedBrandIds}
          setSelectedBrandIds={setSelectedBrandIds}
          setBrandsSearchText={setBrandsSearchText}
          checkAllSegBrands={checkAllSegBrands}
          setCheckAllSegBrands={
            setCheckAllSegBrands as React.Dispatch<
              React.SetStateAction<boolean | undefined>
            >
          }
          setIsCategoryClearFiltersClicked={setIsCategoryClearFiltersClicked}
          isCategoryClearFiltersClicked={isCategoryClearFiltersClicked}
          setUncheckedBrandIds={setUncheckedBrandIds}
          uncheckedBrandIds={uncheckedBrandIds}
          hasNextPage={hasBrandsNextPage}
        />
      </SideDrawer>

      {/* share segments side drawer */}
      <ShareSegmentSideDrawer
        isShareSegmentSideDrawerOpen={isShareSegmentSideDrawerOpen}
        setIsShareSegmentSideDrawerOpen={setIsShareSegmentSideDrawerOpen}
        sharedSegment={sharedSegment}
        setSharedSegment={setSharedSegment}
      />
    </div>
  )
}

const SegmentHeaderButtons = ({
  setIsSideDrawerOpen,
}: {
  setIsSideDrawerOpen: React.Dispatch<React.SetStateAction<boolean>>
}): React.JSX.Element => {
  const { t } = useTranslate('advisory')
  const isMobileView = useIsMobileView()
  const { csvExportProps } = useContext(PriceSegmentContext)

  return (
    <div className='flex justify-content-between align-items-center pat-gap-4'>
      <CsvExport {...csvExportProps} />
      {/* manage segments button */}
      <Button
        styleType='secondary'
        onClick={() => setIsSideDrawerOpen((prev) => !prev)}
      >
        {isMobileView ? t('manage') : t('manageSegments')}
      </Button>
    </div>
  )
}
