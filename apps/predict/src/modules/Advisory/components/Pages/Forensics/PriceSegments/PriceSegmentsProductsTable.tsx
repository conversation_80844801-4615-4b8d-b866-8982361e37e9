import React, { useCallback, useContext, useMemo, useState } from 'react'
import { useInfiniteQuery } from '@tanstack/react-query'
import {
  type ActiveFilterProps,
  CsvExport,
  SearchBar,
  type SortColumnProps,
  standardSortParams,
  StandardTable,
} from '@patterninc/react-ui'
import { useTableTotalRowData } from 'src/modules/Insights/components/Pages/Share/MarketShare/common/hooks/useTableHeaderData'

import {
  c,
  type DownloadOptionsType,
  SecureAxios,
  useTranslate,
} from '../../../../../../common/services'
import { csvToastCallout } from '../../../../../../common/services/CsvHelpers'
import { ThemeContext } from '../../../../../../Context'
import {
  getAllProductsTableAPI,
  priceRangeLabel,
  PriceSegmentsCommonHeaders,
  type PriceSegmentsCommonObject,
} from '../../../../services/PriceSegmentsHelperService'

type PriceSegmentsProductsTableProps = {
  currency: {
    code: string
    symbol: string
  }
  priceRangeMin: string
  priceRangeMax: string
  segmentId: string
  /** Optionally required for brand_id param when component is used for Price Segment Brand Products Page */
  brandId?: string
}

export const PriceSegmentsProductsTable = ({
  currency,
  priceRangeMin,
  priceRangeMax,
  segmentId,
  brandId = '',
}: PriceSegmentsProductsTableProps) => {
  const { t } = useTranslate('advisory')
  const { timeframe, startDate, endDate, selectedComparisonPeriod, customer } =
    useContext(ThemeContext)

  const [sortBy, setSortBy] = useState({
    prop: 'sales_current_period',
    flip: false,
  })

  const sort: SortColumnProps['sorter'] = (sortObj) => {
    setSortBy({
      prop: sortObj.activeColumn,
      flip: sortObj.direction,
    })
  }

  const [searchTerm, setSearchTerm] = useState('')

  const searchInputHandler = (inputText: string) => {
    setSearchTerm(inputText)
  }

  const priceRange = useMemo(() => {
    return priceRangeLabel(currency, priceRangeMin, priceRangeMax)
  }, [currency, priceRangeMax, priceRangeMin])

  const config = [
    { ...PriceSegmentsCommonHeaders.product_title_link, mainColumn: true },
    {
      ...PriceSegmentsCommonHeaders.sales,
      tooltip: {
        content: t('estRevenueColumnTooltip'),
      },
    },
    {
      ...PriceSegmentsCommonHeaders.pct_of_total_sales,
      tooltip: {
        content: t('totalSalesColumnTooltip'),
      },
    },
    {
      ...PriceSegmentsCommonHeaders.units,
      tooltip: {
        content:
          brandId === ''
            ? t('unitsColumnTooltipBasedOnSelectedTime')
            : t('unitsColumnTooltipForSelectedTime'),
      },
    },
    {
      ...PriceSegmentsCommonHeaders.pct_of_total_units,
      tooltip: {
        content: t('totalUnitsColumnTooltip'),
      },
    },
    PriceSegmentsCommonHeaders.details_button,
  ]

  const tableParams = useMemo(() => {
    return {
      price_range_min: priceRangeMin,
      price_range_max: priceRangeMax,
      currency_code: currency.code,
      sort: standardSortParams(sortBy, ['product_title']),
      ...(searchTerm !== '' && { search_for: searchTerm }),
      ...(brandId !== '' && { brand_id: brandId }),
      start_date: startDate,
      end_date: endDate,
      timeframe: timeframe.type,
      comparison_period: selectedComparisonPeriod.value,
      customer_id: customer.id,
    }
  }, [
    brandId,
    currency.code,
    customer.id,
    endDate,
    priceRangeMax,
    priceRangeMin,
    searchTerm,
    selectedComparisonPeriod.value,
    sortBy,
    startDate,
    timeframe.type,
  ])

  const { data, status, hasNextPage, fetchNextPage, isLoading } =
    useInfiniteQuery({
      queryKey: ['price_segments_products_table', tableParams, segmentId],
      queryFn: async ({ pageParam = 1, signal }) => {
        const params = {
          page: pageParam,
          per_page: 20,
          ...tableParams,
        }
        try {
          const response = await SecureAxios.get(
            getAllProductsTableAPI(segmentId),
            {
              params,
              signal,
            },
          ).then((resp) => resp.data)
          return response
        } catch (error) {
          console.error('Error fetching price segments products:', error)
          throw error
        }
      },
      initialPageParam: 1,
      getNextPageParam: (previousResp) => {
        return previousResp?.data?.pagination?.last_page
          ? undefined
          : previousResp?.data?.pagination?.next_page
      },
    })

  const {
    data: tableData,
    totalRowStatus,
    count,
  } = useTableTotalRowData<PriceSegmentsCommonObject>({
    totalRowsHeaderUrl: `${getAllProductsTableAPI(segmentId)}_header`,
    searchFor: searchTerm,
    columnFilter: {} as ActiveFilterProps,
    sort: sortBy,
    tableApiStatus: status,
    tableData: data,
    additionalParamsAndDependencies: {
      params: tableParams,
      dependencies: [tableParams],
    },
  })

  // CSV Download
  const exportCSV = useCallback(
    (params: unknown) => {
      return SecureAxios.get(getAllProductsTableAPI(segmentId), {
        params,
      }).then((response) => response.data)
    },
    [segmentId],
  )

  const csvDownloadOptions: DownloadOptionsType = [
    {
      linkName: 'Price Segments Products Report',
      csvName: t('priceSegmentsProductsReport'),
      csvFormat: {
        asyncDownload: true,
        api: exportCSV,
        params: {
          ...tableParams,
          async: true,
        },
        callout: csvToastCallout(),
      },
    },
  ]

  return (
    <>
      <div className='flex flex-direction-column pat-gap-1 pat-pt-4'>
        <div className='fs-16 fw-medium'>
          {t('priceSegRange', { range: priceRange })}
        </div>
        <div className='fs-12 fw-regular'>
          {t('priceSegRangeTooltip', { range: priceRange })}
        </div>
      </div>
      <div className='flex pat-py-4 justify-content-between align-items-center'>
        <SearchBar
          value={searchTerm}
          onChange={searchInputHandler}
          placeholder={t('serachBarPlaceholderForPriceSegProdTable', {
            productsCount: count,
          })}
        />
        {tableData.length > 0 && (
          <CsvExport
            csvDownloadOptions={csvDownloadOptions}
            initialDisplay
            show
          />
        )}
      </div>
      <StandardTable
        data={tableData}
        config={config}
        dataKey={'product_asin'}
        hasData={tableData.length > 0}
        hasMore={!!(totalRowStatus === 'success' && hasNextPage)}
        successStatus={totalRowStatus === 'success'}
        loading={isLoading}
        tableId={'price_segments_products_table'}
        noDataFields={{
          icon: 'info',
          primaryText: c('noDataFound'),
          secondaryText: t('noDataDisplayForPriceSegment'),
        }}
        sort={sort}
        sortBy={sortBy}
        getData={fetchNextPage}
        stickyTableConfig={{ right: 1 }}
        totalRowKey={totalRowStatus === 'success' ? 'totalRow' : undefined}
      />
    </>
  )
}
