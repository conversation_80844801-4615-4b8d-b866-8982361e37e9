import React, { useContext, useEffect, useMemo, useState } from 'react'
import { Link } from 'react-router-dom'
import { useInfiniteQuery, useMutation } from '@tanstack/react-query'
import {
  Button,
  type SortColumnProps,
  standardSortParams,
  StandardTable,
} from '@patterninc/react-ui'
import { useUser } from 'src/context/user-context'

import { c, SecureAxios, useTranslate } from '../../../../../../common/services'
import { csvToastCallout } from '../../../../../../common/services/CsvHelpers'
import { ThemeContext } from '../../../../../../Context'
import { PriceSegmentContext } from '../../../../context/PriceSegmentsContext'
import {
  getPriceSegmentsAPI,
  PriceSegmentsCommonHeaders,
  type PriceSegmentsCommonObject,
} from '../../../../services/PriceSegmentsHelperService'

export const PriceSegmentsTable = (): React.JSX.Element => {
  const { t } = useTranslate('advisory')
  const {
    priceRangeSliderValue,
    selectedBrand,
    selectedSegment,
    isSegmentLoading,
    priceRangeLimit,
    setCsvExportProps,
  } = useContext(PriceSegmentContext)

  const { timeframe, startDate, endDate, selectedComparisonPeriod } =
    useContext(ThemeContext)
  const currency = useUser().user?.current_currency
  const [sortBy, setSortBy] = useState({
    prop: 'price_range_min',
    flip: true,
  })

  const sort: SortColumnProps['sorter'] = (sortObj) => {
    setSortBy({
      prop: sortObj.activeColumn,
      flip: sortObj.direction,
    })
  }

  // API Params
  const tableParams = useMemo(() => {
    return {
      price_range_increment: priceRangeSliderValue,
      sort: standardSortParams(sortBy),
      currency_code: currency?.code,
      start_date: startDate,
      end_date: endDate,
      timeframe: timeframe.type,
      comparison_period: selectedComparisonPeriod.value,
      ...(selectedBrand.id !== 0 && { brand_id: selectedBrand.id }),
      ...(priceRangeLimit.max !== null &&
        priceRangeLimit.max !== 0 && {
          price_range_upper_limit: priceRangeLimit.max,
        }),
      price_range_lower_limit: priceRangeLimit.min ?? 0,
    }
  }, [
    priceRangeSliderValue,
    sortBy,
    currency?.code,
    startDate,
    endDate,
    timeframe.type,
    selectedComparisonPeriod.value,
    selectedBrand.id,
    priceRangeLimit.max,
    priceRangeLimit.min,
  ])

  // Table Data
  const {
    data: paginatedData,
    fetchNextPage,
    hasNextPage,
    status,
    isLoading,
  } = useInfiniteQuery({
    queryKey: [tableParams, selectedSegment],
    queryFn: async ({ pageParam = 1, signal }) => {
      const params = {
        per_page: 20,
        page: pageParam,
        ...tableParams,
      }
      try {
        const response = await SecureAxios.get(
          getPriceSegmentsAPI(selectedSegment.id),
          {
            params,
            signal,
          },
        )
        return response
      } catch (error) {
        console.error('Error fetching price segments:', error)
        throw error
      }
    },
    initialPageParam: 1,
    getNextPageParam: (previousResponse) => {
      return previousResponse?.data?.pagination?.last_page
        ? undefined
        : previousResponse?.data?.pagination?.next_page
    },
    enabled: !!selectedSegment.id, // To prevent API Calls when no segment is selected.
  })

  const tableData = useMemo(() => {
    if (status === 'success' && paginatedData?.pages) {
      const data = paginatedData.pages.flatMap((page) => {
        return page?.data.data
      })

      return [...data]
    }

    return []
  }, [paginatedData?.pages, status])

  // CSV Download
  const { mutateAsync: exportCSV } = useMutation({
    // Refactor direct API calls to use React Query hooks for better state management and efficiency.
    mutationFn: async (params: unknown) => {
      const response = await SecureAxios.get(
        getPriceSegmentsAPI(selectedSegment.id),
        {
          params,
        },
      )
      return response.data
    },
  })

  useEffect(() => {
    setCsvExportProps({
      show: true,
      initialDisplay: tableData.length > 0,
      csvDownloadOptions: [
        {
          linkName: 'Price Segments Report',
          csvName: t('priceSegmentsReport'),
          csvFormat: {
            asyncDownload: true,
            api: (params: unknown) => exportCSV(params),
            params: {
              ...tableParams,
              async: true,
            },
            callout: csvToastCallout(),
          },
        },
      ],
    })
  }, [exportCSV, setCsvExportProps, t, tableData.length, tableParams])

  const config = [
    { ...PriceSegmentsCommonHeaders.price_range, mainColumn: true },
    {
      ...PriceSegmentsCommonHeaders.sales,
      tooltip: {
        content:
          selectedBrand.id === 0
            ? t('salesAllBrandTooltip')
            : t('salesTooltip'),
      },
    },
    {
      ...PriceSegmentsCommonHeaders.pct_of_total_sales,
      tooltip: {
        content:
          selectedBrand.id === 0
            ? t('totalSalesAllBrandTooltip')
            : t('totalSalesTooltip'),
      },
    },
    {
      ...PriceSegmentsCommonHeaders.units,
      tooltip: {
        content:
          selectedBrand.id === 0
            ? t('unitsAllBrandtooltip')
            : t('unitsTooltip'),
      },
    },
    {
      ...PriceSegmentsCommonHeaders.pct_of_total_units,
      tooltip: {
        content:
          selectedBrand.id === 0
            ? t('totalUnitsAllBrandTooltip')
            : t('totalUnitsTooltip'),
      },
    },
    {
      ...PriceSegmentsCommonHeaders.product_count,
      tooltip: {
        content: t('brands:priceSegmentProductsTooltip'),
      },
    },
    {
      ...PriceSegmentsCommonHeaders.brand_count,
      tooltip: {
        content: t('brands:priceSegmentBrandsTooltip'),
      },
    },
    {
      name: '',
      label: '',
      noSort: true,
      isButton: true,
      cell: {
        children: (data: PriceSegmentsCommonObject) => {
          return (
            <Button
              as='link'
              to={`segment/${selectedSegment.id}/range/${data?.price_range_min}-${data?.price_range_max}${
                selectedBrand.id !== 0
                  ? `/brand-products/${selectedBrand.id}`
                  : ''
              }`}
              routerComponent={Link}
            >
              {selectedBrand.id !== 0 ? c('viewProducts') : c('viewDetails')}
            </Button>
          )
        },
      },
    },
  ]

  return (
    <div className='pat-px-4 pat-pt-4'>
      <StandardTable
        data={tableData}
        config={config}
        dataKey={'price_range_min'}
        hasData={tableData.length > 0}
        hasMore={!!(status === 'success' && hasNextPage)}
        successStatus={status === 'success'}
        loading={isLoading || isSegmentLoading}
        tableId={'price-segments-table'}
        noDataFields={{
          icon: 'info',
          primaryText: c('noDataFound'),
          secondaryText: t('thereIsNoDataToDisplayForthisSegment'),
        }}
        sort={sort}
        sortBy={sortBy}
        // will be implemented when the api is ready
        getData={fetchNextPage}
        // 20px + 20px = 40px margin
        widthOffset={40}
        stickyTableConfig={{ right: 1 }}
        customWidth={'100%'}
      />
    </div>
  )
}
