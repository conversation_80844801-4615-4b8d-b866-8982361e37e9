import React from 'react'
import { useTranslate } from '@predict-services'

type SegmentPillsListTooltipProps = {
  label: string
  dataLimit: number
  data: string[]
  totalCount: number
}

export const SegmentPillsListTooltip = ({
  label,
  dataLimit,
  data,
  totalCount,
}: SegmentPillsListTooltipProps): React.JSX.Element => {
  const visibleData = dataLimit ? data?.slice(0, dataLimit) : data
  const remainingDataCount =
    totalCount && dataLimit ? totalCount - visibleData.length : 0
  const { t } = useTranslate('advisory')
  return (
    <span>
      {data?.length ? (
        <>
          <div className='fw-semi-bold'>{label}</div>
          <div className='fs-12'>
            <div>
              {visibleData.map((item) => (
                <div key={item}>{item}</div>
              ))}
            </div>
            {remainingDataCount > 0 && (
              <div className='fc-purple'>
                {t('remainingDataMore', { length: remainingDataCount })}
              </div>
            )}
          </div>
        </>
      ) : (
        <div className='fs-12'>{t('noDatatoShow')}</div>
      )}
    </span>
  )
}
