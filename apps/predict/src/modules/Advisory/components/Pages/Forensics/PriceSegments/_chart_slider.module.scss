@use '@patterninc/react-ui/dist/variables' as variables;

.pageContainer {
  width: calc(100vw - 113px); // 73px for sidebar and 20px for padding both side
  display: flex;
  flex-direction: column;
  border: 1px solid var(--light-gray);
  border-radius: 4px;

  @media (max-width: variables.$breakpoint-md) {
    width: calc(100vw - 40px); // 20px for padding both side
  }
}

.chartAndSliderContainer {
  display: flex;
  flex-direction: row;
  border-bottom: 1px solid var(--light-gray);

  @media (max-width: variables.$breakpoint-md) {
    flex-direction: column-reverse;
  }
}

.chartWidth {
  @media (min-width: variables.$breakpoint-md) {
    width: 80%;
  }

  @media (max-width: variables.$breakpoint-md) {
    border-bottom: 1px solid var(--light-gray);
  }
}

.sliderWidth {
  display: flex;
  flex-direction: column;
  justify-content: space-between;

  @media (min-width: variables.$breakpoint-md) {
    width: 300px;
  }
}

.chartContainer {
  display: flex;
  flex-direction: column;
  padding-top: 32px;

  @media (min-width: variables.$breakpoint-md) {
    border-right: 1px solid var(--light-gray);
  }
}

.metricContainer {
  display: flex;
  flex-direction: column;
}

.rows {
  display: grid;
  grid-template-columns: 2fr 1fr 1fr;
  padding: 8px;
}

.header {
  font-size: 10px;
  color: var(--purple);
  border-bottom: 1px solid var(--purple);
  font-weight: 600;
}

.graphLegend {
  width: 4px;
  max-width: 4px;
  height: 24px;
  border-radius: 3px;
}

.graphLegendValue {
  color: var(--dark-purple);
  font-size: 12px;
  align-self: center;
}

.footer {
  border-top: 1px solid var(--light-gray);
  background-color: var(--lighter-gray);

  @media (max-width: variables.$breakpoint-md) {
    border-bottom: 1px solid var(--light-gray);
  }
}

.chartEmptyState {
  display: flex;
  height: 300px;
  align-items: center;
}
