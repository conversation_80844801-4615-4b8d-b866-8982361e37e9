@use '@patterninc/react-ui/dist/variables' as variables;

.segmentBoxContainer {
  border-bottom: 1px solid var(--light-gray);
  display: flex;
  gap: 16px;
  padding: 8px 16px 8px 16px;
  background-color: var(--faint-gray);
  justify-content: space-between;

  @media (min-width: variables.$breakpoint-md) {
    align-items: center;
  }

  @media (max-width: variables.$breakpoint-md) {
    padding-bottom: 8px;
    flex-direction: column;
  }
}

.segmentDropDown {
  margin-left: 8px;
  min-width: 240px;

  @media (max-width: variables.$breakpoint-md) {
    margin-left: 0;
  }
}

.segmentsListAndTags {
  display: flex;
  gap: 16px;

  @media (min-width: variables.$breakpoint-md) {
    align-items: center;
    padding: 8px;
  }

  @media (max-width: variables.$breakpoint-md) {
    flex-direction: column;
    padding-bottom: 8px;
  }
}

.segmentListHeaderContainer {
  min-width: auto !important;
  white-space: pre-wrap;
  text-align: center;
}

.createOrEditContainer {
  // To align Stepper heading container with the Sidebar heading container
  margin-top: -16px;
}

.orderedByText {
  font-style: italic;
  color: var(--purple);
}
