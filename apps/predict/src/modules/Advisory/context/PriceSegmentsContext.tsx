import React, { useCallback, useState } from 'react'
import { type CsvExportProps } from '@patterninc/react-ui'

import {
  type BrandType,
  defaultSegment,
  type PriceRangeLimitType,
  type SegmentType,
} from '../services/PriceSegmentsHelperService'

type PriceSegmentsContextType = {
  priceRangeSliderValue: number
  updatePriceRangeSliderValue: (priceRangeSliderValue: number) => void
  selectedBrand: BrandType
  updateSelectedBrand: ({ id, name }: BrandType) => void
  selectedSegment: SegmentType
  updateSelectedSegment: (segment: SegmentType) => void
  isSegmentLoading: boolean
  updateIsSegmentLoading: (value: boolean) => void
  priceRangeLimit: PriceRangeLimitType
  updatePriceRangeLimit: (range: PriceRangeLimitType) => void
  csvExportProps: CsvExportProps
  setCsvExportProps: (csvExportProps: CsvExportProps) => void
}

export const PriceSegmentContext =
  React.createContext<PriceSegmentsContextType>({
    // Defaults
    priceRangeSliderValue: 5,
    updatePriceRangeSliderValue: () => null,
    selectedBrand: { id: 0, name: '' },
    updateSelectedBrand: () => null,
    selectedSegment: {
      id: 0,
      name: '',
      description: '',
      created_by_user: '',
      created_by_user_id: 0,
      is_default: false,
      category_ids: [0],
      brand_ids: [0],
      categories: [''],
      brands: [''],
      category_count: 0,
      brand_count: 0,
      segment_type: 0,
    },
    updateSelectedSegment: () => null,
    isSegmentLoading: false,
    updateIsSegmentLoading: () => null,
    priceRangeLimit: {
      min: null,
      max: null,
    },
    updatePriceRangeLimit: () => null,
    csvExportProps: {
      show: false,
      initialDisplay: false,
      csvDownloadOptions: [],
    },
    setCsvExportProps: () => null,
  })

export const PriceSegmentsContextProvider = ({
  children,
}: {
  children: React.ReactNode
}) => {
  const [state, setState] = useState<{
    priceRangeSliderValue: number
    selectedBrand: BrandType
    selectedSegment: SegmentType
    isSegmentLoading: boolean
    priceRangeLimit: PriceRangeLimitType
    csvExportProps: CsvExportProps
  }>({
    priceRangeSliderValue: 5,
    selectedBrand: { id: 0, name: 'Select Brand' },
    selectedSegment: defaultSegment,
    isSegmentLoading: false,
    priceRangeLimit: {
      min: null,
      max: null,
    },
    csvExportProps: {
      show: false,
      initialDisplay: false,
      csvDownloadOptions: [],
    },
  })

  const updatePriceRangeSliderValue = useCallback(
    (priceRangeSliderValue: number) => {
      setState((prev) => ({
        ...prev,
        priceRangeSliderValue: priceRangeSliderValue,
      }))
    },
    [],
  )

  const updateSelectedBrand = useCallback(({ id, name }: BrandType) => {
    setState((prev) => ({
      ...prev,
      selectedBrand: { id: id, name: name },
    }))
  }, [])

  const updateSelectedSegment = useCallback((segment: SegmentType) => {
    setState((prev) => ({
      ...prev,
      selectedSegment: segment,
    }))
  }, [])

  const updateIsSegmentLoading = useCallback((value: boolean) => {
    setState((prev) => ({
      ...prev,
      isSegmentLoading: value,
    }))
  }, [])

  const updatePriceRangeLimit = useCallback((range: PriceRangeLimitType) => {
    setState((prev) => ({
      ...prev,
      priceRangeLimit: {
        min: range.min,
        max: range.max,
      },
    }))
  }, [])

  const setCsvExportProps = useCallback((csvExportProps: CsvExportProps) => {
    setState((prev) => ({
      ...prev,
      csvExportProps: csvExportProps,
    }))
  }, [])

  const {
    selectedBrand,
    priceRangeSliderValue,
    selectedSegment,
    isSegmentLoading,
    priceRangeLimit,
    csvExportProps,
  } = state

  return (
    <PriceSegmentContext.Provider
      value={{
        // Values
        priceRangeSliderValue: priceRangeSliderValue,
        selectedBrand: selectedBrand,
        selectedSegment: selectedSegment,
        isSegmentLoading: isSegmentLoading,
        priceRangeLimit: priceRangeLimit,
        csvExportProps: csvExportProps,

        // Methods
        updatePriceRangeSliderValue: updatePriceRangeSliderValue,
        updateSelectedBrand: updateSelectedBrand,
        updateSelectedSegment: updateSelectedSegment,
        updateIsSegmentLoading: updateIsSegmentLoading,
        updatePriceRangeLimit: updatePriceRangeLimit,
        setCsvExportProps: setCsvExportProps,
      }}
    >
      {children}
    </PriceSegmentContext.Provider>
  )
}
