import React from 'react'
import { <PERSON> } from 'react-router-dom'
import {
  <PERSON><PERSON>,
  getApiUrl<PERSON><PERSON><PERSON>x,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  PrimaryTableCell,
  TrimText,
} from '@patterninc/react-ui'
import { c, t } from '@predict-services'
import { getListingRedirectionURL } from 'src/modules/Insights/components/Pages/Share/MarketShare/AtAGlance/common'

import { Currency } from '../../../common/components'
import ListChangeData from '../../../common/components/ListChangeData/ListChangeData'

export type PriceSegmentsCommonObject = {
  totalRow: boolean
  price_range_min: number
  price_range_max: number
  sales_current_period: number
  sales_comparison_period: number
  sales_change_from_comparison_period: number
  sales_pct_change_from_comparison_period: number
  pct_total_sales_current_period: number
  pct_total_sales_comparison_period: number
  pct_total_sales_change_from_comparison_period: number
  units_current_period: number
  units_comparison_period: number
  units_change_from_comparison_period: number
  pct_total_units_current_period: number
  pct_total_units_comparison_period: number
  pct_total_units_change_from_comparison_period: number
  product_count_current_period: number
  product_count_comparison_period: number
  change_in_product_count: number
  pct_change_in_product_count: number
  brand_count_current_period: number
  brand_count_comparison_period: number
  change_in_brand_count: number
  pct_change_in_brand_count: number
  sortProp: string
  label?: string
  brand_name: string
  brand_id: number
  image_url: string
  product_title: string
  marketplace_url: string
  marketplace_name: string
  product_asin: string
  currency_code: string
  currency_symbol: string

  is_internal_url: boolean
  master_product_id: number
  market_product_id: number
  marketplace_id: number
  marketplace_page_id: number
}

export type SegmentType = {
  id: number
  name: string | number
  description: string | number
  created_by_user: string
  created_by_user_id: number
  is_default: boolean
  category_ids: Array<number>
  brand_ids: Array<number>
  categories: Array<string>
  brands: Array<string>
  category_count: number
  brand_count: number
  segment_type: number
}

export type SelectedBrandProps = {
  selectedBrandNames: Array<string>
  selectedbrandIds: Array<number>
}

export type BrandsListProps = {
  id: number
  name: string
  est_revenue: number
  is_checked: boolean
}

export type BrandsSearchBarProps = {
  brandsSearchText: string
  setBrandsSearchText: React.Dispatch<React.SetStateAction<string>>
  setCheckAllSegBrands: React.Dispatch<
    React.SetStateAction<boolean | undefined>
  >
  checkAllSegBrands: boolean | undefined
  uncheckedBrandIds: number[]
  setUncheckedBrandIds: React.Dispatch<React.SetStateAction<number[]>>
  setIsCategoryClearFiltersClicked?: React.Dispatch<
    React.SetStateAction<boolean>
  >
  isCategoryClearFiltersClicked?: boolean
}

export type ShareSegmentUserType = {
  user_id: number
  full_user_name: string
  is_shared: boolean
  is_default: boolean
  segment_id: number
  username: string
}

export type BrandType = {
  id: number
  name: string
}

export type PriceRangeLimitType = {
  min: number | null
  max: number | null
}

export const PriceSegmentsCommonHeaders = {
  price_range: {
    name: 'price_range_min',
    label: t('advisory:priceRange'),
    cell: {
      children: (data: PriceSegmentsCommonObject) => {
        return (
          <MdashCheck check={!!data.price_range_min || !!data.price_range_max}>
            <Currency
              value={data?.price_range_min}
              currencyCode={data?.currency_code}
              currencySymbol={data?.currency_symbol}
              customClass={
                data.sortProp === 'price_range_min' ? 'fw-semi-bold' : ''
              }
              customDecimalScale={0}
            />
            {' - '}
            <Currency
              value={data?.price_range_max}
              currencyCode={data?.currency_code}
              currencySymbol={data?.currency_symbol}
              customClass={
                data.sortProp === 'price_range_min' ? 'fw-semi-bold' : ''
              }
              customDecimalScale={0}
            />
          </MdashCheck>
        )
      },
    },
  },
  sales: {
    name: 'sales_current_period',
    label: t('advisory:estRevenue'),
    cell: {
      children: (data: PriceSegmentsCommonObject) => {
        return (
          <ListChangeData
            value={data?.sales_current_period}
            changeValue={data?.sales_change_from_comparison_period}
            secondChangeProps={
              data?.sales_change_from_comparison_period
                ? {
                    change: data?.sales_pct_change_from_comparison_period * 100,
                    suffix: '%',
                    decimalScale: 2,
                  }
                : undefined
            }
            changeFormat='currency'
            currency={{
              currency_code: data?.currency_code,
              currency_symbol: data?.currency_symbol,
            }}
            decimalScale={2}
            changeValueNewFormat
            customClass={
              data.sortProp === 'sales_current_period' ? 'fw-semi-bold' : ''
            }
          />
        )
      },
    },
  },
  pct_of_total_sales: {
    name: 'pct_total_sales_current_period',
    label: t('advisory:%OfTotalRevenue'),
    cell: {
      children: (data: PriceSegmentsCommonObject) => {
        return (
          <ListChangeData
            value={data?.pct_total_sales_current_period}
            changeValue={data?.pct_total_sales_change_from_comparison_period}
            percentage
            changeFormat='percentage'
            decimalScale={2}
            customClass={
              data.sortProp === 'pct_total_sales_current_period'
                ? 'fw-semi-bold'
                : ''
            }
            showLessThanZeroPercentMainValue={
              !(data?.pct_total_sales_current_period < 0.0001)
            }
            lessThanZeroTextPercentMainValue={
              data?.pct_total_sales_current_period < 0.0001
                ? '< 0.01%'
                : undefined
            }
          />
        )
      },
    },
  },
  units: {
    name: 'units_current_period',
    label: t('advisory:estUnits'),
    cell: {
      children: (data: PriceSegmentsCommonObject) => {
        return (
          <ListChangeData
            value={data?.units_current_period}
            changeValue={data?.units_change_from_comparison_period}
            changeFormat='number'
            customClass={
              data.sortProp === 'units_current_period' ? 'fw-semi-bold' : ''
            }
          />
        )
      },
    },
  },
  pct_of_total_units: {
    name: 'pct_total_units_current_period',
    label: t('advisory:%OfTotalUnits'),
    cell: {
      children: (data: PriceSegmentsCommonObject) => {
        return (
          <ListChangeData
            value={data?.pct_total_units_current_period}
            changeValue={data?.pct_total_units_change_from_comparison_period}
            percentage
            changeFormat='percentage'
            decimalScale={2}
            customClass={
              data.sortProp === 'pct_total_units_current_period'
                ? 'fw-semi-bold'
                : ''
            }
            showLessThanZeroPercentMainValue={
              !(data?.pct_total_units_current_period < 0.0001)
            }
            lessThanZeroTextPercentMainValue={
              data?.pct_total_units_current_period < 0.0001
                ? '< 0.01%'
                : undefined
            }
          />
        )
      },
    },
  },
  product_count: {
    name: 'product_count_current_period',
    label: c('products'),
    cell: {
      children: (data: PriceSegmentsCommonObject) => {
        return (
          <ListChangeData
            value={Number(data?.product_count_current_period)}
            changeValue={Number(data?.change_in_product_count)}
            secondChangeProps={
              data?.product_count_comparison_period
                ? {
                    change: Number(data?.pct_change_in_product_count) * 100,
                    suffix: '%',
                    decimalScale: 2,
                  }
                : undefined
            }
            changeFormat='number'
            decimalScale={2}
            changeValueNewFormat
            customClass={
              data.sortProp === 'product_count_current_period'
                ? 'fw-semi-bold'
                : ''
            }
          />
        )
      },
    },
  },
  brand_count: {
    name: 'brand_count_current_period',
    label: c('brands'),
    cell: {
      children: (data: PriceSegmentsCommonObject) => {
        return (
          <ListChangeData
            value={Number(data?.brand_count_current_period)}
            changeValue={Number(data?.change_in_brand_count)}
            secondChangeProps={
              data?.brand_count_comparison_period
                ? {
                    change: Number(data?.pct_change_in_brand_count) * 100,
                    suffix: '%',
                    decimalScale: 2,
                  }
                : undefined
            }
            changeFormat='number'
            changeValueNewFormat
            customClass={
              data.sortProp === 'brand_count_current_period'
                ? 'fw-semi-bold'
                : ''
            }
          />
        )
      },
    },
  },
  brand: {
    name: 'brand_name',
    label: c('brands'),
    cell: {
      children: (data: PriceSegmentsCommonObject) => (
        <TrimText
          text={data?.brand_name}
          limit={75}
          customClass={data?.sortProp === 'brand_name' ? 'fw-semi-bold' : ''}
        />
      ),
    },
  },
  product_title: {
    name: 'product_title',
    label: c('products'),
    cell: {
      children: (data: PriceSegmentsCommonObject) => {
        return (
          <PrimaryTableCell
            imageProps={{
              url: data?.image_url,
              alt: data?.product_title,
            }}
            externalLink={data?.marketplace_url}
            marketplaceNames={data?.marketplace_name}
            sortBy={{ prop: data.sortProp }}
            title={data?.product_title}
            titleProp='product_title'
            uniqId={{
              id: data?.product_asin,
              idLabel: 'ASIN',
              idName: 'product_asin',
            }}
          />
        )
      },
    },
  },
  product_title_link: {
    name: 'product_title',
    label: c('products'),
    cell: {
      children: (data: PriceSegmentsCommonObject) => {
        return !data.totalRow ? (
          <PrimaryTableCell
            imageProps={{
              url: data?.image_url,
              alt: data?.product_title,
            }}
            externalLink={data?.marketplace_url}
            marketplaceNames={data?.marketplace_name}
            sortBy={{ prop: data.sortProp }}
            title={data?.product_title}
            titleProp='product_title'
            uniqId={{
              id: data?.product_asin,
              idLabel: 'ASIN',
              idName: 'product_asin',
            }}
            productLink={getListingRedirectionURL(data) ?? ''}
            routerComponent={Link}
            routerProp='to'
          />
        ) : (
          <span>{data.product_title}</span>
        )
      },
    },
  },
  details_button: {
    name: '',
    label: '',
    isButton: true,
    noSort: true,
    cell: {
      children: (data: PriceSegmentsCommonObject) => {
        const redirectURL = getListingRedirectionURL(data)
        return !data?.totalRow ? (
          redirectURL ? (
            <Button as='link' routerComponent={Link} to={redirectURL}>
              {c('details')}
            </Button>
          ) : (
            <Button disabled>{c('details')}</Button>
          )
        ) : null
      },
    },
  },
}

export const priceRangeLabel = (
  currency: {
    code: string
    symbol: string
  },
  priceRangeMin: string | number,
  priceRangeMax: string | number,
) => {
  const currencyCode = currency.code !== 'USD' ? currency.code : ''
  return `${currency.symbol}${priceRangeMin} ${currencyCode} - ${currency.symbol}${priceRangeMax} ${currencyCode}`
}

export const formattedCurrencyLabel = (
  currency: { code: string; symbol: string },
  value: number | string,
) => {
  return `${currency?.symbol}${value} ${currency?.code !== 'USD' ? currency?.code : ''}`
}

export const getPriceSegmentsAPI = (segmentId: number | string) => {
  return `${getApiUrlPrefix('marketshare')}/api/v3/forensics/${segmentId}/price_segments`
}

export const getFetchBrandsAPI = (segmentId: number | string) => {
  return `${getApiUrlPrefix('marketshare')}/api/v3/segments/${segmentId}/fetch_brands`
}

export const getAllBrandsTableAPI = (segmentId: number | string) => {
  return `${getApiUrlPrefix('marketshare')}/api/v3/brands/${segmentId}/price_segment`
}

export const getAllProductsTableAPI = (segmentId: number | string) => {
  return `${getApiUrlPrefix('marketshare')}/api/v3/segments/${segmentId}/price_segment_products`
}

export const defaultSegment: SegmentType = {
  id: 0,
  name: '',
  description: '',
  created_by_user: '',
  created_by_user_id: 0,
  is_default: false,
  category_ids: [],
  brand_ids: [],
  categories: [],
  brands: [],
  category_count: 0,
  brand_count: 0,
  segment_type: 0,
}
