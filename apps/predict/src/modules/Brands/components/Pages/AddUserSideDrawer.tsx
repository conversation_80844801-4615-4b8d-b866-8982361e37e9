import React, { useState } from 'react'
import { useLocation, useNavigate } from 'react-router-dom'
import { useQuery } from '@tanstack/react-query'
import {
  EmptyState,
  FormFooter,
  Select,
  SideDrawer,
  standardSortParams,
  toast,
} from '@patterninc/react-ui'

import { addUserToBrand, fetchUsers } from '../../services/BrandDetailsService'
import { brandActionConstant } from './brandConstants'
const { ADD_USER } = brandActionConstant

type SideDrawerProps = {
  type: string
  isOpen: boolean
}

type AddUserSideDrawerProps = {
  actionsSidedrawer: SideDrawerProps
  setActionSidedrawer: React.Dispatch<React.SetStateAction<SideDrawerProps>>
  customerId: number
}
type SelectedUserDetails = {
  id: number
  full_user_name: string
}

type User = {
  id: number
  full_user_name: string
}

const AddUserSideDrawer = ({
  actionsSidedrawer,
  setActionSidedrawer,
  customerId,
}: AddUserSideDrawerProps) => {
  const navigate = useNavigate()
  const location = useLocation()
  const defaultSelectedUser = {
    id: 0,
    full_user_name: 'None',
  }
  const [search] = useState(''),
    [isUserAdding, setUserAdding] = useState(false)
  const [selectedUser, setSelectedUser] =
    useState<SelectedUserDetails>(defaultSelectedUser)
  const handleCloseSidedrawer = () => {
    setSelectedUser(defaultSelectedUser)
    setActionSidedrawer({ type: '', isOpen: false })
  }
  const addUser = () => {
    const params = {
      user_customer: {
        customer_id: customerId,
        user_id: selectedUser?.id,
        access: 'full',
      },
    }
    setUserAdding(true)

    addUserToBrand(customerId, params)
      .then((response) => {
        setUserAdding(false)
        if (!response.error) {
          toast({
            type: 'success',
            message: 'User added successfully',
            config: {
              toastId: 'addUserToast',
              autoClose: 5000,
            },
          })
          handleCloseSidedrawer()
          setTimeout(() => {
            location.pathname === '/brands/details/users_and_groups'
              ? navigate(`/brands/details/users_and_groups`, {
                  state: {
                    timestamp: Date.now(),
                  },
                })
              : navigate(`/brands/details/users_and_groups`)
          }, 1000)
        } else {
          toast({
            type: 'error',
            config: {
              toastId: 'addUserToast',
              autoClose: 5000,
            },
            message:
              response?.error?.response?.data?.message ||
              'Something went wrong. Please try again',
          })
        }
      })
      .catch((error) => {
        const existingUserMessage = 'User is already added'
        setUserAdding(false)
        toast({
          type: 'error',
          config: {
            toastId: 'addUserToast',
            autoClose: 5000,
          },
          message:
            error?.response?.data?.message === existingUserMessage
              ? existingUserMessage
              : `Failed to add user. Please try again.`,
        })
      })
  }
  const SideDrawerFooterContent = () => {
    return (
      <FormFooter
        saveButtonProps={{
          ...(selectedUser?.full_user_name === 'None' || isUserAdding
            ? { disabled: true }
            : {}),
          onClick: () => {
            addUser()
          },
          children: 'Add User',
        }}
        cancelButtonProps={{
          onClick: () => {
            handleCloseSidedrawer()
          },
        }}
      />
    )
  }

  const { data: usersData = [] } = useQuery<User[], Error>({
    queryKey: ['customers_data', search],
    queryFn: async () => {
      const params = {
        page: 1,
        per_page: 20,
        query: search,
        sort: standardSortParams({ prop: 'last_name', flip: false }),
        enabled: true,
      }
      return fetchUsers(params).then((response) => response.data as User[])
    },
  })

  return (
    <SideDrawer
      isOpen={actionsSidedrawer.type === ADD_USER && actionsSidedrawer.isOpen}
      closeCallout={handleCloseSidedrawer}
      headerContent='Add a User'
      footerContent={<SideDrawerFooterContent />}
    >
      {isUserAdding ? (
        <EmptyState
          primaryText='Hang on for one moment.'
          secondaryText='User adding...'
        />
      ) : (
        <Select
          labelProps={{ label: 'Search & Select a User' }}
          options={usersData}
          optionKeyName='full_user_name'
          labelKeyName='full_user_name'
          selectedItem={selectedUser}
          onChange={(option) => {
            setSelectedUser(option)
          }}
          required
        />
      )}
    </SideDrawer>
  )
}

export default AddUserSideDrawer
