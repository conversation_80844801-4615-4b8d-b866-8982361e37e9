import React, { useContext, useEffect, useMemo, useState } from 'react'
import { useQuery } from '@tanstack/react-query'
import {
  Button,
  getApiUrlPrefix,
  MdashCheck,
  SearchBar,
  standardSortParams,
  StandardTable,
  useMediaQuery,
} from '@patterninc/react-ui'
import {
  c,
  haveRoles,
  isInternalUser,
  SecureAxios,
  tryLocalStorageParse,
} from '@predict-services'

import { ThemeContext } from '../../../../../Context'
import { useUser } from '../../../../../context/user-context'
import { getGlobalMarketplaces } from '../../../../Settings/services/MarketplacesService'
import { WORKFLOW_TYPES } from './BrandAssignmentConstants'
import BrandAssignmentsSideDrawer from './BrandAssignmentsSideDrawer'
import {
  type BrandAssignmentRegionTableProps,
  type BrandAssignmentTableProps,
  type RolesDataProps,
} from './BrandAssignmentsType'
import EditAssignmentSideDrawer from './EditAssignmentSideDrawer'

const BrandAssignments = (): React.JSX.Element => {
  const { customer } = useContext(ThemeContext),
    customerId = customer.id,
    isMobileView = useMediaQuery({ type: 'max', breakpoint: 'md' })

  const [isBrandAssignmentDrawerOpen, setIsBrandAssignmentDrawerOpen] =
    useState<boolean>(false)
  const [isEditAssignmentSideDrawer, setIsEditAssignmentSideDrawer] =
    useState<boolean>(false)
  const [editWorkflowType, setEditWorkflowType] = useState<string>('')
  const [refreshTable, setRefreshTable] = useState<boolean>(false)
  const [searchInput, setSearchInput] = useState<string>('')
  const [selectedCreateRegion, setSelectedCreateRegion] =
    useState<RolesDataProps>({
      id: 0,
      name: '',
    })

  const tableId = 'brand_assignments_table'
  const [sortBy, setSortBy] = useState(
    tryLocalStorageParse(`sort_by_${tableId}`) ?? {
      prop: 'role_name',
      flip: false,
    },
  )
  const { user } = useUser()
  const isPatternInternalUser = isInternalUser(user)
  const hasCustomerWritePermission = haveRoles(user, [
    'write_settings_customers',
  ])
  const [selectedRegionData, setSelectedRegionData] = useState<
    BrandAssignmentRegionTableProps | undefined
  >()
  const [selectedAssignmentData, setSelectedAssignmentData] = useState<
    BrandAssignmentTableProps | undefined
  >()

  const userListEndPoint = `${getApiUrlPrefix(
      'iserve',
    )}/api/v6/brand_assignments/users_list`,
    deptAndRoleEndPoint = `${getApiUrlPrefix(
      'iserve',
    )}/api/v6/brand_assignment_roles`,
    brandAssignmentTableEndPoint = `${getApiUrlPrefix(
      'iserve',
    )}/api/v6/brand_assignments/${customerId}/list`

  useEffect(() => {
    if (!isEditAssignmentSideDrawer) {
      setEditWorkflowType('')
    }
  }, [isEditAssignmentSideDrawer])

  const { data: regionsData, status: regionsStatus } = useQuery({
    queryKey: ['regions_data', customerId],
    queryFn: async () => {
      const params = {
        customer_id: customerId,
      }
      try {
        const response = await getGlobalMarketplaces(params)
        return response?.data?.regions ?? []
      } catch (error) {
        console.error('Unable to fetch regions', error)
      }
    },
  })

  const { data: userListData, status: userListStatus } = useQuery({
    queryKey: ['user_list_data'],
    queryFn: async () => {
      try {
        const response = await SecureAxios.get(userListEndPoint)
        return response.data.data
      } catch (error) {
        console.error('Error fetching user list:', error)
        throw error
      }
    },
  })

  const { data: deptAndRoleData, status: deptAndRoleStatus } = useQuery({
    queryKey: ['dept_and_role_data'],
    queryFn: async () => {
      try {
        const response = await SecureAxios.get(deptAndRoleEndPoint)
        return response.data.departments
      } catch (error) {
        console.error('Error fetching department and role data:', error)
        throw error
      }
    },
  })

  const isApiStatusFailed = useMemo(() => {
    if (
      (deptAndRoleStatus === 'success' && !deptAndRoleData?.length) ||
      (userListStatus === 'success' && !userListData?.length) ||
      (regionsStatus === 'success' && !regionsData?.length)
    ) {
      return true
    }

    return !!(
      deptAndRoleStatus === 'error' ||
      regionsStatus === 'error' ||
      userListStatus === 'error'
    )
  }, [
    deptAndRoleData?.length,
    deptAndRoleStatus,
    regionsData?.length,
    regionsStatus,
    userListData?.length,
    userListStatus,
  ])

  const { data: brandTableData, status: brandTableStatus } = useQuery({
    queryKey: [
      'brand_assignment_table_data',
      brandAssignmentTableEndPoint,
      searchInput,
      sortBy,
      refreshTable,
    ],
    queryFn: async () => {
      const params = {
        ...(searchInput ? { search_term: searchInput } : {}),
        sort: standardSortParams(sortBy, ['role_name', 'manager_name']),
      }
      try {
        const response = await SecureAxios.get(brandAssignmentTableEndPoint, {
          params,
        })
        setRefreshTable(false)
        return response.data
      } catch (error) {
        setRefreshTable(false)
        console.error('Error fetching brand assignment table data:', error)
        throw error // Re-throwing the error allows React Query to handle it
      }
    },
  })

  useEffect(() => {
    if (
      regionsData?.length &&
      brandTableData?.regions?.length &&
      isBrandAssignmentDrawerOpen &&
      editWorkflowType !== WORKFLOW_TYPES.EDIT_REGION
    ) {
      const selectedCreateRegionData = brandTableData?.regions?.filter(
        (region: BrandAssignmentTableProps) => {
          return region?.region_id === selectedCreateRegion?.id
        },
      )
      setSelectedRegionData(selectedCreateRegionData[0])
    }
  }, [
    brandTableData?.regions,
    editWorkflowType,
    isBrandAssignmentDrawerOpen,
    regionsData?.length,
    selectedCreateRegion?.id,
  ])

  /************************************************************************************************
   * BrandAssignments TABLE CONFIG
   ***********************************************************************************************/
  const BrandAssignmentsTableConfig = useMemo(
    () => [
      {
        label: 'Role',
        name: 'role_name',
        mainColumn: true,
        cell: {
          children: (data: BrandAssignmentTableProps) => {
            return (
              <MdashCheck
                check={!!data.role_name}
                customClass={`${
                  sortBy.prop === 'role_name' ? 'fw-semi-bold' : ''
                }`}
              >
                {data.role_name}
              </MdashCheck>
            )
          },
        },
      },
      {
        label: 'Name',
        name: 'manager_name',
        style: {
          minWidth: '200px',
        },
        cell: {
          children: (data: BrandAssignmentTableProps) => {
            return (
              <MdashCheck
                check={!!data.manager_name}
                customClass={`word-break ${
                  sortBy.prop === 'manager_name' ? 'fw-semi-bold' : ''
                }`}
              >
                {data.manager_name}
              </MdashCheck>
            )
          },
        },
      },
      {
        label: 'Email Address',
        name: 'email',
        noSort: true,
        style: {
          minWidth: '250px',
        },
        cell: {
          children: (data: BrandAssignmentTableProps) => {
            return (
              <MdashCheck check={!!data.email} customClass='word-break'>
                {data.email}
              </MdashCheck>
            )
          },
        },
      },
      ...(isPatternInternalUser
        ? [
            {
              label: '',
              name: '',
              noSort: true,
              isButton: true,
              cell: {
                children: (data: BrandAssignmentTableProps) => {
                  return (
                    <Button
                      as='button'
                      className='pat-mr-2'
                      onClick={() => {
                        setEditWorkflowType(WORKFLOW_TYPES.EDIT_ASSIGNMENT)
                        setIsEditAssignmentSideDrawer(true)
                        setSelectedAssignmentData(data)
                      }}
                    >
                      Edit Assignment
                    </Button>
                  )
                },
              },
            },
          ]
        : []),
    ],
    [sortBy, isPatternInternalUser],
  )

  const createAssignmentPostAction = () => {
    setSelectedRegionData(selectedRegionData)
    setIsBrandAssignmentDrawerOpen(true)
  }

  const getStandardTable = (
    searchInput: string | number,
    brandTableData: BrandAssignmentTableProps[],
  ) => (
    <StandardTable
      data={brandTableData ? brandTableData : []}
      config={BrandAssignmentsTableConfig}
      dataKey='region_id'
      getData={() => {
        /** Data provided on the `brand_assignment` object */
      }}
      hasData={brandTableData?.length > 0}
      hasMore={false}
      successStatus={brandTableStatus === 'success'}
      loading={brandTableStatus === 'pending'}
      tableId={tableId}
      noDataFields={{
        primaryText: 'No Assignments to Display',
        ...(searchInput === ''
          ? {
              secondaryText:
                'Create assignments for different regions this brand operates in.',
              ...(hasCustomerWritePermission
                ? {
                    buttonProps: {
                      children: 'CREATE ASSIGNMENT',
                      onClick: () => createAssignmentPostAction(),
                    },
                  }
                : {}),
            }
          : {
              secondaryText: c('searchDidNotReturnResults'),
            }),
      }}
      mainColumnClassName='pat-mx-4'
      sort={(sortObj) =>
        setSortBy({
          prop: sortObj.activeColumn,
          flip: sortObj.direction,
        })
      }
      sortBy={sortBy}
      widthOffset={350}
      customHeight={
        brandTableData?.length > 6 ? 450 : 'auto' // if records are below 6 then make sure height of table will dynamic
      }
    />
  )

  return (
    <>
      {!brandTableData?.regions?.length && searchInput === '' ? (
        getStandardTable(searchInput, [])
      ) : (
        <>
          <div
            className={`${
              isMobileView
                ? 'pat-pb-4'
                : 'flex justify-content-between pat-mt-6 pat-mb-8'
            }`}
          >
            <SearchBar
              value={searchInput}
              onChange={setSearchInput}
              placeholder='Search Name or Role'
            />
            {hasCustomerWritePermission && (
              <div className={`${isMobileView ? 'pat-mt-4' : ''}`}>
                <Button
                  as='button'
                  styleType='primary-green'
                  className='full-width'
                  onClick={() => createAssignmentPostAction()}
                >
                  Create Assignment
                </Button>
              </div>
            )}
          </div>
          {(!brandTableData?.regions?.length && searchInput !== '') ||
          brandTableStatus === 'pending' ? (
            getStandardTable(searchInput, [])
          ) : (
            <div className='brandAssignmentContainer pat-mt-4'>
              {brandTableData?.regions?.map(
                (region: BrandAssignmentRegionTableProps) => {
                  return (
                    <div
                      key={region.region_id}
                      className='large-box-metric box bgc-faint-gray pat-mb-4'
                    >
                      <div className='pat-m-4 flex justify-content-between'>
                        <div className='align-self-center'>
                          {region.region_name}
                        </div>
                        {hasCustomerWritePermission && (
                          <div>
                            <Button
                              as='button'
                              onClick={() => {
                                setSelectedRegionData(region)
                                setEditWorkflowType(WORKFLOW_TYPES.EDIT_REGION)
                                setIsBrandAssignmentDrawerOpen(true)
                              }}
                            >
                              Edit Region
                            </Button>
                          </div>
                        )}
                      </div>
                      <div className={`bgc-white bdrt bdrc-light-gray `}>
                        <div className='pat-p-4'>
                          {getStandardTable(searchInput, region?.table_data)}
                        </div>
                      </div>
                    </div>
                  )
                },
              )}
            </div>
          )}
        </>
      )}
      <BrandAssignmentsSideDrawer
        isBrandAssignmentDrawerOpen={isBrandAssignmentDrawerOpen}
        setIsBrandAssignmentDrawerOpen={setIsBrandAssignmentDrawerOpen}
        userListData={userListData}
        userListStatus={userListStatus}
        departmentRoleData={deptAndRoleData}
        deptAndRoleStatus={deptAndRoleStatus}
        regionsData={regionsData ?? []}
        regionsStatus={regionsStatus}
        setRefreshTable={setRefreshTable}
        isApiStatusFailed={isApiStatusFailed}
        editWorkflowType={editWorkflowType}
        selectedRegionData={selectedRegionData}
        setSelectedRegionData={setSelectedRegionData}
        setEditWorkflowType={setEditWorkflowType}
        setSelectedCreateRegion={setSelectedCreateRegion}
      />
      <EditAssignmentSideDrawer
        isEditAssignmentSideDrawer={isEditAssignmentSideDrawer}
        setIsEditAssignmentSideDrawer={setIsEditAssignmentSideDrawer}
        editWorkflowType={editWorkflowType}
        userListData={userListData}
        departmentRoleData={deptAndRoleData}
        regionsData={regionsData ?? []}
        setRefreshTable={setRefreshTable}
        selectedRegionData={selectedRegionData}
        setSelectedRegionData={setSelectedRegionData}
        selectedAssignmentData={selectedAssignmentData}
        setSelectedAssignmentData={setSelectedAssignmentData}
        isApiStatusFailed={isApiStatusFailed}
      />
    </>
  )
}

export default BrandAssignments
