import React, {
  useCallback,
  useContext,
  useEffect,
  useMemo,
  useState,
} from 'react'
import { useMutation, useQueryClient } from '@tanstack/react-query'
import {
  Alert,
  Button,
  EmptyState,
  FormFooter,
  getApiUrlPrefix,
  Menu,
  PopoverNew,
  SectionHeader,
  Select,
  SideDrawer,
  toast,
} from '@patterninc/react-ui'

import { c, SecureAxios, useTranslate } from '../../../../../common/services'
import { ThemeContext } from '../../../../../Context'
import { WORKFLOW_TYPES } from './BrandAssignmentConstants'
import {
  type AddRoleAndUserProps,
  type BrandAssignmentsSideDrawerProps,
  type CreateAssignmentProps,
  type DepartmentRoleSelectionProps,
  type RolesDataProps,
  type UserSelectionProps,
} from './BrandAssignmentsType'

const BrandAssignmentsSideDrawer = ({
  isBrandAssignmentDrawerOpen,
  setIsBrandAssignmentDrawerOpen,
  userListData,
  departmentRoleData,
  regionsData,
  isApiStatusFailed,
  editWorkflowType,
  selectedRegionData,
  setSelectedRegionData,
  setEditWorkflowType,
  setSelectedCreateRegion,
}: BrandAssignmentsSideDrawerProps): React.JSX.Element => {
  const { customer } = useContext(ThemeContext)
  const { t } = useTranslate('brands')
  const queryClient = useQueryClient()

  const isDataAvailable = useMemo(
    () =>
      !!regionsData?.length &&
      !!userListData?.length &&
      !!departmentRoleData?.length,
    [regionsData, userListData, departmentRoleData],
  )

  const isEditWorflow = useMemo(
    () => editWorkflowType === WORKFLOW_TYPES.EDIT_REGION,
    [editWorkflowType],
  )

  const isEditCreateWorkflow = useMemo(() => {
    if (isBrandAssignmentDrawerOpen && !isEditWorflow) {
      return true
    }
    return false
  }, [isBrandAssignmentDrawerOpen, isEditWorflow])

  const prevDeptRoleSelection = useMemo(() => {
    return departmentRoleData?.map((dept) => {
      const { roles, id, name } = dept
      const selectedDept = selectedRegionData?.table_data?.filter(
        (data) => data.department_name === name,
      )
      let userRoleSelection

      const defaultUserSelection = [
        {
          id: roles?.[0]?.id,
          name: roles?.[0]?.name,
          user_id: null,
          old_user_id: null,
          full_username: '',
          email: '',
          index: 0,
          action: '',
        },
        ...(roles.length > 1
          ? [
              {
                id: roles?.[1]?.id,
                name: roles?.[1]?.name,
                user_id: null,
                old_user_id: null,
                full_username: '',
                email: '',
                index: 1,
                action: '',
              },
            ]
          : []),
      ]

      const getOldManagerId = (deptId: number, roleId: number) => {
        let oldManagerId = null
        selectedRegionData?.table_data.forEach((extDept) => {
          if (extDept.department_id === deptId && extDept.role_id === roleId) {
            oldManagerId = extDept.manager_id
          }
        })
        return oldManagerId
      }

      switch (selectedDept?.length) {
        case 0:
          userRoleSelection = defaultUserSelection
          break
        case 1:
          userRoleSelection = [
            {
              id: selectedDept?.[0]?.role_id,
              name: selectedDept?.[0]?.role_name,
              user_id: selectedDept?.[0]?.manager_id,
              old_user_id: getOldManagerId(id, roles[0].id),
              full_username: selectedDept?.[0]?.manager_name,
              email: selectedDept?.[0]?.email || '',
              index: 0,
              action: '',
              assignmentId: selectedDept?.[0]?.assignment_id,
            },
            // extracting second default value in case only one user is selected in a single dept.
            ...(roles.length > 1
              ? [
                  selectedDept?.[0]?.role_id === defaultUserSelection?.[0].id
                    ? defaultUserSelection?.[1]
                    : defaultUserSelection?.[0],
                ]
              : []),
          ]
          break
        default:
          userRoleSelection = selectedDept?.map((roleDept, index) => {
            return {
              id: roleDept.role_id,
              name: roleDept.role_name,
              user_id: roleDept.manager_id,
              old_user_id: getOldManagerId(id, roleDept.role_id),
              full_username: roleDept.manager_name,
              email: roleDept.email || '',
              index: index,
              action: '',
              assignmentId: roleDept?.assignment_id,
            }
          })
      }

      return {
        id: id,
        name: name,
        roles: dept.roles,
        userRoleSelection: userRoleSelection || defaultUserSelection,
      }
    })
  }, [departmentRoleData, selectedRegionData])

  const departmentRoleSelectionData = useMemo(() => {
    return isEditWorflow || isEditCreateWorkflow
      ? prevDeptRoleSelection
      : departmentRoleData?.map((dept) => {
          const { roles, id, name } = dept

          const userRoleSelection = [
            {
              id: roles?.[0]?.id,
              name: roles?.[0]?.name,
              user_id: null,
              old_user_id: null,
              full_username: '',
              email: '',
              index: 0,
              action: '',
            },
            ...(roles.length > 1
              ? [
                  {
                    id: roles?.[1]?.id,
                    name: roles?.[1]?.name,
                    user_id: null,
                    old_user_id: null,
                    full_username: '',
                    email: '',
                    index: 1,
                    action: '',
                  },
                ]
              : []),
          ]

          return {
            id: id,
            name: name,
            roles: dept.roles,
            userRoleSelection: userRoleSelection,
          }
        })
  }, [
    departmentRoleData,
    isEditCreateWorkflow,
    isEditWorflow,
    prevDeptRoleSelection,
  ])

  const getSelectedRegion = useMemo(() => {
    if (isEditWorflow || isEditCreateWorkflow) {
      return {
        id: selectedRegionData?.region_id,
        region_name: selectedRegionData?.region_name,
      }
    }

    return regionsData?.length
      ? {
          id: regionsData?.[0]?.id,
          region_name: regionsData?.[0]?.region_name,
        }
      : {
          id: 0,
          region_name: 'None',
        }
  }, [
    isEditCreateWorkflow,
    isEditWorflow,
    regionsData,
    selectedRegionData?.region_id,
    selectedRegionData?.region_name,
  ])

  const departmentRoleSelectionDataReset = () => {
    return departmentRoleData?.map((dept) => {
      const { roles, id, name } = dept

      const userRoleSelection = [
        {
          id: roles?.[0]?.id,
          name: roles?.[0]?.name,
          user_id: null,
          old_user_id: null,
          full_username: '',
          email: '',
          index: 0,
          action: '',
        },
        {
          id: roles?.[1]?.id,
          name: roles?.[1]?.name,
          user_id: null,
          old_user_id: null,
          full_username: '',
          email: '',
          index: 1,
          action: '',
        },
      ]

      return {
        id: id,
        name: name,
        roles: dept.roles,
        userRoleSelection: userRoleSelection,
      }
    })
  }

  const getSelectedRegionReset = () =>
    regionsData?.length
      ? {
          id: regionsData?.[0]?.id,
          region_name: regionsData?.[0]?.region_name,
        }
      : {
          id: 0,
          region_name: 'None',
        }

  const [selectedRegion, setSelectedRegion] = useState(getSelectedRegion)

  const [departmentRoleSelection, setDepartmentRoleSelection] = useState(
    departmentRoleSelectionData,
  )

  useEffect(() => {
    setDepartmentRoleSelection(departmentRoleSelectionData)
  }, [departmentRoleSelectionData, selectedRegion, selectedRegionData])

  useEffect(() => {
    if (getSelectedRegion?.id && getSelectedRegion?.region_name) {
      setSelectedRegion(getSelectedRegion)
      setSelectedCreateRegion({
        id: getSelectedRegion.id,
        name: getSelectedRegion.region_name,
      })
    }
  }, [getSelectedRegion, setSelectedCreateRegion])

  const handleCloseSidedrawer = () => {
    setDepartmentRoleSelection(departmentRoleSelectionDataReset())
    setSelectedRegion(getSelectedRegionReset())
    setIsBrandAssignmentDrawerOpen(false)
    setSelectedRegionData(undefined)

    // in case of edit workflow type
    setEditWorkflowType('')
  }

  const handleUserChange = useCallback(
    (
      value: UserSelectionProps,
      deptId: number,
      roleId: number,
      roleName: string,
      index: number,
    ) => {
      if (value?.full_user_name) {
        const updatedDepartmentRoleSelection = departmentRoleSelection.map(
          (dept) => {
            if (dept.id === deptId) {
              const selectedRegionByDept =
                selectedRegionData?.table_data.filter(
                  (reg) => reg.department_id === deptId,
                )
              const updatedUserRole = dept.userRoleSelection.map((user) => {
                if (roleId === user.id && index === user.index) {
                  user.full_username = value.full_user_name
                  user.user_id = value.id
                  user.email = value.email
                  user.index = index
                  user.id = roleId
                  user.name = roleName

                  // this is to check if in edit flow if preselected array of same dept has length less than current index that means new data has been added
                  if (
                    (isEditWorflow || isEditCreateWorkflow) &&
                    selectedRegionByDept !== undefined
                  ) {
                    user.action =
                      index > selectedRegionByDept?.length - 1
                        ? 'create'
                        : 'update'
                  } else if (!isEditWorflow) {
                    // this is to add create as action to all the new created assignment
                    user.action = 'create'
                  }
                }

                return user
              })
              dept.userRoleSelection = updatedUserRole
            }
            return dept
          },
        )

        setDepartmentRoleSelection(updatedDepartmentRoleSelection)
      }
    },
    [
      departmentRoleSelection,
      isEditCreateWorkflow,
      isEditWorflow,
      selectedRegionData?.table_data,
    ],
  )

  const appendRolesToDepartment = useCallback(
    (
      value: {
        id: number
        name: string
      },
      deptId: number,
    ) => {
      const updatedDepartmentRoleSelection = departmentRoleSelection.map(
        (dept) => {
          if (dept.id === deptId) {
            const { id, name } = dept
            const userRoleSelection = [...dept.userRoleSelection]

            const newUserRoleSelection = {
              id: value.id,
              name: value.name,
              user_id: null,
              old_user_id: null,
              full_username: '',
              email: '',
              index: userRoleSelection.length,
              action: '',
            }

            userRoleSelection.push(newUserRoleSelection)
            const newDept = {
              id: id,
              name: name,
              roles: dept.roles,
              userRoleSelection,
            }

            return newDept
          }
          return dept
        },
      )
      setDepartmentRoleSelection(updatedDepartmentRoleSelection)
    },
    [departmentRoleSelection],
  )

  const isSaveButtonDisabled = useMemo(() => {
    let userSelectionCount = 0
    departmentRoleSelection?.forEach((dept) => {
      dept?.userRoleSelection?.forEach((userRole) => {
        userRole?.user_id && userSelectionCount++
      })
    })

    return !userSelectionCount
  }, [departmentRoleSelection])

  const constructPayload = useCallback(() => {
    const departmentsAndRoles: CreateAssignmentProps[] = []
    departmentRoleSelection.forEach((dept) => {
      const roles = dept.userRoleSelection.filter(
        (userRole) =>
          userRole.user_id !== null && userRole.full_username !== 'None',
      )
      if (roles.length > 0) {
        departmentsAndRoles.push({
          id: dept.id,
          name: dept.name,
          roles: roles.map((userRole) => {
            const oldManagerId =
              (isEditWorflow || isEditCreateWorkflow) &&
              userRole?.old_user_id &&
              userRole.user_id !== userRole.old_user_id
                ? { old_manager_id: userRole.old_user_id }
                : {}
            const action = userRole.action ? { action: userRole.action } : {}

            return {
              id: userRole.id,
              name: userRole.name,
              manager_id: userRole.user_id,
              ...oldManagerId,
              ...action,
              manager_name: userRole.full_username,
              email: userRole.email,
            }
          }),
        })
      }
    })

    return {
      data: {
        regions: [
          {
            id: selectedRegion.id,
            name: selectedRegion.region_name,
            departments: departmentsAndRoles,
          },
        ],
      },
    }
  }, [
    departmentRoleSelection,
    isEditCreateWorkflow,
    isEditWorflow,
    selectedRegion.id,
    selectedRegion.region_name,
  ])

  const saveNewBrandAssignment = useMutation({
    mutationFn: (mutationParam: { apiUrl: string; apiParams: unknown }) => {
      return SecureAxios({
        method: isEditWorflow || isEditCreateWorkflow ? 'PUT' : 'POST',
        url: mutationParam.apiUrl,
        params: mutationParam.apiParams,
      })
    },
    onSuccess: () => {
      toast({
        type: 'success',
        message: t('newAssignmentUpdated'),
        config: {
          autoClose: 3000,
        },
      })
      queryClient.invalidateQueries({
        queryKey: ['brand_assignment_table_data'],
      })
      handleCloseSidedrawer()
    },
    onError: (error: string) => {
      toast({
        type: 'error',
        message: `Failed to save the assignment. Please try again.`,
        config: {
          autoClose: 3000,
        },
      })
      throw error
    },
  })

  const deleteBrandAssignment = useMutation({
    mutationFn: (args: { apiUrl: string }) => {
      return SecureAxios.delete(args.apiUrl)
    },
    onSuccess: () => {
      toast({
        type: 'success',
        message: t('assignmentDeletionSuccess'),
      })
      queryClient.invalidateQueries({
        queryKey: ['brand_assignment_table_data'],
      })
      handleCloseSidedrawer()
    },
    onError: (error) => {
      toast({
        type: 'error',
        message: t('assignmentDeletionError'),
      })
      throw error
    },
  })

  const checkUniqueRoles = (deprtRole: CreateAssignmentProps[]) => {
    for (let i = 0; i < deprtRole.length; i++) {
      const userRoleSelection = deprtRole[i].roles
      for (let j = 0; j < userRoleSelection.length; j++) {
        for (let k = j + 1; k < userRoleSelection.length; k++) {
          if (
            userRoleSelection[j].id === userRoleSelection[k].id &&
            userRoleSelection[j].manager_id === userRoleSelection[k].manager_id
          ) {
            return false
          }
        }
      }
    }
    return true
  }

  const SideDrawerFooterContent = () => {
    return (
      <FormFooter
        saveButtonProps={{
          onClick: () => {
            const payloadParams = constructPayload()

            // validation if any role and user in single department is same
            if (!checkUniqueRoles(payloadParams.data.regions[0].departments)) {
              toast({
                type: 'warning',
                message: `Same department cannot have duplicate roles assigned with same user.`,
                config: {
                  autoClose: 3000,
                },
              })
            } else {
              saveNewBrandAssignment?.mutate({
                apiUrl: `${getApiUrlPrefix(
                  'iserve',
                )}//api/v6/brand_assignments/customers/${customer?.id}`,
                apiParams: payloadParams,
              })
            }
          },
          children: 'Save Changes',
          disabled:
            isSaveButtonDisabled ||
            saveNewBrandAssignment.isPending ||
            deleteBrandAssignment.isPending,
        }}
        cancelButtonProps={{
          onClick: () => {
            // Add more methods to reset side drawer fields
            handleCloseSidedrawer()
          },
        }}
      />
    )
  }

  const AddRoleAndUser = ({ department }: AddRoleAndUserProps) => {
    const getRolesOptionByDepartment = (
      department: DepartmentRoleSelectionProps,
    ) =>
      department.roles.map((role: RolesDataProps) => {
        return {
          ...role,
          name: role.name,
          label: role.name,
        }
      })

    return (
      <div className='pat-mt-4'>
        <SectionHeader
          title={department.name}
          rightDisplay={
            <PopoverNew
              position='bottom'
              noPadding
              popoverContent={({ setVisible }) => {
                const roleOptions = getRolesOptionByDepartment(department)
                const menuActions = roleOptions.map((role) => ({
                  text: role.name,
                  callout: () => {
                    if (role.id) {
                      appendRolesToDepartment(role, department.id)
                    }
                  },
                }))

                return menuActions.length > 0 ? (
                  <Menu actions={menuActions} close={() => setVisible(false)} />
                ) : (
                  <EmptyState primaryText={t('noAdditionalRoles')} />
                )
              }}
            >
              {({ setVisible }) => (
                <Button styleType='text-blue' onClick={() => setVisible(true)}>
                  {t('addRole')}
                </Button>
              )}
            </PopoverNew>
          }
        />
        {department.userRoleSelection.map((userRole, index) => {
          return (
            <div
              className='pat-mt-4 flex align-items-center pat-gap-2'
              key={userRole.name}
            >
              <div style={{ width: '-webkit-fill-available' }}>
                <Select
                  labelProps={{ label: userRole.name }}
                  options={userListData}
                  optionKeyName='full_user_name'
                  labelKeyName='full_user_name'
                  selectedItem={
                    {
                      full_user_name: userRole?.full_username || c('none'),
                    } as UserSelectionProps
                  }
                  onChange={(option) => {
                    handleUserChange(
                      option,
                      department.id,
                      userRole.id,
                      userRole.name,
                      index,
                    )
                  }}
                />
              </div>
              {userRole.assignmentId && (
                <Button
                  as='confirmation'
                  icon='trash'
                  confirmation={{
                    body: t('assignmentDeletionWarning'),
                    confirmCallout: () =>
                      deleteBrandAssignment.mutate({
                        apiUrl: `${getApiUrlPrefix('iserve')}/api/v6/brand_assignments/${userRole.assignmentId}`,
                      }),
                    header: c('areYouSure'),
                    type: 'red',
                  }}
                  destructive
                  className='pat-mt-4'
                />
              )}
            </div>
          )
        })}
      </div>
    )
  }

  const renderSideDrawerContent = () => {
    switch (true) {
      case saveNewBrandAssignment.isPending:
        return (
          <EmptyState
            primaryText={c('hangOnForOneMoment')}
            secondaryText={t('savingBrandAssignmentsData')}
          />
        )
      case deleteBrandAssignment.isPending:
        return (
          <EmptyState
            primaryText={c('hangOnForOneMoment')}
            secondaryText={t('deletingBrandAssignment')}
          />
        )
      case !regionsData?.length:
        return (
          <Alert
            type='warning'
            text='No regions or marketplaces found for selected brand.'
          />
        )
      case isApiStatusFailed:
        return (
          <Alert
            type='error'
            text='Failed to load regions/users/departments. Please try again.'
          />
        )
      case !isDataAvailable:
        return (
          <EmptyState
            primaryText={c('hangOnForOneMoment')}
            secondaryText={t('loadingRegionsAndUsersData')}
          />
        )
      case isBrandAssignmentDrawerOpen:
        return (
          <>
            <Select
              labelProps={{ label: 'Select a Region' }}
              options={regionsData}
              optionKeyName='region_name'
              labelKeyName='region_name'
              selectedItem={selectedRegion}
              onChange={(option) => {
                setSelectedCreateRegion({
                  id: option.id ?? 0,
                  name: option.region_name ?? '',
                })
                setSelectedRegion({
                  id: option.id,
                  region_name: option.region_name,
                })
              }}
              required={true}
              disabled={isEditWorflow}
            />

            {departmentRoleSelection?.length &&
              departmentRoleSelection.map((department, index) => {
                return (
                  <AddRoleAndUser
                    key={department?.id}
                    department={department}
                    index={index}
                  />
                )
              })}
          </>
        )
      default:
        return null
    }
  }

  return (
    <SideDrawer
      isOpen={isBrandAssignmentDrawerOpen}
      closeCallout={() => handleCloseSidedrawer()}
      headerContent={
        isEditWorflow ? 'Edit Assignment' : 'Create New Assignment'
      }
      footerContent={<SideDrawerFooterContent />}
    >
      {renderSideDrawerContent()}
    </SideDrawer>
  )
}

export default BrandAssignmentsSideDrawer
