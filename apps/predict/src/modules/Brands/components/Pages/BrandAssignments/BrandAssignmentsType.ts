export interface UserResponseDataProps {
  id: number
  username: string
  email: string
  first_name: string
  last_name: string
  associated_group_name: string
  full_user_name: string
}

export interface RolesDataProps {
  id: number
  name: string
}

export interface RegionsDataProps {
  id: number
  is_selective_distribution: boolean
  region_name: string
}
export interface DepartmentRoleResponseDataProps {
  id: number
  name: string
  roles: RolesDataProps[]
}

export interface BrandAssignmentsSideDrawerProps {
  isBrandAssignmentDrawerOpen: boolean
  setIsBrandAssignmentDrawerOpen: React.Dispatch<React.SetStateAction<boolean>>
  userListData: UserResponseDataProps[]
  userListStatus: string
  departmentRoleData: DepartmentRoleResponseDataProps[]
  deptAndRoleStatus: string
  regionsData: RegionsDataProps[]
  regionsStatus: string
  setRefreshTable: React.Dispatch<React.SetStateAction<boolean>>
  isApiStatusFailed: boolean
  editWorkflowType: string
  selectedRegionData: BrandAssignmentRegionTableProps | undefined
  setSelectedRegionData: React.Dispatch<
    React.SetStateAction<BrandAssignmentRegionTableProps | undefined>
  >
  setEditWorkflowType: React.Dispatch<React.SetStateAction<string>>
  setSelectedCreateRegion: React.Dispatch<React.SetStateAction<RolesDataProps>>
}

export type UserSelectionProps = {
  id: number | null
  full_user_name: string
  email: string
}

export interface UserRoleSelectionProps {
  id: number
  name: string
  user_id: number | null
  full_username: string
  old_user_id?: number | null
  assignmentId?: number
}
export interface DepartmentRoleSelectionProps {
  id: number
  name: string
  userRoleSelection: UserRoleSelectionProps[]
  roles: RolesDataProps[]
}
export interface EditAssignmentsSideDrawerProps {
  isEditAssignmentSideDrawer: boolean
  setIsEditAssignmentSideDrawer: React.Dispatch<React.SetStateAction<boolean>>
  editWorkflowType: string
  regionsData: RegionsDataProps[]
  userListData: UserResponseDataProps[]
  departmentRoleData: DepartmentRoleResponseDataProps[]
  setRefreshTable: React.Dispatch<React.SetStateAction<boolean>>
  selectedRegionData: BrandAssignmentRegionTableProps | undefined
  setSelectedRegionData: React.Dispatch<
    React.SetStateAction<BrandAssignmentRegionTableProps | undefined>
  >
  selectedAssignmentData: BrandAssignmentTableProps | undefined
  setSelectedAssignmentData: React.Dispatch<
    React.SetStateAction<BrandAssignmentTableProps | undefined>
  >
  isApiStatusFailed: boolean
}
export interface AddRoleAndUserProps {
  department: DepartmentRoleSelectionProps
  index: number
}

export interface BrandAssignmentTableProps {
  name: string
  department_id: number
  department_name: string
  role_name: string
  role_id: number
  manager_name: string
  manager_id: number
  email: string
  region_id: number
  region_name: string
  assignment_id: number
}

export interface BrandAssignmentRegionTableProps {
  region_name: string
  region_id: number
  table_data: BrandAssignmentTableProps[]
}

export interface CreateAssignmentProps {
  id: number
  name: string
  roles: {
    id: number
    name: string
    manager_id: number | null
    manager_name: string
    email: string
  }[]
}
