import React, { useContext, useEffect, useMemo, useState } from 'react'
import {
  <PERSON><PERSON>,
  But<PERSON>,
  EmptyState,
  FormFooter,
  getApiUrlPrefix,
  hasValue,
  Select,
  SideDrawer,
  toast,
  type ToastProps,
} from '@patterninc/react-ui'

import { SecureAxios } from '../../../../../common/services'
import { ThemeContext } from '../../../../../Context'
import { EDIT_OPTION_TYPES, WORKFLOW_TYPES } from './BrandAssignmentConstants'
import {
  type EditAssignmentsSideDrawerProps,
  type RolesDataProps,
} from './BrandAssignmentsType'

const EditAssignmentSideDrawer = ({
  isEditAssignmentSideDrawer,
  setIsEditAssignmentSideDrawer,
  editWorkflowType,
  regionsData,
  userListData,
  setRefreshTable,
  departmentRoleData,
  selectedRegionData,
  selectedAssignmentData,
  setSelectedRegionData,
  setSelectedAssignmentData,
  isApiStatusFailed,
}: EditAssignmentsSideDrawerProps): React.JSX.Element => {
  const [state, setState] = useState({
      selectedRegion: {
        id: 0,
        region_name: '',
      },
      selectedRole: {
        id: 0,
        name: '',
      },
      selectedUser: {
        id: 0,
        full_user_name: '',
      },
    }),
    { customer } = useContext(ThemeContext),
    [isEditingAssignment, setIsEditingAssignment] = useState(false),
    [isDeletingAssignment, setIsDeletingAssignment] = useState(false)

  const { selectedRegion, selectedRole, selectedUser } = state

  const getRolesByDepartment = useMemo(() => {
    const rolesList: RolesDataProps[] = []
    departmentRoleData?.forEach((dept) => {
      dept?.roles?.forEach((role) => rolesList.push(role))
    })
    return rolesList
  }, [departmentRoleData])

  const checkIfRegionAssignment =
    editWorkflowType === WORKFLOW_TYPES.EDIT_REGION
  const checkIfAssignment = editWorkflowType === WORKFLOW_TYPES.EDIT_ASSIGNMENT

  const getSelectedRegion = useMemo(() => {
    return {
      id:
        checkIfRegionAssignment && selectedRegionData
          ? selectedRegionData?.region_id
          : selectedAssignmentData
            ? selectedAssignmentData?.region_id
            : regionsData?.[0]?.id,
      region_name:
        checkIfRegionAssignment && selectedRegionData
          ? selectedRegionData?.region_name
          : selectedAssignmentData
            ? selectedAssignmentData?.region_name
            : regionsData?.[0]?.region_name,
    }
  }, [
    checkIfRegionAssignment,
    selectedRegionData,
    selectedAssignmentData,
    regionsData,
  ])

  const getSelectedRole = useMemo(() => {
    return {
      id:
        checkIfAssignment && selectedAssignmentData
          ? selectedAssignmentData?.role_id
          : getRolesByDepartment?.[0]?.id,
      name:
        checkIfAssignment && selectedAssignmentData
          ? selectedAssignmentData?.role_name
          : getRolesByDepartment?.[0]?.name,
    }
  }, [checkIfAssignment, selectedAssignmentData, getRolesByDepartment])

  const getSelectedUser = useMemo(() => {
    return {
      id:
        checkIfAssignment && selectedAssignmentData
          ? selectedAssignmentData?.manager_id
          : 0,
      name:
        checkIfAssignment && selectedAssignmentData
          ? selectedAssignmentData?.manager_name
          : '',
    }
  }, [checkIfAssignment, selectedAssignmentData])

  useEffect(() => {
    // set the preselected value when edit API is in place instead of the first value from regionsData
    setState((prevState) => ({
      ...prevState,
      selectedRegion: {
        id: getSelectedRegion.id,
        region_name: getSelectedRegion.region_name,
      },
    }))

    // set the preselected value when edit API is in place instead of the first value from getRolesByDepartment
    setState((prevState) => ({
      ...prevState,
      selectedRole: {
        id: getSelectedRole.id,
        name: getSelectedRole.name,
      },
    }))

    // set the preselected value when edit API is in place instead of the first value from userListData
    setState((prevState) => ({
      ...prevState,
      selectedUser: {
        id: getSelectedUser.id,
        full_user_name: getSelectedUser.name,
      },
    }))
  }, [getSelectedRegion, getSelectedRole, getSelectedUser])

  const isSaveButtonDisabled = useMemo(() => {
    return !(selectedRegion?.id && selectedRole?.id && selectedUser?.id)
  }, [selectedRegion?.id, selectedRole?.id, selectedUser?.id])

  const isDataAvailable = useMemo(
    () =>
      !!regionsData?.length &&
      !!userListData?.length &&
      !!departmentRoleData?.length &&
      !!(selectedRegionData || selectedAssignmentData),
    [
      regionsData,
      userListData,
      departmentRoleData,
      selectedRegionData,
      selectedAssignmentData,
    ],
  )

  const toastCreator = (
    message: ToastProps['message'],
    type: ToastProps['type'],
    autoClose: number | false,
  ) => {
    const toastId = `assignment_toast`

    toast({
      message: message,
      type: type,
      config: {
        toastId,
        autoClose: autoClose,
      },
    })
  }

  const updateRegionsAssignmentEndpoint = `${getApiUrlPrefix(
    'iserve',
  )}/api/v6/brand_assignments/regions/${selectedRegionData?.region_id}`

  const updateDeleteAssignmentEndpoint = `${getApiUrlPrefix(
    'iserve',
  )}/api/v6/brand_assignments/${selectedAssignmentData?.assignment_id}`

  const callAssignmentAction = checkIfRegionAssignment
    ? setSelectedRegionData
    : setSelectedAssignmentData

  const handleEditAssignment = () => {
    if (!hasValue(selectedAssignmentData?.assignment_id)) return false
  }

  const handleAssignment = () => {
    if (checkIfAssignment) {
      handleEditAssignment()
    }
    if (
      !(
        hasValue(selectedRegion?.id) &&
        hasValue(selectedRole?.id) &&
        hasValue(selectedUser?.id)
      )
    )
      return false
    setIsEditingAssignment(true)
    SecureAxios.put(
      checkIfRegionAssignment
        ? updateRegionsAssignmentEndpoint
        : updateDeleteAssignmentEndpoint,
      {
        manager_id: selectedUser?.id,
        customer_id: customer?.id,
        role_id: selectedRole?.id,
        ...(checkIfAssignment
          ? {
              region_id: selectedRegion?.id,
            }
          : {}),
      },
    )
      .then(() => {
        setRefreshTable(true)
        setIsEditingAssignment(false)
        toastCreator('Assignment updated.', 'success', 5000)
        setIsEditAssignmentSideDrawer(false)
        callAssignmentAction(undefined)
      })
      .catch((error) => {
        setIsEditingAssignment(false)
        toastCreator(
          error?.response?.data?.error_message ===
            'Role is already assigned to the same user'
            ? `Role already exists. No updates made.`
            : `Failed to update the assignment. Please try again.`,
          'error',
          5000,
        )
      })
  }

  const handleDeleteAssignment = () => {
    setIsDeletingAssignment(true)
    SecureAxios.delete(`${updateDeleteAssignmentEndpoint}`)
      .then(() => {
        toastCreator('Assignment deleted.', 'success', 5000)
      })
      .catch(() => {
        setIsDeletingAssignment(false)
        toastCreator(
          `Failed to delete assignment. Please try again.`,
          'error',
          5000,
        )
      })
      .finally(() => {
        setRefreshTable(true)
        setIsDeletingAssignment(false)
        setIsEditAssignmentSideDrawer(false)
      })
  }

  const SideDrawerFooterContent = () => {
    return (
      <div className='flex justify-content-between'>
        <div className='pat-mt-2'>
          {editWorkflowType === WORKFLOW_TYPES.EDIT_ASSIGNMENT &&
          !isApiStatusFailed ? (
            <Button
              as='confirmation'
              styleType='text-red'
              confirmation={{
                body: 'This action cannot be undone.',
                confirmCallout: handleDeleteAssignment,
                header: 'DELETE ASSIGNMENT?',
                type: 'red',
                confirmButtonText: 'DELETE ASSIGNMENT',
              }}
              popoverProps={{
                position: 'top',
              }}
            >
              Delete
            </Button>
          ) : null}
        </div>
        <FormFooter
          saveButtonProps={{
            onClick: handleAssignment,
            children: 'Save Changes',
            disabled:
              isSaveButtonDisabled ||
              isApiStatusFailed ||
              !isDataAvailable ||
              isEditingAssignment ||
              isDeletingAssignment,
          }}
          cancelButtonProps={{
            onClick: () => {
              // Add more methods to reset side drawer fields
              setIsEditAssignmentSideDrawer(false)
              callAssignmentAction(undefined)
            },
          }}
        />
      </div>
    )
  }

  const handleOptionsChange = (
    optionType: string,
    value: {
      id: number
      full_user_name?: string
      region_name?: string
      name?: string
    },
  ) => {
    switch (optionType) {
      case EDIT_OPTION_TYPES.REGION:
        setState((prevState) => ({
          ...prevState,
          selectedRegion: {
            id: value.id,
            region_name: value.region_name || '',
          },
        }))
        break
      case EDIT_OPTION_TYPES.USER:
        setState((prevState) => ({
          ...prevState,
          selectedUser: {
            id: value.id,
            full_user_name: value.full_user_name || '',
          },
        }))
        break
      default:
        setState((prevState) => ({
          ...prevState,
          selectedRole: {
            id: value.id,
            name: value.name || '',
          },
        }))
    }
  }

  return (
    <SideDrawer
      isOpen={isEditAssignmentSideDrawer}
      closeCallout={() => {
        setIsEditAssignmentSideDrawer(false)
        callAssignmentAction(undefined)
      }}
      headerContent='Edit Assignment'
      footerContent={<SideDrawerFooterContent />}
    >
      {isApiStatusFailed ? (
        <Alert
          type='error'
          text='Failed to load regions/users/departments. Please try again.'
        />
      ) : !isDataAvailable || isEditingAssignment || isDeletingAssignment ? (
        <EmptyState
          primaryText='Hang on for one moment.'
          secondaryText={
            isEditingAssignment
              ? 'Updating assignment...'
              : isDeletingAssignment
                ? 'Deleting assignment...'
                : 'Loading regions and users and roles data...'
          }
        />
      ) : (
        <>
          <Select
            labelProps={{ label: 'Select a Region' }}
            options={regionsData}
            optionKeyName='region_name'
            labelKeyName='region_name'
            selectedItem={selectedRegion}
            onChange={(option) => {
              option?.id &&
                handleOptionsChange(EDIT_OPTION_TYPES.REGION, option)
            }}
            disabled
          />
          <div className='pat-mt-4'>
            <Select
              labelProps={{ label: 'Select a Role' }}
              options={getRolesByDepartment}
              optionKeyName='name'
              labelKeyName='name'
              selectedItem={selectedRole}
              onChange={(option) => {
                option?.id &&
                  handleOptionsChange(EDIT_OPTION_TYPES.ROLE, option)
              }}
              disabled={!checkIfRegionAssignment}
            />
          </div>
          <div className='pat-mt-4'>
            <Select
              labelProps={{ label: 'Select a Person' }}
              options={userListData}
              optionKeyName='full_user_name'
              labelKeyName='full_user_name'
              selectedItem={selectedUser}
              onChange={(option) => {
                option?.id &&
                  handleOptionsChange(EDIT_OPTION_TYPES.USER, option)
              }}
            />
          </div>
        </>
      )}
    </SideDrawer>
  )
}

export default EditAssignmentSideDrawer
