import React, { useEffect, useRef, useState } from 'react'
import {
  NavLink,
  Route,
  Routes,
  useLocation,
  useNavigate,
} from 'react-router-dom'
import { errorCheck, Pill, RouterTabs, toast } from '@patterninc/react-ui'
import { haveRoles, isInternalUser } from '@predict-services'

import { useAuth } from '../../../../context/auth-context'
import {
  cancelBrandNotes,
  fetchNotes,
} from '../../services/BrandDetailsService'
import BrandAssignments from './BrandAssignments/BrandAssignments'
import DetailsTab from './Details/DetailsTab'
import UserGroup from './Groups/UserGroup'
import Histories from './History/Histories'
import BrandInfo from './Info/BrandInfo'
import BrandNotes from './Notes/BrandNotes'

export type BrandDetailsCustomer = {
  id: number
  customer_name: string
  customer_desc: string
  primary_contact: {
    id: number
    first_name: string
    last_name: string
    email?: string
  }[]
  initial_signing_value: string | null
  exclusive_start_date: string
  customer_type: string
  customer_managers: {
    [label: string]: { id: number; full_user_name: string }[]
  }
  thumb_logo_url?: string
  logo_url?: string
}

type BrandDetailsProps = {
  customer: BrandDetailsCustomer
  customerRespStatus: string
  setError: unknown
  toasterMessage: string
  clearToasterMessage(): void
  createCustomer(): void
}

const BrandDetails = ({
  customer,
  customerRespStatus,
  setError,
  toasterMessage,
  clearToasterMessage,
  createCustomer,
}: BrandDetailsProps): React.JSX.Element => {
  const apiController = useRef<AbortController>(undefined),
    user = useAuth().user,
    hasSettingsUserAndGroupsPermissions = haveRoles(user, [
      'read_settings_user_and_groups',
    ]),
    hasSettingsCustomersPermissions = haveRoles(user, [
      'read_settings_customers',
    ]),
    hasInternalUserAccess = isInternalUser(user)

  const [notesCount, updateNotesCount] = useState(0),
    location = useLocation(),
    navigateTo = useNavigate()
  useEffect(() => {
    apiController.current = new AbortController()
    return () => {
      apiController.current?.abort()
    }
  }, [])

  const routeBase = `/brands/details`

  useEffect(() => {
    const params = {
      page: 1,
      per_page: 1,
      enabled: true,
    }

    fetchNotes(customer?.id, params, apiController.current?.signal)
      .then((response) => {
        updateNotesCount(response?.pagination?.count)
      })
      .catch((error) => {
        if (error && error?.status) {
          errorCheck(error?.status, setError)
        }
      })

    return () => {
      cancelBrandNotes()
    }
  }, [customer?.id, setError])

  useEffect(() => {
    clearToasterMessage()

    if (toasterMessage) {
      toast({
        type: 'success',
        message: toasterMessage,
      })
    }

    return () => {
      clearToasterMessage()
    }
  }, [toasterMessage, clearToasterMessage])

  const brandDetailsMobileTabs = [
    {
      label: 'Details',
      link: `${routeBase}/list`,
    },
    ...(hasSettingsUserAndGroupsPermissions
      ? [
          {
            label: 'Users & Groups',
            link: `${routeBase}/users_and_groups`,
          },
        ]
      : []),
    ...(hasInternalUserAccess
      ? [
          {
            label: 'Brand Assignments',
            link: `${routeBase}/brand-assignments`,
          },
        ]
      : []),
    ...(hasSettingsCustomersPermissions
      ? [
          {
            label: 'Notes',
            link: `${routeBase}/notes`,
          },
        ]
      : []),
    {
      label: 'History',
      link: `${routeBase}/history`,
    },
  ]

  const navigate = (link: string) => {
    navigateTo(link)
  }

  return (
    <div className='brand-details-page page-container has-profile-sidebar'>
      <BrandInfo customer={customer} />
      <div className='relative'>
        <RouterTabs
          mobileConfig={brandDetailsMobileTabs}
          navigate={navigate}
          currentPath={location.pathname}
        >
          <NavLink to={`${routeBase}/list`}>Details</NavLink>
          {hasSettingsUserAndGroupsPermissions && (
            <NavLink to={`${routeBase}/users_and_groups`}>
              Users & Groups
            </NavLink>
          )}
          {hasInternalUserAccess && (
            <NavLink to={`${routeBase}/brand-assignments`}>
              Brand Assignments
            </NavLink>
          )}
          {hasSettingsCustomersPermissions && (
            <NavLink
              className='routertabs-link-has-tag'
              to={`${routeBase}/notes`}
            >
              Notes
              <Pill
                color={
                  location.pathname === `${routeBase}/notes`
                    ? 'purple'
                    : 'medium-purple'
                }
                number={notesCount}
              />
            </NavLink>
          )}
          <NavLink to={`${routeBase}/history`}>History</NavLink>
        </RouterTabs>
        <Routes>
          {/** basepath : /brands/details/  */}
          <Route
            path='list/*'
            element={
              <DetailsTab
                customer={customer}
                customerRespStatus={customerRespStatus}
                createCustomer={createCustomer}
              />
            }
          />
          <Route
            path='users_and_groups/*'
            element={<UserGroup customer={customer} />}
          />
          {hasInternalUserAccess && (
            <Route path='brand-assignments/*' element={<BrandAssignments />} />
          )}
          <Route
            path='notes/*'
            element={
              <BrandNotes
                notesCount={notesCount}
                updateCount={updateNotesCount}
                customer={customer}
                setError={setError}
              />
            }
          />
          <Route path='history/*' element={<Histories />} />
        </Routes>
      </div>
    </div>
  )
}

export default BrandDetails
