import React from 'react'

import PrimaryDetails from './PrimaryDetails'

export type BrandDetailsCustomer = {
  id: number
  customer_name: string
  customer_desc: string
  primary_contact: {
    id: number
    first_name: string
    last_name: string
    email?: string
  }[]
  initial_signing_value: string | null
  exclusive_start_date: string
  customer_type: string
  customer_managers: {
    [label: string]: { id: number; full_user_name: string }[]
  }
  thumb_logo_url?: string
  logo_url?: string
}

type CustomerSettingNames =
  | 'only_authorized_sellers'
  | 'emails'
  | 'show_compliant_volume'
  | 'buybox_tracking'
  | 'inventory_collection'

export type DetailsListProps = {
  customer: BrandDetailsCustomer & {
    [SettingName in CustomerSettingNames]?: boolean | null
  }
  createCustomer(): void
  customerRespStatus: string
}

const DetailsTab = ({
  customer,
  createCustomer,
  customerRespStatus,
}: DetailsListProps) => {
  return (
    <PrimaryDetails
      createCustomer={createCustomer}
      customerRespStatus={customerRespStatus}
      brandDetailsCustomer={customer}
    />
  )
}

export default DetailsTab
