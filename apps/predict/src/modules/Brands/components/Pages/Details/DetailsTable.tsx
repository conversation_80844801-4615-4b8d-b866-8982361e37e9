import React, { useEffect, useState } from 'react'
import { isEqual, sortBy as sorter } from 'lodash'
import { Button, StandardTable } from '@patterninc/react-ui'
import { haveRoles } from '@predict-services'

import { useUser } from '../../../../../context/user-context'
import { type BrandDetailsCustomer } from '../BrandDetails'
import { SettingsTableConfig } from './TableConfigs'

const SETTINGS_TABLE_WIDTH = 'calc(100vw - 460px)'

const customerSettings = {
  only_authorized_sellers: 'Allow Only Authorized Sellers',
  emails: 'Send Emails',
  show_compliant_volume: 'Show Compliant Volume',
  buybox_tracking: 'Track Buy Box',
  inventory_collection: 'Track Inventory',
} as const

export type CustomerSettingNames = keyof typeof customerSettings

export type DetailsListProps = {
  customer: BrandDetailsCustomer & {
    [SettingName in CustomerSettingNames]?: boolean | null
  }
  createCustomer: (step: number) => void
  customerRespStatus: string
}

type SettingsData = {
  name: keyof DetailsListProps['customer']
  value: string | boolean | undefined
}

const DetailsList = ({
  customer,
  createCustomer,
  customerRespStatus,
}: DetailsListProps): React.JSX.Element => {
  const { user } = useUser(),
    [settingsData, setSettingsData] = useState<SettingsData[]>([]),
    [settingSortBy, setSettingSortBy] = useState<{
      prop: 'name' | 'value' | ''
      flip: boolean
    }>({ prop: '', flip: false })

  useEffect(() => {
    const settings = (
      Object.keys(customerSettings) as CustomerSettingNames[]
    ).map((setting) => {
      return {
        name: customerSettings[setting] as keyof DetailsListProps['customer'],
        value: customer[setting] ?? undefined, // TS complains about `null` values; `null` && `undefined` return the same result
      }
    })
    setSettingsData(settings)
  }, [customer])

  useEffect(() => {
    if (settingsData.length > 0) {
      const sortedSettings: SettingsData[] =
        settingSortBy.prop === 'name'
          ? settingSortBy.flip
            ? sorter(settingsData, ['name', 'value']).reverse()
            : sorter(settingsData, ['name', 'value'])
          : settingSortBy.flip
            ? sorter(settingsData, ['value', 'name']).reverse()
            : sorter(settingsData, ['value', 'name'])
      setSettingsData((prevSettingsData) =>
        isEqual(prevSettingsData, sortedSettings)
          ? prevSettingsData
          : sortedSettings,
      )
    }
  }, [settingSortBy, settingsData])

  const hasBrandWritePermission = haveRoles(user, ['write_settings_customers'])

  return (
    <div className='large-box-metric box bgc-faint-gray pat-mb-4'>
      <div className='pat-m-4 flex justify-content-between'>
        <div className='align-self-center'>Settings</div>
        {hasBrandWritePermission && (
          <div>
            <Button
              as='button'
              onClick={() => {
                createCustomer(1)
              }}
            >
              Edit
            </Button>
          </div>
        )}
      </div>

      <div className={`bgc-white bdrt bdrc-light-gray `}>
        <div className='pat-p-4'>
          <StandardTable
            data={settingsData}
            config={SettingsTableConfig(settingSortBy)}
            dataKey='value'
            getData={() => {
              /** There is no additional data to get...at this time */
            }}
            hasData={settingsData.length > 0}
            hasMore={false}
            successStatus={!!(customerRespStatus === 'success')}
            loading={!!(customerRespStatus === 'pending')}
            tableId='brand_details_settings_table'
            noDataFields={{ primaryText: 'No History found' }}
            mainColumnClassName='pat-mx-4'
            sortBy={settingSortBy}
            sort={(sortProps) => {
              setSettingSortBy({
                prop: sortProps.activeColumn as 'name' | 'value',
                flip: sortProps.direction,
              })
            }}
            customWidth={SETTINGS_TABLE_WIDTH}
            customHeight={320}
          />
        </div>
      </div>
    </div>
  )
}

export default DetailsList
