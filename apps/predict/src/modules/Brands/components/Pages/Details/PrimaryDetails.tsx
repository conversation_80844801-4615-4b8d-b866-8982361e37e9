import React, { useContext, useMemo, useState } from 'react'
import { useQuery } from '@tanstack/react-query'
import {
  Button,
  getApiUrlPrefix,
  MdashCheck,
  StandardTable,
} from '@patterninc/react-ui'
import {
  handleError,
  haveRoles,
  type ModifiedErrorType,
  SecureAxios,
} from '@predict-services'

import Currency from '../../../../../common/components/Currency/Currency'
import { ThemeContext } from '../../../../../Context'
import { useUser } from '../../../../../context/user-context'
import { type BrandDetailsCustomer } from '../BrandDetails'
import DetailsList, { type CustomerSettingNames } from './DetailsTable'

type Setting = {
  name: string
  value: string
  email?: string
}

type PrimaryDetailsProps = {
  createCustomer: (step: number) => void
  customerRespStatus: string
  brandDetailsCustomer: BrandDetailsCustomer & {
    [SettingName in CustomerSettingNames]?: boolean | null
  }
}

const SETTINGS_TABLE_WIDTH = 'calc(100vw - 460px)'

const PrimaryDetails = ({
  createCustomer,
  customerRespStatus,
  brandDetailsCustomer,
}: PrimaryDetailsProps) => {
  const { customer } = useContext(ThemeContext)

  const [settingSortBy, setSettingSortBy] = useState({ prop: '', flip: false })
  const user = useUser().user,
    currency = user?.current_currency,
    hasCustomerWritePermission = haveRoles(user, ['write_settings_customers'])

  const primaryDetailsAPI = `${getApiUrlPrefix('iserve')}/api/v6/customers/${
    customer.id
  }/primary_details`

  const { data: settingsData, status: settingsStatus } = useQuery({
    queryKey: [],
    queryFn: async ({ signal }) => {
      try {
        const response = await SecureAxios.get(primaryDetailsAPI, { signal })
        const { data } = response.data
        return [
          {
            name: 'Customer Name',
            value: data?.name,
          },
          {
            name: 'Opportunity Source',
            value: data?.opportunity_source,
          },
          {
            name: 'Sales Person',
            value: data?.sales_person.first_name
              ? `${data.sales_person.first_name} ${data.sales_person.last_name}`
              : '',
            email: data?.sales_person.email,
          },
          {
            name: 'Financial Analyst',
            value: data?.financial_analyst.first_name
              ? `${data.financial_analyst.first_name} ${data.financial_analyst.last_name}`
              : '',
            email: data?.financial_analyst.email,
          },
          {
            name: 'Expansion Value',
            value: data?.expansion_value,
          },
          {
            name: 'Marketplaces',
            value: data?.marketplaces.join(', '),
          },
        ]
      } catch (error) {
        handleError(
          error as ModifiedErrorType,
          'PrimaryDetails.tsx',
          'settingsData',
        )
      }
    },
  })

  const tableConfig = useMemo(() => {
    return [
      {
        label: 'Settings',
        name: 'name',
        noSort: true,
        cell: {
          children: (setting: Setting) => {
            return <div>{setting.name || 'Setting Name'}</div>
          },
        },
        mainColumn: true,
      },
      {
        label: 'Value',
        name: 'value',
        noSort: true,
        style: {
          minWidth: '400px',
        },
        cell: {
          children: (setting: Setting) => {
            return (
              <MdashCheck check={!!setting?.value}>
                {setting.name === 'Expansion Value' ? (
                  <Currency
                    value={setting.value ?? 0}
                    currencySymbol={currency.symbol}
                    currencyCode={currency.code}
                    customDecimalScale={0}
                  />
                ) : (
                  <div
                    style={{
                      wordBreak: 'break-word',
                    }}
                  >
                    {setting?.value}
                  </div>
                )}
                {setting?.email && (
                  <div
                    className='fs-10 fc-blue'
                    style={{
                      wordBreak: 'break-word',
                    }}
                  >
                    {setting?.email}
                  </div>
                )}
              </MdashCheck>
            )
          },
        },
      },
    ]
  }, [currency.code, currency.symbol])

  return (
    <>
      <div className='large-box-metric box bgc-faint-gray pat-mb-4'>
        <div className='pat-m-4 flex justify-content-between'>
          <div className='align-self-center'>Primary Details</div>
          {hasCustomerWritePermission && (
            <div>
              <Button
                as='button'
                onClick={() => {
                  createCustomer(0)
                }}
              >
                Edit
              </Button>
            </div>
          )}
        </div>

        <div className={`bgc-white bdrt bdrc-light-gray `}>
          <div className='pat-p-4'>
            <StandardTable
              data={settingsData || []}
              config={tableConfig}
              dataKey='value'
              hasData={settingsData ? settingsData?.length > 0 : false}
              hasMore={false}
              successStatus={settingsStatus === 'success'}
              loading={settingsStatus === 'pending'}
              tableId='brand_details_settings_primary_table'
              noDataFields={{ primaryText: 'No History found' }}
              mainColumnClassName='pat-mx-4'
              customWidth={SETTINGS_TABLE_WIDTH}
              customHeight={360}
              sort={(sortProps) => {
                setSettingSortBy({
                  prop: sortProps.activeColumn,
                  flip: sortProps.direction,
                })
              }}
              sortBy={settingSortBy}
              getData={() => {
                // No additional data to show
              }}
            />
          </div>
        </div>
      </div>

      <DetailsList
        customer={brandDetailsCustomer}
        createCustomer={createCustomer}
        customerRespStatus={customerRespStatus}
      />
    </>
  )
}

export default PrimaryDetails
