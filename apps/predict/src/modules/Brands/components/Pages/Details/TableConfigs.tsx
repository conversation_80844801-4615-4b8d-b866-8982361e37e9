import React from 'react'
import { But<PERSON> } from '@patterninc/react-ui'

import styles from './_details.module.scss'
import type { DetailsListProps } from './DetailsTable'

type SortBy = {
  prop: string
  flip: boolean
}

type Setting = {
  description?: string
  value?: string | boolean
  name?: string
}

type Config = {
  label: string
  name: string
  noSort?: boolean
  cell: { children: (setting: Setting) => React.JSX.Element }
  style?: Record<string, string> // StandardTable config doesn't like React.CSSProperties for a type
  mainColumn?: boolean
  isButton?: boolean
}

/************************************************************************************************
 * DESCRIPTION TABLE CONFIG
 ***********************************************************************************************/
export const DescriptionTableConfig = (
  customer: DetailsListProps['customer'],
  createCustomer: (step: number) => void,
): Config[] => [
  {
    label: 'Setting Name',
    name: 'description',
    noSort: true,
    mainColumn: true,
    cell: {
      children: () => {
        return <div>Description</div>
      },
    },
  },
  {
    label: 'Value',
    name: 'value',
    noSort: true,
    cell: {
      children: () => {
        return (
          <div className={styles.descriptionColumn}>
            {customer.customer_desc ||
              'Descriptions are useful for users who may not know a lot about the brand in question.'}
          </div>
        )
      },
    },
  },
  {
    label: '',
    name: '',
    noSort: true,
    isButton: true,
    cell: {
      children: () => {
        return (
          <Button
            as='button'
            styleType='secondary'
            onClick={() => createCustomer(0)}
          >
            Edit Description
          </Button>
        )
      },
    },
  },
]

/************************************************************************************************
 * SETTINGS TABLE CONFIG
 ***********************************************************************************************/
export const SettingsTableConfig = (sortBy: SortBy): Config[] => [
  {
    label: 'Settings',
    name: 'name',
    noSort: true,
    cell: {
      children: (setting: Setting) => {
        // remove sorting along with toggle
        return (
          <div className={`${sortBy.prop === 'name' ? 'fw-semi-bold' : ''}`}>
            {setting.name || 'Setting Name'}
          </div>
        )
      },
    },
  },
  {
    label: 'Value',
    name: 'value',
    noSort: true,
    cell: {
      children: (setting: Setting) => {
        // remove sorting along with toggle
        return (
          <div
            className={`${sortBy.prop === 'value' ? 'fw-semi-bold' : ''} ${
              setting.value ? 'fc-green' : 'fc-red'
            }`}
          >
            {setting.value ? 'YES' : 'NO'}
          </div>
        )
      },
    },
  },
]
