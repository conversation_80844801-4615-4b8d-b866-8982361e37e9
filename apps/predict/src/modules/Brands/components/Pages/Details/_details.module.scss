@use '@patterninc/react-ui/dist/variables' as variables;

.description_table_container {
  height: 200px;
  @media screen and (max-width: variables.$breakpoint-md) {
    height: auto;
    padding-bottom: 24px;
  }
}

.settings_table_container {
  overflow-y: scroll;

  // unset `top` value of mobile standard table with multiple tables on the same page
  & :global .data-table > div {
    top: unset;
  }
}

.descriptionColumn {
  width: 500px;
  @media screen and (max-width: variables.$breakpoint-md) {
    width: 40%;
  }
}
