import React, { useMemo, useState } from 'react'
import { useNavigate } from 'react-router-dom'
import { type QueryFunction, useQuery } from '@tanstack/react-query'
import {
  FormFooter,
  Select,
  SideDrawer,
  standardSortParams,
  toast,
} from '@patterninc/react-ui'

import {
  assignPrimaryContact,
  fetchbrandUsers,
} from '../../services/BrandDetailsService'
import { brandActionConstant } from './brandConstants'
const { EDIT_BRAND_CONTACT } = brandActionConstant

type SideDrawerProps = {
  type: string
  isOpen: boolean
}

type EditBrandContactProps = {
  actionsSidedrawer: SideDrawerProps
  setActionSidedrawer: React.Dispatch<React.SetStateAction<SideDrawerProps>>
  customerId: number
}
type SelectedUserDetails = {
  id: number
  full_user_name: string
}

type User = {
  id: number
  full_user_name: string
}

type UserQueryKey = [
  string,
  {
    sort: string
    enabled: boolean
    user_type: string
    query?: string | undefined
    page: number
    per_page: number
  },
  string,
  number,
]

const errorMsg = 'Something went wrong. Please try again'

const EditBrandContacts = ({
  actionsSidedrawer,
  setActionSidedrawer,
  customerId,
}: EditBrandContactProps) => {
  const navigate = useNavigate()
  const handleCloseSidedrawer = () => {
    setSelectedUser(defaultSelectedUser)
    setActionSidedrawer({ type: '', isOpen: false })
  }
  const defaultSelectedUser = {
    id: 0,
    full_user_name: 'Select a user',
  }

  const [selectedUser, setSelectedUser] =
    useState<SelectedUserDetails>(defaultSelectedUser)

  const [search] = useState('')

  const savePrimaryBrandContact = () => {
    const params = {
      user_id: selectedUser.id,
      is_primary_brand_contact: true,
    }

    assignPrimaryContact(customerId, params)
      .then((response) => {
        if (!response.error) {
          toast({
            type: 'success',
            message: 'Primary Contact has been updated successfully',
            config: {
              autoClose: 3000,
            },
          })
          handleCloseSidedrawer()
          setTimeout(() => {
            navigate(`/brands/details/list`, {
              state: {
                timestamp: Date.now(),
              },
            })
          }, 1000)
        } else {
          const errMsg = response?.error?.response?.data?.message || errorMsg
          toast({
            type: 'error',
            message: errMsg,
            config: {
              autoClose: 3000,
            },
          })
        }
      })
      .catch(() => {
        toast({
          type: 'error',
          message: errorMsg,
          config: {
            autoClose: 3000,
          },
        })
      })
  }

  const SideDrawerFooterContent = () => {
    return (
      <FormFooter
        saveButtonProps={{
          onClick: () => {
            savePrimaryBrandContact()
          },
          children: 'Save Changes',
          disabled: selectedUser.id === 0,
        }}
        cancelButtonProps={{
          onClick: () => {
            handleCloseSidedrawer()
          },
        }}
      />
    )
  }

  const userParams = useMemo(() => {
    return {
      page: 1,
      per_page: 20,
      ...(search ? { query: search } : {}),
      sort: standardSortParams({ prop: 'last_name', flip: true }),
      enabled: true,
      user_type: 'Brand',
    }
  }, [search])

  const fetchUsers: QueryFunction<User[], UserQueryKey> = async ({
    queryKey,
  }) => {
    const [, params, , customerId] = queryKey
    const response = await fetchbrandUsers(customerId, params)
    return response.data
  }

  const { data: userData = [] } = useQuery<User[], Error, User[], UserQueryKey>(
    {
      queryKey: ['user_data', userParams, search, customerId],
      queryFn: fetchUsers,
      // onError: () => {
      //   toast({
      //     type: 'error',
      //     message: errorMsg,
      //     config: {
      //       autoClose: 3000,
      //     },
      //   })
      // },
    },
  )

  return (
    <SideDrawer
      isOpen={
        actionsSidedrawer.type === EDIT_BRAND_CONTACT &&
        actionsSidedrawer.isOpen
      }
      closeCallout={handleCloseSidedrawer}
      headerContent='Edit Primary Brand Contact'
      footerContent={<SideDrawerFooterContent />}
    >
      <div>
        <Select
          labelProps={{ label: 'Primary Brand Contact' }}
          options={userData}
          optionKeyName='full_user_name'
          labelKeyName='full_user_name'
          selectedItem={selectedUser}
          onChange={(option) => {
            setSelectedUser(option)
          }}
          required={true}
        />
      </div>
    </SideDrawer>
  )
}

export default EditBrandContacts
