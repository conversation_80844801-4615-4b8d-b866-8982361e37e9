import React, { use<PERSON><PERSON>back, useEffect, useMemo, useState } from 'react'
import { useInfiniteQuery } from '@tanstack/react-query'
import {
  Button,
  getApiUrlPrefix,
  hasValue,
  <PERSON>dash<PERSON>heck,
  SearchBar,
  standardSortParams,
  StandardTable,
  TrimText,
  useIsMounted,
  useMediaQuery,
} from '@patterninc/react-ui'
import { useLocationState } from '@predict-hooks'
import { c, haveRoles, SecureAxios, useTranslate } from '@predict-services'

import { useAuth } from '../../../../../context/auth-context'
import ModifyUser from '../../../../Settings/components/Pages/UsersGroups/Users/<USER>/CreateUser'
import { fetchbrandUsers } from '../../../services/BrandDetailsService'
import { type BrandDetailsCustomer } from '../BrandDetails'

interface UserGroupProps {
  customer: BrandDetailsCustomer
}
interface LocationState {
  timestamp?: number
}
interface User {
  id: string
  last_name: string
  first_name: string
  email: string
  group_name: string
}

interface SortState {
  prop: string
  flip: boolean
}

export interface UserState {
  isOpen: boolean
  headerText: string
  userId: string | null
}

interface DataState {
  users: User[]
  loading: boolean
  pageNumber: number
  nextPage: boolean
  endOfFeed: boolean
  lazyLoading: boolean
  count: number
  query?: string
}

const UserGroup: React.FC<UserGroupProps> = ({ customer }) => {
  const defaultUserState: UserState = {
    isOpen: false,
    headerText: '',
    userId: null,
  }
  const { t } = useTranslate('brands')
  const isMounted = useIsMounted(),
    { state } = useLocationState<LocationState>()

  const { user } = useAuth()
  const [selectedUserId, setSelectedUserId] = useState<string | undefined>(
    undefined,
  )
  const [userState, setUserState] = useState<UserState>(defaultUserState)
  const [isSideDrawerCloseBtnClicked, setIsSideDrawerCloseBtnClicked] =
    useState(false)
  const [reactivate, setReactivate] = useState(false)

  const closeDrawer = () => {
    setIsSideDrawerCloseBtnClicked(true)
    setUserState(defaultUserState)
    setReactivate(false)
  }
  const hasWritePermissions = haveRoles(user, [
    'write_settings_user_and_groups',
  ])

  const groupsAPIUrl = `${getApiUrlPrefix('iserve')}/api/v3/customers/${
    customer?.id
  }/users`
  const userGroupsList: User[] = []
  const isMobileView = useMediaQuery({ type: 'max', breakpoint: 'md' })

  const [sort, setSort] = useState<SortState>({
    prop: 'last_name',
    flip: false,
  })

  const setSortBy = (sortObj: { activeColumn: string; direction: boolean }) => {
    setSort((prevState) => ({
      ...prevState,
      prop: sortObj.activeColumn,
      flip: sortObj.direction,
    }))
  }

  const [dataState, setDataState] = useState<DataState>({
    users: [],
    loading: true,
    pageNumber: 1,
    nextPage: true,
    endOfFeed: false,
    lazyLoading: false,
    count: 0,
  })

  const fetchBrandUsers = useCallback(
    (pageNumber: number, value?: string) => {
      if (isMounted()) {
        setDataState((prevState) => ({
          ...prevState,
          lazyLoading: true,
        }))
      }

      const params = {
        page: pageNumber,
        per_page: 20,
        query: value,
        enabled: true,
      }
      fetchbrandUsers(customer.id, params).then((response) => {
        if (isMounted()) {
          setDataState((prevState) => ({
            ...prevState,
            users:
              pageNumber === 1
                ? [...(response?.data ?? [])]
                : prevState.users.concat(response?.data ?? []),
            loading: false,
            lazyLoading: false,
            pageNumber: response.pagination.next_page,
            nextPage: !response.pagination.last_page,
            endOfFeed: response.pagination.last_page,
            count: response.pagination.count,
          }))
        }
      })
    },
    [customer.id, isMounted],
  )

  useEffect(() => {
    fetchBrandUsers(1)
  }, [fetchBrandUsers])

  const searchInputHandler = (value: string | undefined) => {
    if (isMounted()) {
      setDataState((prevState) => ({
        ...prevState,
        loading: true,
        query: value ?? '',
      }))
      fetchBrandUsers(1, value ?? '')
    }
  }

  const { query } = dataState

  const config = useMemo(() => {
    const editUser = (userId: string) => {
      setSelectedUserId(userId)
      setUserState({
        isOpen: true,
        headerText: t('editUser'),
        userId: userId,
      })
      setIsSideDrawerCloseBtnClicked(false)
    }

    return [
      {
        name: 'last_name',
        label: c('name'),
        noSort: false,
        cell: {
          children: (user: User) => {
            const getFullName = () => {
              let fullName = ''
              if (user.last_name) fullName = user.last_name
              if (user.first_name) {
                if (user.last_name) fullName = `${fullName}, `
                fullName = `${fullName}${user.first_name}`
              }
              return fullName
            }
            return (
              <div>
                <MdashCheck
                  check={hasValue(user.last_name) || hasValue(user.first_name)}
                  customClass={`flex ${
                    sort.prop === 'last_name' ? 'fw-semi-bold' : ''
                  }`}
                >
                  {getFullName()}
                </MdashCheck>
              </div>
            )
          },
        },
        mainColumn: true,
      },
      {
        name: 'email',
        label: c('email'),
        cell: {
          children: (user: User) => {
            return (
              <div>
                <MdashCheck
                  check={hasValue(user.email)}
                  customClass={`${sort.prop === 'email' ? 'fw-semi-bold' : ''}`}
                >
                  {isMobileView ? (
                    <span
                      style={{
                        wordBreak: 'break-word',
                      }}
                    >
                      {user.email}
                    </span>
                  ) : (
                    <TrimText text={user.email} limit={30} position={'right'} />
                  )}
                </MdashCheck>
              </div>
            )
          },
        },
      },
      {
        name: 'group_name',
        label: t('group'),
        noSort: false,
        cell: {
          children: (user: User) => {
            return (
              <div>
                <MdashCheck
                  check={hasValue(user.group_name)}
                  customClass={`${
                    sort.prop === 'group_name' ? 'fw-semi-bold' : ''
                  }`}
                >
                  <span>{user.group_name}</span>
                </MdashCheck>
              </div>
            )
          },
        },
      },
      ...(hasWritePermissions
        ? [
            {
              name: '',
              label: '',
              noSort: true,
              isButton: true,
              cell: {
                children: (user: User) => {
                  return (
                    <Button onClick={() => editUser(user?.id)}>
                      {t('editUserSettings')}
                    </Button>
                  )
                },
              },
            },
          ]
        : []),
    ]
  }, [hasWritePermissions, sort.prop, isMobileView, t])

  const {
    status: responseStatus,
    data: paginatedData,
    fetchNextPage,
    hasNextPage,
  } = useInfiniteQuery({
    queryKey: [
      'BrandUserGroupsStandardTableAPI',
      query,
      sort,
      state?.timestamp, //timestamp dependency added to reload page if user added to list from brand actions side-drawer
    ],
    queryFn: async ({ pageParam = 1 }) => {
      const params = {
        page: pageParam,
        per_page: 20,
        query: query,
        enabled: true,
        sort: standardSortParams(sort, ['last_name', 'email', 'group_name']),
      }
      try {
        const response = await SecureAxios.get(groupsAPIUrl, { params })
        return response
      } catch (error) {
        throw new Error(error as string)
      }
    },
    initialPageParam: 1,
    getNextPageParam: (previousPage) =>
      previousPage?.data?.pagination?.last_page
        ? undefined
        : previousPage?.data?.pagination?.next_page,
  })

  const data = paginatedData?.pages

  data?.flatMap((page) =>
    page?.data?.data.forEach((row: User) => userGroupsList.push(row)),
  )

  return (
    <div className='users-page'>
      <div className='page-header'>Brand Users</div>
      <div className='flex action-bar'>
        <SearchBar
          value={query ?? ''}
          onChange={searchInputHandler}
          placeholder='Search Users'
        />
      </div>
      <StandardTable
        data={userGroupsList}
        config={config}
        dataKey='id'
        hasData={userGroupsList.length !== 0}
        hasMore={!!hasNextPage}
        successStatus={responseStatus === 'success'}
        loading={responseStatus === 'pending'}
        tableId={`brand-users-groups-resp-table`}
        noDataFields={{
          primaryText: `No Brand Users Found`,
        }}
        sort={setSortBy}
        sortBy={sort}
        getData={fetchNextPage}
        widthOffset={320}
        stickyTableConfig={{ right: 1 }}
      />
      {!isSideDrawerCloseBtnClicked && (
        <ModifyUser
          parent='brands'
          selectedUserId={selectedUserId}
          reactivateUser={reactivate}
          closeDrawer={closeDrawer}
          isSideDrawerCloseBtnClicked={isSideDrawerCloseBtnClicked}
          isOpen={userState.isOpen}
          headerContent={userState.headerText}
          setUserState={setUserState}
          updateRefetchCustomer={undefined} // ToDo- this prop was not passed so passed a null value. Later on after conversion of CreateUser.js it has to removed from here
        />
      )}
    </div>
  )
}

export default UserGroup
