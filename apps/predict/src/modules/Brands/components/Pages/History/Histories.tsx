import React, { useContext, useState } from 'react'
import { useInfiniteQuery } from '@tanstack/react-query'
import { standardSortParams, StandardTable } from '@patterninc/react-ui'
import { ThemeContext } from 'src/Context'

import { fetchLogs } from '../../../services/BrandDetailsService'
import { historyTableConfig } from './historyTableConfig'

const Histories = () => {
  const [sortBy, setSortBy] = useState({
      prop: 'format_created_at',
      flip: false,
    }),
    { customer } = useContext(ThemeContext)

  // FETCH BRAND HISTORY LOGS
  const {
    status,
    data: apiResponse,
    fetchNextPage,
    hasNextPage,
  } = useInfiniteQuery({
    queryKey: ['historyLogsAPI', sortBy],
    queryFn: async ({ pageParam = 1, signal }) => {
      const params = {
        page: pageParam,
        per_page: 20,
        enabled: true,
        custom_sort: standardSortParams(sortBy, [
          'formatted_label',
          'formatted_value',
          'updated_by',
          'format_created_at',
        ]),
      }
      return fetchLogs(customer.id, params, signal)
        .then((response) => response)
        .catch((error) => {
          console.error(error)
        })
    },
    initialPageParam: 1,
    getNextPageParam: (previousPage) => previousPage?.pagination?.next_page,
  })
  const historyLogs = apiResponse
    ? apiResponse.pages.flatMap((page) => page?.data)
    : []

  return (
    <StandardTable
      data={historyLogs}
      config={historyTableConfig()}
      dataKey='id'
      hasData={true}
      hasMore={!!hasNextPage}
      successStatus={!!historyLogs}
      loading={status === 'pending'}
      tableId='insights_brand_details_history_table'
      noDataFields={{ primaryText: 'No History found' }}
      mainColumnClassName='pat-mx-4'
      sortBy={sortBy}
      getData={fetchNextPage}
      sort={(sortProps) => {
        setSortBy({ prop: sortProps.activeColumn, flip: sortProps.direction })
      }}
      customWidth={'calc(100vw - 420px)'}
    />
  )
}

export default Histories
