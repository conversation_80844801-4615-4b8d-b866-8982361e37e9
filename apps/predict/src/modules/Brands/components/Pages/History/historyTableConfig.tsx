import React from 'react'
import { Mdas<PERSON><PERSON><PERSON>ck } from '@patterninc/react-ui'

import styles from './_history.module.scss'

export const historyTableConfig = () => {
  const formatted_label = 'formatted_label'
  const updated_by = 'updated_by'

  type DataType = {
    formatted_label: string
    previous_value: string
    formatted_value: string
    user: { username: string }
    format_created_at: string
    current_value: string
    sortProp: string
  }

  return [
    {
      name: formatted_label,
      label: 'Events',
      mainColumn: true,
      cell: {
        children: (log: DataType) => {
          return (
            <div
              className={`${
                log.sortProp === formatted_label ? 'fw-semi-bold' : ''
              }`}
            >
              {log.formatted_label}
            </div>
          )
        },
      },
    },
    {
      name: 'previous_value',
      label: 'Old Value',
      noSort: true,
      cell: {
        children: (log: DataType) => {
          return (
            <div className={`pat-ml-2`}>
              <MdashCheck check={!!log.previous_value}>
                {log.previous_value}
              </MdashCheck>
            </div>
          )
        },
      },
    },
    {
      name: 'formatted_value',
      label: 'Updated Value',
      mainColumn: true,
      noSort: true,
      cell: {
        children: (log: DataType) => {
          return (
            <div
              className={`pat-ml-2 ${
                log.sortProp === 'formatted_value' ? 'fw-semi-bold' : ''
              }`}
            >
              <MdashCheck check={!!log.formatted_value || !!log?.current_value}>
                {log.formatted_value || log.current_value}
              </MdashCheck>
            </div>
          )
        },
      },
    },
    {
      name: updated_by,
      label: 'Updated By',
      cell: {
        children: (log: DataType) => {
          return (
            <div
              className={`${log.sortProp === updated_by ? 'fw-semi-bold' : ''}`}
            >
              {log?.user?.username}
            </div>
          )
        },
      },
    },
    {
      name: 'format_created_at',
      label: 'Updated On',
      cell: {
        children: (log: DataType) => {
          return (
            <div
              className={`${styles.history_log_table_end_column} ${
                log.sortProp === 'format_created_at' ? 'fw-semi-bold' : ''
              }`}
            >
              {log.format_created_at}
            </div>
          )
        },
      },
    },
  ]
}
