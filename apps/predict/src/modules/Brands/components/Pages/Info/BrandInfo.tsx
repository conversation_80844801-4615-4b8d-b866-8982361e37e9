import * as React from 'react'
import { InformationPane } from '@patterninc/react-ui'

import { Currency } from '../../../../../common/components'
import { type BrandDetailsCustomer } from '../BrandDetails'

type BrandInfoProps = {
  customer: BrandDetailsCustomer
}
const BrandInfo = ({ customer }: BrandInfoProps): React.JSX.Element => {
  const primaryContact = customer?.primary_contact?.[0]

  return (
    <InformationPane
      header={{
        labelAndData: {
          label: 'Brand Type',
          data: (
            <span className='capitalize fs-14'>
              {/* e.g. full_partner -> Full Partner (uses capitalize class) */}
              {customer.customer_type.replace(/_/g, ' ')}
            </span>
          ),
          check: true,
        },
        tag: {
          color: 'green',
          children: 'Active',
        },
      }}
    >
      <InformationPane.CustomSection>
        <div className='pat-p-5'>
          {(customer.logo_url || customer.thumb_logo_url) && (
            <div className='pat-mb-2.5'>
              <img
                src={customer.logo_url ?? customer.thumb_logo_url}
                alt={customer.customer_name}
                className='marketplace-logo'
                height='80px'
              />
            </div>
          )}

          <div>{customer.customer_name}</div>

          {customer.customer_desc && (
            <div className='pat-mt-2.5 fc-purple fs-10'>
              {customer.customer_desc}
            </div>
          )}
        </div>
      </InformationPane.CustomSection>
      <InformationPane.Divider />
      <InformationPane.Section
        data={[
          {
            label: 'Primary Brand Contact',
            data: (
              <span>
                {primaryContact?.first_name} {primaryContact?.last_name}
              </span>
            ),
            check: !!primaryContact,
          },
          {
            label: 'Email',
            data: (
              <BrandActionsEditLink>
                <a href={`mailto:${primaryContact?.email}`} target='_top'>
                  {primaryContact?.email}
                </a>
              </BrandActionsEditLink>
            ),
            check: !!primaryContact?.email,
          },
        ]}
      />
      <InformationPane.Divider />
      <InformationPane.Section
        data={[
          {
            label: 'Initial Signing Amount',
            data: (
              <Currency
                value={customer.initial_signing_value}
                currencyCode='USD'
                currencySymbol='$'
                customDecimalScale={0}
              />
            ),
            check: customer.initial_signing_value !== null,
          },
          {
            label: 'Partner Since',
            data: customer.exclusive_start_date,
            check: !!customer.exclusive_start_date,
          },
        ]}
      />
    </InformationPane>
  )
}

const BrandActionsEditLink = ({ children }: { children: React.ReactNode }) => {
  return <div className='fc-blue'>{children}</div>
}

export default BrandInfo
