@import '@patterninc/react-ui/dist/variables';
@import '../../../../../common/scss/base/mixins';

.sidebar {
  border: 1px solid var(--light-gray);
  display: flex;
  flex-direction: column;
  overflow: auto;
  @include border-radius;
  text-align: center;
  top: 92px;
  background: var(--white);
  @media only screen and (min-width: variables.$breakpoint-md) {
    position: sticky;
    height: calc(100vh - 114px);
  }
  @media only screen and (min-width: variables.$breakpoint-xxl) {
    height: calc(100vh - 186px);
  }
}

.profileSidebarHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: var(--faint-gray);
  text-align: left;
  padding: 16px;
  border-bottom: 1px solid var(--light-gray);
}

@media only screen and (max-width: variables.$breakpoint-md) {
  .brand_info {
    // 40px is the total padding on the left and right of the app on mobile
    max-width: calc(100vw - 40px);
  }
}
