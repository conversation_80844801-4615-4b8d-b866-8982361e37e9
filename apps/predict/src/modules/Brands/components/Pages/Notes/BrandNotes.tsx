import React, { useCallback, useEffect, useMemo, useState } from 'react'
import { useInfiniteQuery } from '@tanstack/react-query'
import {
  Button,
  hasValue,
  Mdash<PERSON>heck,
  standardSortParams,
  StandardTable,
  toast,
} from '@patterninc/react-ui'
import { useLocationState } from '@predict-hooks'
import moment from 'moment'
import { haveRoles } from '@predict-services'

import { useAuth } from '../../../../../context/auth-context'
import {
  cancelBrandNotes,
  cancelDeleteNote,
  deleteNote,
  fetchNotes,
} from '../../../services/BrandDetailsService'
import { type BrandDetailsCustomer } from '../BrandDetails'

interface BrandNotesProps {
  customer: BrandDetailsCustomer
  notesCount: number
  updateCount: (count: number) => void
  setError: unknown
}

interface Sort {
  prop: string
  flip: boolean
}

interface Note {
  id: string
  body: string
  note_category: { kind: string } | null
  user: { username: string } | null
  created_at: string
}

interface LocationState {
  timestamp?: number | string
}

const BrandNotes: React.FC<BrandNotesProps> = ({ customer, updateCount }) => {
  const [sort, setSort] = useState<Sort>({
    prop: 'updated_at',
    flip: false,
  })

  const setStandardTableSortBy = (sortObj: {
    activeColumn: string
    direction: boolean
  }) => {
    setSort({
      prop: sortObj.activeColumn,
      flip: sortObj.direction,
    })
  }

  const [isDeleted, setIsDeleted] = useState(false)

  const { state } = useLocationState<LocationState>()
  const stickyTableConfig = {
    right: 1,
  }

  const {
    status,
    isFetching,
    data: paginatedData,
    fetchNextPage,
    hasNextPage,
  } = useInfiniteQuery({
    queryKey: ['BrandNotesTableAPI', sort, isDeleted, state?.timestamp],
    queryFn: ({ pageParam = 1 }) => {
      const columnsWithStrings = ['body', 'note_category', 'brand_added_by']
      const params = {
        sort: `${standardSortParams(sort, columnsWithStrings)}`,
        page: pageParam,
        per_page: 20,
        enabled: true,
      }
      isDeleted && setIsDeleted(false)
      return fetchNotes(customer.id, params)
    },
    initialPageParam: 1,
    getNextPageParam: (previousPage) =>
      previousPage?.data?.pagination?.last_page
        ? undefined
        : previousPage?.data?.pagination?.next_page,
  })

  const notesData = paginatedData?.pages?.flatMap(
    (page) => page?.data,
  ) as Note[]

  // HOOKS
  useEffect(() => {
    return () => {
      cancelBrandNotes()
      localStorage.removeItem('sort_by_brand_notes')
    }
  }, [])

  useEffect(() => {
    updateCount(paginatedData?.pages?.[0]?.pagination?.count || 0)
    return () => {
      cancelDeleteNote()
    }
  }, [paginatedData?.pages, updateCount])

  // delete single note
  const deleteNoteItem = useCallback(
    (noteId: string) => {
      deleteNote(customer.id, noteId)
        .then(() => {
          setIsDeleted(true)
          toast({
            type: 'success',
            message: 'Note deleted successfully.',
          })
        })
        .catch(() => {
          toast({
            type: 'error',
            message: 'Note is not deleted.',
          })
        })
    },
    [customer.id, setIsDeleted],
  )

  const user = useAuth().user
  const hasWritePermissions = haveRoles(user, ['write_settings_customers'])

  const config = useMemo(() => {
    return [
      {
        name: 'body',
        label: 'NOTE',
        cell: {
          children: (brand: Note) => {
            return (
              <div
                className={`title-and-note
              ${!hasWritePermissions ? 'read-only' : ''}
              ${sort.prop === 'body' ? 'fw-semi-bold' : ''}`}
              >
                <p className={`${sort.prop === 'body' ? 'fw-semi-bold' : ''}`}>
                  {brand?.body}
                </p>
              </div>
            )
          },
        },
        mainColumn: true,
      },
      {
        name: 'note_category',
        label: 'TYPE',
        cell: {
          children: (brand: Note) => {
            return (
              <MdashCheck
                customClass={`${!hasWritePermissions ? 'read-only' : ''}`}
                check={
                  hasValue(brand?.note_category) &&
                  hasValue(brand?.note_category?.kind)
                }
              >
                <span
                  className={`${
                    sort.prop === 'note_category' ? 'fw-semi-bold' : ''
                  }`}
                >
                  {brand?.note_category?.kind}
                </span>
              </MdashCheck>
            )
          },
        },
      },
      {
        name: 'brand_added_by',
        label: 'ADDED BY',
        cell: {
          children: (brand: Note) => {
            return (
              <span
                className={`${!hasWritePermissions ? 'read-only' : ''} ${
                  sort.prop === 'brand_added_by' ? 'fw-semi-bold' : ''
                }`}
              >
                {brand?.user?.username}
              </span>
            )
          },
        },
      },
      {
        name: 'updated_at',
        label: 'ADDED DATE',
        cell: {
          children: (brand: Note) => {
            return (
              <span
                className={`${!hasWritePermissions ? 'read-only' : ''} ${
                  sort.prop === 'updated_at' ? 'fw-semi-bold' : ''
                }`}
              >
                {moment(brand?.created_at).format('MM/DD/YYYY \\at hh:mm A')}
              </span>
            )
          },
        },
      },
      {
        name: '',
        label: '',
        isButton: true,
        noSort: true,
        cell: {
          children: (brand: Note) => {
            return hasWritePermissions ? (
              // Delete note confirmations
              <Button
                as='confirmation'
                icon='trash'
                destructive={true}
                tooltip={{
                  tooltipContent: 'This action is to delete a note.',
                }}
                confirmation={{
                  type: 'red',
                  header: 'Are you sure?',
                  body: 'This action cannot be undone, once you delete a note it will be permanently removed.',
                  confirmCallout: () => {
                    deleteNoteItem(brand.id)
                  },
                  confirmButtonText: 'confirm',
                }}
              />
            ) : // End Delete note confirmations
            null
          },
        },
      },
    ]
  }, [deleteNoteItem, hasWritePermissions, sort.prop])

  return (
    <div className='brand-notes-list'>
      <div>
        <StandardTable
          data={notesData}
          config={config}
          loading={isFetching}
          successStatus={status === 'success'}
          getData={fetchNextPage}
          hasMore={!!hasNextPage}
          hasData={notesData?.length > 0}
          sort={setStandardTableSortBy}
          sortBy={sort}
          tableId='brand_notes_table'
          noDataFields={{
            primaryText: 'No Products Found',
            secondaryText:
              'This search did not return any results, please clear your search or filter and try again.',
          }}
          dataKey='id'
          widthOffset={320}
          customHeight={notesData?.length < 5 ? 'auto' : 600}
          stickyTableConfig={stickyTableConfig}
        />
      </div>
    </div>
  )
}

export default BrandNotes
