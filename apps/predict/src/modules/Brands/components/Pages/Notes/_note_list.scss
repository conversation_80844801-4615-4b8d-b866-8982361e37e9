.brand-notes-list {
  .list-header,
  .list li {
    display: grid;
    grid-template-columns: 2fr 100px 150px 50px;
    grid-gap: 16px;
    font-weight: var(--font-weight-regular);
    cursor: pointer;

    &.read-only {
      grid-template-columns: 2fr 100px 150px;
    }
    span {
      font-size: var(--font-size-12);
    }
    p,
    strong {
      font-size: var(--font-size-12);
    }
    p {
      margin: 8px 0 0;
      white-space: pre-wrap;
    }

    .switch-popover {
      .dropdown {
        padding: 18px;
      }
      span.popover-text {
        margin-bottom: 16px;
        display: block;
        font-weight: var(--font-weight-regular);
        font-size: var(--font-size-14);
        line-height: 20px;
      }
    }
    .popover-buttons {
      display: flex;
      justify-content: center;
      button {
        &:first-child {
          margin-right: 8px;
        }
      }
    }
  }
}
