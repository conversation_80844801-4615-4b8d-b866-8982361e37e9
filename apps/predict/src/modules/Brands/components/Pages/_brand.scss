@use '../../../../common/scss/base/mixins' as *;

.brand-forms {
  display: flex;
  height: 90%;
  justify-content: center;
  .form-content {
    align-self: center;
    .category-select {
      margin-bottom: 10px;
    }
    .form-row {
      .add-button {
        display: flex;
        justify-content: center;
      }
      &.email-row {
        margin-top: 30px;
      }
      .custom-label {
        position: absolute;
        color: var(--purple);
        font-size: var(--font-size-12);
        margin-top: -60px;
      }
      &.user-display {
        .select-container {
          border: solid var(--light-gray) 1px;
          @include border-radius;
          position: absolute;
          top: -30px;
          width: 100%;
          height: 40px;
        }
        .popover-toggle .select-box {
          align-items: center;
          padding: 10px 10px;
          justify-content: space-between;
        }
        .dropdown-search-item-content {
          position: sticky;
          background-color: var(--white);
          top: 0px;
        }
        .dropdown {
          width: 480px;
          overflow-y: scroll;
          .options-text {
            text-align: center;
            display: block;
            padding: 8px 0;
            font-size: var(--font-size-12);
            color: var(--purple);
          }
        }
      }
    }
  }
}
.brand-form-buttons {
  display: grid;
  gap: 16px;
  justify-content: end;
  grid-template-columns: repeat(2, 105px);
}
