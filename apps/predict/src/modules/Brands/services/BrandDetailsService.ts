import axios, { type AxiosResponse, type Canceler } from 'axios'
import { getApiUrlPrefix } from '@patterninc/react-ui'

import {
  handleError as handleApiError,
  type ModifiedErrorType,
} from '../../../common/services/HandleError'
import SecureAxios from '../../../common/services/SecureAxios'

const apiRootPrefix = `${getApiUrlPrefix('iserve')}`
const brandAPIRoot = `${apiRootPrefix}/api/v3/customers`

const handleError = (
  error: ModifiedErrorType,
  method: string,
  params: unknown,
) => {
  handleApiError(error, 'BrandDetailsService', method, params)
}

const CancelToken = axios.CancelToken

let logCancel: Canceler,
  notesCancel: Canceler,
  noteCategoryCancel: Canceler,
  saveNoteCancel: Canceler,
  updateNoteCancel: Canceler,
  deleteNoteCancel: Canceler,
  assignManagerCancel: Canceler,
  assignCustomerMangerCancel: Canceler,
  assignPrimaryContactCancel: Canceler,
  customerMangerListingCancel: Canceler,
  brandUserCancel: Canceler,
  addUserCancel: Canceler,
  fetchUsersCancel: Canceler

interface AddUserToBrand {
  customer_id?: number
  user_customer: {
    customer_id: number
    user_id: number | { id: number; full_user_name: string }
    access: string
  }
}

interface CreateNoteParams {
  notes: {
    note_category_id: number
    kind: {
      id: number
      kind: string
    }
    body: string
  }
}

export function fetchLogs(
  customerId: number,
  params: {
    page: number
    per_page: number
    enabled: boolean
    custom_sort: string
  },
  signal?: AbortSignal,
) {
  const url = `${brandAPIRoot}/${customerId}/logs`
  return SecureAxios.get(url, {
    params: params,
    cancelToken: new CancelToken(function executor(c) {
      logCancel = c
    }),
    signal,
  })
    .then((response: AxiosResponse) => {
      return response.data
    })
    .catch((error) => {
      const modifiedError: ModifiedErrorType = {
        ...error,
        wasCanceled: axios.isCancel(error),
        file: 'BrandDetailsService',
        method: 'fetchLogs',
        params: { customerId, ...params },
        status: error.response?.status,
        reporter: 'fetchLogsNew',
      }
      handleError(modifiedError, 'fetchLogs', { customerId, ...params })
    })
}

export function cancelLogs(): void {
  if (logCancel) {
    logCancel('Canceled fetching logs')
  }
}

export function fetchNoteCategories(customerId: number, signal?: AbortSignal) {
  const url = `${apiRootPrefix}/api/v3/note_categories`
  return SecureAxios.get(url, {
    cancelToken: new CancelToken(function executor(c) {
      noteCategoryCancel = c
    }),
    signal,
  })
    .then((response: AxiosResponse) => {
      return response.data
    })
    .catch((error) => {
      const modifiedError: ModifiedErrorType = {
        ...error,
        wasCanceled: axios.isCancel(error),
        file: 'BrandDetailsService',
        method: 'fetchNoteCategories',
        params: { customerId },
        status: error.response?.status,
        reporter: 'fetchNoteCategoriesNew',
      }
      handleError(modifiedError, 'fetchNoteCategories', { customerId })
    })
}

export function cancelBrandNoteCategory(): void {
  if (noteCategoryCancel) {
    noteCategoryCancel('Canceled fetching brand note categories')
  }
}

export function fetchNotes(
  customerId: number,
  params: {
    sort?: string
    page: number
    per_page: number
    enabled: boolean
  },
  signal?: AbortSignal,
) {
  const url = `${brandAPIRoot}/${customerId}/notes`
  return SecureAxios.get(url, {
    params: params,
    cancelToken: new CancelToken(function executor(c) {
      notesCancel = c
    }),
    signal,
  })
    .then((response: AxiosResponse) => {
      return response.data
    })
    .catch((error) => {
      const modifiedError: ModifiedErrorType = {
        ...error,
        wasCanceled: axios.isCancel(error),
        file: 'BrandDetailsService',
        method: 'fetchNotes',
        params: { customerId, ...params },
        status: error.response?.status,
        reporter: 'fetchNotesNew',
      }
      handleError(modifiedError, 'fetchNotes', { customerId, ...params })
    })
}

export function cancelBrandNotes(): void {
  if (notesCancel) {
    notesCancel('Canceled fetching brand notes')
  }
}

export function createNote(
  customerId: number,
  params: CreateNoteParams,
  signal?: AbortSignal,
) {
  const url = `${brandAPIRoot}/${customerId}/notes`
  return SecureAxios.post(url, params, {
    cancelToken: new CancelToken(function executor(c) {
      saveNoteCancel = c
    }),
    signal,
  })
    .then((response: AxiosResponse) => {
      return response.data
    })
    .catch((error) => {
      const modifiedError: ModifiedErrorType = {
        ...error,
        wasCanceled: axios.isCancel(error),
        file: 'BrandDetailsService',
        method: 'createNote',
        params: { customerId, ...params },
        status: error.response?.status,
        reporter: 'createNoteNew',
      }
      handleError(modifiedError, 'createNote', { customerId, ...params })
    })
}

export function cancelCreateNote(): void {
  if (saveNoteCancel) {
    saveNoteCancel('Canceled creating brand notes')
  }
}

export function updateNote(
  customerId: number,
  noteId: string,
  signal?: AbortSignal,
) {
  const url = `${brandAPIRoot}/${customerId}/notes/${noteId}`
  return SecureAxios.patch(url, {
    cancelToken: new CancelToken(function executor(c) {
      updateNoteCancel = c
    }),
    signal,
  })
    .then((response: AxiosResponse) => {
      return response.data
    })
    .catch((error) => {
      const modifiedError: ModifiedErrorType = {
        ...error,
        wasCanceled: axios.isCancel(error),
        file: 'BrandDetailsService',
        method: 'updateNote',
        params: { customerId, noteId },
        status: error.response?.status,
        reporter: 'updateNoteNew',
      }
      handleError(modifiedError, 'updateNote', { customerId, noteId })
    })
}

export function cancelUpdateNote(): void {
  if (updateNoteCancel) {
    updateNoteCancel('cancelled updating notes')
  }
}

export function deleteNote(
  customerId: number,
  noteId: string,
  signal?: AbortSignal,
) {
  const url = `${brandAPIRoot}/${customerId}/notes/${noteId}`
  return SecureAxios.delete(url, {
    cancelToken: new CancelToken(function executor(c) {
      deleteNoteCancel = c
    }),
    signal,
  })
    .then((response: AxiosResponse) => {
      return response.data
    })
    .catch((error) => {
      const modifiedError: ModifiedErrorType = {
        ...error,
        wasCanceled: axios.isCancel(error),
        file: 'BrandDetailsService',
        method: 'deleteNote',
        params: { customerId, noteId },
        status: error.response?.status,
        reporter: 'deleteNoteNew',
      }
      handleError(modifiedError, 'deleteNote', { customerId, noteId })
    })
}

export function cancelDeleteNote(): void {
  if (deleteNoteCancel) {
    deleteNoteCancel('cancelled deleting notes')
  }
}
// ToDo- This function is not used in the codebase. It can be removed.
export function fetchBrandManagers(
  customerId: number,
  params: unknown,
  signal?: AbortSignal,
) {
  const url = `${brandAPIRoot}/${customerId}/available_customer_managers`
  return SecureAxios.get(url, {
    params: params,
    cancelToken: new CancelToken(function executor(c) {
      assignCustomerMangerCancel = c
    }),
    signal,
  })
    .then((response: AxiosResponse) => {
      return response.data
    })
    .catch((error) => {
      const modifiedError: ModifiedErrorType = {
        ...error,
        wasCanceled: axios.isCancel(error),
        file: 'BrandDetailsService',
        method: 'fetchBrandManagers',
        params: { customerId, params },
        status: error.response?.status,
        reporter: 'fetchBrandManagersNew',
      }
      handleError(modifiedError, 'fetchBrandManagers', {
        customerId,
        params,
      })
    })
}

export function cancelCustomerManager(): void {
  if (assignCustomerMangerCancel) {
    assignCustomerMangerCancel('Cancelled customer managers')
  }
}
// ToDo- This function is not used in the codebase. It can be removed.
export function assignCustomerManager(
  customerId: number,
  params: unknown,
  signal?: AbortSignal,
) {
  const url = `${brandAPIRoot}/${customerId}/assign_customer_managers`
  return SecureAxios.post(url, params, {
    cancelToken: new CancelToken(function executor(c) {
      assignManagerCancel = c
    }),
    signal,
  })
    .then((response: AxiosResponse) => {
      return response.data
    })
    .catch((error) => {
      const modifiedError: ModifiedErrorType = {
        ...error,
        wasCanceled: axios.isCancel(error),
        file: 'BrandDetailsService',
        method: 'assignCustomerManager',
        params: { customerId, params },
        status: error.response?.status,
        reporter: 'assignCustomerManagerNew',
      }
      handleError(modifiedError, 'assignCustomerManager', {
        customerId,
        params,
      })
    })
}

export function assignPrimaryContact(
  customerId: number,
  params: { user_id: number; is_primary_brand_contact: boolean },
  signal?: AbortSignal,
) {
  const url = `${brandAPIRoot}/${customerId}/assign_primary_contact`
  return SecureAxios.post(url, params, {
    cancelToken: new CancelToken(function executor(c) {
      assignPrimaryContactCancel = c
    }),
    signal,
  })
    .then((response: AxiosResponse) => {
      return response.data
    })
    .catch((error) => {
      const modifiedError: ModifiedErrorType = {
        ...error,
        wasCanceled: axios.isCancel(error),
        file: 'BrandDetailsService',
        method: 'assignPrimaryContact',
        params: { customerId, ...params },
        status: error.response?.status,
        reporter: 'assignPrimaryContactNew',
      }
      handleError(modifiedError, 'assignPrimaryContact', {
        customerId,
        ...params,
      })
    })
}

export function cancelAssignedManager(): void {
  if (assignManagerCancel) {
    assignManagerCancel('Cancelled assigning managers')
  }
}

export function cancelAssignPrimaryContact(): void {
  if (assignPrimaryContactCancel) {
    assignPrimaryContactCancel('Cancelled assign primary contact')
  }
}

export function availableBrandManagers(
  customerId: number,
  signal?: AbortSignal,
) {
  const url = `${apiRootPrefix}/api/v3/manager_labels`
  return SecureAxios.get(url, {
    cancelToken: new CancelToken(function executor(c) {
      customerMangerListingCancel = c
    }),
    signal,
  })
    .then((response: AxiosResponse) => {
      return response.data
    })
    .catch((error) => {
      const modifiedError: ModifiedErrorType = {
        ...error,
        wasCanceled: axios.isCancel(error),
        file: 'BrandDetailsService',
        method: 'availableBrandManagers',
        params: { customerId },
        status: error.response?.status,
        reporter: 'availableBrandManagersNew',
      }
      handleError(modifiedError, 'availableBrandManagers', { customerId })
    })
}

export function cancelAvailableMangers(): void {
  if (customerMangerListingCancel) {
    customerMangerListingCancel('Cancelled available managers')
  }
}

export function fetchbrandUsers(
  customerId: number,
  userParams: {
    sort?: string
    enabled: boolean
    user_type?: string
    query?: string | undefined
    page: number
    per_page: number
  },
  signal?: AbortSignal,
) {
  const url = `${brandAPIRoot}/${customerId}/users`
  return SecureAxios.get(url, {
    params: userParams,
    cancelToken: new CancelToken(function executor(c) {
      brandUserCancel = c
    }),
    signal,
  })
    .then((response: AxiosResponse) => {
      return response.data
    })
    .catch((error) => {
      const modifiedError: ModifiedErrorType = {
        ...error,
        wasCanceled: axios.isCancel(error),
        file: 'BrandDetailsService',
        method: 'fetchbrandUsers',
        params: { customerId, ...userParams },
        status: error.response?.status,
        reporter: 'fetchbrandUsersNew',
      }
      handleError(modifiedError, 'fetchbrandUsers', {
        customerId,
        ...userParams,
      })
    })
}

export function fetchUsers(
  params: {
    page: number
    per_page: number
    query: string
    sort: string
    enabled: boolean
  },
  signal?: AbortSignal,
) {
  const url = `${apiRootPrefix}/api/v3/users`
  return SecureAxios.get(url, {
    params: params,
    cancelToken: new CancelToken(function executor(c) {
      fetchUsersCancel = c
    }),
    signal,
  })
    .then((response: AxiosResponse) => {
      return response.data
    })
    .catch((error) => {
      const modifiedError: ModifiedErrorType = {
        ...error,
        wasCanceled: axios.isCancel(error),
        file: 'BrandDetailsService',
        method: 'fetchUsers',
        params: params,
        status: error.response?.status,
        reporter: 'fetchUsersNew',
      }
      handleError(modifiedError, 'fetchUsers', params)
    })
}

export function cancelFetchingUsers(): void {
  if (fetchUsersCancel) {
    fetchUsersCancel('Canceled fetching user')
  }
}

export function addUserToBrand(
  customerId: number,
  params: AddUserToBrand,
  signal?: AbortSignal,
) {
  const url = `${brandAPIRoot}/${customerId}/users`
  return SecureAxios.post(url, params, {
    cancelToken: new CancelToken(function executor(c) {
      addUserCancel = c
    }),
    signal,
  })
    .then((response: AxiosResponse) => {
      return response.data
    })
    .catch((error) => {
      const modifiedError: ModifiedErrorType = {
        ...error,
        wasCanceled: axios.isCancel(error),
        file: 'BrandDetailsService',
        method: 'addUserToBrand',
        params: { customerId, ...params },
        status: error.response?.status,
        reporter: 'addUserToBrandNew',
      }
      handleError(modifiedError, 'addUserToBrand', { customerId, ...params })
    })
}

export function cancelAddUser(): void {
  if (addUserCancel) {
    addUserCancel('Canceled add user request')
  }
}

export function cancelFetchUser(): void {
  if (brandUserCancel) {
    brandUserCancel('Canceled fetching user')
  }
}
