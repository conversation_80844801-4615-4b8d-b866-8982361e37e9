import { c, t } from '@predict-services'
const obj = {
  value: 'current_period',
  comparison: 'comparison_period',
  change: 'from_comparison_period',
  pct_change: 'pct_change_comparison_period',
}

export const conversionHeaderMetrics = ({
  hideConversionWithoutSnsMetricToggle = false,
  chineseMarketplacesMetrics,
}: {
  hideConversionWithoutSnsMetricToggle?: boolean
  chineseMarketplacesMetrics?: boolean
}) => {
  return {
    page_views: {
      type: 'integer',
      display: c('pageViews'),
      ...obj,
    },
    pattern_page_views: {
      ...obj,
      type: 'integer',
      display: c('patternPageViews'),
    },
    conversion_rate_order: {
      ...obj,
      type: 'integer',
      display: c('orders'),
    },
    conversion: {
      ...obj,
      type: 'percentage',
      display: c('conversionRate'),
    },
    ...(hideConversionWithoutSnsMetricToggle
      ? {
          conversion_without_sns: {
            type: 'percentage',
            display: t('content:conversionRateWithoutSNS'),
            ...obj,
          },
        }
      : {}),
    units: {
      ...obj,
      type: 'integer',
      display: c('units'),
    },
    total_sales: {
      ...obj,
      type: 'currency',
      display: c('sales'),
    },
    avg_order_value: {
      ...obj,
      type: 'currency',
      display: c('avgOrderValue'),
    },
    avg_selling_price: {
      ...obj,
      type: 'currency',
      display: c('averageSellingPrice'),
    },
    units_per_order: {
      ...obj,
      type: 'decimal',
      display: c('unitsPerOrder'),
    },
    buy_box: {
      ...obj,
      type: 'percentage',
      display: c('buyBox'),
    },
    in_stock: {
      ...obj,
      type: 'percentage',
      display: c('inStockRate'),
    },
    ...(chineseMarketplacesMetrics
      ? {
          unique_visitors: {
            ...obj,
            type: 'integer',
            display: t('content:uniqueVisitors'),
          },
          add_to_cart: {
            ...obj,
            type: 'integer',
            display: t('content:addToCart'),
          },
          buyer_conversion_rate: {
            ...obj,
            type: 'percentage',
            display: t('content:buyerConversionRate'),
          },
          avg_order_value_by_buyer: {
            ...obj,
            type: 'currency',
            display: t('content:avgOrderValueByBuyer'),
          },
        }
      : {}),
  }
}
