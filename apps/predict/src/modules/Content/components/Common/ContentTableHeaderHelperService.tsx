import React from 'react'
import { Link } from 'react-router-dom'
import { PrimaryTableCell, TrimText } from '@patterninc/react-ui'
import { c, productDetailRoute, t } from '@predict-services'
import { type TagDetailsType } from 'src/common/components/ProductCategoriesAndTags/helpers'
import { type Currency } from '@predict-types'

import ListChangeData from '../../../../common/components/ListChangeData/ListChangeData'
import { getCurrencyDetails } from '../../../Traffic/services/TrafficHelperService'
import styles from './content.module.scss'
interface KeyProps {
  comparison: number
  value: number
  type: string
  display: string
  change: number
  pct_change: number
}

type AdditionalMetricsBase =
  | 'unique_visitors'
  | 'add_to_cart'
  | 'buyer_conversion_rate'
  | 'avg_order_value_by_buyer'

type AdditionalMetrics = {
  [K in AdditionalMetricsBase as `${K}_${
    | 'current_period'
    | 'comparison_period'
    | 'pct_change_comparison_period'}`]: number
} & {
  [K in `change_in_${AdditionalMetricsBase}_from_comparison_period`]: number
}

export type StandardTrafficObject = {
  totalRow: string
  brand: string
  brand_id: number
  sortProp: string
  master_product_name: string
  thumbnail: string
  master_product_id: string
  marketplace_id: string
  primary_key: string
  product_url: string
  marketplace_names: string
  asin: string
  customer_name: string
  conversion_rate_page_views: KeyProps
  revenue_per_unit: KeyProps
  avg_selling_price: KeyProps
  units_per_order: KeyProps
  pattern_page_views: KeyProps
  total_page_views: KeyProps
  ad_clicks: KeyProps
  total_sales: KeyProps
  ad_orders: KeyProps
  pattern_orders: KeyProps
  in_stock: KeyProps
  country_code: string
  buybox: KeyProps
  weighted_in_stock_pct: KeyProps
  market_product_name: string
  sold_by_pattern: number
  sold_by_threepn: number
  market_product_id: number
  product_title: string
  total_sales_current_period: number
  change_in_total_sales_from_comparison_period: number
  total_sales_comparison_period: number
  total_sales_pct_change_comparison_period: number

  orders_current_period: number
  orders_contribution_to_change: number
  orders_comparison_period: number
  orders_pct_change_comparison_period: number

  pattern_page_views_current_period: number
  change_in_pattern_page_views_from_comparison_period: number
  pattern_page_views_comparison_period: number
  pattern_page_views_pct_change_comparison_period: number

  page_views_current_period: number
  change_in_page_views_from_comparison_period: number
  page_views_comparison_period: number
  page_views_pct_change_comparison_period: number

  buy_box_current_period: number
  change_in_buy_box_from_comparison_period: number
  buy_box_comparison_period: number
  buy_box_pct_change_comparison_period: number

  in_stock_current_period: number
  change_in_in_stock_from_comparison_period: number
  in_stock_comparison_period: number
  in_stock_pct_change_comparison_period: number

  conversion_current_period: number
  change_in_conversion_from_comparison_period: number
  conversion_comparison_period: number
  conversion_pct_change_comparison_period: number

  avg_selling_price_current_period: number
  change_in_avg_selling_price_from_comparison_period: number
  avg_selling_price_comparison_period: number
  avg_selling_price_pct_change_comparison_period: number

  units_per_order_current_period: number
  change_in_units_per_order_from_comparison_period: number
  units_per_order_comparison_period: number
  units_per_order_pct_change_comparison_period: number

  avg_order_value_current_period: number
  change_in_avg_order_value_from_comparison_period: number
  avg_order_value_comparison_period: number
  avg_order_value_pct_change_comparison_period: number
  customer_id: number
  marketplace_ids: number[]

  conversion_rate_order_current_period: number
  change_in_conversion_rate_order_from_comparison_period: number
  conversion_rate_order_comparison_period: number
  conversion_rate_order_pct_change_comparison_period: number
  tag_details: TagDetailsType[]
  leaf_category_name: string
  root_category_id: number
  leaf_category_id: number

  conversion_without_sns_current_period: number
  change_in_conversion_without_sns_from_comparison_period: number
  conversion_without_sns_comparison_period: number
  conversion_without_sns_pct_change_comparison_period: number

  marketplace_name: string
  source: string
  store_name: string
} & AdditionalMetrics

export const commonContentHeaders = {
  product_name: {
    name: 'market_product_name',
    label: c('product'),
    mainColumn: true,
    cell: {
      children: (product: StandardTrafficObject) => {
        return (
          <PrimaryTableCell
            imageProps={{
              alt: product.market_product_name,
              url: product.thumbnail,
            }}
            productLink={`${productDetailRoute.listing({
              productId: product?.master_product_id,
              marketplaceId: product?.marketplace_id,
              listingId: product?.market_product_id,
            })}/content/details/conversion`}
            routerComponent={Link}
            routerProp='to'
            externalLink={product?.product_url}
            marketplaceNames={product.marketplace_names}
            sortBy={{ prop: product.sortProp }}
            title={product.market_product_name}
            titleProp='master_product_name'
            uniqId={{
              id: product.asin,
              idLabel: 'ASIN',
              idName: 'asin',
            }}
          />
        )
      },
    },
    style: { minWidth: '200px' },
  },

  customer_name: {
    name: 'customer_name',
    label: c('brand'),
    cell: {
      children: (product: StandardTrafficObject) => {
        return (
          <TrimText
            text={product.customer_name}
            limit={75}
            customClass={
              product.sortProp === 'customer_name' ? 'fw-semi-bold' : ''
            }
          />
        )
      },
    },
  },
  conversion_rate_page_views: {
    name: 'conversion_current_period',
    label: c('conversionRate'),
    tooltip: {
      content: (
        <div style={{ maxWidth: 400 }}>
          {t('content:conversionRateTooltip')}
        </div>
      ),
    },
    cell: {
      children: (product: StandardTrafficObject) => {
        return (
          <ListChangeData
            value={product?.conversion_current_period ?? 0}
            changeValue={
              (product?.change_in_conversion_from_comparison_period ?? 0) * 100
            }
            comparison={product?.conversion_comparison_period ?? 0}
            tooltipContent={
              product?.conversion_pct_change_comparison_period ?? 0
            }
            customClass={
              product.sortProp === 'conversion_current_period'
                ? 'fw-semi-bold'
                : ''
            }
            percentage
            decimalScale={2}
            changeValueClass={
              product.sortProp === 'change_in_conversion_from_comparison_period'
                ? 'fw-semi-bold'
                : ''
            }
          />
        )
      },
    },
  },
  conversion_rate_without_sns: {
    name: 'conversion_without_sns_current_period',
    label: t('content:conversionRateWithoutSNS'),
    noSort: true,
    tooltip: {
      content: (
        <div style={{ maxWidth: 400 }}>
          {t('content:conversionRateWithoutSNSTooltip')}
        </div>
      ),
    },
    cell: {
      children: (product: StandardTrafficObject) => {
        return (
          <ListChangeData
            value={product?.conversion_without_sns_current_period ?? 0}
            changeValue={
              product?.change_in_conversion_without_sns_from_comparison_period ??
              0
            }
            comparison={product?.conversion_without_sns_comparison_period ?? 0}
            tooltipContent={
              product?.conversion_without_sns_pct_change_comparison_period ?? 0
            }
            customClass={
              product.sortProp === 'conversion_without_sns_current_period'
                ? 'fw-semi-bold'
                : ''
            }
            changeValueClass={
              product.sortProp ===
              'change_in_conversion_without_sns_from_comparison_period'
                ? 'fw-semi-bold'
                : ''
            }
            percentage
            decimalScale={2}
          />
        )
      },
    },
  },
  total_sales: {
    name: 'total_sales_current_period',
    label: c('sales'),
    tooltip: {
      content: <div style={{ maxWidth: 400 }}>{t('content:salesTooltip')}</div>,
    },
    cell: {
      children: (product: StandardTrafficObject) => {
        return (
          <ListChangeData
            value={product?.total_sales_current_period ?? 0}
            changeValue={
              product?.change_in_total_sales_from_comparison_period ?? 0
            }
            comparison={product?.total_sales_comparison_period ?? 0}
            tooltipContent={
              product?.total_sales_pct_change_comparison_period ?? 0
            }
            changeFormat='currency'
            currency={{
              currency_code: getCurrencyDetails()?.code,
              currency_symbol: getCurrencyDetails()?.symbol,
            }}
            customClass={
              product.sortProp === 'total_sales_current_period'
                ? 'fw-semi-bold'
                : ''
            }
            changeValueClass={
              product.sortProp ===
              'change_in_total_sales_from_comparison_period'
                ? 'fw-semi-bold'
                : ''
            }
          />
        )
      },
    },
  },
  pattern_orders: {
    name: 'conversion_rate_order_current_period',
    label: c('orders'),
    tooltip: {
      content: (
        <div style={{ maxWidth: 400 }}>{t('content:ordersTooltip')}</div>
      ),
    },
    noSort: true,
    cell: {
      children: (product: StandardTrafficObject) => {
        return (
          <ListChangeData
            value={product?.conversion_rate_order_current_period ?? 0}
            changeValue={
              product?.change_in_conversion_rate_order_from_comparison_period ??
              0
            }
            comparison={product?.conversion_rate_order_comparison_period ?? 0}
            tooltipContent={
              product?.conversion_rate_order_pct_change_comparison_period ?? 0
            }
            customClass={
              product.sortProp === 'conversion_rate_order_current_period'
                ? 'fw-semi-bold'
                : ''
            }
            changeValueClass={
              product.sortProp ===
              'change_in_conversion_rate_order_from_comparison_period'
                ? 'fw-semi-bold'
                : ''
            }
          />
        )
      },
    },
  },
  ad_orders: {
    name: 'value__ad_orders',
    label: c('adOrders'),
    noSort: true,
    tooltip: {
      content: (
        <div style={{ maxWidth: 400 }}>{t('content:adOrdersTooltip')}</div>
      ),
    },
    cell: {
      children: (product: StandardTrafficObject) => {
        return (
          <ListChangeData
            value={product?.ad_orders?.value ?? 0}
            changeValue={product?.ad_orders?.change ?? 0}
            comparison={product?.ad_orders?.comparison ?? 0}
            tooltipContent={product?.ad_orders?.pct_change ?? 0}
            customClass={
              product.sortProp === 'value__ad_orders' ? 'fw-semi-bold' : ''
            }
            changeValueClass={
              product.sortProp === 'change__ad_orders' ? 'fw-semi-bold' : ''
            }
          />
        )
      },
    },
  },

  revenue_per_unit: {
    name: 'avg_selling_price_current_period',
    label: c('averageSellingPrice'),
    noSort: true,
    tooltip: {
      content: (
        <div style={{ maxWidth: 400 }}>
          {t('content:averageSellingPriceTooltip')}
        </div>
      ),
    },
    cell: {
      children: (product: StandardTrafficObject) => {
        return (
          <ListChangeData
            value={product?.avg_selling_price_current_period ?? 0}
            changeValue={
              product?.change_in_avg_selling_price_from_comparison_period ?? 0
            }
            comparison={product?.avg_selling_price_comparison_period ?? 0}
            tooltipContent={
              product?.avg_selling_price_pct_change_comparison_period ?? 0
            }
            customClass={
              product.sortProp === 'avg_selling_price_current_period'
                ? 'fw-semi-bold'
                : ''
            }
            changeValueClass={
              product.sortProp ===
              'change_in_avg_selling_price_from_comparison_period'
                ? 'fw-semi-bold'
                : ''
            }
            decimalScale={2}
            changeFormat='currency'
            currency={{
              currency_code: getCurrencyDetails()?.code,
              currency_symbol: getCurrencyDetails()?.symbol,
            }}
          />
        )
      },
    },
  },
  avg_selling_price: {
    name: 'avg_order_value_current_period',
    label: c('avgOrderValue'),
    noSort: true,
    tooltip: {
      content: (
        <div style={{ maxWidth: 400 }}>{t('content:avgOrderValueTooltip')}</div>
      ),
    },
    cell: {
      children: (product: StandardTrafficObject) => {
        return (
          <ListChangeData
            value={product?.avg_order_value_current_period ?? 0}
            changeValue={
              product?.change_in_avg_order_value_from_comparison_period ?? 0
            }
            comparison={product?.avg_order_value_comparison_period ?? 0}
            tooltipContent={
              product?.avg_order_value_pct_change_comparison_period ?? 0
            }
            decimalScale={2}
            customClass={
              product.sortProp === 'avg_order_value_current_period'
                ? 'fw-semi-bold'
                : ''
            }
            changeValueClass={
              product.sortProp ===
              'change_in_avg_order_value_from_comparison_period'
                ? 'fw-semi-bold'
                : ''
            }
            changeFormat='currency'
            currency={{
              currency_code: getCurrencyDetails()?.code,
              currency_symbol: getCurrencyDetails()?.symbol,
            }}
          />
        )
      },
    },
  },
  units_per_order: {
    name: 'units_per_order_current_period',
    label: c('unitsPerOrder'),
    noSort: true,
    tooltip: {
      content: (
        <div style={{ maxWidth: 400 }}>{t('content:unitsPerOrderTooltip')}</div>
      ),
    },
    cell: {
      children: (product: StandardTrafficObject) => {
        return (
          <ListChangeData
            value={product?.units_per_order_current_period ?? 0}
            changeValue={
              product?.change_in_units_per_order_from_comparison_period ?? 0
            }
            comparison={product?.units_per_order_comparison_period ?? 0}
            tooltipContent={
              product?.units_per_order_pct_change_comparison_period ?? 0
            }
            customClass={
              product.sortProp === 'units_per_order_current_period'
                ? 'fw-semi-bold'
                : ''
            }
            changeValueClass={
              product.sortProp ===
              'change_in_units_per_order_from_comparison_period'
                ? 'fw-semi-bold'
                : ''
            }
            decimalScale={2}
          />
        )
      },
    },
  },
  pattern_page_views: {
    name: 'pattern_page_views_current_period',
    label: c('patternPageViews'),
    tooltip: {
      content: (
        <div style={{ maxWidth: 400 }}>
          {t('content:patternPageViewsTooltip')}
        </div>
      ),
    },
    cell: {
      children: (product: StandardTrafficObject) => {
        return (
          <ListChangeData
            value={product?.pattern_page_views_current_period ?? 0}
            changeValue={
              product?.change_in_pattern_page_views_from_comparison_period ?? 0
            }
            comparison={product?.pattern_page_views_comparison_period ?? 0}
            tooltipContent={
              product?.pattern_page_views_pct_change_comparison_period ?? 0
            }
            customClass={
              product.sortProp === 'pattern_page_views_current_period'
                ? 'fw-semi-bold'
                : ''
            }
            changeValueClass={
              product.sortProp ===
              'change_in_pattern_page_views_from_comparison_period'
                ? 'fw-semi-bold'
                : ''
            }
          />
        )
      },
    },
  },

  total_page_views: {
    name: 'page_views_current_period',
    label: c('pageViews'),
    tooltip: {
      content: (
        <div style={{ maxWidth: 400 }}>{t('content:pageViewsTooltip')}</div>
      ),
    },
    cell: {
      children: (product: StandardTrafficObject) => {
        return (
          <ListChangeData
            value={product?.page_views_current_period ?? 0}
            changeValue={
              product?.change_in_page_views_from_comparison_period ?? 0
            }
            comparison={product?.page_views_comparison_period ?? 0}
            tooltipContent={
              product?.page_views_pct_change_comparison_period ?? 0
            }
            customClass={
              product.sortProp === 'page_views_current_period'
                ? 'fw-semi-bold'
                : ''
            }
            changeValueClass={
              product.sortProp === 'change_in_page_views_from_comparison_period'
                ? 'fw-semi-bold'
                : ''
            }
          />
        )
      },
    },
  },
  ad_clicks: {
    name: 'value__ad_clicks',
    label: c('adClicks'),
    tooltip: {
      content: (
        <div style={{ maxWidth: 400 }}>{t('content:adClicksTooltip')}</div>
      ),
    },
    cell: {
      children: (product: StandardTrafficObject) => {
        return (
          <ListChangeData
            value={product?.ad_clicks?.value ?? 0}
            changeValue={product?.ad_clicks?.change ?? 0}
            comparison={product?.ad_clicks?.comparison ?? 0}
            tooltipContent={product?.ad_clicks?.pct_change ?? 0}
            customClass={
              product.sortProp === 'value__ad_clicks' ? 'fw-semi-bold' : ''
            }
            changeValueClass={
              product.sortProp === 'change__ad_clicks' ? 'fw-semi-bold' : ''
            }
          />
        )
      },
    },
  },
  buybox: {
    name: 'buy_box_current_period',
    label: c('buyBox'),
    tooltip: {
      content: (
        <div style={{ maxWidth: 400 }}>{t('content:buyBoxTooltip')}</div>
      ),
    },
    cell: {
      children: (product: StandardTrafficObject) => {
        return (
          <ListChangeData
            value={product?.buy_box_current_period ?? 0}
            changeValue={
              (product?.change_in_buy_box_from_comparison_period ?? 0) * 100
            }
            comparison={product?.buy_box_comparison_period ?? 0}
            tooltipContent={product?.buy_box_pct_change_comparison_period ?? 0}
            customClass={
              product.sortProp === 'buy_box_current_period'
                ? 'fw-semi-bold'
                : ''
            }
            changeValueClass={
              product.sortProp === 'change_in_buy_box_from_comparison_period'
                ? 'fw-semi-bold'
                : ''
            }
            percentage
            decimalScale={2}
          />
        )
      },
    },
  },
  weighted_in_stock_pct: {
    name: 'in_stock_current_period',
    label: c('inStockRate'),
    tooltip: {
      content: (
        <div style={{ maxWidth: 400 }}>
          {t('content:inStockPercentageTooltip')}
        </div>
      ),
    },
    cell: {
      children: (product: StandardTrafficObject) => {
        return (
          <ListChangeData
            value={product?.in_stock_current_period ?? 0}
            changeValue={
              (product?.change_in_in_stock_from_comparison_period ?? 0) * 100
            }
            comparison={product?.in_stock_comparison_period ?? 0}
            tooltipContent={product?.in_stock_pct_change_comparison_period ?? 0}
            customClass={
              product.sortProp === 'in_stock_current_period'
                ? 'fw-semi-bold'
                : ''
            }
            changeValueClass={
              product.sortProp === 'change_in_in_stock_from_comparison_period'
                ? 'fw-semi-bold'
                : ''
            }
            percentage
            decimalScale={2}
          />
        )
      },
    },
  },
  marketplaces: {
    name: 'marketplace_name',
    label: c('marketplace'),
    cell: {
      children: (product: StandardTrafficObject) => {
        return (
          <TrimText
            text={product?.marketplace_name}
            limit={75}
            customClass={
              product.sortProp === 'marketplace_name' ? 'fw-semi-bold' : ''
            }
          />
        )
      },
    },
  },
  stores: {
    name: 'store_name',
    label: c('store'),
    cell: {
      children: (product: StandardTrafficObject) => {
        return (
          <div className={styles.storeNameColumn}>
            <TrimText
              text={product?.store_name}
              limit={75}
              customClass={
                product.sortProp === 'store_name' ? 'fw-semi-bold' : ''
              }
            />
          </div>
        )
      },
    },
  },
}

export const additionalMetricsHeaders = ({
  metricName,
  columnLabel,
  decimalScale = 0,
  isPercentage = false,
  tooltipContent,
  currency,
}: {
  metricName: AdditionalMetricsBase
  columnLabel: string
  decimalScale?: number
  isPercentage?: boolean
  tooltipContent?: string
  currency?: Currency
}) => ({
  name: `${metricName}_current_period`,
  label: columnLabel,
  ...(tooltipContent
    ? {
        tooltip: {
          content: tooltipContent,
        },
      }
    : {}),
  cell: {
    children: (product: StandardTrafficObject) => {
      return (
        <ListChangeData
          value={product?.[`${metricName}_current_period`] ?? 0}
          changeValue={
            (product?.[`change_in_${metricName}_from_comparison_period`] ?? 0) *
            (isPercentage ? 100 : 1)
          }
          comparison={product?.[`${metricName}_comparison_period`] ?? 0}
          tooltipContent={
            product?.[`${metricName}_pct_change_comparison_period`] ?? 0
          }
          customClass={
            product.sortProp === `${metricName}_current_period`
              ? 'fw-semi-bold'
              : ''
          }
          changeValueClass={
            product.sortProp === `change_in_${metricName}_current_period`
              ? 'fw-semi-bold'
              : ''
          }
          decimalScale={decimalScale}
          percentage={isPercentage}
          currency={
            currency
              ? {
                  currency_code: currency?.code,
                  currency_symbol: currency?.symbol,
                }
              : undefined
          }
          changeFormat={currency ? 'currency' : undefined}
        />
      )
    },
  },
})
