import React from 'react'
import { c, t } from '@predict-services'

import {
  TrafficMetricPropsTooltipData,
  TrafficTooltipData,
} from '../../../Traffic/services/TrafficTooltipHelper'
import { sparkLineTooltipData } from '../Pages/AtAGlance/TooltipHelper'
import type { MetricPropsType } from '../../../Traffic/components/common/AdvertisingGraphData/trafficGraphData.models'

export const ContentTooltipData = {
  ...TrafficTooltipData, // content specific tooltips below here
  adSales: t('content:adSalesTooltip'),
  adSpend: t('content:adSpendTooltip'),
  adConversionRate: t('content:adConversionRateTooltip'),
  adClickThroughRate: t('content:adClickThroughRateTooltip'),
  organicClickShare: t('content:organicClickShareTooltip'),
  keywordsInTop4: t('content:keywordsInTopFourTooltip'),
  keywordsOnPage1: t('content:keywordsOnPageOneTooltip'),
  keywordsWithProductsInTop4: t('content:keywordsWithProductsInTopFourTooltip'),
  keywordsWithProductsOnPage1: t(
    'content:keywordsWithProductsOnPageOneTooltip',
  ),
  productsInTop4: t('content:productsInTopFourTooltip'),
  productsOnPage1: t('content:productsOnPageOneTooltip'),
  estOrganicClicks: t('content:estOrganicClicksTooltip'),
  advertisedKeywords: t('content:advertisedKeywordsTooltip'),
  estDailyClicksForTopPosition: t(
    'content:estDailyClicksForTopPositionTooltip',
  ),
  total_sales: t('content:totalSalesTooltip'),
  orders: t('content:ordersTooltip'),
  ad_orders: t('content:adOrdersTooltip'),
  avg_selling_price: t('content:avgSellingPriceTooltip'),
  avg_order_value: t('content:avgOrderValueTooltip'),
  units_per_order: t('content:unitsPerOrderTooltip'),
  pattern_page_views: t('content:patternPageViewsTooltip'),
  page_views: t('content:pageViewsTooltip'),
  ad_clicks: t('content:adClicksTooltip'),
  buy_box: t('content:buyBoxTooltip'),
  in_stock: t('content:inStockPercentageTooltip'),
  conversion: t('content:conversionRateTooltip'),
  units: t('content:unitsTooltip'),
  conversion_rate_without_sns: t('content:conversionRateWithoutSNSTooltip'),
  unique_visitors: t('content:uniqueVisitorsTooltip'),
  add_to_cart: t('content:addToCartTooltip'),
  buyer_conversion_rate: t('content:buyerConversionRateTooltip'),
  avg_order_value_by_buyer: t('content:avgOrderValueByBuyerTooltip'),
  clicks_share: c('clicksShareTooltip'),
  daily_search_query_volume: t('content:searchVolumeTooltip'),
}

export const ContentMetricPropsTooltipData: MetricPropsType[] = [
  {
    type: 'tooltip',
    key: 'attributed_sales',
    content: ContentTooltipData.adSales,
  },
  {
    type: 'tooltip',
    key: 'cost',
    content: ContentTooltipData.adSpend,
  },
  {
    type: 'tooltip',
    key: 'conversion_rate',
    content: ContentTooltipData.adConversionRate,
  },
  {
    type: 'tooltip',
    key: 'click_through_rate',
    content: ContentTooltipData.adClickThroughRate,
  },
  {
    type: 'tooltip',
    key: 'organic_click_share',
    content: ContentTooltipData.organicClickShare,
  },
  {
    type: 'tooltip',
    key: 'keywords_in_top_4',
    content: ContentTooltipData.keywordsInTop4,
  },
  {
    type: 'tooltip',
    key: 'keywords_on_page_1',
    content: ContentTooltipData.keywordsOnPage1,
  },
  {
    type: 'tooltip',
    key: 'keywords_with_products_in_top_4',
    content: ContentTooltipData.keywordsWithProductsInTop4,
  },
  {
    type: 'tooltip',
    key: 'keywords_with_products_on_page_1',
    content: ContentTooltipData.keywordsWithProductsOnPage1,
  },
  {
    type: 'tooltip',
    key: 'products_in_top_4',
    content: ContentTooltipData.productsInTop4,
  },
  {
    type: 'tooltip',
    key: 'products_on_page_1',
    content: ContentTooltipData.productsOnPage1,
  },
  {
    type: 'tooltip',
    key: 'est_organic_clicks',
    content: ContentTooltipData.estOrganicClicks,
  },
  {
    type: 'tooltip',
    key: 'advertised_keywords',
    content: ContentTooltipData.advertisedKeywords,
  },
  {
    type: 'tooltip',
    key: 'est_daily_clicks_for_top_position',
    content: ContentTooltipData.estDailyClicksForTopPosition,
  },
  ...TrafficMetricPropsTooltipData(), // content specific tooltips above here
  {
    type: 'tooltip',
    key: 'page_views',
    content: ContentTooltipData.page_views,
  },
  {
    type: 'tooltip',
    key: 'pattern_page_views',
    content: ContentTooltipData.pattern_page_views,
  },
  {
    type: 'tooltip',
    key: 'units_per_order',
    content: ContentTooltipData.units_per_order,
  },
  {
    type: 'tooltip',
    key: 'avg_order_value',
    content: ContentTooltipData.avg_order_value,
  },

  {
    type: 'tooltip',
    key: 'avg_selling_price',
    content: ContentTooltipData.avg_selling_price,
  },
  {
    type: 'tooltip',
    key: 'ad_orders',
    content: ContentTooltipData.ad_orders,
  },
  {
    type: 'tooltip',
    key: 'orders',
    content: ContentTooltipData.orders,
  },
  {
    type: 'tooltip',
    key: 'total_sales',
    content: ContentTooltipData.total_sales,
  },
  {
    type: 'tooltip',
    key: 'ad_clicks',
    content: ContentTooltipData.ad_clicks,
  },
  {
    type: 'tooltip',
    key: 'buy_box',
    content: ContentTooltipData.buy_box,
  },
  {
    type: 'tooltip',
    key: 'in_stock',
    content: ContentTooltipData.in_stock,
  },
  {
    type: 'tooltip',
    key: 'conversion',
    content: ContentTooltipData.conversion,
  },
  {
    type: 'tooltip',
    key: 'units',
    content: ContentTooltipData.units,
  },
  {
    type: 'tooltip',
    key: 'conversion_rate_order',
    content: sparkLineTooltipData.orders,
  },
  {
    type: 'tooltip',
    key: 'conversion_without_sns',
    content: ContentTooltipData.conversion_rate_without_sns,
  },
  {
    type: 'tooltip',
    key: 'unique_visitors',
    content: ContentTooltipData.unique_visitors,
  },
  {
    type: 'tooltip',
    key: 'add_to_cart',
    content: ContentTooltipData.add_to_cart,
  },
  {
    type: 'tooltip',
    key: 'buyer_conversion_rate',
    content: ContentTooltipData.buyer_conversion_rate,
  },
  {
    type: 'tooltip',
    key: 'avg_order_value_by_buyer',
    content: ContentTooltipData.avg_order_value_by_buyer,
  },
]

export const getContentTableHeaderTooltip = (
  key: keyof typeof ContentTooltipData,
) => {
  return {
    tooltip: {
      content: <div>{ContentTooltipData[key]}</div>,
    },
  }
}
