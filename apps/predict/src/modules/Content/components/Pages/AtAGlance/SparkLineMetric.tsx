import React, { useContext, useMemo } from 'react'
import { Link } from 'react-router-dom'
import moment from 'moment'
import {
  <PERSON>ton,
  type CompareWith,
  hasValue,
  type HeaderMetricProps,
  type TimeframeType,
} from '@patterninc/react-ui'
import { type GraphColorType } from 'src/common/components/SparklineMetrics/SparklineMetricsType'

import { SecondaryMetric } from '../../../../../common/components/SecondaryMetric/SecondaryMetric'
import SparklineMetric from '../../../../../common/components/SparklineMetrics/SparklineMetrics'
import { ThemeContext } from '../../../../../Context'
import { useUser } from '../../../../../context/user-context'
import {
  type ChangeValueProps,
  type ContextPropTypes,
  type SecondaryMetricsDataFilterKey,
  type SparkLineMetricsParams,
} from './SparkLineTypes'
import { getStrokeColor, sparkLineTooltipData } from './TooltipHelper'

const SparkLineHeaderMetric = (
  sparkLineMetricsParams: SparkLineMetricsParams,
) => {
  const user = useUser().user,
    currentCurrency = user?.current_currency
  const {
    startDate,
    endDate,
    timeframe,
    selectedComparisonPeriod,
  }: ContextPropTypes = useContext(ThemeContext)

  const currentStartDateEnddate = useMemo(() => {
    if (
      timeframe.type === 'current' ||
      (timeframe.type === 'quarterly' &&
        moment().quarter() === timeframe.quarter)
    ) {
      return {
        startDate,
        endDate: moment().format(),
      }
    }
    return {
      startDate,
      endDate,
    }
  }, [endDate, startDate, timeframe.quarter, timeframe.type])

  const {
    headerMetricIcon,
    footerButtonText = '',
    footerButtonLink = '',
    headerMetricTitle,
    change,
    graphData,
    metricsValue,
    pctChange,
    graphColor,
    formatType = 'number',
    decimalScale = 0,
    customContainerClass = '',
    tooltipContent = '',
    hasCurrencyPrefix = false,
    dataType,
    summaryData,
    isLoading,
    sparkLineData,
    comparisionData,
    maxHeadermetricHeight,
    onHeightChange,
    productContentPath,
    sparklineMetricsData,
  } = sparkLineMetricsParams

  let headerChangeValue: ChangeValueProps = change,
    headerPctChangeValue: ChangeValueProps = pctChange

  if (!hasValue(metricsValue)) {
    headerChangeValue = null
    headerPctChangeValue = null
  }

  const strokeColor =
    graphColor === 'good' ? 'green' : graphColor === 'bad' ? 'red' : 'blue'

  const headerMetricPropsData: HeaderMetricProps = {
    title: headerMetricTitle,
    tooltip: tooltipContent,
    value: metricsValue,
    loading: isLoading,
    change: headerChangeValue ?? undefined,
    ...(hasCurrencyPrefix
      ? {
          currency: {
            currencyCode: currentCurrency?.code,
            currencySymbol: currentCurrency?.symbol,
          },
        }
      : {}),
    pctChange: headerPctChangeValue ?? undefined,
    formatType: formatType,
    decimalScale: decimalScale,
    comparisonTooltip: {
      primaryMetricTitle: headerMetricTitle,
      comparison: comparisionData ?? 0,
      currentPeriodDates: currentStartDateEnddate,
      timeframe: timeframe as TimeframeType,
      compareWith: selectedComparisonPeriod?.value as CompareWith,
    },
  }

  const sparklineMetricPropsData = {
    dataKey: 'value',
    graphData: graphData?.length ? graphData : [],
    graphColor: strokeColor as GraphColorType,
    dataType: dataType ?? formatType,
    periodKey: 'date',
    headerMetricProps: {
      title: headerMetricTitle,
      value: metricsValue ?? 0,
      loading: isLoading,
      change: change,
      pctChange: pctChange,
      formatType: formatType,
      decimalScale: decimalScale,
      ...(hasCurrencyPrefix
        ? {
            currency: {
              currencyCode: currentCurrency?.code,
              currencySymbol: currentCurrency?.symbol,
            },
          }
        : {}),
    },
    secondaryDisplay: () => getSecondaryMetric(headerMetricTitle),
  }

  const getSecondaryMetric = (headerMetricTitle: string) => {
    const filteredData = getSecondaryMetricsData(headerMetricTitle)
    const metrics = filteredData.map((secondaryMetricsData, index) => {
      const sparklineData = {
        graphData: secondaryMetricsData.graphData,
        strokeColor:
          secondaryMetricsData.strokeColor === 'good' ? 'green' : 'red',
        dataKey: 'value',
        threshold: 'threshold',
        periodKey: 'date',
      }
      const headerMetricData = {
        ...secondaryMetricsData.headerMetricData,
        loading: isLoading,
        className: 'secondary-metrics-header-container',
      }
      return (
        <div
          key={`${headerMetricTitle}-${index}`}
          className={
            filteredData.length - 1 !== index ? 'bdrb bdrc-light-gray' : ''
          }
        >
          <SecondaryMetric
            headerMetricProps={headerMetricData}
            sparklineProps={sparklineData}
          />
        </div>
      )
    })
    return <div>{metrics}</div>
  }

  const getSecondaryMetricsData = (headerMetricTitle: string) => {
    let filterDataKey: SecondaryMetricsDataFilterKey = []
    switch (headerMetricTitle) {
      case 'Weighted Avg Retail Readiness':
        filterDataKey = [
          {
            graphData: sparkLineData?.avg_retail_readiness ?? [],
            strokeColor: getStrokeColor(
              summaryData?.retail_readiness?.pct_change,
            ),
            headerMetricData: {
              change: summaryData?.retail_readiness?.change,
              decimalScale: 2,
              formatType: 'number',
              pctChange: summaryData?.retail_readiness?.pct_change,
              title: 'Avg retail readiness',
              value: summaryData?.retail_readiness?.value,
              tooltip: sparkLineTooltipData.avg_retail_readiness,
            },
          },
          {
            graphData: sparkLineData?.listings_100_percent_ready ?? [],
            strokeColor: getStrokeColor(
              summaryData?.listings_100_percent_ready?.pct_change,
            ),
            headerMetricData: {
              change: summaryData?.listings_100_percent_ready?.change,
              decimalScale: 0,
              formatType: 'number',
              pctChange: summaryData?.listings_100_percent_ready?.pct_change,
              title: 'Listings 100% ready',
              value: summaryData?.listings_100_percent_ready?.value,
              tooltip: sparkLineTooltipData.listing_ready,
            },
          },
        ]
        break
      case 'Est Organic Clicks':
        filterDataKey = [
          {
            graphData: sparkLineData?.keywords_in_top_4 ?? [],
            strokeColor: getStrokeColor(
              summaryData?.keywords_in_top_4?.pct_change,
            ),
            headerMetricData: {
              change: summaryData?.keywords_in_top_4?.change,
              decimalScale: 0,
              formatType: 'number',
              pctChange: summaryData?.keywords_in_top_4?.pct_change,
              title: 'Keywords in top 4',
              value: summaryData?.keywords_in_top_4?.value,
              tooltip: sparkLineTooltipData.keyword_in_top_4,
            },
          },
          {
            graphData: sparkLineData?.keywords_on_page_1 ?? [],
            strokeColor: getStrokeColor(
              summaryData?.keywords_on_page_1?.pct_change,
            ),
            headerMetricData: {
              change: summaryData?.keywords_on_page_1?.change,
              decimalScale: 0,
              formatType: 'number',
              pctChange: summaryData?.keywords_on_page_1?.pct_change,
              title: 'Keywords on page 1',
              value: summaryData?.keywords_on_page_1?.value,
              tooltip: sparkLineTooltipData.keywords_on_page_1,
            },
          },
        ]
        break
      case 'Avg Rating':
        filterDataKey = [
          {
            graphData: sparklineMetricsData?.reviews_chart_data ?? [],
            strokeColor: sparklineMetricsData?.reviews_stroke_color,
            headerMetricData: {
              change:
                sparklineMetricsData?.change_in_reviews_from_comparison_period,
              decimalScale: 0,
              formatType: 'number',
              pctChange:
                sparklineMetricsData?.reviews_pct_change_comparison_period,
              title: 'Ratings',
              value: sparklineMetricsData?.reviews_current_period,
              tooltip: sparkLineTooltipData.ratings,
            },
          },
          // Disabling metrics until now, we don't have data.
          // {
          //   graphData: sparkLineData?.reviews_count ?? [],
          //   strokeColor: getStrokeColor(summaryData?.reviews_count?.pct_change),
          //   headerMetricData: {
          //     change: summaryData?.reviews_count?.change,
          //     decimalScale: 0,
          //     formatType: 'number',
          //     pctChange: summaryData?.reviews_count?.pct_change,
          //     title: 'Reviews',
          //     value: summaryData?.reviews_count?.value,
          //     tooltip: sparkLineTooltipData.reviews,
          //   },
          // },
        ]
        break
      case 'Pattern Page Views':
        filterDataKey = [
          {
            graphData:
              sparklineMetricsData?.pattern_page_views_chart_data ?? [],
            strokeColor: sparklineMetricsData?.pattern_page_views_stroke_color,
            headerMetricData: {
              change:
                sparklineMetricsData?.change_in_page_views_from_comparison_period,
              decimalScale: 0,
              formatType: 'number',
              pctChange:
                sparklineMetricsData?.page_views_pct_change_comparison_period,
              title: 'Page Views',
              value: sparklineMetricsData?.page_views_current_period,
              tooltip: sparkLineTooltipData.total_page_views,
            },
          },
          // Disabling metrics until now, we don't have data.
          // {
          //   graphData: sparkLineData?.ad_clicks ?? [],
          //   strokeColor: getStrokeColor(summaryData?.ad_clicks?.pct_change),
          //   headerMetricData: {
          //     change: summaryData?.ad_clicks?.change,
          //     decimalScale: 0,
          //     formatType: 'number',
          //     pctChange: summaryData?.ad_clicks?.pct_change,
          //     title: 'Ad clicks',
          //     value: summaryData?.ad_clicks?.value,
          //     tooltip: sparkLineTooltipData.ad_clicks,
          //   },
          // },
        ]
        break
      case 'Orders':
        filterDataKey = [
          // Disabling metrics until now, we don't have data.
          // {
          //   graphData: sparkLineData?.ad_orders ?? [],
          //   strokeColor: getStrokeColor(summaryData?.ad_orders?.pct_change),
          //   headerMetricData: {
          //     change: summaryData?.ad_orders?.change,
          //     decimalScale: 0,
          //     formatType: 'number',
          //     pctChange: summaryData?.ad_orders?.pct_change,
          //     title: 'Ad orders',
          //     value: summaryData?.ad_orders?.value,
          //     tooltip: sparkLineTooltipData.ad_orders,
          //   },
          // },
          {
            graphData: sparklineMetricsData?.avg_order_value_chart_data ?? [],
            strokeColor: sparklineMetricsData?.avg_order_value_stroke_color,
            headerMetricData: {
              change:
                sparklineMetricsData?.change_in_avg_order_value_from_comparison_period,
              decimalScale: 2,
              formatType: 'number',
              pctChange:
                sparklineMetricsData?.avg_order_value_pct_change_comparison_period,
              title: 'Average Order Value',
              value: sparklineMetricsData?.avg_order_value_current_period,
              tooltip: sparkLineTooltipData.revenue_per_order,
              currency: {
                currencyCode: currentCurrency?.code,
                currencySymbol: currentCurrency?.symbol,
              },
            },
          },
        ]
        break
      default:
        filterDataKey = []
        break
    }
    return filterDataKey
  }

  return (
    <SparklineMetric
      iconProps={{
        icon: headerMetricIcon,
      }}
      headerMetricProps={headerMetricPropsData}
      maxHeadermetricHeight={maxHeadermetricHeight}
      onHeightChange={onHeightChange}
      productContentPath={productContentPath}
      sparklineProps={sparklineMetricPropsData}
      footerButton={
        footerButtonText ? (
          <Button as='link' to={footerButtonLink} routerComponent={Link}>
            {footerButtonText}
          </Button>
        ) : (
          <div />
        )
      }
      customeContainerClass={customContainerClass}
    />
  )
}

export default SparkLineHeaderMetric
