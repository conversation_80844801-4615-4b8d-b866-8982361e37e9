import { type IconStringList } from '@patterninc/react-ui'

export type ContextPropTypes = {
  endDate?: string
  startDate?: string
  timeframe: {
    aggregation?: string
    timeValue: string
    type: string
    value?: string
    display?: string
    quarter?: number
  }
  selectedComparisonPeriod?: { value: string }
}

export type ChangeValueProps = number | undefined | null

export type SecondaryMetricsDataFilterKey = {
  graphData: { date: string; value: number | null }[]
  strokeColor?: string
  headerMetricData: {
    change?: number
    decimalScale: number
    formatType: 'number' | 'percentage'
    pctChange?: number
    title: string
    reverse?: boolean
    value?: number
    currency?: {
      currencyCode: string
      currencySymbol: string
    }
    tooltip?: React.ReactNode
  }
}[]

export type SparkLineMetricsParams = {
  footerButtonText?: string
  footerButtonLink?: string
  headerMetricTitle: string
  headerMetricIcon: IconStringList
  pctChange: number | undefined
  change: number | undefined
  graphColor?: string
  metricsValue: number | undefined
  formatType?: 'percentage' | 'number'
  graphData: SparkLineChartData[] | undefined
  decimalScale?: number
  customContainerClass?: string
  tooltipContent?: string
  hasCurrencyPrefix?: boolean
  comparisionData: number | undefined
  comparisionMetricTitle?: string
  dataType?: 'number' | 'currency' | 'percentage' | 'sales'
  summaryData: SummaryDataProps
  isLoading: boolean
  sparkLineData: SparkLineData
  maxHeadermetricHeight: number
  onHeightChange: (height: number) => void
  productContentPath?: string
  sparklineMetricsData?: SparklineMetricsData
}

export type SparkLineData = {
  ad_orders: SparkLineChartData[]
  revenue_per_order: SparkLineChartData[]
  pattern_page_views: SparkLineChartData[]
  ad_clicks: SparkLineChartData[]
  total_page_views: SparkLineChartData[]
  reviews_count: SparkLineChartData[]
  ratings_count: SparkLineChartData[]
  keywords_on_page_1: SparkLineChartData[]
  keywords_in_top_4: SparkLineChartData[]
  avg_retail_readiness: SparkLineChartData[]
  listings_100_percent_ready: SparkLineChartData[]
}

export type SparkLineChartData = {
  date: string
  value: number
}

export type SparkLineWidgetsProps = {
  api: string
  params: Record<string, unknown>
  summaryData: SummaryDataProps
  productContentPath?: string
  sparklineMetricsData?: SparklineMetricsData
}

type SparklineMetricsData = {
  conversion_rate_order_current_period: number
  change_in_conversion_rate_order_from_comparison_period: number
  conversion_rate_order_pct_change_comparison_period: number
  conversion_comparison_period: number
  pattern_page_views_current_period: number
  change_in_pattern_page_views_from_comparison_period: number
  pattern_page_views_pct_change_comparison_period: number
  pattern_page_views_comparison_period: number
  conversion_rate_order_comparison_period: number
  conversion_rate_order_chart_data: SparkLineChartData[]
  pattern_page_views_chart_data: SparkLineChartData[]
  reviews_chart_data: SparkLineChartData[]
  ratings_chart_data: SparkLineChartData[]
  avg_order_value_chart_data: SparkLineChartData[]
  pattern_page_views_stroke_color: string
  conversion_rate_order_stroke_color: string
  ratings_current_period: number
  ratings_pct_change_comparison_period: number
  change_in_ratings_from_comparison_period: number
  reviews_current_period: number
  change_in_reviews_from_comparison_period: number
  reviews_pct_change_comparison_period: number
  reviews_stroke_color: string
  ratings_stroke_color: string
  avg_order_value_current_period: number
  change_in_avg_order_value_from_comparison_period: number
  avg_order_value_pct_change_comparison_period: number
  avg_order_value_stroke_color: string
  page_views_current_period: number
  change_in_page_views_from_comparison_period: number
  page_views_pct_change_comparison_period: number
}

export type KeyProps = {
  comparison: number
  value: number
  type: string
  display: string
  change: number
  pct_change: number
}

export type SummaryDataProps = {
  ad_orders: KeyProps
  conversion_rate_page_views: KeyProps
  pattern_page_views: KeyProps
  avg_rating: KeyProps
  est_organic_clicks: KeyProps
  revenue_weighted_retail_readiness: KeyProps
  pattern_orders: KeyProps
  revenue_per_order: KeyProps
  total_page_views: KeyProps
  ad_clicks: KeyProps
  ratings_count: KeyProps
  reviews_count: KeyProps
  keywords_in_top_4: KeyProps
  keywords_on_page_1: KeyProps
  listings_100_percent_ready: KeyProps
  retail_readiness: KeyProps
}
