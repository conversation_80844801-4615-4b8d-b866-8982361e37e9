import React, { useContext, useEffect, useMemo, useState } from 'react'
import { useQuery } from '@tanstack/react-query'
import { useIsMobileView } from '@patterninc/react-ui'
import { ThemeContext } from 'src/Context'

import { c, SecureAxios, useTranslate } from '../../../../../common/services'
import { trafficCacheTime } from '../../../../Traffic/services/TrafficHelperService'
import DivideMetric from './DivisionMetric'
import SparkLineHeaderMetric from './SparkLineMetric'
import { type SparkLineWidgetsProps } from './SparkLineTypes'
import { getStrokeColor, sparkLineTooltipData } from './TooltipHelper'

const SparkLineWidgets = ({
  api,
  params,
  summaryData,
  productContentPath,
  sparklineMetricsData,
}: SparkLineWidgetsProps) => {
  const isMobileView = useIsMobileView()
  const { checkIfAllChinaMarketplaces } = useContext(ThemeContext)
  const showItemsForChinaRegion = useMemo(() => {
      const areAllSelectedMarketplacesChinese = checkIfAllChinaMarketplaces()

      return !areAllSelectedMarketplacesChinese
    }, [checkIfAllChinaMarketplaces]),
    [maxHeadermetricHeight, setMaxHeadermetricHeight] = useState<number>(0),
    { t } = useTranslate('content'),
    [triggerRecalculation, setTriggerRecalculation] = useState<boolean>(false),
    handleSparklineHeightChange = (height: number) => {
      // Update the maximum height whenever a Sparkline's height changes
      setMaxHeadermetricHeight((prevHeight) => Math.max(prevHeight, height))
    }

  const atGlanceHistoryApi = `${api}/sparkline_charts`,
    {
      status: sparklineStatus,
      data: sparklineChartsResponse,
      isLoading,
    } = useQuery({
      queryKey: [params],
      queryFn: ({ signal }) =>
        SecureAxios.get(atGlanceHistoryApi, { params: params, signal }),
      gcTime: trafficCacheTime,
    }),
    sparkLineData =
      sparklineStatus === 'success' && sparklineChartsResponse?.data?.data?.[0]

  useEffect(() => {
    setTriggerRecalculation(true)
  }, [sparkLineData])

  useEffect(() => {
    if (triggerRecalculation) {
      const headermetricElements = document.querySelectorAll(
        '.sparklineHeaderSection',
      )

      let newMaxHeight = headermetricElements[0]?.clientHeight || 0

      headermetricElements.forEach((element) => {
        if (element.clientHeight > newMaxHeight) {
          newMaxHeight = element.clientHeight
        }
      })

      setMaxHeadermetricHeight(newMaxHeight)
      setTriggerRecalculation(false) // Reset triggerRecalculation to false after recalculating
    }
  }, [triggerRecalculation])

  return (
    <div
      className={`${
        isMobileView ? '' : 'flex pat-gap-4 sparkline-metric-container'
      }`}
    >
      <div
        className={`${
          isMobileView
            ? 'border-bottom-wrap'
            : `flex flex-direction-column pat-px-4 `
        } ${showItemsForChinaRegion ? `primary-subcontainer` : `full-width`}
        } bdr bdrc-light-gray sparkline-main-container-wrap`}
      >
        <div className='flex justify-content-center pat-py-4 fw-semi-bold'>
          {t('conversionCalculation')}
        </div>
        <div className={`${isMobileView ? '' : 'flex'} justify-content-around`}>
          <div
            className={`${
              isMobileView
                ? 'pat-px-4'
                : 'primary-subcontainer-metrics orders-metric'
            } pat-pb-4`}
          >
            <SparkLineHeaderMetric
              headerMetricTitle={c('orders')}
              headerMetricIcon='shield'
              footerButtonText={c('conversion')}
              footerButtonLink={
                productContentPath
                  ? `${productContentPath}/conversion`
                  : '/content/conversion'
              }
              comparisionData={
                sparklineMetricsData?.conversion_rate_order_comparison_period
              }
              graphColor={
                sparklineMetricsData?.conversion_rate_order_stroke_color
              }
              pctChange={
                sparklineMetricsData?.conversion_rate_order_pct_change_comparison_period
              }
              change={
                sparklineMetricsData?.change_in_conversion_rate_order_from_comparison_period
              }
              metricsValue={
                sparklineMetricsData?.conversion_rate_order_current_period
              }
              graphData={
                sparklineMetricsData?.conversion_rate_order_chart_data ?? []
              }
              formatType={'number'}
              tooltipContent={sparkLineTooltipData.orders}
              summaryData={summaryData}
              sparkLineData={sparkLineData}
              sparklineMetricsData={sparklineMetricsData}
              isLoading={isLoading}
              maxHeadermetricHeight={maxHeadermetricHeight}
              onHeightChange={handleSparklineHeightChange}
              productContentPath={productContentPath}
            />
          </div>

          <DivideMetric />

          <div
            className={`${
              isMobileView
                ? 'pat-px-4 pat-mt-4'
                : 'primary-subcontainer-metrics'
            } pat-pb-4`}
          >
            <SparkLineHeaderMetric
              headerMetricTitle={
                checkIfAllChinaMarketplaces()
                  ? c('pageViews')
                  : c('patternPageViews')
              }
              headerMetricIcon='globe1'
              footerButtonText={c('conversion')}
              footerButtonLink={
                productContentPath
                  ? `${productContentPath}/conversion`
                  : '/content/conversion'
              }
              comparisionData={
                sparklineMetricsData?.pattern_page_views_comparison_period
              }
              graphColor={sparklineMetricsData?.pattern_page_views_stroke_color}
              pctChange={
                sparklineMetricsData?.pattern_page_views_pct_change_comparison_period
              }
              change={
                sparklineMetricsData?.change_in_pattern_page_views_from_comparison_period
              }
              metricsValue={
                sparklineMetricsData?.pattern_page_views_current_period
              }
              graphData={sparklineMetricsData?.pattern_page_views_chart_data}
              formatType='number'
              tooltipContent={
                checkIfAllChinaMarketplaces()
                  ? sparkLineTooltipData.page_views
                  : sparkLineTooltipData.pattern_page_views
              }
              summaryData={summaryData}
              sparkLineData={sparkLineData}
              sparklineMetricsData={sparklineMetricsData}
              isLoading={isLoading}
              maxHeadermetricHeight={maxHeadermetricHeight}
              onHeightChange={handleSparklineHeightChange}
              productContentPath={productContentPath}
            />
          </div>
        </div>
      </div>

      {/* Driving factor metrics */}
      <div
        className={`${
          isMobileView
            ? 'border-top-wrap'
            : 'flex secondary-subcontainer flex-direction-column pat-px-4'
        } bdr bdrc-light-gray sparkline-main-container-wrap`}
      >
        <div className='flex justify-content-center pat-py-4 fw-semi-bold'>
          {t('drivingFactors')}
        </div>
        <div
          className={`${isMobileView || showItemsForChinaRegion ? 'flex' : ''} pat-gap-4`}
        >
          <div
            className={`${
              isMobileView ? 'pat-mx-4' : 'flex flex-direction-column '
            } ${
              showItemsForChinaRegion ? 'secondary-subcontainer-metrics' : ''
            }`}
          >
            <SparkLineHeaderMetric
              headerMetricTitle={c('avgRating')}
              headerMetricIcon='traffic'
              footerButtonText={c('reviews')}
              footerButtonLink={
                productContentPath
                  ? `${productContentPath}/reviews`
                  : '/content/reviews/products'
              }
              formatType='number'
              decimalScale={2}
              customContainerClass='pat-mb-4'
              comparisionData={sparklineMetricsData?.ratings_current_period}
              pctChange={
                sparklineMetricsData?.ratings_pct_change_comparison_period
              }
              change={
                sparklineMetricsData?.change_in_ratings_from_comparison_period
              }
              metricsValue={sparklineMetricsData?.ratings_current_period}
              graphData={sparklineMetricsData?.ratings_chart_data}
              graphColor={sparklineMetricsData?.ratings_stroke_color}
              tooltipContent={sparkLineTooltipData.avg_rating}
              summaryData={summaryData}
              sparklineMetricsData={sparklineMetricsData}
              sparkLineData={sparkLineData}
              isLoading={isLoading}
              maxHeadermetricHeight={maxHeadermetricHeight}
              onHeightChange={handleSparklineHeightChange}
              productContentPath={productContentPath}
            />
          </div>
          {showItemsForChinaRegion && (
            <>
              <div
                className={`${
                  isMobileView
                    ? 'pat-mx-4'
                    : 'flex flex-direction-column secondary-subcontainer-metrics'
                }`}
              >
                <SparkLineHeaderMetric
                  headerMetricTitle={t('estOrganicClicks')}
                  comparisionData={summaryData?.est_organic_clicks?.comparison}
                  headerMetricIcon='traffic'
                  footerButtonText={c('rankTracker')}
                  footerButtonLink={
                    productContentPath
                      ? `${productContentPath}/rank-tracker`
                      : '/content/rank-tracker'
                  }
                  formatType='number'
                  customContainerClass='pat-mb-4'
                  pctChange={summaryData?.est_organic_clicks?.pct_change}
                  change={summaryData?.est_organic_clicks?.change}
                  metricsValue={summaryData?.est_organic_clicks?.value}
                  graphData={sparkLineData?.est_organic_clicks}
                  graphColor={getStrokeColor(
                    summaryData?.est_organic_clicks?.pct_change,
                  )}
                  tooltipContent={sparkLineTooltipData.est_organic_clicks}
                  summaryData={summaryData}
                  sparkLineData={sparkLineData}
                  isLoading={isLoading}
                  maxHeadermetricHeight={maxHeadermetricHeight}
                  onHeightChange={handleSparklineHeightChange}
                  productContentPath={productContentPath}
                />
              </div>

              <div
                className={`${
                  isMobileView
                    ? 'pat-mx-4'
                    : 'flex flex-direction-column secondary-subcontainer-metrics'
                }`}
              >
                <SparkLineHeaderMetric
                  headerMetricTitle={t('weightedAvgRetailReadiness')}
                  headerMetricIcon='traffic'
                  footerButtonText={t('retailReadiness')}
                  footerButtonLink={
                    productContentPath
                      ? `${productContentPath}/retail-readiness`
                      : '/content/retail-readiness'
                  }
                  formatType='number'
                  decimalScale={2}
                  customContainerClass='pat-mb-4'
                  comparisionData={
                    summaryData?.revenue_weighted_retail_readiness?.comparison
                  }
                  graphColor={getStrokeColor(
                    summaryData?.revenue_weighted_retail_readiness?.pct_change,
                  )}
                  pctChange={
                    summaryData?.revenue_weighted_retail_readiness?.pct_change
                  }
                  change={
                    summaryData?.revenue_weighted_retail_readiness?.change
                  }
                  metricsValue={
                    summaryData?.revenue_weighted_retail_readiness?.value
                  }
                  graphData={sparkLineData?.revenue_weighted_retail_readiness}
                  tooltipContent={
                    sparkLineTooltipData.weighted_avg_retail_readiness
                  }
                  summaryData={summaryData}
                  sparkLineData={sparkLineData}
                  isLoading={isLoading}
                  maxHeadermetricHeight={maxHeadermetricHeight}
                  onHeightChange={handleSparklineHeightChange}
                  productContentPath={productContentPath}
                />
              </div>
            </>
          )}
        </div>
      </div>
    </div>
  )
}

export default SparkLineWidgets
