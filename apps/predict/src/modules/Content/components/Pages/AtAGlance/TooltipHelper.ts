import { t } from '@predict-services'

import type { MetricPropsType } from '../../../../Traffic/components/common/AdvertisingGraphData/trafficGraphData.models'

export const getStrokeColor = (metricValue: number) => {
  return metricValue > 0 ? 'good' : 'bad'
}

export const sparkLineTooltipData = {
  ad_orders: t('content:sparkLineTooltipDataAdOrders'),
  revenue_per_order: t('content:sparkLineTooltipDataRevenuePerOrder'),
  pattern_page_views: t('content:sparkLineTooltipDataPatternPageViews'),
  total_page_views: t('content:sparkLineTooltipDataTotalPageViews'),
  ad_clicks: t('content:sparkLineTooltipDataAdClicks'),
  ratings: t('content:sparkLineTooltipDataRatings'),
  reviews: t('content:sparkLineTooltipDataReviews'),
  keyword_in_top_4: t('content:sparkLineTooltipDataKeywordInTop4'),
  keywords_on_page_1: t('content:sparkLineTooltipDataKeywordsOnPage1'),
  avg_retail_readiness: t('content:sparkLineTooltipDataAvgRetailReadiness'),
  listing_ready: t('content:sparkLineTooltipDataListingReady'),
  est_organic_clicks: t('content:sparkLineTooltipDataEstOrganicClicks'),
  avg_rating: t('content:sparkLineTooltipDataAvgRating'),
  orders: t('content:sparkLineTooltipDataOrders'),
  weighted_avg_retail_readiness: t(
    'content:sparkLineTooltipDataWeightedAvgRetailReadiness',
  ),
  page_views: t('content:pageViewsTooltip'),
}

export const MetricTooltipData: MetricPropsType[] = [
  {
    type: 'tooltip',
    key: 'conversion_rate_page_views',
    content: t('content:metricTooltipDataConversionRatePageViews'),
  },
  {
    type: 'tooltip',
    key: 'total_sales',
    content: t('content:metricTooltipDataTotalSales'),
  },
  {
    type: 'tooltip',
    key: 'conversion_rate_without_sns',
    content: t('content:conversionRateWithoutSNSTooltip'),
  },
]
