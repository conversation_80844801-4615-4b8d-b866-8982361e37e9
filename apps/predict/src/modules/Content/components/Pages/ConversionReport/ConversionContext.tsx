import { tryLocalStorageParse } from '@predict-services'
import React, { createContext, type Dispatch, useReducer } from 'react'

type FilterAction =
  | {
      type: 'UPDATE_BRAND_ID'
      payload: { brandId: number; primaryTag?: string }
    }
  | { type: 'UPDATE_FILTER'; payload: { filters: filterType } }
  | { type: 'RESET_FILTER' }

export interface filterType {
  productsDrivingOfRevenue?: number | string
  productsSoldBy: { id: number; state: string; value: string }
  status: { id: number; state: string; value: string }
  brandId?: number
  primaryTag?: string
}

export const defaultFilter: filterType = {
  productsDrivingOfRevenue: '',
  productsSoldBy: {
    id: 1,
    state: 'Sold by Pattern',
    value: 'sold_by_pattern',
  },
  status: {
    id: 1,
    state: 'Active',
    value: 'active',
  },
}

const initialState = () =>
  tryLocalStorageParse('conversion_report_filter') ?? {
    ...defaultFilter,
  }

const ConversionReportContext = createContext<{
  state: filterType
  dispatch: Dispatch<FilterAction>
}>({
  state: defaultFilter,
  dispatch: () => null,
})
const { Provider } = ConversionReportContext

const reducer = (state: filterType, action: FilterAction): filterType => {
  switch (action.type) {
    case 'UPDATE_BRAND_ID':
      return {
        ...state,
        brandId: action.payload.brandId,
        primaryTag: action.payload.primaryTag,
      }
    case 'UPDATE_FILTER':
      localStorage.setItem(
        'conversion_report_filter',
        JSON.stringify({
          ...state,
          ...action.payload.filters,
        }),
      )
      return {
        ...state,
        ...action.payload.filters,
      }
    case 'RESET_FILTER':
      localStorage.setItem(
        'conversion_report_filter',
        JSON.stringify(defaultFilter),
      )
      return { ...defaultFilter }
    default:
      return { ...state }
  }
}

const ConversionReportContextProvider = ({
  children,
}: {
  children: React.ReactNode
}) => {
  const [state, dispatch] = useReducer(reducer, initialState())
  return <Provider value={{ state, dispatch }}>{children}</Provider>
}

export { ConversionReportContext, ConversionReportContextProvider, reducer }
