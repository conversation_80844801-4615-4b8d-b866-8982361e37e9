import React, { useEffect, useMemo, useRef, useState } from 'react'
import { Filter } from '@patterninc/react-ui'
import { createFilterCount } from 'src/common/services/FilterHelperService'

import { c, useTranslate } from '../../../../../common/services'
import { defaultFilter, type filterType } from './ConversionContext'

type ConversionReportFilterProps = {
  filterState: filterType
  resetCallout: () => void
  update: (filter: filterType) => void
  acceptedFilters: string[]
}

type objectType = {
  [key: string]: unknown
}

const ConversionReportFilter = ({
  filterState,
  resetCallout,
  update,
  acceptedFilters,
}: ConversionReportFilterProps): React.JSX.Element => {
  const isCancelled = useRef(false),
    { t } = useTranslate('content')
  const [filterStateCopy, setFilterStateCopy] = useState(filterState),
    [filtersCount, setFiltersCount] = useState(0)
  const defaultObj = useMemo(() => {
    const defaultObj: objectType = {}
    acceptedFilters.forEach((a) => {
      defaultObj[a] = (defaultFilter as unknown as objectType)[a]
    })

    // these if statements enable the filter count to show for the default selections for these filter types
    if (acceptedFilters.includes('productsSoldBy')) {
      defaultObj['productsSoldBy'] = {
        id: 1,
        state: c('soldByPattern'),
        value: 'sold_by_pattern',
      }
    }
    if (acceptedFilters.includes('status')) {
      defaultObj['status'] = {
        id: 1,
        state: c('active'),
        value: 'active',
      }
    }

    if (acceptedFilters.includes('productsDrivingOfRevenue')) {
      defaultObj['productsDrivingOfRevenue'] = ''
    }
    return defaultObj
  }, [acceptedFilters])

  const possibleFilters = useMemo(() => {
    const possibleFilters: React.ComponentProps<typeof Filter>['filterStates'] =
      {
        productsDrivingOfRevenue: {
          type: 'text',
          inputType: 'number',
          defaultValue: filterStateCopy.productsDrivingOfRevenue,
          stateName: 'productsDrivingOfRevenue',
          labelText: t('productsDrivingPercentageOfRevenue'),
          placeholder: '100',
          numberProps: {
            min: 1,
            max: 100,
            onlyWholeNumbers: true,
            onlyPositiveNumbers: true,
            suffix: '%',
          },
          labelTooltip: {
            tooltipContent: t('productsDrivingPercentageOfRevenueTooltip'),
          },
        },
        productsSoldBy: {
          type: 'select',
          defaultValue: filterStateCopy.productsSoldBy,
          options: [
            {
              id: 0,
              state: t('allSellers'),
              value: 'all_sellers',
            },
            {
              id: 1,
              state: t('soldByPattern'),
              value: 'sold_by_pattern',
            },
          ],
          stateName: 'productsSoldBy',
          optionKeyName: 'state',
          labelText: c('sellers'),
        },
        status: {
          type: 'select',
          defaultValue: filterStateCopy.status,
          options: [
            {
              id: 0,
              state: c('all'),
              value: 'all',
            },
            {
              id: 1,
              state: c('active'),
              value: 'active',
            },
            {
              id: 2,
              state: c('inactive'),
              value: 'inactive',
            },
          ],
          stateName: 'status',
          optionKeyName: 'state',
          labelText: c('status'),
        },
      }
    return possibleFilters
  }, [
    filterStateCopy.productsDrivingOfRevenue,
    filterStateCopy.productsSoldBy,
    filterStateCopy.status,
    t,
  ])

  const filter = useMemo((): React.ComponentProps<
    typeof Filter
  >['filterStates'] => {
    const filters: React.ComponentProps<typeof Filter>['filterStates'] = {}
    acceptedFilters.forEach(
      (key) =>
        ((filters as objectType)[key] = (possibleFilters as objectType)[key]),
    )
    return filters
  }, [acceptedFilters, possibleFilters])

  const updateSelect = (...params: unknown[]) => {
    const stateAttr = params[0] as string
    const value = params[1]
    !isCancelled.current &&
      setFilterStateCopy({
        ...filterStateCopy,
        [stateAttr]: value,
      })
  }

  const updateFilter = () => {
    update({ ...filterStateCopy })
  }

  const cancelCallout = () => {
    setFilterStateCopy(filterState)
  }

  useEffect(() => {
    if (!isCancelled.current) {
      setFilterStateCopy(filterState)
    }
  }, [filterState])

  useEffect(() => {
    const filters: objectType = {}
    acceptedFilters.forEach((key) => {
      filters[key] = (filterState as unknown as objectType)[key]
    })
    setFiltersCount(createFilterCount(filters, defaultObj))
  }, [filterState, defaultObj, acceptedFilters])

  useEffect(() => {
    isCancelled.current = false
    return () => {
      isCancelled.current = true
    }
  }, [])

  return (
    <Filter
      filterStates={filter}
      filterCallout={updateFilter}
      resetCallout={resetCallout}
      onChangeCallout={updateSelect}
      cancelCallout={cancelCallout}
      appliedFilters={filtersCount}
    />
  )
}

export default ConversionReportFilter
