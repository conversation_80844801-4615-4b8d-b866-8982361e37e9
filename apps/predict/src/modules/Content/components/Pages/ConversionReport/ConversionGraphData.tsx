import React, {
  useCallback,
  useContext,
  useEffect,
  useMemo,
  useState,
} from 'react'
import { useQuery } from '@tanstack/react-query'
import moment from 'moment'
import { hasValue, isEqual, usePrevious, useToggle } from '@patterninc/react-ui'
import { omit } from 'lodash'
import { useHeaderGraphMetrics } from '@predict-hooks'
import { getFilteredIds } from 'src/modules/Traffic/hooks/useCommonAPIParams'

import { type DynamicGraphDataType } from '../../../../../common/components/Charts/Graphs/GraphHelperService'
import { useComparisonDates } from '../../../../../common/hooks/CustomHooks'
import { c, SecureAxios, useTranslate } from '../../../../../common/services'
import { type DownloadOptionsType } from '../../../../../common/services/CsvHelpers'
import { noUTCOffset } from '../../../../../common/services/TimeService'
import { ThemeContext } from '../../../../../Context'
import { useUser } from '../../../../../context/user-context'
import useDataTableParams from '../../../../Insights/components/Common/reportParamsHook'
import {
  type GraphData,
  type TrafficGraphData,
  type TrafficGraphDataProps,
} from '../../../../Traffic/components/common/AdvertisingGraphData/trafficGraphData.models'
import TrafficHeaderData from '../../../../Traffic/components/common/AdvertisingGraphData/TrafficHeaderData'
import { trafficCacheTime } from '../../../../Traffic/services/TrafficHelperService'
import { ContentMetricPropsTooltipData } from '../../Common/ContentTooltipHelper'
import { ConversionReportContext, type filterType } from './ConversionContext'
import ConversionReportFilter from './ConversionFIlter'
import { conversionHeaderMetrics } from '../../Common/ContentGraphHeaderHelper'

type ConversionReportDataProps = {
  api: string
  csvTitle: string
  allowFilter?: boolean
  otherMetricCount?: number
  commonParams: Record<string, unknown>
  productTitle?: string
  vendor_id?: number | null
  allBrand?: boolean
  extraParams?: Record<string, unknown>
  isProductDetailPage?: boolean
  marketProductId?: string
  primaryTag?: string
}

export type GraphStateType = {
  graphData: GraphData
  unformattedGraphData: DynamicGraphDataType[]
  metaEndDate?: string
  loadingGraph?: boolean
  comparisonDates?: {
    startDate: string
    endDate: string
  }
  totalsData?: []
}

type BrandGroupCustomerType = {
  vendor_ids: number[]
} | null

const sparkLineColumns = [
  'total_sales',
  'change_in_total_sales',
  'pct_of_all_sales',
  'units',
  'in_stock',
  'buy_box',
  'pattern_page_views',
  'page_views',
  'conversion',
  'ad_spend',
  'ad_sales',
  'suuppression',
  'estimated_unit_share',
  'reviews',
  'ratings',
  'units_per_order',
  'avg_selling_price',
  'conversion_rate_order',
  'avg_order_value',
  'pattern_orders',
  'conversion_rate_order',
  'conversion_without_sns',
  'sns_orders',
]

interface KeyProps {
  comparison?: number
  value?: number
  type?: string
  display: string
  change?: number
  pct_change?: number
}

type TypeSummery = {
  report_date: KeyProps
  total_sales?: KeyProps
  page_views?: KeyProps
  pattern_page_views?: KeyProps
  conversion_rate_order?: KeyProps
  conversion?: KeyProps
  units?: KeyProps
  avg_order_value?: KeyProps
  avg_selling_price?: KeyProps
  units_per_order?: KeyProps
  buy_box?: KeyProps
  in_stock?: KeyProps
  ad_orders?: KeyProps
  ad_clicks?: KeyProps
  market_product_id?: KeyProps | number
}

type TypeCSV = {
  [key: string]: string | number
}

type TypeHistory = {
  report_date?: string | KeyProps
  id?: string | KeyProps
  total_sales?: KeyProps
  page_views?: KeyProps
  pattern_page_views?: KeyProps
  conversion_rate_order?: KeyProps
  conversion?: KeyProps
  units?: KeyProps
  avg_order_value?: KeyProps
  avg_selling_price?: KeyProps
  units_per_order?: KeyProps
  buy_box?: KeyProps
  in_stock?: KeyProps
  ad_orders?: KeyProps
  ad_clicks?: KeyProps
}

const ConversionGraphData = ({
  api,
  csvTitle,
  allowFilter = false,
  otherMetricCount = 2,
  productTitle,
  vendor_id,
  allBrand,
  isProductDetailPage,
  marketProductId,
  primaryTag,
}: ConversionReportDataProps): React.JSX.Element => {
  const hideConversionWithoutSnsMetricToggle = useToggle(
    'hide_conversion_without_sns_metric_toggle',
  )
  const addMoreMetricsToConversion = useToggle('add_more_metrics_to_conversion')

  const {
    startDate,
    endDate,
    marketplaceIds,
    customer,
    timeframe,
    secondRange_endDate,
    secondRange_startDate,
    brandGroupCustomer,
    tags,
    isVatAdjustmentTurnedOn,
    areMarketplacesFiltered,
    amazonMarketplaceIds,
    allMarketplaces,
    regions: selectedRegions,
    checkIfAllChinaMarketplaces,
  } = useContext(ThemeContext)

  const aggregate_by = timeframe.aggregation
  const currency = useUser().user.current_currency
  const isBrandGroupSelected = !!brandGroupCustomer
  const hasValidSecondRange = !!secondRange_endDate && !!secondRange_startDate
  const { state: ConversionReportState, dispatch: ConversionReportDispatch } =
    useContext(ConversionReportContext)
  const [csvHistoryData, setCsvHistoryData] = useState<TypeHistory[]>([]),
    { t } = useTranslate('content'),
    searchFor = useMemo(
      () => primaryTag || ConversionReportState?.primaryTag,
      [primaryTag, ConversionReportState?.primaryTag],
    )

  const updatedContentMetricPropsTooltipData = useMemo(() => {
    return ContentMetricPropsTooltipData.map((tooltip) => {
      if (tooltip.key === 'conversion' && checkIfAllChinaMarketplaces()) {
        return {
          ...tooltip,
          content: t('content:conversionRateTooltipChina'),
        }
      }

      return tooltip
    })
  }, [checkIfAllChinaMarketplaces, t])

  const brandGroupCustomerData = brandGroupCustomer as BrandGroupCustomerType
  const brandGroupCustomerVendorIds = useMemo(() => {
    return (
      brandGroupCustomerData?.vendor_ids?.filter(
        (brandVendor) => brandVendor,
      ) || []
    )
  }, [brandGroupCustomerData?.vendor_ids])

  const acceptedFilters = useMemo(() => {
    const filters: Array<string> = [
      'productsDrivingOfRevenue',
      'productsSoldBy',
      'status',
    ]
    return filters
  }, [])
  const additionalColumns = useMemo(
    () => [
      'unique_visitors',
      'add_to_cart',
      'buyer_conversion_rate',
      'avg_order_value_by_buyer',
    ],
    [],
  )
  const previousDates = useComparisonDates(),
    isCustomTimeframe = timeframe.value === 'custom'

  const [summaryData, setSummaryData] = useState<TrafficGraphData>({})

  const sparklineParamsFromHooks = useDataTableParams({
    aggregation:
      customer.id === 0 || isBrandGroupSelected ? 'brand' : 'product',
    selectedColumnNames: [
      ...sparkLineColumns,
      ...(addMoreMetricsToConversion && checkIfAllChinaMarketplaces()
        ? additionalColumns
        : []),
    ],
    sortBy: {
      flip: false,
      prop: 'change_in_total_sales_from_comparison_period',
    },
    sortByProp: 'change_in_total_sales_from_comparison_period',
    ...(isCustomTimeframe
      ? {
          comparison_start_date: secondRange_startDate
            ? secondRange_startDate
            : previousDates?.startDate,
          comparison_end_date: secondRange_endDate
            ? secondRange_endDate
            : previousDates?.endDate,
        }
      : {}),
  })

  const sparklineParams = useMemo(() => {
    const selectedTags = tags.selectedTags?.map((tag: { id: number }) => tag.id)
    return {
      per_page: 20,
      aggregate_by: timeframe.aggregation ?? 'hour',
      timeframe: timeframe.value === 'custom' ? 'custom' : timeframe.type,
      start_date: noUTCOffset(startDate ?? ''),
      end_date: noUTCOffset(endDate ?? ''),
      ...(timeframe.value === 'custom' && hasValidSecondRange
        ? {
            comparison_start_date: noUTCOffset(secondRange_startDate),
            comparison_end_date: noUTCOffset(secondRange_endDate),
          }
        : {}),
      tag_ids: selectedTags,
      marketplace_ids:
        getFilteredIds(
          true,
          false,
          marketplaceIds ?? [],
          selectedRegions,
          allMarketplaces,
        ) ?? [],
      match_all_tags: tags.matchAllTags,
      vat_adjusted: isVatAdjustmentTurnedOn,
      show_total: true,
      currency: currency?.id ?? 1,
      columns: [
        ...sparkLineColumns,
        ...(addMoreMetricsToConversion && checkIfAllChinaMarketplaces()
          ? additionalColumns
          : []),
      ],
      ...(marketProductId ? { market_product_id: marketProductId } : {}),
      sort: `change_in_total_sales_from_comparison_period:desc`,
      ...(areMarketplacesFiltered
        ? {
            marketplace_ids:
              getFilteredIds(
                true,
                false,
                marketplaceIds ?? [],
                selectedRegions,
                allMarketplaces,
              ) ?? [],
          }
        : {}),

      comparison_start_date: secondRange_startDate
        ? secondRange_startDate
        : previousDates?.startDate,
      comparison_end_date: secondRange_endDate
        ? secondRange_endDate
        : previousDates?.endDate,

      ...(vendor_id
        ? { customer_id: vendor_id }
        : {
            ...(isBrandGroupSelected
              ? {
                  vendor_ids: brandGroupCustomerVendorIds?.length
                    ? brandGroupCustomerVendorIds
                    : [],
                }
              : { customer_id: customer.id }),
          }),
    }
  }, [
    areMarketplacesFiltered,
    brandGroupCustomerVendorIds,
    currency?.id,
    customer.id,
    endDate,
    hasValidSecondRange,
    isBrandGroupSelected,
    isVatAdjustmentTurnedOn,
    marketProductId,
    marketplaceIds,
    previousDates?.endDate,
    previousDates?.startDate,
    secondRange_endDate,
    secondRange_startDate,
    startDate,
    tags.matchAllTags,
    tags.selectedTags,
    timeframe.aggregation,
    timeframe.type,
    timeframe.value,
    vendor_id,
    selectedRegions,
    allMarketplaces,
    addMoreMetricsToConversion,
    additionalColumns,
    checkIfAllChinaMarketplaces,
  ])

  const [updatedParams, setUpdatedParams] = useState({})
  const prevSparklineParams = usePrevious(sparklineParamsFromHooks)
  useEffect(() => {
    if (!isEqual(prevSparklineParams, sparklineParamsFromHooks)) {
      if (vendor_id) {
        const newSparklineParamsFromHooks = sparklineParamsFromHooks
        delete newSparklineParamsFromHooks.vendor_ids
        delete newSparklineParamsFromHooks.brand_group_name
        newSparklineParamsFromHooks.customer_id = vendor_id
        setUpdatedParams(newSparklineParamsFromHooks)
      } else {
        setUpdatedParams(sparklineParamsFromHooks)
      }
    }
  }, [prevSparklineParams, sparklineParamsFromHooks, vendor_id])
  // Fetch history data
  const { aggregate_by: aggregateBy } = sparklineParamsFromHooks
  const historyApi = isProductDetailPage
      ? `${api}/product_performance_sub_table`
      : `${api}/sparkline_charts`,
    { status: historyStatus, data: historyApiResponse } = useQuery({
      queryKey: [
        sparklineParams,
        updatedParams,
        customer,
        currency?.id,
        allBrand,
        vendor_id,
        isBrandGroupSelected,
        brandGroupCustomerVendorIds,
        secondRange_startDate,
        previousDates,
        secondRange_endDate,
        marketProductId,
        amazonMarketplaceIds,
        isCustomTimeframe,
      ],
      queryFn: ({ signal }) => {
        return SecureAxios.get(historyApi, {
          params: {
            customer_id:
              vendor_id || ConversionReportState?.brandId || customer.id,
            ...(aggregateBy === 'hour'
              ? omit(updatedParams, ['aggregate_by'])
              : updatedParams),
            ...(!isCustomTimeframe
              ? {
                  comparison_start_date: secondRange_startDate
                    ? secondRange_startDate
                    : previousDates?.startDate,
                  comparison_end_date: secondRange_endDate
                    ? secondRange_endDate
                    : previousDates?.endDate,
                }
              : {}),
            // When on the product detail page, aggregate data by 'product' to fetch product-specific metrics.
            // Otherwise, default to aggregating by 'customer' for broader customer-level metrics.
            table_aggregation: isProductDetailPage ? 'product' : 'customer',
            marketplace_ids:
              getFilteredIds(
                true,
                false,
                marketplaceIds ?? [],
                selectedRegions,
                allMarketplaces,
              ) ?? [],
            ...(isProductDetailPage
              ? { market_product_id: marketProductId }
              : {}),
            ...(vendor_id
              ? { customer_id: vendor_id }
              : {
                  ...(isBrandGroupSelected
                    ? {
                        vendor_ids: brandGroupCustomerVendorIds?.length
                          ? brandGroupCustomerVendorIds
                          : [],
                      }
                    : { customer_id: customer.id }),
                }),
          },
          signal,
        })
      },
      gcTime: trafficCacheTime,
    })

  const { status: summeryAPIStatus, data: summaryApiResponse } = useQuery({
    queryKey: [
      sparklineParams,
      updatedParams,
      currency?.id,
      allBrand,
      searchFor,
      secondRange_startDate,
      secondRange_endDate,
      previousDates,
      amazonMarketplaceIds,
    ],
    queryFn: ({ signal }) => {
      return SecureAxios.get(`${api}/product_performance`, {
        params: {
          ...updatedParams,
          marketplace_ids:
            getFilteredIds(
              true,
              false,
              marketplaceIds ?? [],
              selectedRegions,
              allMarketplaces,
            ) ?? [],
          table_aggregation: 'product',
          search_term: searchFor,
          comparison_start_date: secondRange_startDate
            ? secondRange_startDate
            : previousDates?.startDate,
          comparison_end_date: secondRange_endDate
            ? secondRange_endDate
            : previousDates?.endDate,
          customer_id:
            vendor_id || ConversionReportState?.brandId || customer.id,
        },
        signal,
      })
    },
    gcTime: trafficCacheTime,
    enabled: hasValue(searchFor),
  })

  const csvName = `${
    productTitle ? `${csvTitle} - ${productTitle}` : `${csvTitle}`
  }`

  const convertData = useCallback(
    (data: KeyProps) => {
      const value =
        data.type === 'currency'
          ? `${currency?.symbol}${(
              Math.round(Number(data.value) * 100) / 100
            ).toFixed(2)}`
          : data.type === 'percentage'
            ? `${(Number(data.value) * 100).toFixed(2)}%`
            : data.type === 'integer'
              ? `${Number(data.value).toFixed(0)}`
              : Number(data.value).toFixed(2)

      return value
    },
    [currency?.symbol],
  )

  const createCsvData = () => {
    const data: TypeCSV[] = []
    csvHistoryData?.forEach((i) => {
      const resp: TypeCSV = {}
      Object.entries(i).forEach(([k, value]) => {
        if (k === 'report_date') {
          resp[c('reportDate')] = value.toString()
        } else {
          const key = typeof value === 'string' ? '' : value?.display
          resp[key] = typeof value === 'string' ? value : convertData(value)
        }
      })
      data.push(resp)
    })
    return data
  }

  const csvDownloadOptions: DownloadOptionsType = [
    {
      linkName: csvName,
      csvName: csvName,
      csvData: createCsvData(),
      callout: (element?: string | HTMLDivElement) =>
        element && typeof element !== 'string' && element.click(),
    },
  ]

  const updateFilters = useCallback(
    (filter: filterType) => {
      ConversionReportDispatch({
        type: 'UPDATE_FILTER',
        payload: {
          filters: filter,
        },
      })
    },
    [ConversionReportDispatch],
  )

  const resetFilter = useCallback(() => {
    ConversionReportDispatch({ type: 'RESET_FILTER' })
  }, [ConversionReportDispatch])

  const defaultTopMetrics = useMemo(
      () => [
        'conversion',
        checkIfAllChinaMarketplaces() ? 'page_views' : 'pattern_page_views',
      ],
      [checkIfAllChinaMarketplaces],
    ),
    defaultCustomMetrics = useMemo(
      () => [
        'total_sales',
        'conversion_rate_order',
        'avg_selling_price',
        'units_per_order',
      ],
      [],
    ),
    customMetricOptions = useMemo(
      () => [
        'total_sales',
        'conversion_rate_order',
        ...(!(addMoreMetricsToConversion && checkIfAllChinaMarketplaces())
          ? ['page_views']
          : []),
        'avg_selling_price',
        'avg_order_value',
        'units_per_order',
        'units',
        'buy_box',
        'in_stock',
        ...(hideConversionWithoutSnsMetricToggle
          ? ['conversion_without_sns']
          : []),
        ...(addMoreMetricsToConversion && checkIfAllChinaMarketplaces()
          ? additionalColumns
          : []),
      ],
      [
        hideConversionWithoutSnsMetricToggle,
        addMoreMetricsToConversion,
        additionalColumns,
        checkIfAllChinaMarketplaces,
      ],
    )
  const [graphState, setGraphState] = useState<GraphStateType>({
    graphData: { dataSet: [], max: 0 },
    unformattedGraphData: [],
    metaEndDate: '',
    comparisonDates: {
      startDate: '',
      endDate: '',
    },
  })
  const { unformattedGraphData, comparisonDates, metaEndDate } = graphState

  const {
    customMetricCallout,
    showEventMarker,
    showSingleYAxis,
    selectedGraphMetrics,
    userSelectedMetrics,
    updateGraphMetrics,
    graphData,
  } = useHeaderGraphMetrics({
    defaultTopMetrics,
    defaultCustomMetrics: customMetricOptions,
    unformattedGraphData,
    summaryData,
    userSettingsKey: 'content:conversionReport',
  })

  const List = useMemo(
    () =>
      conversionHeaderMetrics({
        chineseMarketplacesMetrics:
          addMoreMetricsToConversion && checkIfAllChinaMarketplaces(),
      }),
    [addMoreMetricsToConversion, checkIfAllChinaMarketplaces],
  )

  useEffect(() => {
    if (isProductDetailPage) {
      if (historyApiResponse) {
        if (summeryAPIStatus === 'success' && summaryApiResponse) {
          const apidata = summaryApiResponse?.data?.data?.find(
            (i: TypeSummery) => i.market_product_id === Number(marketProductId),
          )
          const summaryApiResponse1: TrafficGraphData = {}
          Object.entries(List).forEach(([key, value]) => {
            summaryApiResponse1[key] = {
              value: apidata?.[`${key}_${value?.value}`],
              comparison: apidata?.[`${key}_${value?.comparison}`],
              change: apidata?.[`change_in_${key}_${value?.change}`],
              pct_change: apidata?.[`${key}_${value?.pct_change}`],
              type: value?.type,
              display: value?.display,
              pct_of_total: 0,
            }
          })
          setSummaryData(summaryApiResponse1)
        }
        const historyData: DynamicGraphDataType[] = (
          historyApiResponse?.data || []
        ).map((conversion: { [key: string]: number }) => {
          const dateParse = conversion.id.toString()?.split(' - ')
          const date =
            aggregate_by === 'hour' || aggregate_by === 'day'
              ? conversion.id.toString()
              : dateParse[0]
          const updatedHistoryResponse: DynamicGraphDataType =
            Object.fromEntries(
              Object.entries(List).map(([k, value]) => {
                const key = k as keyof DynamicGraphDataType
                return [
                  key,
                  {
                    value: conversion?.[`${key}_${value?.value}`],
                    type: value?.type,
                    display: value?.display,
                  },
                ]
              }),
            )
          return {
            report_date: moment(date).format('YYYY-MM-DD'),
            ...updatedHistoryResponse,
          }
        })
        setGraphState((prevState) => ({
          ...prevState,
          unformattedGraphData: historyData,
        }))
      }
    } else {
      if (historyApiResponse) {
        const updatedSummaryResponse: TrafficGraphData = {}
        Object.entries(List).forEach(([key, value]) => {
          updatedSummaryResponse[key] = {
            value: historyApiResponse?.data?.[`${key}_${value?.value}`],
            comparison:
              historyApiResponse?.data?.[`${key}_${value?.comparison}`],
            change:
              historyApiResponse?.data?.[`change_in_${key}_${value?.change}`],
            pct_change:
              historyApiResponse?.data?.[`${key}_${value?.pct_change}`],
            type: value?.type,
            display: value?.display,
            pct_of_total: 0,
          }
        })
        setSummaryData(updatedSummaryResponse)

        const historyData: DynamicGraphDataType[] = (
          historyApiResponse?.data?.conversion_chart_data || []
        ).map((conversion: { date: string; value: number }) => {
          const updatedHistoryResponse: DynamicGraphDataType =
            Object.fromEntries(
              Object.entries(List).map(([k, value]) => {
                const key = k as keyof DynamicGraphDataType
                const objValue = historyApiResponse?.data?.[
                  `${key}_chart_data`
                ]?.find(
                  (sales: { date: string; value: number }) =>
                    sales.date === conversion.date,
                )
                return [
                  key,
                  {
                    value: objValue?.value,
                    type: value?.type,
                    display: value?.display,
                  },
                ]
              }),
            )

          return {
            report_date: conversion.date,
            ...updatedHistoryResponse,
          }
        })

        setCsvHistoryData(historyData)
        setGraphState((prevState) => ({
          ...prevState,
          unformattedGraphData: historyData,
        }))
      }
    }
  }, [
    endDate,
    previousDates,
    secondRange_endDate,
    secondRange_startDate,
    historyApiResponse,
    List,
    isProductDetailPage,
    summeryAPIStatus,
    summaryApiResponse,
    marketProductId,
    aggregate_by,
    timeframe,
    startDate,
  ])

  const tooltifInfo = {
    title: t('whatIsConversionReport'),
    text: t('conversionReportTooltip'),
    position: 'bottom',
    maxWidth: '500px',
  } as TrafficGraphDataProps['tooltipContent']

  return (
    <TrafficHeaderData
      tooltipContent={tooltifInfo}
      downloads={csvDownloadOptions}
      filter={
        allowFilter && (
          <ConversionReportFilter
            filterState={ConversionReportState}
            update={updateFilters}
            resetCallout={resetFilter}
            acceptedFilters={acceptedFilters}
          />
        )
      }
      loading={
        isProductDetailPage
          ? historyStatus === 'pending' || Object.keys(summaryData).length === 0
          : historyStatus === 'pending'
      }
      graphData={graphData}
      data={summaryData}
      metaEndDate={metaEndDate}
      defaultCustomMetrics={defaultCustomMetrics}
      defaultTopMetrics={defaultTopMetrics}
      customMetricOptions={customMetricOptions}
      selectedMetrics={userSelectedMetrics}
      updateMetricsCallout={customMetricCallout}
      selectedGraphMetrics={selectedGraphMetrics}
      updateGraphMetricsCallout={updateGraphMetrics}
      showEventMarker={showEventMarker}
      comparisonDates={comparisonDates}
      otherMetricCount={otherMetricCount}
      isSpecialCornerCase={true}
      metricProps={
        updatedContentMetricPropsTooltipData as typeof ContentMetricPropsTooltipData
      }
      showSingleYAxis={showSingleYAxis}
      productName={productTitle}
    />
  )
}

export default ConversionGraphData
