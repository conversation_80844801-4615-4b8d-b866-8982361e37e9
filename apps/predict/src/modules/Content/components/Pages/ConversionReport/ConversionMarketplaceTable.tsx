import {
  getApiUrlPrefix,
  type SortColumnProps,
  standardSortParams,
  StandardTable,
  useIsMounted,
  useToggle,
} from '@patterninc/react-ui'
import {
  c,
  type DownloadOptionsType,
  getUserSettingEndpoint,
  saveCustomization,
  SecureAxios,
  tryLocalStorageParse,
  useTranslate,
} from '@predict-services'
import { ThemeContext } from 'src/Context'
import { useInfiniteQuery, useQuery } from '@tanstack/react-query'
import { useCallback, useContext, useEffect, useMemo, useState } from 'react'
import useDataTableParams from 'src/modules/Insights/components/Common/reportParamsHook'
import { trafficCacheTime } from 'src/modules/Traffic/services/TrafficHelperService'
import { convertTableHeaderCount } from 'src/modules/Traffic/services/TrafficTableHeaderHelperService'
import type { AggregationType } from 'src/modules/Insights/components/Pages/InsightsReport/helpers'
import { getSelectedConfig } from 'src/common/services/TableConfigService'
import { useUser } from 'src/context/user-context'

import {
  additionalMetricsHeaders,
  commonContentHeaders,
} from '../../Common/ContentTableHeaderHelperService'
type TableProps = {
  tableName: 'marketplace' | 'store'
  commonParams: Record<string, unknown>
  defaultColumns: string[]
  productColumns: string[]
  filteredMarketplaceIds: number[]
}
const ConversionMarketplaceTable = ({
  tableName,
  commonParams,
  defaultColumns,
  productColumns,
  filteredMarketplaceIds,
}: TableProps) => {
  const currency = useUser().user.current_currency
  const addMoreMetricsToConversion = useToggle('add_more_metrics_to_conversion')
  const { t } = useTranslate('content')
  const [searchTerm, setSearchTerm] = useState('')
  const [selectionList, setSelectionList] = useState(defaultColumns)
  const [sortBy, setSortBy] = useState(
    tryLocalStorageParse(`sort_by_conversion_${tableName}`) ?? {
      prop: 'conversion_current_period',
      flip: false,
      isByChange: false,
    },
  )
  const additionalColumns = useMemo(
    () => [
      'unique_visitors',
      'add_to_cart',
      'buyer_conversion_rate',
      'avg_order_value_by_buyer',
    ],
    [],
  )
  const isMounted = useIsMounted()
  const { timeframe, checkIfAllChinaMarketplaces } = useContext(ThemeContext)
  const aggregateBy = timeframe.aggregation
  const sort: SortColumnProps['sorter'] = (sortObj) => {
    setSortBy({
      prop: `${sortObj.activeColumn}`,
      flip: sortObj.direction,
      lowerCaseParam: sortObj.lowerCaseParam,
    })
  }

  const { endpoint: tableUrl, decoded } = getUserSettingEndpoint(
    `content:conversion:${tableName}:columns`,
  )
  const productParamsFromHooks = useDataTableParams({
    aggregation: tableName as AggregationType,
    selectedColumnNames: [
      ...productColumns,
      ...(addMoreMetricsToConversion && checkIfAllChinaMarketplaces()
        ? additionalColumns
        : []),
    ],
    searchTerm: searchTerm,
    sortBy: {
      flip: false,
      prop: 'conversion_current_period',
    },
    sortByProp: 'conversion_current_period',
  })
  const stickyTableConfig = useMemo(
    () => ({
      left: 1,
    }),
    [],
  )
  const originalConfig = useMemo(
    () => [
      tableName === 'marketplace'
        ? commonContentHeaders.marketplaces
        : commonContentHeaders.stores,
      {
        ...commonContentHeaders.conversion_rate_page_views,
        ...(checkIfAllChinaMarketplaces()
          ? {
              tooltip: {
                content: (
                  <div style={{ maxWidth: 400 }}>
                    {t('content:conversionRateTooltipChina')}
                  </div>
                ),
              },
            }
          : {}),
      },
      commonContentHeaders.total_sales,
      commonContentHeaders.pattern_orders,
      ...(!checkIfAllChinaMarketplaces()
        ? [commonContentHeaders.pattern_page_views]
        : []),
      commonContentHeaders.revenue_per_unit,
      commonContentHeaders.avg_selling_price,
      commonContentHeaders.units_per_order,
      commonContentHeaders.total_page_views,
      commonContentHeaders.buybox,
      commonContentHeaders.weighted_in_stock_pct,
      ...(addMoreMetricsToConversion && checkIfAllChinaMarketplaces()
        ? [
            additionalMetricsHeaders({
              metricName: 'unique_visitors',
              columnLabel: t('uniqueVisitors'),
              tooltipContent: t('uniqueVisitorsTooltip'),
            }),
            additionalMetricsHeaders({
              metricName: 'add_to_cart',
              columnLabel: t('addToCart'),
              tooltipContent: t('addToCartTooltip'),
            }),
            additionalMetricsHeaders({
              metricName: 'buyer_conversion_rate',
              columnLabel: t('buyerConversionRate'),
              tooltipContent: t('buyerConversionRateTooltip'),
              isPercentage: true,
              decimalScale: 2,
            }),
            additionalMetricsHeaders({
              metricName: 'avg_order_value_by_buyer',
              columnLabel: t('avgOrderValueByBuyer'),
              tooltipContent: t('avgOrderValueByBuyerTooltip'),
              currency: {
                id: currency?.id,
                name: currency?.name,
                type: currency?.type,
                label: currency?.label,
                code: currency?.code,
                symbol: currency?.symbol,
              },
              decimalScale: 2,
            }),
          ]
        : []),
    ],
    [
      tableName,
      checkIfAllChinaMarketplaces,
      t,
      addMoreMetricsToConversion,
      currency?.id,
      currency?.name,
      currency?.type,
      currency?.label,
      currency?.code,
      currency?.symbol,
    ],
  )
  const { status: columnsStatus, data: customColumnApiResponse } = useQuery({
      queryKey: [decoded, tableUrl],
      queryFn: ({ signal }) => SecureAxios.get(tableUrl, { signal }),
      gcTime: trafficCacheTime,
    }),
    customColumnApiData =
      columnsStatus === 'success' &&
      customColumnApiResponse?.data?.selectedColumns

  useEffect(() => {
    if (isMounted() && customColumnApiData) {
      setSelectionList(customColumnApiData)
    }
  }, [customColumnApiData, isMounted])
  const customSelectionCallout = useCallback(
    (selectedList: string[], setToDefault?: boolean) => {
      isMounted() && setSelectionList(selectedList)
      isMounted() &&
        saveCustomization({
          api: tableUrl,
          selected: selectedList,
          setToDefault: setToDefault,
          type: 'table',
        })
    },
    [tableUrl, isMounted],
  )
  const config = useMemo(() => {
    return [
      originalConfig[0],
      ...getSelectedConfig(originalConfig, selectionList, 'selected'),
    ]
  }, [originalConfig, selectionList])
  const exportCSVData = useCallback((params: unknown = {}) => {
    return SecureAxios.get(
      `${getApiUrlPrefix(
        'business-reports',
      )}/api/v3/reports/conversion_reports/products`,
      {
        params,
      },
    ).then((response) => response.data)
  }, [])

  const totalList = originalConfig.slice(1).map((ele) => ele.label)

  const api = `${getApiUrlPrefix('iserve')}/api/v7/sales`
  const tableApi = `${api}/product_performance`
  const tableParams = useMemo(() => {
    return {
      ...commonParams,
      ...productParamsFromHooks,
      sort: standardSortParams(sortBy, ['marketplace_name', 'store_name']),
      ...(aggregateBy === 'hour' ? { aggregate_by: undefined } : {}),
      marketplace_ids: filteredMarketplaceIds,
    }
  }, [
    aggregateBy,
    commonParams,
    filteredMarketplaceIds,
    productParamsFromHooks,
    sortBy,
  ])
  const {
    status: tableStatus,
    data: tableApiResponse,
    fetchNextPage,
    hasNextPage,
    isLoading: loadingTable,
  } = useInfiniteQuery({
    queryKey: [tableApi, tableParams],
    queryFn: ({ pageParam = 1, signal }) => {
      const params = {
        page: pageParam,
        ...tableParams,
      }

      return SecureAxios.get(tableApi, { params, signal })
    },
    initialPageParam: 1,
    gcTime: 1000 * 60 * 60 * 8,
    getNextPageParam: (previousResponse) => {
      return previousResponse?.data?.pagination?.last_page
        ? undefined
        : previousResponse?.data?.pagination?.next_page
    },
  })

  const count = tableApiResponse?.pages[0]?.data?.pagination?.count
  const csvName = `${tableName === 'marketplace' ? c('marketPlace') : c('store')} ${c('report')}`
  const tableData = useMemo(() => {
    if (tableStatus === 'success' && tableApiResponse?.pages) {
      const detailData = tableApiResponse.pages.flatMap((page) => {
        return page?.data?.data
      })
      return [...detailData]
    }
    return []
  }, [tableApiResponse?.pages, tableStatus])

  const newDownloadParams = useMemo(() => {
    return {
      ...commonParams,
      csv_name: csvName,
      format: 'csv',
      download: true,
      ...productParamsFromHooks,
      table_aggregation: tableName,
      ...(sortBy.prop === 'conversion_current_period'
        ? {
            sort: undefined,
          }
        : {
            sort: standardSortParams(sortBy, [
              'marketplace_name',
              'store_name',
            ]),
          }),
      ...(aggregateBy === 'hour' ? { aggregate_by: undefined } : {}),
      marketplace_ids: filteredMarketplaceIds,
    }
  }, [
    commonParams,
    productParamsFromHooks,
    sortBy,
    tableName,
    aggregateBy,
    filteredMarketplaceIds,
    csvName,
  ])

  const csvDownloadOptions: DownloadOptionsType = useMemo(
    () => [
      {
        linkName: csvName,
        csvName: csvName,
        csvFormat: {
          api: exportCSVData,
          params: newDownloadParams,
          callout: (element?: string | HTMLDivElement) =>
            element && typeof element !== 'string' && element.click(),
        },
      },
    ],
    [exportCSVData, newDownloadParams, csvName],
  )
  const productTableProps = useMemo(() => {
    return {
      data: tableData,
      config,
      dataKey: tableName === 'marketplace' ? 'marketplace_id' : 'store_id',
      hasData: tableData?.length > 0,
      loading: loadingTable,
      hasMore: !!(tableStatus === 'success' && hasNextPage),
      tableId: `conversion_${tableName}`,
      twoLineLabel: true,
      sort,
      sortBy,
      noDataFields: {
        primaryText:
          tableName === 'marketplace'
            ? t('noMarketplaceFound')
            : t('noStoreFound'),
        secondaryText:
          tableName === 'marketplace'
            ? t('couldNotFindAnyMarketplace')
            : t('couldNotFindAnyStore'),
      },
      getData: fetchNextPage,
      successStatus: tableStatus === 'success',
      stickyTableConfig,
      tableHeaderProps: {
        header: {
          name: t(tableName, { count: count ?? 0 }),
          value: convertTableHeaderCount(count),
        },
        search: {
          value: searchTerm,
          onChange: (value: string) => setSearchTerm(value),
          placeholder: t('searchPlaceholder', {
            key: tableName === 'marketplace' ? c('marketplaces') : c('stores'),
          }),
          debounce: 250,
        },
        download: {
          csvDownloadOptions: csvDownloadOptions,
          initialDisplay: true,
          show: true,
        },
        customColumnProps: {
          list: totalList,
          selected: selectionList,
          callout: customSelectionCallout,
          setToDefaultCallout: () =>
            customSelectionCallout(defaultColumns, true),
          isColumnsReorderable: true,
        },
      },
    }
  }, [
    config,
    count,
    csvDownloadOptions,
    customSelectionCallout,
    defaultColumns,
    fetchNextPage,
    hasNextPage,
    loadingTable,
    searchTerm,
    selectionList,
    sortBy,
    stickyTableConfig,
    t,
    tableData,
    tableStatus,
    totalList,
    tableName,
  ])
  return <StandardTable {...productTableProps} />
}

export default ConversionMarketplaceTable
