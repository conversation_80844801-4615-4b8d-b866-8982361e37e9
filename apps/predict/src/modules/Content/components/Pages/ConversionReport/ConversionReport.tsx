import React, {
  useCallback,
  useContext,
  useEffect,
  useMemo,
  useState,
} from 'react'
import { Link, useLocation, useNavigate } from 'react-router-dom'
import { useInfiniteQuery, useQuery } from '@tanstack/react-query'
import {
  Button,
  EmptyState,
  getApiUrlPrefix,
  type SortColumnProps,
  standardSortParams,
  StandardTable,
  Tabs,
  useIsMounted,
  useToggle,
} from '@patterninc/react-ui'
import { useDecodedUserTokenId } from '@predict-hooks'
import {
  c,
  parseParams,
  productDetailRoute,
  SecureAxios,
  tryLocalStorageParse,
  useTranslate,
} from '@predict-services'
import { omit } from 'lodash'
import CategoriesAndTags from 'src/common/components/ProductCategoriesAndTags/CategoriesAndTags'
import { initialCtDrawerState } from 'src/common/components/ProductCategoriesAndTags/helpers'
import { getFilteredIds } from 'src/modules/Traffic/hooks/useCommonAPIParams'

import { type IndividualBrandGroupData } from '../../../../../common/components/BrandGroup/BrandGroupTypes'
import {
  type ActiveFilterAPIProps,
  CustomFilterHelper,
} from '../../../../../common/components/Tables/CustomFilterHelper'
import { useComparisonDates } from '../../../../../common/hooks/CustomHooks'
import { type DownloadOptionsType } from '../../../../../common/services/CsvHelpers'
import { saveCustomization } from '../../../../../common/services/CustomDisplayService'
import { getSelectedConfig } from '../../../../../common/services/TableConfigService'
import { noUTCOffset } from '../../../../../common/services/TimeService'
import { getUserSettingEndpoint } from '../../../../../common/services/UserSettingsService'
import { ThemeContext } from '../../../../../Context'
import { useUser } from '../../../../../context/user-context'
import useDataTableParams from '../../../../Insights/components/Common/reportParamsHook'
import { trafficCacheTime } from '../../../../Traffic/services/TrafficHelperService'
import {
  commonTrafficHeaders,
  convertTableHeaderCount,
} from '../../../../Traffic/services/TrafficTableHeaderHelperService'
import {
  additionalMetricsHeaders,
  commonContentHeaders,
  type StandardTrafficObject,
} from '../../Common/ContentTableHeaderHelperService'
import { ConversionReportContext } from './ConversionContext'
import ConversionGraphData from './ConversionGraphData'
import { useLanguageParams } from '../../Common/ContentHooks'
import ConversionMarketplaceTable from './ConversionMarketplaceTable'

interface KeyProps {
  comparison: number
  value: number
  type: string
  display: string
  change: number
  pct_change: number
}

export type keyProductRow = {
  master_product_id: number
  product_image_url: string
  asin: string
  master_product_name: string
  market_product_id: string
  product_url: string
  marketplace: string
  clicks: KeyProps
  pattern_page_views_as_pct_of_total_page_views: KeyProps
  total_page_views: KeyProps
  pattern_page_views: KeyProps
  total_sales: KeyProps
  attributed_sales: KeyProps
  attributed_sales_as_pct_of_total_sales: KeyProps
  total_units_ordered: KeyProps
  attributed_units_ordered: KeyProps
  units_from_ads_as_pct_of_pattern_page_views: KeyProps
  total_orders: KeyProps
  attributed_conversions: KeyProps
  order_from_ads_as_pct_of_total_orders: KeyProps
  total_conversion_rate: KeyProps
  conversion_rate: KeyProps
  avg_selling_price: KeyProps
  attributed_sales_per_order: KeyProps
  page_views_as_pct_of_ad_sales: KeyProps
  marketplace_names: string
  recommendation: string
  sortProp: string
  marketplace_id: string
  primary_key: string
  orders: KeyProps
  ad_orders: KeyProps
  product_title: string
  vendor_id?: number | null
  customer_id: number
  customer_name: string
}

type BrandGroupCustomerType = {
  vendor_ids: number[]
} | null

const ConversionReport = (): React.JSX.Element => {
  const addMoreMetricsToConversion = useToggle('add_more_metrics_to_conversion')
  const {
      allMarketplaces,
      updateBreadcrumbs,
      startDate,
      endDate,
      marketplaceIds,
      tags,
      customer,
      timeframe,
      secondRange_endDate,
      secondRange_startDate,
      brandGroupCustomer,
      isVatAdjustmentTurnedOn,
      selectedComparisonPeriod,
      regions: selectedRegions,
      checkIfAllChinaMarketplaces,
    } = useContext(ThemeContext),
    { state: conversionState } = useContext(ConversionReportContext),
    { t } = useTranslate('content')
  const productColumns = useMemo(
    () => [
      'units_per_order',
      'total_sales',
      'avg_selling_price',
      'conversion_current_period',
      'pct_of_all_sales',
      'units',
      'in_stock',
      'buy_box',
      ...(checkIfAllChinaMarketplaces()
        ? ['page_views_current_period']
        : ['pattern_page_views']),
      'page_views',
      'conversion',
      'ad_spend',
      'ad_sales',
      'suppression',
      'estimated_unit_share',
      'reviews',
      'ratings',
      'avg_order_value',
      'orders',
      'conversion_rate_order',
    ],
    [checkIfAllChinaMarketplaces],
  )
  const navigate = useNavigate()
  const [ctDrawerState, setCtDrawerState] = useState(initialCtDrawerState)

  const brandGroupContextData =
    brandGroupCustomer as IndividualBrandGroupData | null

  const aggregate_by = timeframe.aggregation
  const currency = useUser().user.current_currency

  const stickyTableConfig = useMemo(
    () => ({
      right: 1,
      left: 1,
    }),
    [],
  )

  const additionalColumns = useMemo(
    () => [
      'unique_visitors',
      'add_to_cart',
      'buyer_conversion_rate',
      'avg_order_value_by_buyer',
    ],
    [],
  )
  const [searchTerm, setSearchTerm] = useState<string>('')
  const isBrandGroupSelected = !!brandGroupCustomer
  const { pathname } = useLocation()

  const isProspectBrand = customer?.id !== 0 && !customer?.vendor_id
  const isAllBrand = customer?.id === 0 || isBrandGroupSelected
  const { state: ConversionReportState } = useContext(ConversionReportContext)

  const brandGroupCustomerData = brandGroupCustomer as BrandGroupCustomerType
  const brandGroupCustomerVendorIds = useMemo(() => {
    return (
      brandGroupCustomerData?.vendor_ids?.filter(
        (brandVendor) => brandVendor,
      ) || []
    )
  }, [brandGroupCustomerData?.vendor_ids])

  const { search: searchLocation } = useLocation(),
    searchParams = parseParams(searchLocation),
    vendor_id = Number(searchParams['vendor_id'])

  const api = `${getApiUrlPrefix('iserve')}/api/v7/sales`
  const isSingleBrandView = Boolean(vendor_id || !isAllBrand)
  const extraParams = useMemo(() => {
    return {
      ...(conversionState?.productsDrivingOfRevenue
        ? {
            products_driving_of_revenue:
              conversionState?.productsDrivingOfRevenue,
          }
        : {}),
      ...(conversionState?.status?.id !== 0
        ? { active: conversionState?.status.value === 'active' }
        : {}),
      ...(conversionState?.productsSoldBy?.id !== 0
        ? { sold_by_pattern: true }
        : {}),
    }
  }, [
    conversionState?.productsDrivingOfRevenue,
    conversionState?.productsSoldBy?.id,
    conversionState?.status?.id,
    conversionState?.status.value,
  ])
  const languageParams = useLanguageParams()
  const previousDates = useComparisonDates()
  const commonParams = useMemo(() => {
    return {
      start_date: startDate,
      end_date: endDate,
      ...(aggregate_by ? { aggregate_by } : {}),

      ...(vendor_id
        ? { customer_id: vendor_id }
        : {
            ...(isAllBrand
              ? { customer_id: brandGroupContextData?.vendor_ids }
              : {
                  customer_id: customer.vendor_id,
                }),
          }),

      marketplace_ids:
        getFilteredIds(
          true,
          false,
          marketplaceIds ?? [],
          selectedRegions,
          allMarketplaces,
        ) ?? [],
      comparison_start: secondRange_startDate
        ? secondRange_startDate
        : previousDates?.startDate,
      comparison_end: secondRange_endDate
        ? secondRange_endDate
        : previousDates?.endDate,
      ...extraParams,
      ...(tags?.tag_ids?.length > 0
        ? {
            tag_ids: tags.tag_ids,
            match_all_tags: tags.matchAllTags,
            expires_in: 300,
          }
        : {}),
      ...(aggregate_by ? { aggregate_by } : {}),
      currency_code: currency?.code,
      ...(isVatAdjustmentTurnedOn
        ? { vat_adjusted: isVatAdjustmentTurnedOn }
        : {}),
      ...languageParams,
    }
  }, [
    startDate,
    endDate,
    aggregate_by,
    isAllBrand,
    brandGroupContextData?.vendor_ids,
    vendor_id,
    customer.vendor_id,
    marketplaceIds,
    secondRange_startDate,
    previousDates?.startDate,
    previousDates?.endDate,
    secondRange_endDate,
    extraParams,
    tags.tag_ids,
    tags.matchAllTags,
    currency?.code,
    isVatAdjustmentTurnedOn,
    languageParams,
    selectedRegions,
    allMarketplaces,
  ])

  const [sortBy, setSortBy] = useState(
    tryLocalStorageParse(`sort_by_conversion_report`) ?? {
      prop: 'conversion_current_period',
      flip: false,
      isByChange: false,
    },
  )

  const sort: SortColumnProps['sorter'] = (sortObj) => {
    setSortBy({
      prop: `${sortObj.activeColumn}`,
      flip: sortObj.direction,
      lowerCaseParam: sortObj.lowerCaseParam,
    })
  }

  const selectedBrandName = searchParams?.selected_brand

  useEffect(() => {
    updateBreadcrumbs({
      name: vendor_id ? selectedBrandName : c('conversion'),
      link: `${pathname}${searchLocation}`,
      ...(!vendor_id ? { changeType: 'rootLevel' } : {}),
    })
    setSearchTerm('')
  }, [
    pathname,
    updateBreadcrumbs,
    searchLocation,
    customer,
    selectedBrandName,
    vendor_id,
    isBrandGroupSelected,
    brandGroupCustomer,
  ])
  const hideConversionWithoutSnsMetricToggle = useToggle(
    'hide_conversion_without_sns_metric_toggle',
  )
  const defaultColumns = useMemo(
    () => [
      c('conversionRate'),
      c('sales'),
      checkIfAllChinaMarketplaces() ? c('pageViews') : c('patternPageViews'),
      c('orders'),
      c('unitsPerOrder'),
      c('averageSellingPrice'),
      c('category'),
      c('tags'),
    ],
    [checkIfAllChinaMarketplaces],
  )

  const isMounted = useIsMounted()

  const [state, setState] = useState({
      selectionList: defaultColumns,
    }),
    { selectionList } = state
  const isConversionWithoutSNSSelected =
    hideConversionWithoutSnsMetricToggle &&
    selectionList.includes(t('conversionRateWithoutSNS'))

  const userId = useDecodedUserTokenId(),
    apiUrl = `${getApiUrlPrefix('user-settings')}/v1`,
    columnsUrl = apiUrl + `/${userId}/predict/content:conversion:columns`

  const [secondaryFiltersAPI, setSecondaryFiltersAPI] =
    useState<ActiveFilterAPIProps>({})

  const [responseType, setResponseType] = useState('get')

  const customFilterTableAPIendpoint = `content:conversionReport`
  const { endpoint: customFilterUserSettingUrl } = getUserSettingEndpoint(
      customFilterTableAPIendpoint,
    ),
    { status: customFilterStatus, data: customGetFilterApiResponse } = useQuery(
      {
        queryKey: [customFilterUserSettingUrl, vendor_id],
        queryFn: ({ signal }) =>
          SecureAxios.get(customFilterUserSettingUrl, { signal }),
        gcTime: trafficCacheTime,
      },
    )
  const customFilterAPIData =
    customFilterStatus === 'success' && customGetFilterApiResponse?.data

  useEffect(() => {
    if (customFilterAPIData) {
      const customFilters = { ...customFilterAPIData }
      Object.keys(customFilterAPIData).forEach((filter) => {
        if (!(selectionList as string[]).includes(filter)) {
          delete customFilters?.[filter]
        }
      })
      isMounted() && setSecondaryFiltersAPI(customFilters)
    }
  }, [
    customFilterAPIData,
    isMounted,
    selectionList,
    setResponseType,
    setSecondaryFiltersAPI,
  ])

  useEffect(() => {
    if (responseType === 'post') {
      CustomFilterHelper({
        api: customFilterUserSettingUrl,
        data: secondaryFiltersAPI,
        type: 'custom-filter',
      })
    }
  }, [customFilterUserSettingUrl, responseType, secondaryFiltersAPI])

  const customSelectionCallout = useCallback(
    (selectedList: string[], setToDefault?: boolean) => {
      isMounted() &&
        setState((prevState) => ({
          ...prevState,
          selectionList: selectedList,
        }))
      isMounted() &&
        saveCustomization({
          api: columnsUrl,
          selected: selectedList,
          setToDefault: setToDefault,
          type: 'table',
        })
    },
    [columnsUrl, isMounted],
  )

  const { status: columnStatus, data: columnApiResponse } = useQuery({
      queryKey: [userId],
      queryFn: ({ signal }) => SecureAxios.get(columnsUrl, { signal }),
      gcTime: trafficCacheTime,
    }),
    columnData =
      columnStatus === 'success' && columnApiResponse.data?.selectedColumns

  useEffect(() => {
    if (isMounted() && columnData) {
      setState((prevState) => ({
        ...prevState,
        selectionList: columnData,
      }))
    }
  }, [columnData, isMounted])

  const originalConfig = useMemo(
    () => [
      ...(isAllBrand && !vendor_id
        ? [{ ...commonContentHeaders.customer_name }]
        : [{ ...commonContentHeaders.product_name }]),
      {
        ...commonContentHeaders.conversion_rate_page_views,
        ...(checkIfAllChinaMarketplaces()
          ? {
              tooltip: {
                content: (
                  <div style={{ maxWidth: 400 }}>
                    {t('content:conversionRateTooltipChina')}
                  </div>
                ),
              },
            }
          : {}),
        noSort: isConversionWithoutSNSSelected,
      },
      ...(hideConversionWithoutSnsMetricToggle
        ? [commonContentHeaders.conversion_rate_without_sns]
        : []),
      commonContentHeaders.total_sales,
      commonContentHeaders.pattern_orders,
      ...(!checkIfAllChinaMarketplaces()
        ? [commonContentHeaders.pattern_page_views]
        : []),
      commonContentHeaders.revenue_per_unit,
      commonContentHeaders.avg_selling_price,
      commonContentHeaders.units_per_order,
      commonContentHeaders.total_page_views,
      commonContentHeaders.buybox,
      commonContentHeaders.weighted_in_stock_pct,
      ...(addMoreMetricsToConversion && checkIfAllChinaMarketplaces()
        ? [
            additionalMetricsHeaders({
              metricName: 'unique_visitors',
              columnLabel: t('uniqueVisitors'),
              tooltipContent: t('uniqueVisitorsTooltip'),
            }),
            additionalMetricsHeaders({
              metricName: 'add_to_cart',
              columnLabel: t('addToCart'),
              tooltipContent: t('addToCartTooltip'),
            }),
            additionalMetricsHeaders({
              metricName: 'buyer_conversion_rate',
              columnLabel: t('buyerConversionRate'),
              tooltipContent: t('buyerConversionRateTooltip'),
              isPercentage: true,
              decimalScale: 2,
            }),
            additionalMetricsHeaders({
              metricName: 'avg_order_value_by_buyer',
              columnLabel: t('avgOrderValueByBuyer'),
              tooltipContent: t('avgOrderValueByBuyerTooltip'),
              currency: {
                code: currency?.code,
                symbol: currency?.symbol,
                id: currency?.id,
                name: currency?.name,
                type: currency?.type,
                label: currency?.label,
              },
              decimalScale: 2,
            }),
          ]
        : []),
      ...(isSingleBrandView
        ? CategoriesAndTags({
            ctDrawerState,
            setCtDrawerState,
          }).config<StandardTrafficObject>({
            categoryNameKey: 'leaf_category_name',
            tagDetailsKey: 'tag_details',
            rootCategoryIdKey: 'root_category_id',
            leafCategoryIdKey: 'leaf_category_id',
            sortTagsList: true,
          })
        : []),
      {
        ...commonTrafficHeaders().placeholder,
        isButton: true,
        cell: {
          children: (product: StandardTrafficObject) => {
            const link =
              isAllBrand && !vendor_id
                ? `?vendor_id=${product?.customer_id}&selected_brand=${product?.customer_name}&customer_id=${product?.customer_id}`
                : `${productDetailRoute.listing({
                    productId: product?.master_product_id,
                    marketplaceId: product?.marketplace_ids?.[0],
                    listingId: product?.market_product_id,
                  })}/content/details/conversion?customer_id=${product?.customer_id}&primary_tag=${product?.asin}`
            return (
              <div className='first-sticky-cell-right extra-left-padding'>
                <Button
                  disabled={
                    isAllBrand && !vendor_id ? false : !product?.marketplace_ids
                  }
                  as='link'
                  to={link}
                  routerComponent={Link}
                >
                  {isAllBrand && !vendor_id ? c('view') : c('viewProduct')}
                </Button>
              </div>
            )
          },
        },
      },
    ],
    [
      isAllBrand,
      vendor_id,
      checkIfAllChinaMarketplaces,
      t,
      isConversionWithoutSNSSelected,
      hideConversionWithoutSnsMetricToggle,
      addMoreMetricsToConversion,
      currency?.code,
      currency?.symbol,
      currency?.id,
      currency?.name,
      currency?.type,
      currency?.label,
      isSingleBrandView,
      ctDrawerState,
    ],
  )

  const totalConfigItems = originalConfig.slice(1, -1)
  const totalList = totalConfigItems.map((item) => item.label)

  const config = useMemo(() => {
    return [
      originalConfig[0],
      ...getSelectedConfig(originalConfig, selectionList, 'selected'),
      originalConfig[originalConfig.length - 1],
    ]
  }, [originalConfig, selectionList])

  const columnSettingsApiKey = 'content:conversion:conversion_view_report',
    reqUrl = apiUrl + `/${userId}/predict/${columnSettingsApiKey}`,
    { status: columnSettingStatus, data: columnSettingResponse } = useQuery({
      queryKey: [userId, columnSettingsApiKey],
      queryFn: ({ signal }) => SecureAxios.get(reqUrl, { signal }),

      gcTime: trafficCacheTime,
    }),
    columnSettingsDataAggregaredView =
      columnSettingStatus === 'success' &&
      columnSettingResponse.data?.selectedColumns

  useEffect(() => {
    if (columnSettingsDataAggregaredView) {
      setState((prevState) => ({
        ...prevState,
        selectionList: columnSettingsDataAggregaredView,
      }))
    }
  }, [columnSettingsDataAggregaredView])

  const searchInputHandler = (searchTerm: string) => {
    setSearchTerm(searchTerm)
  }
  const productParamsFromHooks = useDataTableParams({
    aggregation:
      customer.id === 0 || isBrandGroupSelected ? 'brand' : 'product',
    selectedColumnNames: [
      ...productColumns,
      ...(addMoreMetricsToConversion && checkIfAllChinaMarketplaces()
        ? additionalColumns
        : []),
      ...(isConversionWithoutSNSSelected
        ? ['conversion_without_sns', 'sns_orders']
        : []),
    ],
    searchTerm: searchTerm,
    sortBy: {
      flip: false,
      prop: 'conversion_current_period',
    },
    sortByProp: 'conversion_current_period',
  })
  const { aggregate_by: aggregateBy } = productParamsFromHooks
  const productParams = useMemo(() => {
    return {
      per_page: 20,
      aggregate_by: timeframe.aggregation ?? 'hour',
      timeframe: timeframe.value === 'custom' ? 'custom' : timeframe.type,
      start_date: noUTCOffset(startDate ?? ''),
      end_date: noUTCOffset(endDate ?? ''),
      match_all_tags: tags.matchAllTags,
      show_total: true,
      currency: currency?.id ?? 1,
      columns: [
        ...productColumns,
        ...(addMoreMetricsToConversion && checkIfAllChinaMarketplaces()
          ? additionalColumns
          : []),
      ],
      is_contribution_to_change_enabled: true,
      comparison_period: selectedComparisonPeriod?.value,
      sort: 'conversion_current_period',
      ...(isAllBrand
        ? isBrandGroupSelected
          ? {
              vendor_ids: brandGroupCustomerVendorIds?.length
                ? brandGroupCustomerVendorIds
                : [],
            }
          : ConversionReportState?.brandId
            ? { customer_id: ConversionReportState?.brandId }
            : { customer_id: customer.id }
        : { customer_id: customer.id }),
    }
  }, [
    timeframe.aggregation,
    timeframe.value,
    timeframe.type,
    startDate,
    endDate,
    tags.matchAllTags,
    currency?.id,
    productColumns,
    addMoreMetricsToConversion,
    checkIfAllChinaMarketplaces,
    additionalColumns,
    selectedComparisonPeriod?.value,
    isAllBrand,
    isBrandGroupSelected,
    brandGroupCustomerVendorIds,
    ConversionReportState?.brandId,
    customer.id,
  ])

  const tableApi = `${api}/product_performance`
  const {
    status: tableStatus,
    data: tableApiResponse,
    fetchNextPage,
    hasNextPage,
    isLoading: loadingTable,
  } = useInfiniteQuery({
    queryKey: [
      tableApi,
      productParams,
      productParamsFromHooks,
      sortBy,
      vendor_id,
      isAllBrand,
      isVatAdjustmentTurnedOn,
      isBrandGroupSelected,
      customer,
      secondRange_startDate,
      previousDates,
      secondRange_endDate,
      conversionState,
      currency,
    ],
    queryFn: ({ pageParam = 1, signal }) => {
      const params = {
        ...(aggregateBy === 'hour'
          ? omit(productParamsFromHooks, ['aggregate_by'])
          : productParamsFromHooks),
        page: pageParam,
        per_page: 20,
        sort: standardSortParams(sortBy, ['product_title', 'customer_name']),
        ...(searchTerm ? { search_for: searchTerm } : {}),
        table_aggregation: isAllBrand && !vendor_id ? 'customer' : 'product',
        vat_adjusted: isVatAdjustmentTurnedOn,
        ...(vendor_id
          ? { customer_id: vendor_id }
          : {
              ...(isBrandGroupSelected
                ? {
                    vendor_ids: brandGroupCustomerVendorIds?.length
                      ? brandGroupCustomerVendorIds
                      : [],
                  }
                : { customer_id: customer.id }),
            }),

        comparison_start_date: secondRange_startDate
          ? secondRange_startDate
          : previousDates?.startDate,
        comparison_end_date: secondRange_endDate
          ? secondRange_endDate
          : previousDates?.endDate,
        marketplace_ids:
          getFilteredIds(
            true,
            false,
            marketplaceIds ?? [],
            selectedRegions,
            allMarketplaces,
          ) ?? [],
        ...(conversionState?.productsSoldBy?.id !== 0
          ? { sold_by_pattern: true }
          : {}),
        currency_code: currency?.code,
        ...(conversionState?.status?.id !== 0
          ? { active: conversionState?.status.value === 'active' }
          : {}),
      }
      return SecureAxios.get(tableApi, { params, signal })
    },
    initialPageParam: 1,
    gcTime: 1000 * 60 * 60 * 8,
    getNextPageParam: (previousResponse) => {
      return previousResponse?.data?.pagination?.last_page
        ? undefined
        : previousResponse?.data?.pagination?.next_page
    },
  })

  const count = tableApiResponse?.pages[0]?.data?.pagination?.count

  const tableData = useMemo(() => {
    if (tableStatus === 'success' && tableApiResponse?.pages) {
      const detailData = tableApiResponse.pages.flatMap((page) => {
        return page?.data?.data
      })
      return [...detailData]
    }
    return []
  }, [tableApiResponse?.pages, tableStatus])

  const exportCSVData = useCallback((params: unknown = {}) => {
    return SecureAxios.get(
      `${getApiUrlPrefix(
        'business-reports',
      )}/api/v3/reports/conversion_reports/products`,
      {
        params,
      },
    ).then((response) => response.data)
  }, [])

  const tableCSVName = 'Conversion Report'
  const newDownloadParams = useMemo(() => {
    const { customer_id } = commonParams
    return {
      ...commonParams,
      csv_name: tableCSVName,
      format: 'csv',
      download: true,
      ...productParamsFromHooks,
      columns: [
        ...productColumns,
        ...(addMoreMetricsToConversion && checkIfAllChinaMarketplaces()
          ? additionalColumns
          : []),
        ...(hideConversionWithoutSnsMetricToggle
          ? ['conversion_without_sns', 'sns_orders']
          : []),
      ],
      ...(isAllBrand ? { customer_id } : {}),
      table_aggregation: isAllBrand && !vendor_id ? 'customer' : 'product',
      ...(searchTerm ? { search_for: searchTerm } : {}),

      ...(hideConversionWithoutSnsMetricToggle &&
      sortBy.prop === 'conversion_current_period'
        ? {
            sort: undefined,
          }
        : {
            sort: standardSortParams(sortBy, [
              'product_title',
              'customer_name',
            ]),
          }),
    }
  }, [
    commonParams,
    productParamsFromHooks,
    productColumns,
    addMoreMetricsToConversion,
    checkIfAllChinaMarketplaces,
    additionalColumns,
    hideConversionWithoutSnsMetricToggle,
    isAllBrand,
    vendor_id,
    searchTerm,
    sortBy,
  ])
  const csvDownloadOptions: DownloadOptionsType = useMemo(
    () => [
      {
        linkName: t('conversionReport'),
        csvName: t('conversionReport'),
        csvFormat: {
          api: exportCSVData,
          params: newDownloadParams,
          callout: (element?: string | HTMLDivElement) =>
            element && typeof element !== 'string' && element.click(),
        },
      },
    ],
    [exportCSVData, newDownloadParams, t],
  )

  const VendorChanged = useMemo(() => {
    return brandGroupContextData?.vendor_ids
  }, [brandGroupContextData?.vendor_ids])

  const productTableProps = useMemo(() => {
    return {
      data: tableData,
      config,
      dataKey: 'asin',
      hasData: tableData?.length > 0,
      loading: loadingTable,
      hasMore: !!(tableStatus === 'success' && hasNextPage),
      tableId: 'content-conversion-table',
      twoLineLabel: true,
      sort,
      sortBy,
      noDataFields: {
        primaryText: t('noProductsFound'),
        secondaryText: t('couldNotFindAnyProduct'),
      },
      getData: fetchNextPage,
      successStatus: tableStatus === 'success',
      stickyTableConfig,
      tableHeaderProps: {
        header: {
          name:
            isAllBrand && !vendor_id
              ? c('brand', { count })
              : c('product', { count }),
          value: convertTableHeaderCount(count),
        },
        search: {
          value: searchTerm,
          onChange: (value: string) => searchInputHandler(value),
          placeholder: t('searchPlaceholder', {
            key: isAllBrand && !vendor_id ? c('brands') : c('products'),
          }),
          debounce: 250,
        },
        download: {
          csvDownloadOptions: csvDownloadOptions,
          initialDisplay: true,
          show: true,
        },
        customColumnProps: {
          list: totalList,
          selected: selectionList,
          callout: customSelectionCallout,
          setToDefaultCallout: () =>
            customSelectionCallout(defaultColumns, true),
          isColumnsReorderable: true,
        },
      },
    }
  }, [
    config,
    count,
    csvDownloadOptions,
    customSelectionCallout,
    defaultColumns,
    fetchNextPage,
    hasNextPage,
    isAllBrand,
    loadingTable,
    searchTerm,
    selectionList,
    sortBy,
    stickyTableConfig,
    t,
    tableData,
    tableStatus,
    totalList,
    vendor_id,
  ])
  useEffect(() => {
    if (!isAllBrand || VendorChanged) {
      // updateBreadcrumbs is not supporting to redirect after switching brand group to single brand with some query params in url
      // TODO - Fix in updateBreadcrumbs
      navigate('/content/conversion')
    }
  }, [navigate, isAllBrand, VendorChanged])

  useEffect(() => {
    if (!isAllBrand && sortBy?.prop === 'customer_name') {
      setSortBy({
        prop: 'product_title',
      })
    }
    if (isAllBrand && sortBy?.prop === 'product_title') {
      setSortBy({
        prop: 'customer_name',
      })
    }
    if (
      isConversionWithoutSNSSelected &&
      sortBy?.prop === 'conversion_current_period'
    ) {
      setSortBy({
        prop: 'total_sales_current_period',
      })
    }
  }, [isAllBrand, sortBy, isConversionWithoutSNSSelected])
  const filteredMarketplaceIds = useMemo(() => {
    return (
      getFilteredIds(
        true,
        false,
        marketplaceIds ?? [],
        selectedRegions,
        allMarketplaces,
      ) ?? []
    )
  }, [allMarketplaces, marketplaceIds, selectedRegions])

  return isProspectBrand ? (
    <EmptyState primaryText={t('contentUnavailableBrand')} />
  ) : (
    <>
      <ConversionGraphData
        api={api}
        commonParams={commonParams}
        extraParams={extraParams}
        csvTitle={t('conversionOverview')}
        vendor_id={vendor_id ?? ConversionReportState?.brandId}
        allBrand={isAllBrand && !vendor_id}
      ></ConversionGraphData>

      <Tabs
        active={0}
        tabs={[
          {
            id: 0,
            tabName: isSingleBrandView ? c('products') : c('brands'),
            content: <StandardTable {...productTableProps} />,
          },
          ...(filteredMarketplaceIds?.length > 1
            ? [
                {
                  id: 1,
                  tabName: c('marketplaces'),
                  content: (
                    <ConversionMarketplaceTable
                      commonParams={commonParams}
                      defaultColumns={defaultColumns}
                      productColumns={productColumns}
                      tableName='marketplace'
                      filteredMarketplaceIds={filteredMarketplaceIds}
                      key='marketplace'
                    />
                  ),
                },
              ]
            : []),
          ...(checkIfAllChinaMarketplaces()
            ? [
                {
                  id: filteredMarketplaceIds?.length > 1 ? 2 : 1,
                  tabName: c('stores'),
                  content: (
                    <ConversionMarketplaceTable
                      commonParams={commonParams}
                      defaultColumns={defaultColumns}
                      productColumns={productColumns}
                      tableName='store'
                      filteredMarketplaceIds={filteredMarketplaceIds}
                      key='store'
                    />
                  ),
                },
              ]
            : []),
        ]}
      />

      {isSingleBrandView &&
        CategoriesAndTags({ ctDrawerState, setCtDrawerState }).sideDrawer()}
    </>
  )
}

export default ConversionReport
