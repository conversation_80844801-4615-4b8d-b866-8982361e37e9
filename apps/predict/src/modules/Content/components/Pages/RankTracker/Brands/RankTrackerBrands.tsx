import React, { useContext, useEffect, useMemo, useState } from 'react'
import { NumericFormat } from 'react-number-format'
import { Link, useLocation } from 'react-router-dom'
import { useInfiniteQuery, useQuery } from '@tanstack/react-query'
import {
  <PERSON><PERSON>,
  getApiUrlPrefix,
  hasV<PERSON>ue,
  MdashCheck,
  type SortColumnProps,
  standardSortParams,
  StandardTable,
  TrimText,
  useIsMounted,
} from '@patterninc/react-ui'
import {
  c,
  csvToastCallout,
  getUserSettingEndpoint,
  saveCustomization,
  SecureAxios,
  tryLocalStorageParse,
  useTranslate,
} from '@predict-services'
import { convertTableHeaderCount } from 'src/modules/Traffic/services/TrafficTableHeaderHelperService'
import { getFilteredIds } from 'src/modules/Traffic/hooks/useCommonAPIParams'

import { type IndividualBrandGroupData } from '../../../../../../common/components/BrandGroup/BrandGroupTypes'
import { useComparisonDates } from '../../../../../../common/hooks/CustomHooks'
import { type DownloadOptionsType } from '../../../../../../common/services/CsvHelpers'
import { getSelectedConfig } from '../../../../../../common/services/TableConfigService'
import { ThemeContext } from '../../../../../../Context'
import { useUser } from '../../../../../../context/user-context'
import useCustomTableFilter, {
  getCustomFilterDetails,
} from '../../../../../Traffic/services/TrafficFilterHelper'
import { trafficCacheTime } from '../../../../../Traffic/services/TrafficHelperService'
import { getContentTableHeaderTooltip } from '../../../Common/ContentTooltipHelper'
import RankGraphData from '../Common/RankGraphData/RankGraphData'
import { RankTrackerContext } from '../RankTrackerContext'
import { filterDetails } from './RankTrackerBrandsFilter'
import { useLanguageParams } from '../../../Common/ContentHooks'

const stickyTableConfig = {
  right: 1,
}

export type GraphData = {
  date: string
  est_organic_clicks: number
  avg_previous: number
}
interface KeyProps {
  value: number
  type: string
  display: string
  change: number
  pct_change: number
}

export type RankTrackerItem = {
  brand: string
  brand_id: number
  customer_id: number
  keywords_in_top_4: KeyProps
  keywords_on_page_1: KeyProps
  est_organic_clicks: KeyProps
}

type StateType = {
  search: string
}

const RankTrackerBrands = (): React.JSX.Element => {
  const {
      updateBreadcrumbs,
      startDate,
      endDate,
      marketplaceIds,
      secondRange_endDate,
      secondRange_startDate,
      brandGroupCustomer,
      isVatAdjustmentTurnedOn,
      allMarketplaces,
      regions: selectedRegions,
    } = useContext(ThemeContext),
    { t } = useTranslate('content'),
    { state: rankTrackerState } = useContext(RankTrackerContext),
    [state, setState] = useState<StateType>({
      search: '',
    }),
    { search } = state,
    brandGroupData = brandGroupCustomer as IndividualBrandGroupData | null,
    tableId = brandGroupData?.customer_ids
      ? 'brandGroup_rank_tracker'
      : 'brand_rank_tracker',
    [sortBy, setSortBy] = useState(
      tryLocalStorageParse(`sort_by_${tableId}`) ?? {
        prop: 'est_organic_clicks',
        flip: false,
        isByChange: false,
      },
    ),
    isMounted = useIsMounted(),
    user = useUser().user
  const { code, symbol } = user?.current_currency ?? {},
    userCurrency = useMemo(() => {
      return {
        currency_code: code,
        currency_symbol: symbol,
      }
    }, [code, symbol])

  const CustomFilterTableAPIendpoint = useMemo(() => {
    return brandGroupData?.customer_ids
      ? 'content:rankTracker:brandGroup:customFilterTable'
      : 'content:rankTracker:brand:customFilterTable'
  }, [brandGroupData?.customer_ids])

  const defaultColumns = [t('estimatedOrganicClicks')]
  const [selectionList, setSelectionList] = useState(defaultColumns)

  const {
    filterData,
    removeFilter,
    removeFilters,
    extraCustomFilterParams: filterAPIValues,
    activeFilters,
    refetchTableFilterSettings,
  } = useCustomTableFilter({
    columnFilterDetails: filterDetails,
    selectionList,
    userSettingKey: CustomFilterTableAPIendpoint,
  })

  const { pathname } = useLocation()

  const previousDates = useComparisonDates()
  const languageParams = useLanguageParams()
  // Fetch data

  const rankTrackerApi = `${getApiUrlPrefix('rank-tracker')}/api/v3/customers`,
    rankTrackerProductsApi = `${rankTrackerApi}/list`,
    commonParams = useMemo(() => {
      return {
        start_date: startDate,
        end_date: endDate,
        ...(search ? { search_for: search } : {}),
        currency_code: userCurrency?.currency_code,
        marketplace_ids:
          getFilteredIds(
            true,
            false,
            marketplaceIds ?? [],
            selectedRegions,
            allMarketplaces,
          ) ?? [],
        comparison_start: secondRange_startDate
          ? secondRange_startDate
          : previousDates?.startDate,
        comparison_end: secondRange_endDate
          ? secondRange_endDate
          : previousDates?.endDate,
        sort: standardSortParams(sortBy),
        ...(rankTrackerState.productsSoldBy.id !== 0
          ? { sold_by_pattern: true }
          : {}),
        ...(rankTrackerState?.keywordSource?.id === 0
          ? { keyword_status: true }
          : {}),
        ...{ filter: { ...filterAPIValues } },
        ...(brandGroupData?.customer_ids
          ? { customer_ids: brandGroupData?.customer_ids }
          : {}),
        ...(isVatAdjustmentTurnedOn
          ? { vat_adjusted: isVatAdjustmentTurnedOn }
          : {}),
        ...languageParams,
      }
    }, [
      startDate,
      endDate,
      search,
      userCurrency?.currency_code,
      marketplaceIds,
      secondRange_startDate,
      previousDates?.startDate,
      previousDates?.endDate,
      secondRange_endDate,
      sortBy,
      rankTrackerState.productsSoldBy.id,
      rankTrackerState?.keywordSource?.id,
      brandGroupData?.customer_ids,
      filterAPIValues,
      isVatAdjustmentTurnedOn,
      languageParams,
      allMarketplaces,
      selectedRegions,
    ]),
    {
      status,
      data: apiResponse,
      fetchNextPage,
      hasNextPage,
    } = useInfiniteQuery({
      queryKey: [
        rankTrackerProductsApi,
        startDate,
        endDate,
        search,
        sortBy,
        marketplaceIds,
        previousDates,
        commonParams,
        secondRange_endDate,
        secondRange_startDate,
      ],
      queryFn: ({ pageParam = 1, signal }) => {
        const params = {
          ...commonParams,
          page: pageParam,
          per_page: 20,
        }
        return SecureAxios.get(rankTrackerProductsApi, { params, signal })
      },
      initialPageParam: 1,
      gcTime: trafficCacheTime,
      getNextPageParam: (previousResponse) => {
        return previousResponse?.data?.data?.pagination?.lastPage
          ? undefined
          : previousResponse?.data?.data?.pagination?.next_page
      },
    }),
    count = apiResponse?.pages[0]?.data?.data?.pagination?.count,
    data = apiResponse
      ? apiResponse.pages.flatMap((page) => {
          return page?.data?.data?.data
        })
      : []

  const searchInputHandler = (value: string) => {
    setState((prevState) => ({
      ...prevState,
      search: value ?? '',
    }))
  }

  const sort: SortColumnProps['sorter'] = (sortObj) => {
    isMounted() &&
      setSortBy({
        prop: sortObj.activeColumn,
        flip: sortObj.direction,
        lowerCaseParam: sortObj.lowerCaseParam,
      })
  }

  const csvDownloadOptions: DownloadOptionsType = [
    {
      linkName: c('rankTracker'),
      csvName: c('rankTracker'),
      csvFormat: {
        api: (csvParams) =>
          SecureAxios.get(rankTrackerProductsApi, { params: csvParams }).then(
            (response) => {
              return response.data
            },
          ),
        params: {
          ...commonParams,
          async: true,
          csv_name: c('rankTracker'),
        },
        callout: csvToastCallout(),
      },
    },
  ]

  useEffect(() => {
    updateBreadcrumbs({
      name: c('conversion'),
      link: pathname,
      changeType: 'rootLevel',
    })
  }, [pathname, updateBreadcrumbs])

  const originalConfig = useMemo(
    () => [
      {
        name: 'brand',
        label: c('brand'),
        cell: {
          children: (product: RankTrackerItem) => {
            return (
              <span className='flex'>
                <TrimText text={product?.brand} limit={75} />
              </span>
            )
          },
        },
        style: {
          minWidth: '300px',
        },
        mainColumn: true,
      },
      {
        name: 'est_organic_clicks',
        label: t('estimatedOrganicClicks'),
        ...getContentTableHeaderTooltip('estOrganicClicks'),
        cell: {
          children: (product: RankTrackerItem) => {
            return (
              <MdashCheck check={hasValue(product?.est_organic_clicks?.value)}>
                <NumericFormat
                  value={product?.est_organic_clicks?.value}
                  thousandSeparator={true}
                  displayType='text'
                  className={
                    sortBy.prop === 'est_organic_clicks' ? 'fw-semi-bold' : ''
                  }
                />
              </MdashCheck>
            )
          },
        },
        filter: getCustomFilterDetails(filterData, 'est_organic_clicks'),
      },
      {
        name: 'keywords_in_top_4',
        label: t('keywordsInTopFour'),
        ...getContentTableHeaderTooltip('keywordsInTop4'),
        cell: {
          children: (product: RankTrackerItem) => {
            return (
              <MdashCheck check={hasValue(product?.keywords_in_top_4?.value)}>
                <NumericFormat
                  value={product?.keywords_in_top_4?.value}
                  thousandSeparator={true}
                  displayType='text'
                  className={
                    sortBy.prop === 'keywords_in_top_4' ? 'fw-semi-bold' : ''
                  }
                />
              </MdashCheck>
            )
          },
        },
        filter: getCustomFilterDetails(filterData, 'keywords_in_top_4'),
      },
      {
        name: 'keywords_on_page_1',
        label: t('keywordsOnPageOne'),
        ...getContentTableHeaderTooltip('keywordsOnPage1'),
        cell: {
          children: (product: RankTrackerItem) => {
            return (
              <MdashCheck check={hasValue(product?.keywords_on_page_1?.value)}>
                <NumericFormat
                  value={product?.keywords_on_page_1?.value}
                  thousandSeparator={true}
                  displayType='text'
                  className={
                    sortBy.prop === 'keywords_on_page_1' ? 'fw-semi-bold' : ''
                  }
                />
              </MdashCheck>
            )
          },
        },
        filter: getCustomFilterDetails(filterData, 'keywords_on_page_1'),
      },
      {
        name: '',
        label: '',
        cell: {
          children: (product: RankTrackerItem) => {
            const link = `/content/rank-tracker?customer_id=${product?.customer_id}&brand_id=${product?.brand_id}&customer_name=${product?.brand}`
            return (
              <Button as='link' to={link} routerComponent={Link}>
                {c('view')}
              </Button>
            )
          },
        },
        isButton: true,
        style: {
          minWidth: '85px',
        },
        noSort: true,
      },
    ],
    [filterData, sortBy.prop, t],
  )

  const apiUrl = getUserSettingEndpoint(
    `content:rank_tracker:${
      brandGroupData?.customer_ids ? 'brandGroup' : 'allBrands'
    }`,
  )

  const { status: userSettingStatus, data: userSettingsApiResponse } = useQuery(
    {
      queryKey: [apiUrl.decoded, pathname, refetchTableFilterSettings],
      queryFn: ({ signal }) => SecureAxios.get(apiUrl.endpoint, { signal }),
      gcTime: trafficCacheTime,
    },
  )

  const userSettingsData =
    userSettingStatus === 'success' &&
    userSettingsApiResponse?.data?.selectedColumns

  useEffect(() => {
    if (userSettingsData) {
      setSelectionList(userSettingsData)
    }
  }, [userSettingsData])

  const totalConfigItems = originalConfig.slice(1, -1)
  const totalList = totalConfigItems.map((item) => item.label)
  const customizeColumnConfig = useMemo(() => {
    return [
      originalConfig[0],
      ...getSelectedConfig(originalConfig, selectionList, 'selected'),
      originalConfig[originalConfig.length - 1],
    ]
  }, [originalConfig, selectionList])

  const customSelectionCallout = (
    selectedList: string[],
    setToDefault?: boolean,
  ) => {
    setSelectionList(selectedList)
    saveCustomization({
      api: apiUrl.endpoint,
      selected: selectedList,
      setToDefault: setToDefault,
      type: 'table',
    })
    refetchTableFilterSettings()
  }

  return (
    <div>
      <RankGraphData
        api={rankTrackerApi}
        csvTitle={c('rankTracker')}
        summaryEntity='Keywords'
        allowFilter
        extraParams={{
          ...(rankTrackerState.productsSoldBy.id !== 0
            ? { sold_by_pattern: true }
            : {}),
          ...(brandGroupData?.customer_ids
            ? { customer_ids: brandGroupData?.customer_ids }
            : {}),
          ...(rankTrackerState?.keywordSource?.id === 0
            ? { keyword_status: true }
            : {}),
        }}
        showNewHeaderMetric
        pageName='brands'
        otherMetricCount={1}
      />
      <StandardTable
        data={data}
        config={customizeColumnConfig}
        dataKey='brand'
        hasData={data?.length > 0}
        loading={status === 'pending'}
        hasMore={!!(status === 'success' && hasNextPage)}
        tableId={tableId}
        twoLineLabel
        sort={sort}
        sortBy={sortBy}
        noDataFields={{
          primaryText: t('noBrandsFound'),
          secondaryText: t('noBrandForSelectedCriteria'),
        }}
        tableHeaderProps={{
          header: {
            name: c('brand', { count }),
            value: convertTableHeaderCount(count),
          },
          search: {
            value: search,
            onChange: (value) => searchInputHandler(value),
            placeholder: c('searchBrands'),
            debounce: 250,
          },
          download: {
            csvDownloadOptions: csvDownloadOptions,
            initialDisplay: true,
            show: true,
          },
          columnFilterProps: {
            activeFilters,
            remove: removeFilters,
          },
          customColumnProps: {
            list: totalList,
            selected: selectionList,
            callout: (selectedList) =>
              customSelectionCallout(selectedList, false),
            setToDefaultCallout: () =>
              customSelectionCallout(defaultColumns, true),
            isColumnsReorderable: true,
          },
        }}
        getData={fetchNextPage}
        successStatus={status === 'success'}
        stickyTableConfig={stickyTableConfig}
        activeFilters={activeFilters}
        removeFilters={removeFilter}
      />
    </div>
  )
}

export default RankTrackerBrands
