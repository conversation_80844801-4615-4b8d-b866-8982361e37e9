import React, {
  use<PERSON><PERSON>back,
  useContext,
  useEffect,
  useMemo,
  useState,
} from 'react'
import { useLocation } from 'react-router-dom'
import { useQuery } from '@tanstack/react-query'
import moment from 'moment'
import {
  CartesianGrid,
  Line,
  LineChart,
  ResponsiveContainer,
  Tooltip,
  XAxis,
  YAxis,
} from 'recharts'
import {
  EmptyState,
  GraphLoading,
  HeaderMetric,
  HeaderMetricGroup,
  Icon,
  Tooltip as TooltipComponent,
  useToggle,
} from '@patterninc/react-ui'
import { PngImage } from '@predict-components'
import { CsvButtonHelper } from 'src/common/components/Image/PngImage'
import SparkLineCustomTooltip from 'src/common/components/Charts/SparkLineCustomTooltip/SparkLineCustomTooltip'
import { useHeaderGraphMetrics } from '@predict-hooks'
import { useGetCustomCategoryIds } from 'src/common/components/GlobalFilter/GlobalFilterHelpers'
import { useLanguageParams } from 'src/modules/Content/components/Common/ContentHooks'
import { getFilteredIds } from 'src/modules/Traffic/hooks/useCommonAPIParams'
import {
  c,
  csvToastCallout,
  type DownloadOptionsType,
  parseParams,
  useTranslate,
} from '@predict-services'

import { type IndividualBrandGroupData } from '../../../../../../../common/components/BrandGroup/BrandGroupTypes'
import {
  abbreviateNumber,
  type DynamicGraphDataType,
} from '../../../../../../../common/components/Charts/Graphs/GraphHelperService'
import TwoLineDateLabel from '../../../../../../../common/components/Charts/Labels/TwoLineDateLabel'
import { ThemeContext } from '../../../../../../../Context'
import { useUser } from '../../../../../../../context/user-context'
import {
  type GraphData,
  type TrafficGraphDataProps,
} from '../../../../../../Traffic/components/common/AdvertisingGraphData/trafficGraphData.models'
import TrafficHeaderData from '../../../../../../Traffic/components/common/AdvertisingGraphData/TrafficHeaderData'
import {
  getOrPostAPI,
  trafficCacheTime,
} from '../../../../../../Traffic/services/TrafficHelperService'
import { ContentMetricPropsTooltipData } from '../../../../Common/ContentTooltipHelper'
import { type filterType, RankTrackerContext } from '../../RankTrackerContext'
import RankTrackerFilter from '../../RankTrackerFilter'
import { useRankTrackerDates } from '../RankTrackerHelperService'

const DOWNLOAD_BUTTON_ID = 'conversion_rank_tracker_chart_image_download'

type RankGraphDataProps = {
  api: string
  extraParams?: Record<string, unknown>
  csvTitle: string
  summaryEntity: string
  keywordSummary?: React.ReactNode
  allowFilter?: boolean
  showNewHeaderMetric?: boolean
  pageName?: string
  amazonSearchUrlCallback?: (type: string) => void
  otherMetricCount?: number
}

type lineChartgraphData = {
  report_date: string
  date: string
  est_organic_clicks:
    | {
        value: number
      }
    | number
}

export type GraphStateType = {
  graphData: GraphData
  unformattedGraphData: DynamicGraphDataType[]
  metaEndDate?: string
  loadingGraph?: boolean
  comparisonDates?: {
    startDate: string
    endDate: string
  }
  totalsData?: Record<string, unknown>[]
}

const RankGraphData = ({
  api,
  extraParams,
  csvTitle,
  summaryEntity,
  keywordSummary,
  allowFilter = false,
  showNewHeaderMetric = false,
  pageName = '',
  amazonSearchUrlCallback,
  otherMetricCount = 2,
}: RankGraphDataProps): React.JSX.Element => {
  const enableKeywordsPageChanges = useToggle(
    'enable_rank_tracker_keywords_page_changes',
  )
  const [showCategories, setShowCategories] = useState(false)
  const {
      startDate,
      endDate,
      marketplaceIds,
      tags,
      customer,
      timeframe,
      secondRange_endDate,
      secondRange_startDate,
      brandGroupCustomer,
      regions,
      allMarketplaces,
    } = useContext(ThemeContext),
    { t } = useTranslate('content')
  const filteredMarketplaceIds = useMemo(
    () =>
      getFilteredIds(
        true,
        false,
        marketplaceIds ?? [],
        regions,
        allMarketplaces,
      ),
    [marketplaceIds, regions, allMarketplaces],
  )
  const aggregate_by =
    timeframe.aggregation === 'hour' ? '' : timeframe.aggregation // Hourly aggregation is currently disabled, but it still reappears sometimes. Added in case for that.
  const currency = useUser().user.current_currency
  const { state: rankTrackerState, dispatch: rankTrackerDispatch } =
      useContext(RankTrackerContext),
    searchParams = useLocation().search,
    searchQueryParams = parseParams(searchParams)
  const brandGroupData = brandGroupCustomer as IndividualBrandGroupData | null

  const acceptedFilters = useMemo(() => {
    const pagesContainKeywordSourceFilter = [
      'KeywordRankings',
      'keywordsWithProducts',
      'products',
      'brands',
    ]
    const filters: Array<string> = []
    if (pageName === 'KeywordRankings') {
      filters.push('amazonChoice')
    } else {
      filters.push('productsSoldBy')
    }
    if (pageName === 'keywordsWithProducts') {
      filters.push('defaultEstOrganicClicks')
    }
    if (pagesContainKeywordSourceFilter.includes(pageName)) {
      filters.push('keywordSource')
    }
    return filters
  }, [pageName])

  const tooltipData = {
    title: t('whatIsRankTracker'),
    text: [t('rankTrackerHelpText1')],
    useSideDrawerForMobile: true,
    maxWidth: '500px',
  }
  const rankTrackerTooltipData = {
    title: t('whatIsRankTracker'),
    text: t('rankTrackerHelpText1'),
    position: 'bottom',
    maxWidth: '500px',
  } as TrafficGraphDataProps['tooltipContent']
  const isShowCategoriesToggleEnabled = [
    'keywordsWithProducts',
    'products',
  ].includes(pageName)

  const customCategories = useGetCustomCategoryIds()
  const doCustomCategoriesExist =
    isShowCategoriesToggleEnabled && customCategories?.length > 0
  const transformGraphData = (arr: lineChartgraphData[]) => {
    return arr
      ? arr.map((a, index) => {
          let weekDate
          if (index === 0) {
            weekDate =
              moment(a.report_date).diff(startDate, 'days') > 0
                ? a.report_date
                : moment(startDate).format('Y-MM-DD')
          } else {
            weekDate = a.report_date
          }
          return {
            date: Date.parse(weekDate),
            est_organic_clicks:
              typeof a.est_organic_clicks === 'object'
                ? Math.round(a.est_organic_clicks?.value)
                : typeof a.est_organic_clicks === 'number'
                  ? Math.round(a.est_organic_clicks)
                  : Math.round(a.est_organic_clicks),
          }
        })
      : []
  }

  const customGraphUserSettingKey =
    pageName === 'KeywordRankings'
      ? 'content:rankTracker:keywordRankings:customGraphMetrics'
      : pageName === 'keywordsWithProducts'
        ? 'content:rankTracker:keywordsWithProducts:customGraphMetrics'
        : pageName === 'ProductRankings'
          ? 'content:rankTracker:ProductRankings:customGraphMetrics'
          : pageName === 'brands' && brandGroupData?.customer_ids
            ? 'content:rankTracker:brandGroup:customGraphMetrics'
            : pageName === 'brands'
              ? 'content:rankTracker:allBrands:customGraphMetrics'
              : 'content:rankTracker:products:customGraphMetrics'

  const {
    newStartDate,
    newEndDate,
    comparisonStartDate,
    comparisonEndDate,
    currentTimeframeCheck,
    previousDates,
  } = useRankTrackerDates()
  const languageParams = useLanguageParams()
  // Fetch summary data
  const rankTrackerSummaryApi = `${api}/summary/`,
    summaryParams = useMemo(() => {
      return {
        start_date: currentTimeframeCheck ? newStartDate : startDate,
        end_date: timeframe.type === 'current' ? newEndDate : endDate,
        ...(aggregate_by ? { aggregate_by } : {}),
        ...(customer.vendor_id ? { brand_id: customer.vendor_id } : {}),
        ...(searchQueryParams?.brand_id &&
        searchQueryParams?.brand_id !== 'null'
          ? { brand_id: searchQueryParams?.brand_id }
          : {}),
        currency_code: currency?.code,
        marketplace_ids: filteredMarketplaceIds,
        comparison_start: secondRange_startDate
          ? secondRange_startDate
          : currentTimeframeCheck
            ? comparisonStartDate
            : previousDates?.startDate,
        comparison_end: secondRange_endDate
          ? secondRange_endDate
          : timeframe.type === 'current'
            ? comparisonEndDate
            : previousDates?.endDate,
        ...extraParams,
        ...(tags?.tag_ids?.length > 0
          ? {
              tag_ids: tags.tag_ids,
              match_all_tags: tags.matchAllTags,
              expires_in: 300,
            }
          : {}),
        ...(aggregate_by ? { aggregate_by } : {}),
        ...(doCustomCategoriesExist
          ? { custom_category_ids: customCategories }
          : {}),
        ...languageParams,
      }
    }, [
      currentTimeframeCheck,
      newStartDate,
      startDate,
      timeframe.type,
      newEndDate,
      endDate,
      aggregate_by,
      customer.vendor_id,
      searchQueryParams?.brand_id,
      currency?.code,
      secondRange_startDate,
      comparisonStartDate,
      previousDates?.startDate,
      previousDates?.endDate,
      secondRange_endDate,
      comparisonEndDate,
      extraParams,
      tags.tag_ids,
      tags.matchAllTags,
      customCategories,
      doCustomCategoriesExist,
      languageParams,
      filteredMarketplaceIds,
    ]),
    { status: summaryStatus, data: summaryApiResponse } = useQuery({
      queryKey: [rankTrackerSummaryApi, summaryParams],
      queryFn: ({ signal }) =>
        getOrPostAPI(
          !doCustomCategoriesExist,
          rankTrackerSummaryApi,
          summaryParams,
          signal,
        ),
      gcTime: trafficCacheTime,
    }),
    summaryData =
      summaryStatus === 'success' && summaryApiResponse?.data?.data?.data[0]
  amazonSearchUrlCallback?.(summaryData?.search_url)
  // Fetch history data
  const rankTrackerHistoryApi = `${api}/history/`,
    historyParams = useMemo(() => {
      return {
        start_date: currentTimeframeCheck ? newStartDate : startDate,
        end_date: timeframe.type === 'current' ? newEndDate : endDate,
        ...(customer.vendor_id ? { brand_id: customer.vendor_id } : {}),
        ...(searchQueryParams?.brand_id &&
        searchQueryParams?.brand_id !== 'null'
          ? { brand_id: searchQueryParams?.brand_id }
          : {}),
        currency_code: currency?.code,
        marketplace_ids: getFilteredIds(
          true,
          false,
          marketplaceIds ?? [],
          regions,
          allMarketplaces,
        ),
        comparison_start: secondRange_startDate
          ? secondRange_startDate
          : currentTimeframeCheck
            ? comparisonStartDate
            : previousDates?.startDate,
        comparison_end: secondRange_endDate
          ? secondRange_endDate
          : timeframe.type === 'current'
            ? comparisonEndDate
            : previousDates?.endDate,
        sort: 'report_date:asc',
        ...extraParams,
        ...(tags?.tag_ids?.length > 0
          ? {
              tag_ids: tags.tag_ids,
              match_all_tags: tags.matchAllTags,
              expires_in: 300,
            }
          : {}),
        ...(aggregate_by ? { aggregate_by } : {}),
        source: pageName === 'keywordsWithProducts' ? 'keywords' : 'products',
        ...(showCategories ? { category_report: true } : {}),
        ...(doCustomCategoriesExist
          ? { custom_category_ids: customCategories }
          : {}),
        ...languageParams,
      }
    }, [
      currentTimeframeCheck,
      newStartDate,
      startDate,
      timeframe.type,
      newEndDate,
      endDate,
      customer.vendor_id,
      searchQueryParams?.brand_id,
      currency?.code,
      marketplaceIds,
      secondRange_startDate,
      comparisonStartDate,
      previousDates?.startDate,
      previousDates?.endDate,
      secondRange_endDate,
      comparisonEndDate,
      extraParams,
      tags.tag_ids,
      tags.matchAllTags,
      aggregate_by,
      pageName,
      showCategories,
      doCustomCategoriesExist,
      customCategories,
      languageParams,
      regions,
      allMarketplaces,
    ]),
    { status: historyStatus, data: historyApiResponse } = useQuery({
      queryKey: [rankTrackerHistoryApi, historyParams],
      queryFn: ({ signal }) =>
        getOrPostAPI(
          !doCustomCategoriesExist,
          rankTrackerHistoryApi,
          historyParams,
          signal,
        ),
      gcTime: trafficCacheTime,
    }),
    lineChartgraphData =
      historyStatus === 'success' &&
      summaryStatus === 'success' &&
      transformGraphData(historyApiResponse?.data?.data?.data),
    historyGraphData =
      historyStatus === 'success' &&
      summaryStatus === 'success' &&
      historyApiResponse?.data?.data?.data,
    graphDataLength = historyApiResponse?.data?.data?.data?.length
  useEffect(() => {
    if (historyGraphData) {
      setGraphState((prevState) => ({
        ...prevState,
        unformattedGraphData: historyGraphData,
      }))
    }
  }, [historyGraphData])
  const csvDownloadOptions: DownloadOptionsType = [
    {
      linkName: `${csvTitle} ${c('overview')}`,
      csvName: `${csvTitle} ${c('overview')}`,
      csvFormat: {
        api: (csvParams) =>
          getOrPostAPI(
            !doCustomCategoriesExist,
            rankTrackerHistoryApi,
            csvParams,
          ).then((response) => {
            return response.data
          }),
        params: {
          ...historyParams,
          csv_name: `${csvTitle} ${c('overview')}`,
          ...(pageName === 'brands' ? { async: true } : {}),
        },
        ...(pageName === 'brands' && {
          callout: csvToastCallout(),
        }),
      },
    },
  ]

  const color = !summaryData?.est_organic_clicks?.change
    ? 'chart-standard-purple'
    : summaryData?.est_organic_clicks?.change < 0
      ? 'red'
      : 'green'

  const updateFilters = useCallback(
    (filter: filterType) => {
      rankTrackerDispatch({
        type: 'UPDATE_FILTER',
        payload: {
          filters: filter,
        },
      })
    },
    [rankTrackerDispatch],
  )

  const resetFilter = useCallback(() => {
    rankTrackerDispatch({ type: 'RESET_FILTER' })
  }, [rankTrackerDispatch])

  const isKeywordsWithProducts = summaryEntity === 'Keywords with Products'

  const keywordsInTop4 = isKeywordsWithProducts
    ? summaryData?.keywords_with_products_in_top_4?.value
    : summaryData?.keywords_in_top_4?.value
  const keywordsInTop4Change = isKeywordsWithProducts
    ? summaryData?.keywords_with_products_in_top_4?.change
    : summaryData?.keywords_in_top_4?.change
  const keywordsInTop4PctChange = isKeywordsWithProducts
    ? summaryData?.keywords_with_products_in_top_4?.pct_change
    : summaryData?.keywords_in_top_4?.pct_change
  const keywordsOnPage1 = isKeywordsWithProducts
    ? summaryData?.keywords_with_products_on_page_1?.value
    : summaryData?.keywords_on_page_1?.value
  const keywordsOnPage1Change = isKeywordsWithProducts
    ? summaryData?.keywords_with_products_on_page_1?.change
    : summaryData?.keywords_on_page_1?.change
  const keywordsOnPage1PctChange = isKeywordsWithProducts
    ? summaryData?.keywords_with_products_on_page_1?.pct_change
    : summaryData?.keywords_on_page_1?.pct_change

  const defaultTopMetrics = useMemo(
      () => [
        ...(pageName === 'ProductRankings' || pageName === 'brands'
          ? ['est_organic_clicks']
          : enableKeywordsPageChanges && pageName === 'keywordsWithProducts'
            ? ['total_sales', 'daily_clicks']
            : ['total_sales', 'est_organic_clicks']),
      ],
      [enableKeywordsPageChanges, pageName],
    ),
    defaultCustomMetrics = useMemo(
      () => [
        ...(pageName === 'brands'
          ? ['est_organic_clicks']
          : [
              ...(pageName === 'keywordsWithProducts' &&
              enableKeywordsPageChanges
                ? []
                : ['organic_click_share']),
              ...(pageName === 'keywordsWithProducts'
                ? [
                    'keywords_with_products_in_top_4',
                    'keywords_with_products_on_page_1',
                    ...(enableKeywordsPageChanges
                      ? ['daily_search_query_volume', 'clicks_share']
                      : []),
                  ]
                : pageName === 'ProductRankings'
                  ? ['products_in_top_4', 'products_on_page_1']
                  : ['keywords_in_top_4', 'keywords_on_page_1']),

              'cost',
            ]),
      ],
      [enableKeywordsPageChanges, pageName],
    ),
    customMetricOptions = useMemo(
      () => [
        ...(pageName === 'brands'
          ? ['keywords_in_top_4', 'keywords_on_page_1']
          : [...defaultCustomMetrics, 'attributed_sales', 'conversion_rate']),
      ],
      [defaultCustomMetrics, pageName],
    )
  const [graphState, setGraphState] = useState<GraphStateType>({
    graphData: { dataSet: [], max: 0 },
    unformattedGraphData: [],
    metaEndDate: '',
    comparisonDates: {
      startDate: '',
      endDate: '',
    },
    totalsData: [],
  })
  const { unformattedGraphData, comparisonDates, metaEndDate, totalsData } =
    graphState

  useEffect(() => {
    if (summaryApiResponse) {
      const dates = summaryApiResponse?.data?.data?.metadata
      setGraphState((prevState) => ({
        ...prevState,
        metaEndDate: dates?.end_date,
        comparisonDates: {
          startDate: dates?.previous_start_date,
          endDate: dates?.previous_end_date,
        },
      }))
    }
  }, [summaryApiResponse])

  const [updatedSummaryData, setUpdatedSummaryData] = useState({})
  useEffect(() => {
    // Some metrics data are not available for prospects brand
    const data = [
      {
        total_sales: {
          value: null,
          pct_of_total: null,
          comparison: null,
          change: null,
          pct_change: null,
          type: 'currency',
          display: 'Total Sales',
        },
        attributed_sales: {
          value: null,
          pct_of_total: null,
          comparison: null,
          change: null,
          pct_change: null,
          type: 'currency',
          display: 'Ad Sales',
        },
        cost: {
          value: null,
          pct_of_total: null,
          comparison: null,
          change: null,
          pct_change: null,
          type: 'currency',
          display: 'Ad Spend',
        },
        conversion_rate: {
          value: null,
          pct_of_total: null,
          comparison: null,
          change: null,
          pct_change: null,
          type: 'percentage',
          display: 'Ad Conversion Rate',
        },
      },
    ]
    if (
      summaryData &&
      (customer.vendor_id === null || searchQueryParams?.brand_id === 'null')
    ) {
      const newData = Object.assign(summaryData, data[0])
      setUpdatedSummaryData(newData)
    } else {
      setUpdatedSummaryData(summaryData)
    }
  }, [summaryData, customer.vendor_id, searchQueryParams?.brand_id])
  // Total //
  const { isLoading: totalsDataLoading } = useQuery({
    queryKey: ['totalsData', rankTrackerHistoryApi, historyParams],
    queryFn: ({ signal }) => {
      const params = { ...historyParams, category_report: undefined }
      return getOrPostAPI(
        !doCustomCategoriesExist,
        rankTrackerHistoryApi,
        params,
        signal,
      ).then((response) => {
        setGraphState((prevState) => ({
          ...prevState,
          totalsData: response.data.data.data,
        }))
      })
    },
    enabled: isShowCategoriesToggleEnabled && showCategories,
    gcTime: trafficCacheTime,
  })

  // Aggregate Summary//
  const aggregateSummaryUrl = `${api}/category_summary/`
  const {
      data: aggregateSummaryResponse,
      status: aggregateSummaryStatus,
      isLoading: aggregateSummaryLoading,
    } = useQuery({
      queryKey: ['aggregate-data', aggregateSummaryUrl, summaryParams],
      queryFn: ({ signal }) =>
        getOrPostAPI(
          !doCustomCategoriesExist,
          aggregateSummaryUrl,
          summaryParams,
          signal,
        ),
      enabled: isShowCategoriesToggleEnabled && showCategories,
      gcTime: trafficCacheTime,
    }),
    aggregateData =
      aggregateSummaryStatus === 'success' &&
      aggregateSummaryResponse?.data?.data?.data
  const [isImageCapture, setIsImageCapture] = useState(false),
    [isSquare, setIsSquare] = useState(false),
    Downloads = () =>
      CsvButtonHelper({
        pngProps: {
          setIsImageCapture,
          setIsSquare,
          DOWNLOAD_BUTTON_ID,
        },
        anythingToDownload: true,
        csvDownloadOptions: csvDownloadOptions,
      })
  const {
    customMetricCallout,
    showEventMarker,
    showSingleYAxis,
    selectedGraphMetrics,
    userSelectedMetrics,
    updateGraphMetrics,
    graphData,
    showCategories: areCategoriesShowing,
  } = useHeaderGraphMetrics({
    defaultTopMetrics,
    defaultCustomMetrics,
    unformattedGraphData,
    summaryData: updatedSummaryData !== null ? updatedSummaryData : summaryData,
    userSettingsKey: customGraphUserSettingKey,
    singleDefaultTopMetric: defaultTopMetrics[0],
    totalsData: totalsData ? totalsData : undefined,
    viewFlags: {
      showCategoriesToggle: isShowCategoriesToggleEnabled,
    },
  })
  useEffect(() => {
    setShowCategories(areCategoriesShowing)
  }, [areCategoriesShowing])
  return (
    <PngImage
      pngProps={{
        isImageCapture,
        setIsImageCapture,
        replaceId: DOWNLOAD_BUTTON_ID,
        isSquare,
      }}
    >
      {showNewHeaderMetric ? (
        <TrafficHeaderData
          allowedToggles={
            isShowCategoriesToggleEnabled
              ? ['EVENT_MARKER', 'SINGLE_AXIS', 'ALL_CATEGORIES']
              : undefined
          }
          downloads={csvDownloadOptions}
          tooltipContent={
            pageName === 'brands' ? undefined : rankTrackerTooltipData
          }
          filter={
            allowFilter && (
              <RankTrackerFilter
                filterState={rankTrackerState}
                update={updateFilters}
                resetCallout={resetFilter}
                acceptedFilters={acceptedFilters}
              />
            )
          }
          loading={
            summaryStatus === 'pending' ||
            historyStatus === 'pending' ||
            totalsDataLoading ||
            aggregateSummaryLoading
          }
          aggregatedOn={showCategories ? 'root_category_name' : undefined}
          graphData={graphData}
          data={updatedSummaryData !== null ? updatedSummaryData : summaryData}
          metaEndDate={metaEndDate}
          defaultCustomMetrics={defaultCustomMetrics}
          defaultTopMetrics={defaultTopMetrics}
          customMetricOptions={customMetricOptions}
          selectedMetrics={userSelectedMetrics}
          updateMetricsCallout={customMetricCallout}
          selectedGraphMetrics={selectedGraphMetrics}
          updateGraphMetricsCallout={updateGraphMetrics}
          showEventMarker={showEventMarker}
          comparisonDates={comparisonDates}
          otherMetricCount={otherMetricCount}
          isSpecialCornerCase={!showCategories}
          metricProps={ContentMetricPropsTooltipData}
          showSingleYAxis={showSingleYAxis}
          showCategoriesToggle={showCategories}
          aggregatedSummaryData={aggregateData ? aggregateData : undefined}
        />
      ) : (
        <div className='large-box-metric box'>
          <div className='flex justify-content-between pat-pr-4'>
            <div className='flex'>
              {keywordSummary ? (
                <div className='pat-p-4 bdrr bdrc-light-gray'>
                  {keywordSummary}
                </div>
              ) : null}
              <HeaderMetric
                key={'metric'}
                title={t('organicClicks')}
                value={summaryData?.est_organic_clicks?.value}
                checkboxColor='chart-dark-3-blue'
                showRadio={false}
                change={summaryData?.est_organic_clicks?.change}
                pctChange={summaryData?.est_organic_clicks?.pct_change}
                // TODO: Add a loading state here
                loading={false}
              />
            </div>
            <div
              id={DOWNLOAD_BUTTON_ID}
              className='flex align-items-center pat-gap-4'
            >
              <Downloads />
              <TooltipComponent
                tooltipContent={
                  <div className='flex flex-direction-column pat-gap-4'>
                    <span className='fs-18'>{tooltipData.title}</span>
                    <span>{tooltipData.text}</span>
                  </div>
                }
              >
                <Icon
                  icon='info'
                  className='cursor-pointer'
                  iconSize='20px'
                  color='dark-blue'
                />
              </TooltipComponent>
            </div>
          </div>
          <div className='bgc-white'>
            <HeaderMetricGroup
              data={[
                {
                  title: t('organicClickShare'),
                  value: summaryData?.organic_click_share?.value,
                  formatType: 'percentage',
                  change: summaryData?.organic_click_share?.change,
                  pctChange: summaryData?.organic_click_share?.pct_change,
                  decimalScale: 2,
                  truncateValues: true,
                },
                {
                  title: t('entityInTopFour', {
                    summaryEntity: summaryEntity,
                  }),
                  value: keywordsInTop4,
                  change: keywordsInTop4Change,
                  pctChange: keywordsInTop4PctChange,
                  metricIcon: 'cup',
                  truncateValues: true,
                },
                {
                  title: t('entityOnPageOne', {
                    summaryEntity: summaryEntity,
                  }),
                  value: keywordsOnPage1,
                  change: keywordsOnPage1Change,
                  pctChange: keywordsOnPage1PctChange,
                  truncateValues: true,
                },
              ]}
              // TODO: Add a loading state here
              loading={false}
            />
          </div>
          <div className='graph-section large pat-p-4 bgc-white'>
            {summaryStatus !== 'pending' &&
              graphDataLength !== 0 &&
              graphData && (
                <>
                  <ResponsiveContainer width='99%' height={250}>
                    <LineChart
                      data={lineChartgraphData ? lineChartgraphData : []}
                    >
                      <CartesianGrid vertical={false} />
                      <Tooltip
                        content={
                          <SparkLineCustomTooltip
                            className='basic'
                            active={false}
                            label=''
                            payload={[]}
                          />
                        }
                        cursor
                      />
                      <YAxis
                        dataKey='est_organic_clicks'
                        axisLine={false}
                        tickLine={false}
                        tickCount={5}
                        tickMargin={5}
                        width={44}
                        orientation='right'
                        tickFormatter={(t) => {
                          const tick = abbreviateNumber(
                            Math.abs(t),
                          ).toLocaleString()
                          return tick
                        }}
                        style={{
                          fontSize: '12px',
                          fill: 'rgb(116, 121, 157)',
                        }}
                        type='number'
                        domain={['auto', 'auto']}
                      />
                      <XAxis
                        dataKey='date'
                        axisLine={false}
                        tickCount={8}
                        domain={[
                          (dataMin: number) => dataMin,
                          (dataMax: number) => dataMax,
                        ]}
                        type='category'
                        interval='preserveStart'
                        height={45}
                        tickLine={false}
                        tickMargin={10}
                        tick={
                          // @ts-expect-error LineTooltip is a .js component. TS is expecting more props here, but they are optional and not needed.
                          <TwoLineDateLabel />
                        }
                      />
                      <Line
                        dataKey='est_organic_clicks'
                        stroke={`var(--${color})`}
                        strokeWidth={4}
                        dot={false}
                        fillOpacity={1}
                        fill='var(--white)'
                        connectNulls
                        activeDot={{
                          stroke: `var(--${color})`,
                          fill: 'var(--white)',
                          strokeWidth: 4,
                          r: 4.6,
                        }}
                        isAnimationActive={!isImageCapture}
                      />
                    </LineChart>
                  </ResponsiveContainer>
                  <div className='graph-legend flex justify-content-center pat-mt-4'>
                    <div className='legend-single-stat no-stat flex'>
                      <div className={`line bgc-${color}`} />
                      <div className='text'>{t('estOrganicClick')}</div>
                    </div>
                  </div>
                </>
              )}
            {summaryStatus === 'pending' && <GraphLoading />}
            {summaryStatus !== 'pending' && graphDataLength === 0 && (
              <div
                className='flex justify-content-center align-items-center'
                style={{ height: '280px' }}
              >
                <EmptyState primaryText={c('noDataFound')} />
              </div>
            )}
          </div>
        </div>
      )}
    </PngImage>
  )
}

export default RankGraphData
