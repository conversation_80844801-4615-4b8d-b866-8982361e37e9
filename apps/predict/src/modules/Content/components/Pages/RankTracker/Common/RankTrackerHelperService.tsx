import { useContext, useMemo } from 'react'
import moment from 'moment'

import { useComparisonDates } from '../../../../../../common/hooks/CustomHooks'
import { ThemeContext } from '../../../../../../Context'

export const getOrganicClickShareChange = (change: number): number => {
  return !isNaN(change) && Math.abs(change) < 0.01 ? 0 : change
}

export const getPreviousDay = (
  date: Date | string | undefined,
  timeOfDay?: string,
) => {
  const previousDay = moment(date).subtract(1, 'day')
  let value
  switch (timeOfDay) {
    case 'start':
      value = previousDay?.startOf('day')
      break
    case 'end':
      value = previousDay?.endOf('day')
      break
    default:
      value = previousDay
      break
  }
  return value?.format()
}

export const useRankTrackerDates = () => {
  const { timeframe } = useContext(ThemeContext)
  const currentDay = useMemo(() => {
    return new Date()
  }, [])

  const previousDates = useComparisonDates()

  const isCurrentDayFilter =
      timeframe.type === 'current' && timeframe.timeValue === 'day',
    IsFirstDayOfCurrentMonthFilter =
      timeframe.type === 'current' &&
      timeframe.timeValue === 'month' &&
      currentDay.getDate() === 1

  const rankTrackerData = useMemo(() => {
    return {
      newStartDate: getPreviousDay(currentDay, 'start'),
      newEndDate: getPreviousDay(currentDay, 'end'),
      comparisonStartDate: getPreviousDay(previousDates?.startDate),
      comparisonEndDate: getPreviousDay(previousDates?.endDate),
      currentTimeframeCheck:
        isCurrentDayFilter || IsFirstDayOfCurrentMonthFilter,
      previousDates: previousDates,
    }
  }, [
    IsFirstDayOfCurrentMonthFilter,
    currentDay,
    isCurrentDayFilter,
    previousDates,
  ])
  return rankTrackerData
}
