import React from 'react'
import moment from 'moment'
import { <PERSON>, Line<PERSON>hart, ResponsiveContainer, YA<PERSON>s } from 'recharts'
import { Button, PopoverNew, useMediaQuery } from '@patterninc/react-ui'
import { c } from '@predict-services'

import { type DownloadOptionsType } from '../../../../../../../common/services/CsvHelpers'
import styles from './_rank-tracker-sparkline.module.scss'
import SparklinePopover from './SparklinePopover'

type RankTrackerSparklineProps = EstOrganicClicksData | OrganicRankData

type RankTrackerSparklinePropsBase = {
  title: string
  topValue: number
  dataKey: string
  showTrophy?: boolean
  reversed?: boolean
  min?: number
  max?: number
  brand?: string
  asin?: number
  productTitle?: string
  marketplace?: string
  isDisplayCsvDownloadOption?: boolean
  csvData?: { [key: string]: unknown }[]
  csvTitle?: string
  linkName?: string
  sparklineText?: string
  keyword?: string
}

type EstOrganicClicksData = RankTrackerSparklinePropsBase & {
  graphData: {
    date: number
    est_organic_clicks?: number
    avg_previous?: number
    organic_rank?: never
    avg_for_comparison_dates?: number
  }[]
}

type OrganicRankData = RankTrackerSparklinePropsBase & {
  graphData: {
    date: number
    est_organic_clicks?: number
    avg_previous?: number
    organic_rank: number
    avg_for_comparison_dates?: number
  }[]
}

const BLUE = 'var(--chart-standard-blue)'
const LIGHTBLUE = 'var(--chart-light-2-blue)'

const RankTrackerSparkline = ({
  title,
  topValue,
  dataKey,
  graphData,
  showTrophy,
  reversed,
  min,
  max,
  asin,
  productTitle,
  brand,
  marketplace,
  isDisplayCsvDownloadOption,
  csvData,
  csvTitle,
  linkName,
  sparklineText,
  keyword,
}: RankTrackerSparklineProps): React.JSX.Element => {
  const rankTrackerCsvData = () => {
    if (csvData) {
      return csvData
    }
    const defaultObj = {
      Date: null,
      Brand: null,
      'Est Organic Clicks': null,
      keyword: null,
      'Avg For Comparison Dates': null,
    }

    const result = graphData.map((g) => {
      const csvRow = Object.create(defaultObj)
      csvRow['Date'] = g?.date ? moment.utc(g.date).format('YYYY-MM-DD') : ''
      csvRow['Brand'] = brand
      keyword && (csvRow['keyword'] = keyword)
      asin && (csvRow['ASIN'] = asin)
      productTitle && (csvRow['Product Title'] = productTitle)
      csvRow['Est Organic Clicks'] = Math.round(Number(g.est_organic_clicks))
      g?.avg_for_comparison_dates &&
        (csvRow[
          `${sparklineText ? sparklineText : 'Avg'} For Comparison Dates`
        ] = g?.avg_for_comparison_dates)
      return csvRow
    })
    result.push(defaultObj)
    return result
  }
  const screenIsMdMin = useMediaQuery({ type: 'max', breakpoint: 'md' })
  const csvDownloadOptions: DownloadOptionsType = [
    {
      linkName: linkName || 'Product Rank Tracker',
      csvName: csvTitle || `Est Organic Clicks - ASIN ${asin} - ${marketplace}`,
      hiddenClass: 'rank-tracker-csv',
      csvData: rankTrackerCsvData(),
      callout: (element) =>
        element && typeof element !== 'string' && element.click(),
    },
  ]

  return (
    <PopoverNew
      position='top'
      maxWidth='500'
      popoverContent={
        <SparklinePopover
          title={title}
          topValue={topValue}
          dataKey={dataKey}
          showTrophy={showTrophy}
          reversed={reversed}
          min={min}
          max={max}
          graphData={graphData}
          sparklineText={sparklineText}
          {...(isDisplayCsvDownloadOption
            ? {
                displayCsvDownloadOption: true,
                csvDownloadOptions,
              }
            : { displayCsvDownloadOption: false })}
        />
      }
      noPadding
    >
      {({ visible, setVisible }) => (
        <div className='text-center cursor-pointer'>
          {/* Spark line - dumbed down version of the graph within this Tippy popover */}
          <ResponsiveContainer
            width={screenIsMdMin ? '95%' : '100%'}
            height={50}
          >
            <LineChart
              width={300}
              height={100}
              data={graphData}
              className={styles.container}
            >
              <YAxis
                domain={['auto', 'auto']}
                type='number'
                reversed={reversed}
                hide
              />
              <Line
                dataKey={dataKey}
                stroke={BLUE}
                strokeWidth={2}
                dot={
                  graphData.length === 1
                    ? {
                        fill: BLUE,
                        strokeWidth: 1,
                        r: 2,
                      }
                    : false
                }
              />
              <Line
                dataKey={
                  sparklineText === 'Median'
                    ? 'median_for_comparison_dates'
                    : 'avg_for_comparison_dates'
                }
                stroke={LIGHTBLUE}
                dot={false}
                strokeDasharray='5,5'
              />
            </LineChart>
          </ResponsiveContainer>
          <Button
            styleType='text-blue'
            className={graphData.length === 1 ? 'pat-mt-2' : ''}
            onClick={() => setVisible(!visible)}
          >
            {c('view')}
          </Button>
        </div>
      )}
    </PopoverNew>
  )
}

export default RankTrackerSparkline
