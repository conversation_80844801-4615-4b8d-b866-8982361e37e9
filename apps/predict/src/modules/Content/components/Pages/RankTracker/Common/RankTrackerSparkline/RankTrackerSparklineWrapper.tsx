import React, { useCallback, useEffect, useMemo, useState } from 'react'
import { useQuery } from '@tanstack/react-query'
import moment from 'moment'
import { Button, getApiUrlPrefix, PopoverNew } from '@patterninc/react-ui'

import { c, SecureAxios, t } from '../../../../../../../common/services'
import { type DownloadOptionsType } from '../../../../../../../common/services/CsvHelpers'
import { trafficCacheTime } from '../../../../../../Traffic/services/TrafficHelperService'
import SparklinePopover, {
  type EstOrganicClicksData,
  type OrganicRankData,
} from './SparklinePopover'

type RankTrackerSparklineWrapperProps = {
  title: string
  topValue: number
  dataKey: string
  showTrophy?: boolean
  reversed?: boolean
  min?: number
  max?: number
  brand?: string
  asin?: string
  productTitle?: string
  marketplace?: string
  isDisplayCsvDownloadOption?: boolean
  csvData?: { [key: string]: unknown }[]
  csvTitle?: string
  linkName?: string
  sparklineText?: string
  keyword?: string
  params?: { [key: string]: unknown }
  marketProductId?: string
  customerId?: number
  apiEndpoint: string
  type?: string
  marketplaceIds?: string
  brandId?: number
  countryCode?: string
}

type apiParamsState = {
  [key: string]: unknown
}

type clickbyDayType = {
  report_date?: number
  date: string
  est_organic_clicks?: number
  avg_previous?: number
  organic_rank: number
  avg_for_comparison_dates?: number
  comparison_median_organic_rank?: number
  'Avg For Comparison Dates'?: number
  previous_median_rank?: number
  comp_median_rank?: number
}

const RankTrackerSparklineWrapper = ({
  title,
  topValue,
  dataKey,
  showTrophy,
  reversed,
  min,
  max,
  asin,
  productTitle,
  brand,
  marketplace,
  isDisplayCsvDownloadOption,
  csvData,
  csvTitle,
  linkName,
  sparklineText,
  keyword,
  params,
  marketProductId,
  apiEndpoint,
  type = 'product',
  marketplaceIds,
  brandId,
  countryCode,
}: RankTrackerSparklineWrapperProps): React.JSX.Element => {
  const [graphData, setGraphData] = useState<
    EstOrganicClicksData[] | OrganicRankData[]
  >([])
  const rankTrackerCsvData = useCallback(() => {
    if (csvData) {
      return csvData
    }
    const defaultObj = {
      Date: null,
      ...(asin ? { ASIN: null } : {}),
      ...(brand ? { Brand: null } : {}),
      ...(productTitle ? { 'Product Title': null } : {}),
      ...(keyword ? { Keyword: null } : {}),
      ...(graphData?.[0]?.organic_rank ? { 'Organic Rank': null } : {}),
      'Est Organic Clicks': null,

      ...(graphData?.[0]?.previous_median_rank
        ? { 'Median For Comparison Dates': null }
        : {}),
      'Avg For Comparison Dates': null,
    }

    const result = graphData?.map((g) => {
      const csvRow = Object.create(defaultObj)
      csvRow['Date'] = g?.date ? moment.utc(g.date).format('YYYY-MM-DD') : ''
      csvRow['Brand'] = brand
      keyword && (csvRow['keyword'] = keyword)
      asin && (csvRow['ASIN'] = asin)
      g.organic_rank && (csvRow['Organic Rank'] = g.organic_rank)
      productTitle && (csvRow['Product Title'] = productTitle)
      g?.est_organic_clicks &&
        (csvRow['Est Organic Clicks'] = Math.round(
          Number(g?.est_organic_clicks),
        ))
      g?.previous_median_rank &&
        (csvRow['Median For Comparison Dates'] = Math.round(
          Number(g?.previous_median_rank),
        ))
      g?.avg_for_comparison_dates &&
        (csvRow[
          `${sparklineText ? sparklineText : 'Avg'} For Comparison Dates`
        ] = g?.avg_for_comparison_dates)

      g?.median_for_comparison_dates &&
        (csvRow[
          `${sparklineText ? sparklineText : 'Avg'} For Comparison Dates`
        ] = g?.median_for_comparison_dates)
      return csvRow
    })

    return result
  }, [asin, brand, csvData, graphData, keyword, productTitle, sparklineText])

  const csvDownloadOptions: DownloadOptionsType = [
    {
      linkName: linkName || t('content:productRankTracker'),
      csvName: csvTitle || `Est Organic Clicks - ASIN ${asin} - ${marketplace}`,
      hiddenClass: 'rank-tracker-csv',
      csvData: rankTrackerCsvData(),
      callout: (element) =>
        element && typeof element !== 'string' && element.click(),
    },
  ]

  const [apiParams, setApiParams] = useState<apiParamsState>()

  useEffect(() => {
    if (params) {
      const newParams = { ...params }
      delete newParams.sort
      delete newParams.marketplace_ids
      delete newParams.search_for
      setApiParams(newParams)
    }
  }, [params, marketProductId, type])

  const commonParams = useMemo(() => {
    return {
      ...apiParams,
      ...(brandId ? { brand_Id: brandId } : {}),
      ...(countryCode ? { country_code: countryCode } : {}),
      'marketplace_ids[]': marketplaceIds,
      ...(marketProductId
        ? {
            ...(type === 'product'
              ? {
                  market_product_id: marketProductId,
                }
              : {
                  keyword: marketProductId,
                }),
          }
        : {}),
    }
  }, [apiParams, brandId, countryCode, marketProductId, marketplaceIds, type])

  const rankTrackerSparklineApi = `${getApiUrlPrefix(
      'rank-tracker',
    )}/${apiEndpoint}`,
    {
      status: graphDataStatus,
      isLoading: refetchLoading,
      data: graphDataNew,
      refetch,
    } = useQuery({
      queryKey: [apiParams, marketProductId],
      queryFn: ({ signal }) => {
        return SecureAxios.get(rankTrackerSparklineApi, {
          params: commonParams,
          signal,
        })
      },
      gcTime: trafficCacheTime,
      enabled: false,
    })

  useEffect(() => {
    if (graphDataStatus === 'success' && graphDataNew?.data?.data?.data) {
      const rankTrackerSparkData =
        graphDataStatus === 'success' && type === 'rankedKeywords'
          ? graphDataNew?.data?.data?.data?.[0]?.ranks
          : graphDataNew?.data?.data?.data?.[0]?.clicks_by_day
      if (type === 'rankedKeywords') {
        const clickByDay = rankTrackerSparkData?.map(
          (keyword: clickbyDayType) => {
            return {
              organic_rank: keyword?.organic_rank,
              est_organic_clicks: keyword?.est_organic_clicks,
              date: keyword?.date,
              median_for_comparison_dates: Number(keyword?.comp_median_rank),
            }
          },
        )
        setGraphData(clickByDay)
      } else {
        const clickByDay = rankTrackerSparkData?.map((g: clickbyDayType) => {
          return {
            date: Date.parse(g.date),
            ...(g?.organic_rank
              ? {
                  organic_rank: Math.round(g?.organic_rank ?? 0),
                }
              : {}),
            est_organic_clicks: Math.round(g.est_organic_clicks ?? 0),
            avg_for_comparison_dates: g?.avg_previous
              ? Math.round(g?.avg_previous ?? 0)
              : Math.round(g?.['Avg For Comparison Dates'] ?? 0),

            ...(g?.previous_median_rank
              ? {
                  median_for_comparison_dates: Math.round(
                    g?.previous_median_rank ?? 0,
                  ),
                }
              : {}),
          }
        })
        setGraphData(clickByDay)
      }
    }
  }, [graphDataNew, graphDataStatus, type])
  return (
    <PopoverNew
      position='top'
      popoverContent={
        <SparklinePopover
          title={title}
          topValue={topValue}
          dataKey={dataKey}
          showTrophy={showTrophy}
          reversed={reversed}
          min={min}
          max={max}
          loading={refetchLoading}
          graphData={graphData ?? []}
          sparklineText={sparklineText}
          {...(isDisplayCsvDownloadOption
            ? {
                ...(graphData?.length > 0
                  ? {
                      displayCsvDownloadOption: true,
                      csvDownloadOptions,
                    }
                  : {
                      displayCsvDownloadOption: false,
                    }),
              }
            : { displayCsvDownloadOption: false })}
        />
      }
      noPadding
      maxWidth='500'
    >
      {({ visible, setVisible }) => (
        <div className='text-center cursor-pointer'>
          <Button
            onClick={() => {
              setVisible(!visible)
              refetch()
            }}
            styleType='text-blue'
            className={graphData?.length === 1 ? 'pat-mt-2' : ''}
          >
            {c('view')}
          </Button>
        </div>
      )}
    </PopoverNew>
  )
}

export default RankTrackerSparklineWrapper
