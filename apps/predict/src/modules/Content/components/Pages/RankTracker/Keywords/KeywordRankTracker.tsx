import React, {
  useCallback,
  useContext,
  useEffect,
  useMemo,
  useState,
} from 'react'
import { useLocation } from 'react-router-dom'
import { useInfiniteQuery, useQuery } from '@tanstack/react-query'
import {
  type ActiveTableFilterObj,
  getApiUrlPrefix,
  Icon,
  <PERSON>dash<PERSON>he<PERSON>,
  PageFooter,
  Picker,
  type SortColumnProps,
  standardSortParams,
  StandardTable,
  Tabs,
  Tooltip,
  useIsMounted,
  useMediaQuery,
  useToggle,
} from '@patterninc/react-ui'
import { useDecodedUserTokenId } from '@predict-hooks'
import {
  c,
  csvToastCallout,
  type DownloadOptionsType,
  getUserSettingEndpoint,
  parseParams,
  saveCustomization,
  SecureAxios,
  tryLocalStorageParse,
  useTranslate,
} from '@predict-services'
import { convertTableHeaderCount } from 'src/modules/Traffic/services/TrafficTableHeaderHelperService'
import { useGetCustomCategoryIds } from 'src/common/components/GlobalFilter/GlobalFilterHelpers'
import {
  brandedKeywordsActionConstant,
  keywordData,
} from 'src/modules/Settings/components/Pages/Advertising/Pages/Brand/Tabs/SettingsAdBrandedKeywordsTab'
import AddKeywordsSideDrawer, {
  type SideDrawerProps,
} from 'src/modules/Settings/components/Pages/Advertising/Pages/NegativeKeywords/AddKeywordsSideDrawer'
import { type Marketplace } from '@predict-types'
import { getGlobalMarketplaces } from 'src/modules/Settings/services/MarketplacesService'
import { getFilteredIds } from 'src/modules/Traffic/hooks/useCommonAPIParams'
import Divider from 'src/common/components/Divider/Divider'

import ListChangeData from '../../../../../../common/components/ListChangeData/ListChangeData'
import {
  type ActiveFilterAPIProps,
  applySecondaryFilterFunction,
  CustomFilterHelper,
  customFilterParams,
  RemoveFilter,
  removeFilterFromAPIState,
  removeFilterFromState,
  RemoveFilterState,
} from '../../../../../../common/components/Tables/CustomFilterHelper'
import SecondaryFilter, {
  type ActiveFilterProps,
  type numberProps,
  type SelectProps,
} from '../../../../../../common/components/Tables/SecondaryFilter'
import { getSelectedConfig } from '../../../../../../common/services/TableConfigService'
import { ThemeContext } from '../../../../../../Context'
import { useUser } from '../../../../../../context/user-context'
import {
  decimalScaleHelper,
  getOrPostAPI,
  trafficCacheTime,
} from '../../../../../Traffic/services/TrafficHelperService'
import { getContentTableHeaderTooltip } from '../../../Common/ContentTooltipHelper'
import RankGraphData from '../Common/RankGraphData/RankGraphData'
import { useRankTrackerDates } from '../Common/RankTrackerHelperService'
import RankTracker from '../Products/RankTracker'
import { RankTrackerContext } from '../RankTrackerContext'
import RankTrackerCategoryTable from '../Common/RankTrackerCategoryTable'
import { useLanguageParams } from '../../../Common/ContentHooks'
import { type DataObj } from './KeywordsTableConfig'
import config from './KeywordsTableConfig'
type filterOptions = {
  id: number
  text: string
  value: number
}
const stickyTableConfig = {
  right: 1,
}

type StateType = {
  search: string
}

type customSelectionCalloutProps = {
  name: string
  options?: ArrOption[]
}
type ArrOption = {
  label: string
  name: string
}

const tabOptions = [
  {
    id: 0,
    text: c('allKeywords'),
    value: 0,
  },
  {
    id: 1,
    text: c('branded'),
    value: 1,
  },
  {
    id: 2,
    text: c('nonBranded'),
    value: 2,
  },
]

const KeywordRankTracker = (): React.JSX.Element => {
  const enableKeywordsPageChanges = useToggle(
    'enable_rank_tracker_keywords_page_changes',
  )
  const [activeTab, setActiveTab] = useState(0)
  const [keywordType, setKeywordType] = useState(0)
  const [optionSelected, setOptionSelected] = useState<
    filterOptions | undefined
  >(tabOptions[0])
  const screenIsMdMax = useMediaQuery({ type: 'max', breakpoint: 'md' })
  const [actionsSidedrawer, setActionSidedrawer] = useState<SideDrawerProps>({
    type: '',
    isOpen: false,
    keywordData: keywordData,
  })
  const [checkedBoxes, setCheckedBoxes] = useState<DataObj[]>([])
  const {
      updateBreadcrumbs,
      customer,
      startDate,
      endDate,
      marketplaceIds,
      tags,
      secondRange_startDate,
      secondRange_endDate,
      prevCustomer,
      timeframe,
      allMarketplaces,
      regions: selectedRegions,
    } = useContext(ThemeContext),
    { t } = useTranslate('content'),
    { t: s } = useTranslate('traffic'),
    { t: k } = useTranslate('keywords'),
    { state: rankTrackerState } = useContext(RankTrackerContext),
    aggregate_by = timeframe.aggregation,
    [state, setState] = useState<StateType>({
      search: '',
    }),
    { search } = state,
    tableId = 'keyword_rank_tracker',
    [sortBy, setSortBy] = useState(
      tryLocalStorageParse(`sort_by_${tableId}`) ?? {
        prop:
          enableKeywordsPageChanges && activeTab === 1
            ? 'keyword_est_revenue_comp'
            : 'est_organic_clicks',
        flip: false,
        isByChange: false,
      },
    ),
    isMounted = useIsMounted()
  const { pathname, search: searchParams } = useLocation(),
    selectedBrandName = new URLSearchParams(searchParams).get('customer_name'),
    selectedBrandId = new URLSearchParams(searchParams).get('brand_id'),
    searchQueryParams = parseParams(searchParams)

  // Secondary Filter
  const [secondaryFiltersback, setSecondaryFiltersback] =
    useState<ActiveFilterProps>({})

  const [secondaryFiltersAPI, setSecondaryFiltersAPI] =
    useState<ActiveFilterAPIProps>({})
  const [responseType, setResponseType] = useState<string>('get')

  const SecondaryFilterCallback = useCallback((filter: ActiveFilterProps) => {
    setSecondaryFiltersback((prevState) => ({
      ...prevState,
      ...filter,
    }))
  }, [])
  const customCategories = useGetCustomCategoryIds()
  const doCustomCategoriesExist = customCategories?.length > 0

  const userId = useDecodedUserTokenId()
  const CustomFilterTableAPIendpoint =
    'content:rankTracker:products:customFilterTable'
  const { endpoint: customFilterUserSettingUrl } = getUserSettingEndpoint(
      CustomFilterTableAPIendpoint,
    ),
    { status: customFilterStatus, data: customGetFilterApiResponse } = useQuery(
      {
        queryKey: [customFilterUserSettingUrl, customer?.vendor_id],
        queryFn: ({ signal }) =>
          SecureAxios.get(customFilterUserSettingUrl, { signal }),
        gcTime: trafficCacheTime,
      },
    )

  const columnSettingsData =
    customFilterStatus === 'success' && customGetFilterApiResponse?.data

  const ProspectBrandNotSupportedList: Array<string> = useMemo(() => {
    return [
      'Ad Sales',
      'Ad Spend',
      'Ad Conversion Rate',
      'ACoS',
      'ROAS',
      'CPC',
      'Click Through Rate',
    ]
  }, [])

  useEffect(() => {
    if (columnSettingsData) {
      if (!customer.vendor_id) {
        const keys = Object.keys(columnSettingsData)
        const newdata = { ...columnSettingsData }
        let isSalesColumnExist = false
        keys?.forEach((item: string) => {
          const itemPresent = ProspectBrandNotSupportedList.includes(item)
          if (itemPresent) {
            isSalesColumnExist = true
            delete newdata?.[item]
          }
        })
        if (isSalesColumnExist) {
          setResponseType('post')
          setSecondaryFiltersAPI(newdata)
        } else {
          setResponseType('get')
          setSecondaryFiltersAPI(newdata)
        }
      } else {
        setResponseType('get')
        setSecondaryFiltersAPI(columnSettingsData)
      }
    }
  }, [ProspectBrandNotSupportedList, columnSettingsData, customer.vendor_id])

  useEffect(() => {
    if (responseType === 'post') {
      CustomFilterHelper({
        api: customFilterUserSettingUrl,
        data: secondaryFiltersAPI,
        type: 'custom-filter',
      })
    }
  }, [customFilterUserSettingUrl, responseType, secondaryFiltersAPI])

  const [filtersToReset, setFiltersToReset] = useState([''])
  const applySecondaryFilter = useCallback(
    (headerTitle?: string) => {
      const headerText = headerTitle?.replace('Filter ', '')
      const applySecondaryFilter = applySecondaryFilterFunction(
        secondaryFiltersback,
        headerText,
      )
      if (applySecondaryFilter !== null) {
        setSecondaryFiltersAPI((prevState) => ({
          ...prevState,
          ...applySecondaryFilter,
        }))
        setResponseType('post')
      }
      setFiltersToReset([headerTitle ?? ''])
    },
    [secondaryFiltersback],
  )

  const [tempSort, setTempSort] = useState('')

  const handleCustomFilterClose = useCallback(
    (headerText: string, secondaryFiltersAPI: ActiveFilterProps) => {
      setSecondaryFiltersback(secondaryFiltersAPI)
      setFiltersToReset([headerText])
      setTempSort(sortBy?.prop)
    },
    [sortBy?.prop],
  )
  const resetSecondaryFilter = useCallback(
    (headerText: string) => {
      const headerTextReplace = headerText?.replace('Filter ', '')
      const filters = { ...secondaryFiltersAPI }
      const tempFilters = { ...secondaryFiltersback }
      const resultFilters: ActiveFilterAPIProps = {}
      Object.keys(filters).forEach((ele) => {
        if (!ele.includes(headerTextReplace)) {
          resultFilters[ele] = filters[ele]
        }
      })
      const resultFiltersTemp: ActiveFilterAPIProps = {}
      Object.keys(tempFilters).forEach((ele) => {
        if (!ele.includes(headerTextReplace)) {
          resultFiltersTemp[ele] = filters[ele]
        }
      })
      setSortBy({
        prop:
          enableKeywordsPageChanges && activeTab === 1
            ? 'keyword_est_revenue_comp'
            : 'est_organic_clicks',
        flip: false,
        isByChange: false,
      })
      setSecondaryFiltersAPI(resultFilters)
      setSecondaryFiltersback(resultFiltersTemp)
      setResponseType('post')
    },
    [
      activeTab,
      enableKeywordsPageChanges,
      secondaryFiltersAPI,
      secondaryFiltersback,
    ],
  )
  const getSecondaryFilterObject = useCallback(
    (
      filterKey: string,
      tableKey: string,
      type: 'number' | 'currency' | 'percentage' = 'number',
      typeField?: string | undefined,
      selectProps?: SelectProps,
      numberProps?: numberProps,
      changeValueLabel?: string,
    ) => {
      return {
        headerText: filterKey,
        secondarySortProp: tempSort,
        closeCallout: (headerText: string) =>
          handleCustomFilterClose(headerText, secondaryFiltersAPI),
        onSortChange: (sortPropName: string) => setTempSort(sortPropName),
        callout: applySecondaryFilter,
        enableFilterByChangeValues: Boolean(changeValueLabel),
        resetCallback: changeValueLabel ? resetSecondaryFilter : undefined,
        disabled: !(
          (secondaryFiltersback?.[filterKey]?.text &&
            secondaryFiltersback?.[filterKey]?.value) ||
          (changeValueLabel &&
            secondaryFiltersback?.[changeValueLabel]?.text &&
            secondaryFiltersback?.[changeValueLabel]?.value)
        ),
        children: (
          <SecondaryFilter
            filterKey={filterKey}
            tableKey={tableKey}
            {...{ textFieldType: type }}
            prefix={type === 'currency' ? '$' : ''}
            suffix={type === 'percentage' ? '%' : ''}
            SecondaryFilterCallback={SecondaryFilterCallback}
            selectedSecondaryFilters={secondaryFiltersAPI}
            typeField={typeField}
            selectProps={selectProps}
            numberProps={numberProps}
            filtersToReset={filtersToReset}
            enableFilterByChangeValues={Boolean(changeValueLabel)}
          />
        ),
      }
    },
    [
      SecondaryFilterCallback,
      applySecondaryFilter,
      filtersToReset,
      handleCustomFilterClose,
      secondaryFiltersAPI,
      secondaryFiltersback,
      tempSort,
      resetSecondaryFilter,
    ],
  )

  const removeFilter = useCallback(
    (removeFilter?: string) => {
      const removedFilter = RemoveFilter(secondaryFiltersAPI, removeFilter)
      setSecondaryFiltersAPI(removedFilter)
      const removedFiltersState = RemoveFilterState(
        secondaryFiltersback,
        removeFilter,
      )
      setSecondaryFiltersback(removedFiltersState)
      setResponseType('post')
    },
    [secondaryFiltersAPI, secondaryFiltersback],
  )

  const removeFilters = useCallback(
    (removeFilter?: ActiveTableFilterObj) => {
      const removedFilter = removeFilterFromAPIState(
        secondaryFiltersAPI,
        removeFilter,
      )
      const removedFiltersState = removeFilterFromState(
        secondaryFiltersback,
        removeFilter,
      )
      setSecondaryFiltersAPI(removedFilter)
      setSecondaryFiltersback(removedFiltersState)
      setResponseType('post')
    },
    [secondaryFiltersAPI, secondaryFiltersback],
  )

  // default Columns
  const defaultColumns = [
    t('estOrganicClicks'),
    t('productsInTopFour'),
    t('productsOnPageOne'),
    t('brandsBestRank'),
    t('brandsOrganicShareClick'),
    t('percentageOfBrandOrganicClicks'),
    t('estDailySearchVolume'),
    t('estDailyClicks'),
    t('adSales'),
    c('adSpend'),
    t('adConversionRate'),
    c('source'),
    t('keywordType'),
  ]

  const defaultKeywordsColumns = [
    c('searchVolume'),
    t('brandsBestRank'),
    c('totalKeywordSales'),
    c('keywordSales'),
    k('totalClicks'),
    c('clicks'),
    c('perOfMyClicks'),
    s('clickShare'),
    t('totalPurchases'),
    s('purchases'),
    t('purchaseShare'),
    t('totalConversionRate'),
    c('conversionRate'),
    t('productsInTopFour'),
    t('productsOnPageOne'),
    c('adSpend'),
  ]
  const [selectionList, setSelectionList] = useState(
    enableKeywordsPageChanges ? defaultKeywordsColumns : defaultColumns,
  )
  const filterChangeValueDetails: { [key: string]: string } = useMemo(
    () => ({
      "Brand's Est. Organic Clicks": t('changeInBrandEstOrganicClicks'),
      'Products in Top 4': t('changeInProductsInTopFour'),
      'Products on Page 1': t('changeInProductsOnPage1'),
      'Best Organic Rank': t('changeInBrandsBestRank'),
      "Brand's Organic Click Share": t('changeInBrandsOrganicClick'),
      "% of Brand's Organic Clicks": t(
        'changeInPercentageOfBrandOrganicClicks',
      ),
      'Ad Sales': t('changeInAdSales'),
      'Ad Spend': t('changeInAdSpend'),
      'Ad Conversion Rate': t('changeInAdConversionRate'),
      ACoS: t('changeInAcos'),
      ROAS: t('changeInROAS'),
      CPC: t('changeInCPC'),
      'Click Through Rate': t('changeInClickThroughRate'),
      'Search Volume': c('changeInSearchVolume'),
      'Total Keyword Sales': c('changeInTotalKeywordSales'),
      'Total Impressions': t('changeInTotalImpressions'),
      Impressions: s('changeInImpressions'),
      'Impression Share': t('changeInImpressionShare'),
      'Total Clicks': k('changeInTotalClicks'),
      Clicks: c('changeInClicks'),
      'Total Click Through Rate': t('changeInTotalClickThroughRate'),
      'Total Cart Adds': t('changeInTotalCartAdds'),
      'Cart Adds': t('changeInCartAdds'),
      'Cart Add Share': t('changeInCartAddShare'),
      'Total Cart Add Rate': t('changeInTotalCartAddRate'),
      'Cart Add Rate': t('changeInCartAddRate'),
      'Total Purchases': t('changeInTotalPurchases'),
      Purchases: s('changeInPurchases'),
      'Total Purchase Rate': t('changeInTotalPurchaseRate'),
      'Purchase Rate': s('changeInPurchaseRate'),
      'Purchase Share': t('changeInPurchaseShare'),
      'Total Conversion Rate': t('changeInTotalConversionRate'),
      'Conversion Rate': c('changeInConversionRate'),
      'True ROAS': s('changeInTrueROAS'),
      'Click Share': s('changeInClickShare'),
    }),
    [k, s, t],
  )
  const extraCustomFilterParams = useMemo(() => {
    const keys = Object.keys(secondaryFiltersAPI)
    const newdata = { ...secondaryFiltersAPI }
    if (!customer.vendor_id) {
      keys?.forEach((item: string) => {
        const itemPresent = ProspectBrandNotSupportedList.includes(item)
        if (itemPresent) {
          delete newdata?.[item]
        }
      })
    }

    const filterParams = customFilterParams(
      newdata,
      selectionList,
      filterChangeValueDetails,
    )
    return { filter: filterParams }
  }, [
    ProspectBrandNotSupportedList,
    customer.vendor_id,
    secondaryFiltersAPI,
    selectionList,
    filterChangeValueDetails,
  ])

  const user = useUser().user,
    { code, symbol } = user?.current_currency ?? {}

  const userCurrency = useMemo(() => {
    return {
      currency_code: code,
      currency_symbol: symbol,
    }
  }, [code, symbol])
  const languageParams = useLanguageParams()
  const {
    newStartDate,
    newEndDate,
    comparisonStartDate,
    comparisonEndDate,
    currentTimeframeCheck,
    previousDates,
  } = useRankTrackerDates()
  const rankTrackerApi = `${getApiUrlPrefix('rank-tracker')}/api/v3/customer/${
      searchQueryParams?.customer_id
        ? searchQueryParams?.customer_id
        : customer.id
    }`,
    rankTrackerProductsApi = `${rankTrackerApi}/keywords`,
    commonParams = useMemo(() => {
      return {
        start_date: currentTimeframeCheck ? newStartDate : startDate,
        end_date: timeframe.type === 'current' ? newEndDate : endDate,
        ...(search ? { search_for: search } : {}),
        ...(selectedBrandId
          ? { brand_id: selectedBrandId }
          : customer.vendor_id
            ? { brand_id: customer.vendor_id }
            : {}),
        currency_code: code,
        marketplace_ids:
          getFilteredIds(
            true,
            false,
            marketplaceIds ?? [],
            selectedRegions,
            allMarketplaces,
          ) ?? [],
        comparison_start: secondRange_startDate
          ? secondRange_startDate
          : currentTimeframeCheck
            ? comparisonStartDate
            : previousDates?.startDate,
        comparison_end: secondRange_endDate
          ? secondRange_endDate
          : timeframe.type === 'current'
            ? comparisonEndDate
            : previousDates?.endDate,
        sort: standardSortParams(sortBy, ['keyword']),
        ...(rankTrackerState?.productsSoldBy?.id !== 0
          ? { sold_by_pattern: true }
          : {}),
        ...(rankTrackerState?.keywordSource?.id === 0
          ? {
              keyword_status: true,
            }
          : {}),
        ...(keywordType === 0
          ? {}
          : { branded_keyword: keywordType === 1 ? true : false }),

        ...(tags?.tag_ids?.length > 0
          ? {
              tag_ids: tags.tag_ids,
              match_all_tags: tags.matchAllTags,
              expires_in: 300,
            }
          : {}),
        ...extraCustomFilterParams,
        ...(aggregate_by ? { aggregate_by } : {}),
        ...(rankTrackerState.defaultEstOrganicClicks?.id !== 0
          ? {
              limit: 1000,
            }
          : {}),
        ...(doCustomCategoriesExist
          ? { custom_category_ids: customCategories }
          : {}),
        ...languageParams,
      }
    }, [
      aggregate_by,
      code,
      comparisonEndDate,
      comparisonStartDate,
      currentTimeframeCheck,
      customer.vendor_id,
      endDate,
      extraCustomFilterParams,
      marketplaceIds,
      newEndDate,
      newStartDate,
      previousDates?.endDate,
      previousDates?.startDate,
      rankTrackerState.defaultEstOrganicClicks?.id,
      rankTrackerState?.productsSoldBy?.id,
      search,
      secondRange_endDate,
      secondRange_startDate,
      sortBy,
      startDate,
      tags.matchAllTags,
      tags.tag_ids,
      timeframe.type,
      rankTrackerState.keywordSource,
      keywordType,
      doCustomCategoriesExist,
      customCategories,
      languageParams,
      selectedBrandId,
      selectedRegions,
      allMarketplaces,
    ])
  const brandId = selectedBrandId ?? customer.vendor_id
  const { data: marketplacesData } = useQuery({
    queryKey: ['marketplaces_data_branded', brandId],
    queryFn: async () => {
      const params = {
        customer_id: customer?.id ?? brandId,
      }
      try {
        const response = await getGlobalMarketplaces(params)
        return (
          response?.data?.marketplaces?.filter(
            (item: Marketplace) => item.amazon,
          ) ?? []
        )
      } catch {
        console.error('Unable to fetch marketplaces')
      }
    },
    enabled: !!brandId,
  })

  const originalConfig = useMemo(
    () => [
      ...config({
        getSecondaryFilterObject,
        sortBy,
        commonParams,
        searchQueryParams,
        customer,
        enableKeywordsPageChanges,
        setActionSidedrawer,
        screenIsMdMax,
      }).slice(0, -1),
      {
        name: 'attributed_sales',
        label: t('adSales'),
        ...(customer?.vendor_id
          ? {
              options: [
                {
                  name: 'attributed_sales',
                  label: t('adSales'),
                },
                {
                  name: 'change__attributed_sales',
                  label: t('changeInAdSales'),
                },
              ],
            }
          : {}),
        ...getContentTableHeaderTooltip('adSales'),
        cell: {
          children: (keyword: DataObj) => {
            return (
              <MdashCheck check={!!keyword?.attributed_sales?.value}>
                <div className='flex'>
                  <div>
                    <ListChangeData
                      value={keyword?.attributed_sales?.value ?? 0}
                      changeValue={keyword?.attributed_sales?.change ?? 0}
                      tooltipContent={
                        keyword?.attributed_sales?.pct_change ?? 0
                      }
                      changeFormat='currency'
                      currency={userCurrency}
                      decimalScale={decimalScaleHelper(
                        keyword?.attributed_sales?.change
                          ? Math.abs(keyword?.attributed_sales?.change)
                          : 0,
                      )}
                      changeDecimalScale={decimalScaleHelper(
                        keyword?.attributed_sales?.change
                          ? Math.abs(keyword.attributed_sales?.change)
                          : 0,
                      )}
                      comparison={keyword?.attributed_sales?.comparison}
                      customClass={
                        sortBy.prop === 'attributed_sales' ? 'fw-semi-bold' : ''
                      }
                      changeValueClass={
                        sortBy.prop === 'change__attributed_sales'
                          ? 'fw-semi-bold'
                          : ''
                      }
                    />
                  </div>
                  {keyword?.multiple_market_products === 1 && (
                    <div>
                      <Tooltip
                        tooltipContent={t('productRankingsTooltip')}
                        maxWidth='300px'
                        position='right'
                        children={
                          <span>
                            <Icon
                              icon='info'
                              className='pat-mr-2'
                              iconSize='12px'
                            />
                          </span>
                        }
                      />
                    </div>
                  )}
                </div>
              </MdashCheck>
            )
          },
        },
        ...(customer?.vendor_id
          ? {
              filter: getSecondaryFilterObject(
                t('adSales'),
                'attributed_sales',
                'currency',
                undefined,
                undefined,
                undefined,
                t('changeInAdSales'),
              ),
            }
          : {}),
        ...(customer?.vendor_id ? {} : { noSort: true }),
      },
      {
        name: 'cost',
        label: c('adSpend'),
        ...(customer?.vendor_id
          ? {
              options: [
                {
                  name: 'cost',
                  label: c('adSpend'),
                },
                {
                  name: 'change__cost',
                  label: t('changeInAdSpend'),
                },
              ],
            }
          : {}),
        ...getContentTableHeaderTooltip('adSpend'),
        cell: {
          children: (keyword: DataObj) => {
            return (
              <MdashCheck check={!!keyword?.cost?.value}>
                <ListChangeData
                  value={keyword?.cost?.value ?? 0}
                  changeValue={keyword?.cost?.change ?? 0}
                  tooltipContent={keyword?.cost?.pct_change ?? 0}
                  currency={userCurrency}
                  changeFormat='currency'
                  decimalScale={decimalScaleHelper(
                    keyword?.cost?.change ? Math.abs(keyword.cost.change) : 0,
                  )}
                  changeDecimalScale={decimalScaleHelper(
                    keyword?.cost?.change ? Math.abs(keyword.cost?.change) : 0,
                  )}
                  comparison={keyword?.cost?.comparison}
                  customClass={sortBy.prop === 'cost' ? 'fw-semi-bold' : ''}
                  changeValueClass={
                    sortBy.prop === 'change__cost' ? 'fw-semi-bold' : ''
                  }
                />
              </MdashCheck>
            )
          },
        },
        ...(customer?.vendor_id
          ? {
              filter: getSecondaryFilterObject(
                c('adSpend'),
                'cost',
                'currency',
                undefined,
                undefined,
                undefined,
                t('changeInAdSpend'),
              ),
            }
          : {}),
        ...(customer?.vendor_id ? {} : { noSort: true }),
      },
      {
        name: 'conversion_rate',
        label: t('adConversionRate'),
        ...getContentTableHeaderTooltip('adConversionRate'),
        cell: {
          children: (keyword: DataObj) => {
            return (
              <MdashCheck check={!!keyword?.conversion_rate?.value}>
                <ListChangeData
                  value={keyword?.conversion_rate?.value ?? 0}
                  changeValue={
                    keyword?.conversion_rate?.change
                      ? keyword.conversion_rate.change * 100
                      : 0
                  }
                  changeFormat='number'
                  tooltipContent={keyword?.conversion_rate?.pct_change ?? 0}
                  percentage
                  decimalScale={2}
                  comparison={keyword?.conversion_rate?.comparison}
                  customClass={
                    sortBy.prop === 'conversion_rate' ? 'fw-semi-bold' : ''
                  }
                  changeValueClass={
                    sortBy.prop === 'change__conversion_rate'
                      ? 'fw-semi-bold'
                      : ''
                  }
                />
              </MdashCheck>
            )
          },
        },
        ...(customer?.vendor_id
          ? {
              options: [
                {
                  name: 'conversion_rate',
                  label: t('adConversionRate'),
                },
                {
                  name: 'change__conversion_rate',
                  label: t('changeInAdConversionRate'),
                },
              ],
            }
          : {}),
        ...(customer?.vendor_id
          ? {
              filter: getSecondaryFilterObject(
                t('adConversionRate'),
                'conversion_rate',
                'percentage',
                undefined,
                undefined,
                undefined,
                t('changeInAdConversionRate'),
              ),
            }
          : {}),
        ...(customer?.vendor_id ? {} : { noSort: true }),
      },
      {
        name: 'acos',
        label: c('acos'),
        ...(customer?.vendor_id
          ? {
              options: [
                {
                  name: 'acos',
                  label: c('acos'),
                },
                {
                  name: 'change__acos',
                  label: t('changeInAcos'),
                },
              ],
            }
          : {}),
        ...getContentTableHeaderTooltip('acos'),
        cell: {
          children: (keyword: DataObj) => {
            return (
              <MdashCheck check={!!keyword?.acos?.value}>
                <ListChangeData
                  value={keyword?.acos?.value ?? 0}
                  changeValue={
                    keyword?.acos?.change ? keyword.acos.change * 100 : 0
                  }
                  tooltipContent={keyword?.acos?.pct_change ?? 0}
                  percentage
                  decimalScale={2}
                  reverse
                  comparison={keyword?.acos?.comparison}
                  customClass={sortBy.prop === 'acos' ? 'fw-semi-bold' : ''}
                  changeValueClass={
                    sortBy.prop === 'change__acos' ? 'fw-semi-bold' : ''
                  }
                />
              </MdashCheck>
            )
          },
        },
        ...(customer?.vendor_id
          ? {
              filter: getSecondaryFilterObject(
                c('acos'),
                'acos',
                'percentage',
                undefined,
                undefined,
                undefined,
                t('changeInAcos'),
              ),
            }
          : {}),
        ...(customer?.vendor_id ? {} : { noSort: true }),
      },
      {
        name: 'roas',
        label: c('roas'),
        ...getContentTableHeaderTooltip('roas'),
        cell: {
          children: (keyword: DataObj) => {
            return (
              <MdashCheck check={!!keyword?.roas?.value}>
                <ListChangeData
                  value={keyword?.roas?.value ?? 0}
                  changeValue={keyword?.roas?.change ?? 0}
                  comparison={keyword?.roas?.comparison}
                  tooltipContent={keyword?.roas?.pct_change ?? 0}
                  decimalScale={2}
                  customClass={sortBy.prop === 'roas' ? 'fw-semi-bold' : ''}
                  changeValueClass={
                    sortBy.prop === 'change__roas' ? 'fw-semi-bold' : ''
                  }
                />
              </MdashCheck>
            )
          },
        },
        ...(customer?.vendor_id
          ? {
              options: [
                {
                  name: 'roas',
                  label: c('roas'),
                },
                {
                  name: 'change__roas',
                  label: t('changeInROAS'),
                },
              ],
            }
          : {}),
        ...(customer?.vendor_id
          ? {
              filter: getSecondaryFilterObject(
                c('roas'),
                'roas',
                undefined,
                undefined,
                undefined,
                undefined,
                t('changeInROAS'),
              ),
            }
          : {}),
        ...(customer?.vendor_id ? {} : { noSort: true }),
      },
      {
        name: 'cpc',
        label: c('cpc'),
        ...getContentTableHeaderTooltip('cpc'),
        cell: {
          children: (keyword: DataObj) => {
            return (
              <MdashCheck check={!!keyword?.cpc?.value}>
                <ListChangeData
                  value={keyword?.cpc?.value ?? 0}
                  changeValue={keyword?.cpc?.change ?? 0}
                  currency={userCurrency}
                  changeFormat='currency'
                  comparison={keyword?.cpc?.comparison}
                  decimalScale={2}
                  reverse
                  customClass={sortBy.prop === 'cpc' ? 'fw-semi-bold' : ''}
                  tooltipContent={keyword?.cpc?.pct_change ?? 0}
                  changeValueClass={
                    sortBy.prop === 'change__cpc' ? 'fw-semi-bold' : ''
                  }
                />
              </MdashCheck>
            )
          },
        },
        ...(customer?.vendor_id
          ? {
              options: [
                {
                  name: 'cpc',
                  label: c('cpc'),
                },
                {
                  name: 'change__cpc',
                  label: t('changeInCPC'),
                },
              ],
            }
          : {}),
        ...(customer?.vendor_id
          ? {
              filter: getSecondaryFilterObject(
                c('cpc'),
                'cpc',
                'currency',
                undefined,
                undefined,
                undefined,
                t('changeInCPC'),
              ),
            }
          : {}),
        ...(customer?.vendor_id ? {} : { noSort: true }),
      },
      {
        name: 'click_through_rate',
        label: t('clickThroughRate'),
        ...(enableKeywordsPageChanges && activeTab === 1
          ? { tooltip: { content: t('clickThroughRateTooltip') } }
          : getContentTableHeaderTooltip('clickThroughRate')),
        cell: {
          children: (keyword: DataObj) => {
            return (
              <MdashCheck check={!!keyword?.click_through_rate?.value}>
                <ListChangeData
                  value={keyword?.click_through_rate?.value ?? 0}
                  changeValue={
                    keyword?.click_through_rate?.change
                      ? keyword.click_through_rate.change * 100
                      : 0
                  }
                  comparison={keyword?.click_through_rate?.comparison}
                  changeFormat='number'
                  tooltipContent={keyword?.click_through_rate?.pct_change ?? 0}
                  percentage
                  decimalScale={2}
                  customClass={
                    sortBy.prop === 'click_through_rate' ? 'fw-semi-bold' : ''
                  }
                  changeValueClass={
                    sortBy.prop === 'change__click_through_rate'
                      ? 'fw-semi-bold'
                      : ''
                  }
                />
              </MdashCheck>
            )
          },
        },
        ...(customer?.vendor_id
          ? {
              options: [
                {
                  name: 'click_through_rate',
                  label: t('clickThroughRate'),
                },
                {
                  name: 'change__click_through_rate',
                  label: t('changeInClickThroughRate'),
                },
              ],
            }
          : {}),
        ...(customer?.vendor_id
          ? {
              filter: getSecondaryFilterObject(
                t('clickThroughRate'),
                'click_through_rate',
                'percentage',
                undefined,
                undefined,
                undefined,
                t('changeInClickThroughRate'),
              ),
            }
          : {}),
        ...(customer?.vendor_id ? {} : { noSort: true }),
      },
      ...config({
        getSecondaryFilterObject,
        sortBy,
        commonParams,
        searchQueryParams,
        customer,
        enableKeywordsPageChanges,
        setActionSidedrawer,
        screenIsMdMax,
      }).slice(-1),
    ],
    [
      getSecondaryFilterObject,
      sortBy,
      commonParams,
      searchQueryParams,
      customer,
      enableKeywordsPageChanges,
      screenIsMdMax,
      t,
      activeTab,
      userCurrency,
    ],
  )

  const {
      status,
      data: apiResponse,
      fetchNextPage,
      hasNextPage,
    } = useInfiniteQuery({
      queryKey: [
        rankTrackerProductsApi,
        startDate,
        endDate,
        search,
        sortBy,
        marketplaceIds,
        previousDates,
        secondRange_startDate,
        secondRange_endDate,
        tags,
        rankTrackerState?.productsSoldBy?.id,
        rankTrackerState?.defaultEstOrganicClicks?.id,
        rankTrackerState?.keywordSource?.id,
        extraCustomFilterParams,
        aggregate_by,
        keywordType,
        customCategories,
      ],
      queryFn: ({ pageParam = 1, signal }) => {
        const params = {
          ...commonParams,
          page: pageParam,
          per_page: 20,
        }
        return getOrPostAPI(
          !doCustomCategoriesExist,
          rankTrackerProductsApi,
          params,
          signal,
        )
      },
      initialPageParam: 1,
      gcTime: trafficCacheTime,
      getNextPageParam: (previousResponse) => {
        return previousResponse?.data?.data?.pagination?.lastPage
          ? undefined
          : previousResponse?.data?.data?.pagination?.next_page
      },
    }),
    count = apiResponse?.pages[0]?.data?.data?.pagination?.count,
    apiData = useMemo(() => {
      return apiResponse
        ? apiResponse.pages.flatMap((page) => {
            return page?.data?.data?.data
          })
        : []
    }, [apiResponse])
  const totalRowParams = useMemo(() => {
    return { ...commonParams, fetch_all_row_data: true, sort: undefined }
  }, [commonParams])
  const { data: totalRowData, isLoading: isTotalRowLoading } = useQuery({
    queryKey: ['totalRow', rankTrackerProductsApi, totalRowParams],
    queryFn: async ({ signal }) => {
      const response = await getOrPostAPI(
        !doCustomCategoriesExist,
        rankTrackerProductsApi,
        totalRowParams,
        signal,
      )
      return response.data?.data?.data?.[0]
    },
  })
  const data = useMemo(() => {
    if (totalRowData) {
      return [{ ...totalRowData, totalRow: 'totalRow' }, ...apiData]
    }
    return apiData
  }, [apiData, totalRowData])
  const searchInputHandler = (value: string) => {
    setState((prevState) => ({
      ...prevState,
      search: value ?? '',
    }))
  }

  const sort: SortColumnProps['sorter'] = (sortObj) => {
    isMounted() &&
      setSortBy({
        prop: sortObj.activeColumn
          ? sortObj.activeColumn
          : enableKeywordsPageChanges && activeTab === 1
            ? 'keyword_est_revenue_comp'
            : 'est_organic_clicks',
        flip: sortObj.direction,
        lowerCaseParam: sortObj.lowerCaseParam,
      })
  }

  const csvDownloadOptions: DownloadOptionsType = [
    {
      linkName: t('rankTrackerKeywords'),
      csvName: t('rankTrackerKeywords'),
      csvFormat: {
        api: (csvParams) =>
          getOrPostAPI(
            !doCustomCategoriesExist,
            rankTrackerProductsApi,
            csvParams,
          ).then((response) => {
            return response.data
          }),
        params: {
          ...commonParams,
          async: true,
          csv_name: t('rankTrackerKeywords'),
        },
        callout: csvToastCallout(),
      },
    },
  ]

  useEffect(() => {
    updateBreadcrumbs({
      name:
        searchQueryParams?.customer_id && selectedBrandName
          ? selectedBrandName
          : c('conversion'),
      link: `${pathname}${searchParams}`,
      changeType: !searchQueryParams?.customer_id ? 'rootLevel' : 'tab',
    })
  }, [
    pathname,
    searchQueryParams?.customer_id,
    selectedBrandName,
    updateBreadcrumbs,
    searchParams,
  ])

  const customFilterTableQueryKey = 'keywords'
  const apiUrl = `${getApiUrlPrefix('user-settings')}/v1/${
    userId
  }/predict/content:rank_tracker:keyword_rank_tracker`

  const { status: userSettingStatus, data: userSettingsApiResponse } = useQuery(
    {
      queryKey: [userId, customFilterTableQueryKey],
      queryFn: ({ signal }) => SecureAxios.get(apiUrl, { signal }),
      gcTime: trafficCacheTime,
    },
  )

  const userSettingsData =
    userSettingStatus === 'success' &&
    userSettingsApiResponse?.data?.selectedColumns

  useEffect(() => {
    if (userSettingsData) {
      setSelectionList(userSettingsData)
    }
  }, [userSettingsData])

  const totalConfigItems = originalConfig.slice(1, -1)
  const totalList = totalConfigItems.map((item) => item.label)

  const customizeColumnConfig = useMemo(() => {
    return [
      originalConfig[0],
      ...getSelectedConfig(originalConfig, selectionList, 'selected'),
      originalConfig[originalConfig.length - 1],
    ]
  }, [originalConfig, selectionList])

  const customSelectionCallout = (
    selectedList: string[],
    setToDefault?: boolean,
  ) => {
    if (
      !selectedList.includes(
        originalConfig.filter((selectionItem) => {
          const item = selectionItem as customSelectionCalloutProps
          if (item?.options) {
            const value = item.options.find(
              (option) => option.name === sortBy.prop,
            )
            return !!value
          }
          return item.name === sortBy.prop
        })[0].label,
      )
    ) {
      setSortBy((prevState: { prop: string; flip: boolean }) => ({
        ...prevState,
        prop: originalConfig[0].name,
      }))
    }
    setSelectionList(selectedList)
    saveCustomization({
      api: apiUrl,
      selected: selectedList,
      setToDefault: setToDefault,
      type: 'table',
    })

    const keys = Object.keys(secondaryFiltersAPI)
    const originLength = Object.keys(secondaryFiltersAPI)?.length
    const newdata = { ...secondaryFiltersAPI }
    keys?.forEach((item: string) => {
      const itemNotPresent = !selectedList.includes(item)
      if (itemNotPresent) {
        delete newdata?.[item]
      }
    })
    const updatedLength = Object.keys(newdata)?.length
    if (originLength !== updatedLength) {
      setSecondaryFiltersAPI(newdata)
      setResponseType('post')
    }
  }

  const ProspectBrandNotSupportedSortList: Array<string> = useMemo(() => {
    return [
      'change__attributed_sales',
      'attributed_sales',
      'cost',
      'change__cost',
      'conversion_rate',
      'change__conversion_rate',
      'acos',
      'change__acos',
      'roas',
      'change__roas',
      'cpc',
      'change__cpc',
      'click_through_rate',
      'change__click_through_rate',
    ]
  }, [])

  useEffect(() => {
    if (!customer.vendor_id) {
      const isSortSupport = ProspectBrandNotSupportedSortList.includes(
        sortBy.prop,
      )
      if (isSortSupport) {
        setSortBy((prevState: { prop: string; flip: boolean }) => ({
          ...prevState,
          prop: originalConfig[1].name,
        }))
      }
    }
  }, [
    originalConfig,
    customer.vendor_id,
    ProspectBrandNotSupportedSortList,
    sortBy.prop,
    activeTab,
    selectionList,
    enableKeywordsPageChanges,
  ])

  useEffect(() => {
    if (prevCustomer !== customer) {
      setState((prevState) => ({
        ...prevState,
        search: '',
      }))
    }
  }, [customer, prevCustomer])

  const selectedKeyword = checkedBoxes.map((item) => item.keyword).join('\n')

  return (
    <div>
      <RankGraphData
        allowFilter
        api={rankTrackerApi}
        csvTitle={t('rankTrackerKeywords')}
        summaryEntity='Keywords with Products'
        extraParams={{
          ...(rankTrackerState?.productsSoldBy?.id !== 0
            ? { sold_by_pattern: true }
            : {}),
          ...(rankTrackerState?.keywordSource?.id === 0
            ? { keyword_status: true }
            : {}),
        }}
        showNewHeaderMetric
        pageName={activeTab === 1 ? 'keywordsWithProducts' : 'products'}
      />
      <Tabs
        active={0}
        callout={(id) => {
          setActiveTab(id)
          if (enableKeywordsPageChanges && activeTab === 1) {
            setSortBy((prevState: { prop: string; flip: boolean }) => ({
              ...prevState,
              prop: selectionList.includes(c('keywordSales'))
                ? 'keyword_est_revenue_comp'
                : selectionList.includes(c('clicks'))
                  ? 'daily_clicks'
                  : originalConfig[1].name,
            }))
          }
        }}
        tabs={[
          { id: 0, tabName: c('products'), content: <RankTracker /> },
          {
            id: 1,
            tabName: c('keywords'),
            content: (
              <>
                {!enableKeywordsPageChanges && (
                  <Tabs
                    subtabs
                    active={0}
                    callout={(id) => {
                      setKeywordType(id)
                    }}
                    tabs={[
                      { id: 0, tabName: c('allKeywords') },
                      { id: 1, tabName: c('branded') },
                      { id: 2, tabName: c('nonBranded') },
                    ]}
                  />
                )}
                <StandardTable
                  hasCheckboxes={true}
                  handleCheckedBoxes={(checkedBoxes) =>
                    setCheckedBoxes(checkedBoxes)
                  }
                  data={data}
                  config={customizeColumnConfig}
                  dataKey='keyword'
                  hasData={apiData?.length > 0}
                  loading={status === 'pending' || isTotalRowLoading}
                  hasMore={!!(status === 'success' && hasNextPage)}
                  tableId={tableId}
                  twoLineLabel
                  sort={sort}
                  sortBy={sortBy}
                  noDataFields={{
                    primaryText: c('noKeywordsFound'),
                    secondaryText: t('noKeywordsMsg'),
                  }}
                  tableHeaderProps={{
                    header: {
                      name: t('keyword', { count: Number(count) }),
                      value: convertTableHeaderCount(count),
                    },
                    search: {
                      value: search,
                      onChange: (value) => searchInputHandler(value),
                      placeholder: t('searchKeywords'),
                      debounce: 250,
                    },
                    download: {
                      csvDownloadOptions: csvDownloadOptions,
                      initialDisplay: true,
                      show: true,
                    },
                    columnFilterProps: {
                      activeFilters: secondaryFiltersAPI,
                      remove: removeFilters,
                    },
                    customColumnProps: {
                      list: totalList,
                      selected: selectionList,
                      callout: customSelectionCallout,
                      setToDefaultCallout: () =>
                        customSelectionCallout(
                          enableKeywordsPageChanges
                            ? defaultKeywordsColumns
                            : defaultColumns,
                          true,
                        ),
                      isColumnsReorderable: true,
                    },
                    ...(enableKeywordsPageChanges
                      ? {
                          rightSectionChildren: (
                            <div className='flex'>
                              <Divider className='pat-mr-4' />
                              <div className='pat-mt-4 pat-pb-4'>
                                <Picker
                                  options={tabOptions}
                                  state={optionSelected}
                                  stateName='value'
                                  callout={(_, value) => {
                                    const option = tabOptions.find(
                                      (f) => f.value === value,
                                    )
                                    setKeywordType(
                                      option?.value ?? tabOptions[0]?.value,
                                    )
                                    setOptionSelected(option ?? tabOptions[0])
                                  }}
                                  customClass='every-options full-width'
                                />
                              </div>
                            </div>
                          ),
                        }
                      : {}),
                  }}
                  getData={fetchNextPage}
                  successStatus={status === 'success'}
                  stickyTableConfig={stickyTableConfig}
                  activeFilters={secondaryFiltersAPI}
                  removeFilters={removeFilter}
                  totalRowKey='totalRow'
                />
                <PageFooter
                  rightSection={[
                    {
                      type: 'button',
                      styleType: 'primary-green',
                      children: t('bulkEditKeywordsButton'),
                      disabled:
                        checkedBoxes.length === 0 || !customer?.vendor_id,
                      onClick: () => {
                        setActionSidedrawer({
                          type: brandedKeywordsActionConstant.ADD_KEYWORDS_RANK_TRACKER,
                          isOpen: true,
                          keywordData: {
                            id: 0,
                            keyword_text: selectedKeyword,
                            marketplaces: [],
                            match_type: '',
                          },
                        })
                      },
                    },
                  ]}
                />
              </>
            ),
          },
          {
            id: 2,
            tabName: c('categories'),
            content: <RankTrackerCategoryTable api={rankTrackerApi} />,
          },
        ]}
      />
      <AddKeywordsSideDrawer
        actionsSidedrawer={actionsSidedrawer}
        setActionSidedrawer={setActionSidedrawer}
        refreshKeywordsList={() => undefined}
        brandId={brandId ?? ''}
        allMarketplaces={
          marketplacesData
            ? marketplacesData.map((item: Marketplace) => ({
                ...item,
                name: item.marketplace_name,
              }))
            : []
        }
      />
    </div>
  )
}

export default KeywordRankTracker
