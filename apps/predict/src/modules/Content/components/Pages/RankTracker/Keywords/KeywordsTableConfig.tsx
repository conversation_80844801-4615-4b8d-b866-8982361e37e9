import {
  ButtonGroup,
  type ConfigItemType,
  hasValue,
  <PERSON><PERSON>,
  MdashCheck,
  Tag,
  TrimText,
} from '@patterninc/react-ui'
import { c, t } from '@predict-services'
import { type TrafficDataItem } from 'src/modules/Traffic/components/common/AdvertisingGraphData/TrafficCommonHeadersData.models'
import ListChangeData from 'src/common/components/ListChangeData/ListChangeData'
import { type ParsedQuery } from 'query-string'
import { type Customer } from '@predict-types'
import {
  type numberProps,
  type SelectProps,
} from 'src/common/components/Tables/SecondaryFilter'
import { NumericFormat } from 'react-number-format'
import { brandedKeywordsActionConstant } from 'src/modules/Settings/components/Pages/Advertising/Pages/Brand/Tabs/SettingsAdBrandedKeywordsTab'
import { Link } from 'react-router-dom'
import { type SideDrawerProps } from 'src/modules/Settings/components/Pages/Advertising/Pages/NegativeKeywords/AddKeywordsSideDrawer'
import TrafficWaterfallTooltip from 'src/modules/Traffic/components/common/TrafficWaterfallTooltip/TrafficWaterfallTooltip'
import { filterWaterfallData } from 'src/common/components/Charts/Graphs/WaterfallChart/WaterfallHelpers'
import { getCurrencyDetails } from 'src/modules/Traffic/services/TrafficHelperService'
import { ListChangeDataCell } from 'src/common/components/ListChangeData/ListChangeDataCell'

import RankTrackerSparklineWrapper from '../Common/RankTrackerSparkline/RankTrackerSparklineWrapper'
import styles from '../Common/_rank-tracker.module.scss'
import { getOrganicClickShareChange } from '../Common/RankTrackerHelperService'
import { getContentTableHeaderTooltip } from '../../../Common/ContentTooltipHelper'

type GraphData = {
  date: string
  est_organic_clicks: number
  previous_avg_clicks: number
}

interface KeyProps {
  value: number
  type: string
  display: string
  change: number
  pct_change: number
  comparison?: number
}

export type DataObj = {
  [key in
    | 'products_in_top_4'
    | 'products_on_page_1'
    | 'est_organic_clicks'
    | 'organic_click_share'
    | 'pct_brand_organic_clicks'
    | 'brands_best_rank'
    | 'daily_search_query_volume'
    | 'total_keyword_est_revenue_comp'
    | 'impressions_total_count'
    | 'impressions_brand_count'
    | 'impression_share'
    | 'clicks_total_count'
    | 'daily_clicks'
    | 'clicks_brand_share'
    | 'sqp_total_click_through_rate'
    | 'cart_adds_total_count'
    | 'cart_adds_brand_count'
    | 'cart_adds_share'
    | 'total_cart_adds_rate'
    | 'purchases_total_count'
    | 'purchases_brand_count'
    | 'cart_adds_rate'
    | 'true_roas'
    | 'conversion_rate'
    | 'total_conversion_rate'
    | 'click_share'
    | 'total_purchases_rate'
    | 'purchases_rate'
    | 'purchases_brand_share'
    | 'clicks_share'
    | 'keyword_est_revenue_comp']: TrafficDataItem
} & {
  // Basic properties
  keyword: string
  marketplace_id: string
  marketplace_name: string
  country_code: string
  est_daily_search_volume: number
  est_clicks_for_top_position: number
  clicks_by_day: GraphData[]
  search_url: string
  sold_by_pattern: boolean
  is_branded_keyword: string
  keyword_status: string
  totalRow: string
  multiple_market_products: number

  // TrafficDataItem properties (all these follow the same pattern)
  products_in_top_4: TrafficDataItem
  products_on_page_1: TrafficDataItem
  est_organic_clicks: TrafficDataItem
  organic_click_share: TrafficDataItem
  pct_brand_organic_clicks: TrafficDataItem
  brands_best_rank: TrafficDataItem
  daily_search_query_volume: TrafficDataItem
  click_share: TrafficDataItem
  total_keyword_est_revenue_comp: TrafficDataItem
  keyword_est_revenue_comp: TrafficDataItem
  impressions_total_count: TrafficDataItem
  impressions_brand_count: TrafficDataItem
  impression_share: TrafficDataItem
  clicks_total_count: TrafficDataItem
  daily_clicks: TrafficDataItem
  clicks_brand_share: TrafficDataItem
  sqp_total_click_through_rate: TrafficDataItem
  cart_adds_total_count: TrafficDataItem
  cart_adds_brand_count: TrafficDataItem
  cart_adds_share: TrafficDataItem
  total_cart_adds_rate: TrafficDataItem
  purchases_total_count: TrafficDataItem
  purchases_brand_count: TrafficDataItem
  cart_adds_rate: TrafficDataItem

  // KeyProps properties
  attributed_sales: KeyProps
  cost: KeyProps
  conversion_rate: KeyProps
  acos: KeyProps
  roas: KeyProps
  cpc: KeyProps
  click_through_rate: KeyProps
  sortProp?: string
}
type getSecondaryFilterObjectType = (
  filterKey: string,
  tableKey: string,
  type?: 'number' | 'currency' | 'percentage',
  typeField?: string | undefined,
  selectProps?: SelectProps,
  numberProps?: numberProps,
  changeValueLabel?: string,
) => {
  headerText: string
  children: React.ReactNode
  callout: (headerText: string) => void
  disabled: boolean
}
type sortBy = {
  prop: string
  order?: 'asc' | 'desc' | undefined
}
type configType = {
  getSecondaryFilterObject: getSecondaryFilterObjectType
  sortBy: sortBy
  commonParams: Record<string, unknown>
  searchQueryParams: ParsedQuery<string>
  customer: Partial<Customer>
  enableKeywordsPageChanges: boolean
  setActionSidedrawer: React.Dispatch<React.SetStateAction<SideDrawerProps>>
  screenIsMdMax?: boolean
}

// Define a type for keys that have TrafficDataItem values
type TrafficDataItemKeys = Extract<
  keyof DataObj,
  {
    [K in keyof DataObj]: DataObj[K] extends TrafficDataItem ? K : never
  }[keyof DataObj]
>

const getCell = ({
  node,
  label,
  changeInLabel,
  tooltipStr,
  sortBy,
  getSecondaryFilterObject,
  filterType = 'number',
  decimalScale,
  changeFormat,
  isWaterfallTooltip,
  showFilterByChangePct,
  reverse = false,
}: {
  node: TrafficDataItemKeys
  label: string
  changeInLabel: string
  tooltipStr: string
  sortBy: sortBy
  getSecondaryFilterObject: getSecondaryFilterObjectType
  filterType?: 'number' | 'currency' | 'percentage'
  decimalScale?: boolean
  changeFormat?: 'number' | 'currency' | 'percentage'
  isWaterfallTooltip?: boolean
  showFilterByChangePct?: boolean
  reverse?: boolean
}) => {
  return [
    {
      name: node,
      label: label,
      cell: {
        children: (e: DataObj) => {
          const currency = getCurrencyDetails()
          return (
            <div className='flex justify-content-between align-items-center pat-gap-4'>
              <ListChangeDataCell
                metricData={e[node]}
                metricName={node}
                sortBy={sortBy}
                showFilterByChangePct={showFilterByChangePct}
                currency={filterType === 'currency' ? currency : undefined}
                reverse={reverse}
                percentage={filterType === 'percentage'}
                decimalScale={decimalScale ? 2 : undefined}
                changeFormat={changeFormat}
              />
              {isWaterfallTooltip && (
                <TrafficWaterfallTooltip
                  type='roas'
                  data={filterWaterfallData({ data: e })}
                  extraRightMargin
                />
              )}
            </div>
          )
        },
      },
      tooltip: {
        content: tooltipStr,
      },
      options: [
        {
          name: node,
          label: label,
        },
        {
          name: 'change__' + node,
          label: changeInLabel,
        },
      ],
      filter:
        filterType === 'currency'
          ? getSecondaryFilterObject(
              label,
              node,
              'currency',
              undefined,
              undefined,
              undefined,
              changeInLabel,
            )
          : filterType === 'percentage'
            ? getSecondaryFilterObject(
                label,
                node,
                'percentage',
                undefined,
                undefined,
                undefined,
                changeInLabel,
              )
            : getSecondaryFilterObject(
                label,
                node,
                'number',
                'number',
                {},
                {
                  onlyWholeNumbers: true,
                },
                changeInLabel,
              ),
    },
  ]
}

const KeywordsTableConfig = ({
  getSecondaryFilterObject,
  sortBy,
  commonParams,
  searchQueryParams,
  customer,
  enableKeywordsPageChanges,
  setActionSidedrawer,
  screenIsMdMax,
}: configType): ConfigItemType<DataObj, Record<string, unknown>>[] => {
  return [
    {
      name: 'keyword',
      label: t('content:keywords'),
      cell: {
        children: (e: DataObj) => {
          return !e?.totalRow ? (
            <div className='flex flex-direction-column pat-gap-1'>
              <TrimText
                customClass={`${
                  sortBy.prop === 'keyword' ? 'fw-semi-bold' : ''
                } fs-12`}
                text={e?.keyword}
                limit={60}
              />
              <a href={e?.search_url} className='fs-10 fc-blue'>
                {e?.marketplace_name}
              </a>
            </div>
          ) : (
            <span>{c('all')}</span>
          )
        },
      },
      mainColumn: true,
      style: {
        minWidth: '200px',
      },
    },
    ...(enableKeywordsPageChanges
      ? [
          ...getCell({
            node: 'daily_search_query_volume',
            label: c('searchVolume'),
            changeInLabel: c('changeInSearchVolume'),
            tooltipStr: t('content:searchVolumeTooltip'),
            sortBy,
            getSecondaryFilterObject,
          }),
        ]
      : []),
    {
      name: 'brands_best_rank',
      label: t('content:brandsBestRank'),
      cell: {
        children: (e: DataObj) => {
          return (
            <ListChangeData
              value={e?.brands_best_rank?.value}
              changeValue={e?.brands_best_rank?.change}
              decimalScale={0}
              customClass={
                sortBy.prop === 'brands_best_rank' ? 'fw-semi-bold' : ''
              }
              changeValueClass={
                sortBy.prop === 'change__brands_best_rank' ? 'fw-semi-bold' : ''
              }
            />
          )
        },
      },
      options: [
        {
          name: 'brands_best_rank',
          label: t('content:brandsBestRank'),
        },
        {
          name: 'change__brands_best_rank',
          label: t('content:changeInBrandsBestRank'),
        },
      ],
      tooltip: {
        content: t('content:brandsBestRankTooltip'),
      },
      filter: getSecondaryFilterObject(
        t('content:brandsBestRank'),
        'brands_best_rank',
        'number',
        'number', // typefield
        {}, // selectProps
        {
          onlyWholeNumbers: true,
        }, // numberProps
        t('content:changeInBrandsBestRank'),
      ),
    },
    {
      name: 'is_branded_keyword',
      label: t('content:keywordType'),
      cell: {
        children: (e: DataObj) => {
          return (
            <MdashCheck check={hasValue(e?.is_branded_keyword)}>
              {e?.is_branded_keyword === 'true' ? (
                <Tag color='blue'>{c('branded')}</Tag>
              ) : (
                <Tag color='pink'>{c('nonBranded')}</Tag>
              )}
            </MdashCheck>
          )
        },
      },
      tooltip: {
        content: t('content:keywordTypeTooltip'),
      },
      noSort: true,
    },
    {
      name: 'keyword_status',
      label: c('source'),
      cell: {
        children: (e: DataObj) => {
          return (
            <MdashCheck check={hasValue(e?.keyword_status)}>
              {e?.keyword_status === 'true' ? (
                <Tag color='blue'>{t('content:highestVolumeKeyword')}</Tag>
              ) : (
                <Tag color='orange'>{t('content:content:advertised')}</Tag>
              )}
            </MdashCheck>
          )
        },
      },
      tooltip: {
        content: t('content:keywordStatusTooltip'),
      },
      noSort: true,
    },
    {
      name: 'est_organic_clicks',
      label: t('content:estOrganicClicks'),
      cell: {
        children: (e: DataObj) => {
          const sparklineParams = {
            ...commonParams,
            branded_keyword: undefined,
          }
          return (
            <div
              className={
                screenIsMdMax
                  ? `flex-direction-column ${styles.width100} `
                  : styles.estOrganicClicksColumn
              }
            >
              <ListChangeData
                value={e.est_organic_clicks?.value}
                changeValue={e.est_organic_clicks?.change}
                customClass={
                  sortBy.prop === 'est_organic_clicks' ? 'fw-semi-bold' : ''
                }
                changeValueClass={
                  sortBy.prop === 'change__est_organic_clicks'
                    ? 'fw-semi-bold'
                    : ''
                }
                thresholdValue={0}
              />
              {!e?.totalRow ? (
                <div className={screenIsMdMax ? 'pat-mt-4' : ''}>
                  <MdashCheck check={!!e?.est_organic_clicks?.value}>
                    <RankTrackerSparklineWrapper
                      topValue={e?.est_organic_clicks?.value}
                      title='Est. Organic Clicks'
                      dataKey='est_organic_clicks'
                      brand={customer.customer_name}
                      keyword={e?.keyword}
                      marketplace={e?.marketplace_name}
                      isDisplayCsvDownloadOption
                      csvTitle={`Brand's Est Organic Clicks - ${e.keyword} - ${e.marketplace_name}`}
                      params={sparklineParams}
                      type='keyword'
                      marketProductId={e.keyword}
                      customerId={
                        searchQueryParams?.customer_id
                          ? Number(searchQueryParams?.customer_id)
                          : Number(customer.id)
                      }
                      // apiEndpoint={'keyword_sparkline'}
                      apiEndpoint={`api/v3/customer/${
                        searchQueryParams?.customer_id
                          ? Number(searchQueryParams?.customer_id)
                          : Number(customer.id)
                      }/keyword_sparkline`}
                      marketplaceIds={e.marketplace_id}
                    ></RankTrackerSparklineWrapper>
                  </MdashCheck>
                </div>
              ) : null}
            </div>
          )
        },
      },
      tooltip: {
        content: t('content:estOrganicClickTooltip'),
      },
      options: [
        {
          name: 'est_organic_clicks',
          label: t('content:estOrganicClicks'),
        },
        {
          name: 'change__est_organic_clicks',
          label: t('content:changeInBrandEstOrganicClicks'),
        },
      ],
      filter: getSecondaryFilterObject(
        t('content:estOrganicClicks'),
        'est_organic_clicks',
        'number',
        'number', // typefield
        {}, // selectProps
        {
          onlyWholeNumbers: true,
        }, // numberProps
        t('content:changeInBrandEstOrganicClicks'),
      ),
    },
    {
      name: 'products_in_top_4',
      label: t('content:productsInTopFour'),
      cell: {
        children: (e: DataObj) => {
          return (
            <ListChangeData
              value={e.products_in_top_4?.value}
              changeValue={e.products_in_top_4?.change}
              icon={<Icon icon='cup' iconSize='12px' />}
              customClass={
                sortBy.prop === 'products_in_top_4' ? 'fw-semi-bold' : ''
              }
              changeValueClass={
                sortBy.prop === 'change__products_in_top_4'
                  ? 'fw-semi-bold'
                  : ''
              }
            />
          )
        },
      },
      options: [
        {
          name: 'products_in_top_4',
          label: t('content:productsInTopFour'),
        },
        {
          name: 'change__products_in_top_4',
          label: t('content:changeInProductsInTopFour'),
        },
      ],
      tooltip: {
        content: t('content:productsInTopFourTooltip'),
      },
      filter: getSecondaryFilterObject(
        t('content:productsInTopFour'),
        'products_in_top_4',
        'number',
        'number', // typefield
        {}, // selectProps
        {
          onlyWholeNumbers: true,
        }, // numberProps
        t('content:changeInProductsInTopFour'),
      ),
    },
    {
      name: 'products_on_page_1',
      label: t('content:productsOnPageOne'),
      cell: {
        children: (e: DataObj) => {
          return (
            <ListChangeData
              value={e?.products_on_page_1?.value}
              changeValue={e?.products_on_page_1?.change}
              customClass={
                sortBy.prop === 'products_on_page_1' ? 'fw-semi-bold' : ''
              }
              changeValueClass={
                sortBy.prop === 'change__products_on_page_1'
                  ? 'fw-semi-bold'
                  : ''
              }
            />
          )
        },
      },
      options: [
        {
          name: 'products_on_page_1',
          label: t('content:productsOnPageOne'),
        },
        {
          name: 'change__products_on_page_1',
          label: t('content:changeInProductsOnPage1'),
        },
      ],
      tooltip: {
        content: t('content:productsOnPageOneTooltip'),
      },
      filter: getSecondaryFilterObject(
        t('content:productsOnPageOne'),
        'products_on_page_1',
        'number',
        'number', // typefield
        {}, // selectProps
        {
          onlyWholeNumbers: true,
        }, // numberProps
        t('content:changeInProductsOnPage1'),
      ),
    },
    ...(enableKeywordsPageChanges
      ? [
          ...getCell({
            node: 'total_keyword_est_revenue_comp',
            label: c('totalKeywordSales'),
            changeInLabel: c('changeInTotalKeywordSales'),
            tooltipStr: t('content:totalKeywordSalesTooltip'),
            sortBy,
            getSecondaryFilterObject,
            filterType: 'currency',
            changeFormat: 'currency',
            showFilterByChangePct: true,
          }),
          ...getCell({
            node: 'keyword_est_revenue_comp',
            label: c('keywordSales'),
            changeInLabel: c('changeInKeywordSales'),
            tooltipStr: c('keywordSalesTooltip'),
            sortBy,
            getSecondaryFilterObject,
            filterType: 'currency',
            changeFormat: 'currency',
            showFilterByChangePct: true,
          }),
          ...getCell({
            node: 'clicks_total_count',
            label: t('keywords:totalClicks'),
            changeInLabel: t('keywords:changeInTotalClicks'),
            tooltipStr: t('keywords:totalClicksMetricTooltip'),
            sortBy,
            getSecondaryFilterObject,
            showFilterByChangePct: true,
          }),
          ...getCell({
            node: 'daily_clicks',
            label: c('clicks'),
            changeInLabel: c('changeInClicks'),
            tooltipStr: c('clicksTooltip'),
            sortBy,
            getSecondaryFilterObject,
            showFilterByChangePct: true,
          }),
          ...getCell({
            node: 'click_share',
            label: t('traffic:clickShare'),
            changeInLabel: t('traffic:changeInClickShare'),
            tooltipStr: t('traffic:clickShareTooltip'),
            sortBy,
            getSecondaryFilterObject,
            filterType: 'percentage',
            decimalScale: true,
            showFilterByChangePct: true,
          }),
          ...getCell({
            node: 'purchases_total_count',
            label: t('content:totalPurchases'),
            changeInLabel: t('content:changeInTotalPurchases'),
            tooltipStr: t('content:totalPurchasesTooltip'),
            sortBy,
            getSecondaryFilterObject,
            showFilterByChangePct: true,
          }),
          ...getCell({
            node: 'purchases_brand_count',
            label: t('traffic:purchases'),
            changeInLabel: t('traffic:changeInPurchases'),
            tooltipStr: t('traffic:purchasesMetricTooltip'),
            sortBy,
            getSecondaryFilterObject,
            showFilterByChangePct: true,
          }),
          ...getCell({
            node: 'total_purchases_rate',
            label: t('content:totalPurchaseRate'),
            changeInLabel: t('content:changeInTotalPurchaseRate'),
            tooltipStr: t('content:totalPurchaseRateTooltip'),
            sortBy,
            getSecondaryFilterObject,
            filterType: 'percentage',
            showFilterByChangePct: true,
          }),
          ...getCell({
            node: 'purchases_rate',
            label: t('traffic:purchaseRate'),
            changeInLabel: t('traffic:changeInPurchaseRate'),
            tooltipStr: t('traffic:purchaseRateMetricTooltip'),
            sortBy,
            getSecondaryFilterObject,
            filterType: 'percentage',
            showFilterByChangePct: true,
          }),
          ...getCell({
            node: 'purchases_brand_share',
            label: t('content:purchaseShare'),
            changeInLabel: t('content:changeInPurchaseShare'),
            tooltipStr: t('content:purchaseShareTooltip'),
            sortBy,
            getSecondaryFilterObject,
            filterType: 'percentage',
            showFilterByChangePct: true,
          }),
          ...getCell({
            node: 'total_conversion_rate',
            label: t('content:totalConversionRate'),
            changeInLabel: t('content:changeInTotalConversionRate'),
            tooltipStr: t('content:totalConversionRateTooltip'),
            sortBy,
            getSecondaryFilterObject,
            filterType: 'percentage',
            decimalScale: true,
            showFilterByChangePct: true,
          }),
          ...getCell({
            node: 'conversion_rate',
            label: c('conversionRate'),
            changeInLabel: c('changeInConversionRate'),
            tooltipStr: t('content:conversionRateMetricTooltip'),
            sortBy,
            getSecondaryFilterObject,
            filterType: 'percentage',
            decimalScale: true,
            showFilterByChangePct: true,
          }),
        ]
      : []),
    ...(enableKeywordsPageChanges
      ? [
          ...getCell({
            node: 'impressions_total_count',
            label: t('content:totalImpressions'),
            changeInLabel: t('content:changeInTotalImpressions'),
            tooltipStr: t('content:totalImpressionsTooltip'),
            sortBy,
            getSecondaryFilterObject,
            showFilterByChangePct: true,
          }),
          ...getCell({
            node: 'impressions_brand_count',
            label: t('traffic:impressions'),
            changeInLabel: t('traffic:changeInImpressions'),
            tooltipStr: t('traffic:impressionsMetricTooltip'),
            sortBy,
            getSecondaryFilterObject,
            showFilterByChangePct: true,
          }),
          ...getCell({
            node: 'impression_share',
            label: t('content:impressionShare'),
            changeInLabel: t('content:changeInImpressionShare'),
            tooltipStr: t('content:impressionShareTooltip'),
            sortBy,
            getSecondaryFilterObject,
            filterType: 'percentage',
            decimalScale: true,
            showFilterByChangePct: true,
          }),
          ...getCell({
            node: 'sqp_total_click_through_rate',
            label: t('content:totalClickThroughRate'),
            changeInLabel: t('content:changeInTotalClickThroughRate'),
            tooltipStr: t('content:totalClickThroughRateTooltip'),
            sortBy,
            getSecondaryFilterObject,
            showFilterByChangePct: true,
            filterType: 'percentage',
            decimalScale: true,
          }),
          ...getCell({
            node: 'cart_adds_total_count',
            label: t('content:totalCartAdds'),
            changeInLabel: t('content:changeInTotalCartAdds'),
            tooltipStr: t('content:totalCartAddsTooltip'),
            sortBy,
            getSecondaryFilterObject,
            showFilterByChangePct: true,
          }),
          ...getCell({
            node: 'cart_adds_brand_count',
            label: t('content:cartAdds'),
            changeInLabel: t('content:changeInCartAdds'),
            tooltipStr: t('content:cartAddsTooltip'),
            sortBy,
            getSecondaryFilterObject,
            showFilterByChangePct: true,
          }),
          ...getCell({
            node: 'cart_adds_share',
            label: t('content:cartAddShare'),
            changeInLabel: t('content:changeInCartAddShare'),
            tooltipStr: t('content:cartAddShareTooltip'),
            sortBy,
            getSecondaryFilterObject,
            showFilterByChangePct: true,
            filterType: 'percentage',
            decimalScale: true,
          }),
          ...getCell({
            node: 'total_cart_adds_rate',
            label: t('content:totalCartAddRate'),
            changeInLabel: t('content:changeInTotalCartAddRate'),
            tooltipStr: t('content:totalCartAddRateTooltip'),
            sortBy,
            getSecondaryFilterObject,
            showFilterByChangePct: true,
            filterType: 'percentage',
            decimalScale: true,
          }),
          ...getCell({
            node: 'cart_adds_rate',
            label: t('content:cartAddRate'),
            changeInLabel: t('content:changeInCartAddRate'),
            tooltipStr: t('content:cartAddRateTooltip'),
            sortBy,
            getSecondaryFilterObject,
            showFilterByChangePct: true,
            filterType: 'percentage',
            decimalScale: true,
          }),
          ...getCell({
            node: 'true_roas',
            label: c('trueRoas'),
            changeInLabel: t('traffic:changeInTrueROAS'),
            tooltipStr: t('content:trueRoasTooltip'),
            sortBy,
            getSecondaryFilterObject,
            showFilterByChangePct: true,
            decimalScale: true,
          }),
        ]
      : []),
    {
      name: 'organic_click_share',
      label: t('content:brandsOrganicShareClick'),
      cell: {
        children: (e: DataObj) => {
          return (
            <ListChangeData
              value={e?.organic_click_share?.value}
              changeValue={getOrganicClickShareChange(
                e?.organic_click_share?.change * 100,
              )}
              percentage
              decimalScale={2}
              customClass={
                sortBy.prop === 'organic_click_share' ? 'fw-semi-bold' : ''
              }
              changeValueClass={
                sortBy.prop === 'change__organic_click_share'
                  ? 'fw-semi-bold'
                  : ''
              }
            />
          )
        },
      },
      options: [
        {
          name: 'organic_click_share',
          label: t('content:brandsOrganicShareClick'),
        },
        {
          name: 'change__organic_click_share',
          label: t('content:changeInBrandsOrganicClick'),
        },
      ],
      tooltip: {
        content: t('content:organicClickTooltip'),
      },
      filter: getSecondaryFilterObject(
        t('content:brandsOrganicShareClick'),
        'organic_click_share',
        'percentage',
        undefined,
        undefined,
        undefined,
        t('content:changeInBrandsOrganicClick'),
      ),
    },
    {
      name: 'pct_brand_organic_clicks',
      label: enableKeywordsPageChanges
        ? c('perOfMyClicks')
        : t('content:percentageOfBrandOrganicClicks'),
      cell: {
        children: (e: DataObj) => {
          return (
            <ListChangeData
              value={e?.pct_brand_organic_clicks?.value}
              changeValue={getOrganicClickShareChange(
                e?.pct_brand_organic_clicks?.change * 100,
              )}
              percentage
              decimalScale={2}
              customClass={
                sortBy.prop === 'pct_brand_organic_clicks' ? 'fw-semi-bold' : ''
              }
              changeValueClass={
                sortBy.prop === 'change__pct_brand_organic_clicks'
                  ? 'fw-semi-bold'
                  : ''
              }
            />
          )
        },
      },
      options: [
        {
          name: 'pct_brand_organic_clicks',
          label: enableKeywordsPageChanges
            ? c('perOfMyClicks')
            : t('content:percentageOfBrandOrganicClicks'),
        },
        {
          name: 'change__pct_brand_organic_clicks',
          label: enableKeywordsPageChanges
            ? c('changeInPerOfMyClicks')
            : t('content:changeInPercentageOfBrandOrganicClicks'),
        },
      ],
      tooltip: {
        content: enableKeywordsPageChanges
          ? c('perOfMyClicksTooltip')
          : t('content:pctBrandOrganicClicksTooltip'),
      },
      filter: getSecondaryFilterObject(
        enableKeywordsPageChanges
          ? c('perOfMyClicks')
          : t('content:percentageOfBrandOrganicClicks'),
        'pct_brand_organic_clicks',
        'percentage',
        undefined,
        undefined,
        undefined,
        enableKeywordsPageChanges
          ? c('changeInPerOfMyClicks')
          : t('content:changeInPercentageOfBrandOrganicClicks'),
      ),
    },
    {
      name: 'est_daily_search_volume',
      label: t('content:estDailySearchVolume'),
      cell: {
        children: (keyword: DataObj) => {
          return (
            <NumericFormat
              value={Math.round(keyword?.est_daily_search_volume)}
              thousandSeparator={true}
              displayType='text'
              className={
                sortBy.prop === 'est_daily_search_volume' ? 'fw-semi-bold' : ''
              }
            />
          )
        },
      },
      tooltip: {
        content: t('content:estDailySearchVolumeTooltip'),
      },
      filter: getSecondaryFilterObject(
        `Est. Daily Search Volume`,
        'est_daily_search_volume',
        'number',
        'number', // typefield
        {}, // selectProps
        {
          onlyWholeNumbers: true,
        }, // numberProps
      ),
    },
    {
      name: 'est_clicks_for_top_position',
      label: t('content:estDailyClicks'),
      ...getContentTableHeaderTooltip('estDailyClicksForTopPosition'),
      cell: {
        children: (keyword: DataObj) => {
          return (
            <NumericFormat
              value={Math.round(keyword?.est_clicks_for_top_position)}
              thousandSeparator={true}
              displayType='text'
              className={
                sortBy.prop === 'est_clicks_for_top_position'
                  ? 'fw-semi-bold'
                  : ''
              }
            />
          )
        },
      },
      filter: getSecondaryFilterObject(
        `Est. Daily Clicks for Top Position`,
        'est_clicks_for_top_position',
        'number',
        'number', // typefield
        {}, // selectProps
        {
          onlyWholeNumbers: true,
        }, // numberProps
      ),
    },
    {
      name: '',
      label: '',
      cell: {
        children: (e: DataObj) => {
          const link = `/content/rank-tracker/keywords/${encodeURIComponent(
            e?.keyword,
          )}/ranked-products?marketplace_id=${
            e?.marketplace_id
          }&country_code=${e?.country_code}&marketplace_name=${
            e?.marketplace_name
          }&customer_id=${searchQueryParams?.customer_id ?? customer.id}&brand_id=${
            searchQueryParams?.brand_id ?? customer.id
          }`
          return !e?.totalRow ? (
            <ButtonGroup
              buttons={[
                {
                  actions: [
                    {
                      icon: 'pencil',
                      callout: () => {
                        setActionSidedrawer({
                          type: brandedKeywordsActionConstant.ADD_KEYWORDS_RANK_TRACKER,
                          isOpen: true,
                          keywordData: {
                            id: 0,
                            keyword_text: e.keyword,
                            marketplaces: [
                              {
                                ad_market_id: e.marketplace_id,
                                name: e.marketplace_name,
                              },
                            ],
                            match_type: '',
                          },
                        })
                      },
                      text: t('content:editKeywordButton'),
                    },
                  ],
                },
                {
                  children: c('viewProducts'),
                  as: 'link',
                  to: link,
                  routerComponent: Link,
                },
              ]}
            />
          ) : null
        },
      },
      isButton: true,
      style: {
        minWidth: '140px',
      },
      noSort: true,
    },
  ]
}

export default KeywordsTableConfig
