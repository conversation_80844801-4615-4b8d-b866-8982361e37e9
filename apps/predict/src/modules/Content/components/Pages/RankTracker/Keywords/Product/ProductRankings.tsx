import React, {
  useCallback,
  useContext,
  useEffect,
  useMemo,
  useState,
} from 'react'
import { Link, useLocation, useParams } from 'react-router-dom'
import { useInfiniteQuery, useQuery } from '@tanstack/react-query'
import moment from 'moment'
import {
  type ActiveTableFilterObj,
  Alert,
  capitalize,
  getApiUrlPrefix,
  Icon,
  Mdash,
  MdashCheck,
  PrimaryTableCell,
  type SortColumnProps,
  standardSortParams,
  StandardTable,
  Tooltip,
  useIsMounted,
  useMediaQuery,
} from '@patterninc/react-ui'
import { useDecodedUserTokenId } from '@predict-hooks'
import {
  c,
  csvToastCallout,
  type DownloadOptionsType,
  getUserSettingEndpoint,
  parseParams,
  productListingRoute,
  saveCustomization,
  SecureAxios,
  tryLocalStorageParse,
  useTranslate,
} from '@predict-services'
import { useLanguageParams } from 'src/modules/Content/components/Common/ContentHooks'

import ListChangeData from '../../../../../../../common/components/ListChangeData/ListChangeData'
import {
  type ActiveFilterAPIProps,
  applySecondaryFilterFunction,
  CustomFilterHelper,
  customFilterParams,
  RemoveFilter,
  removeFilterFromAPIState,
  removeFilterFromState,
  RemoveFilterState,
  resetCustomFilter,
} from '../../../../../../../common/components/Tables/CustomFilterHelper'
import SecondaryFilter, {
  type ActiveFilterProps,
} from '../../../../../../../common/components/Tables/SecondaryFilter'
import TableTooltip from '../../../../../../../common/components/TableTooltip/TableTooltip'
import { useBrandChangeRedirect } from '../../../../../../../common/hooks/useBrandChange'
import { getSelectedConfig } from '../../../../../../../common/services/TableConfigService'
import { ThemeContext } from '../../../../../../../Context'
import { useUser } from '../../../../../../../context/user-context'
import {
  decimalScaleHelper,
  trafficCacheTime,
} from '../../../../../../Traffic/services/TrafficHelperService'
import {
  commonTrafficHeaders,
  convertTableHeaderCount,
} from '../../../../../../Traffic/services/TrafficTableHeaderHelperService'
import { getContentTableHeaderTooltip } from '../../../../Common/ContentTooltipHelper'
import styles from '../../Common/_rank-tracker.module.scss'
import RankGraphData from '../../Common/RankGraphData/RankGraphData'
import {
  getOrganicClickShareChange,
  useRankTrackerDates,
} from '../../Common/RankTrackerHelperService'
import RankTrackerSparklineWrapper from '../../Common/RankTrackerSparkline/RankTrackerSparklineWrapper'
import { RankTrackerContext } from '../../RankTrackerContext'

type GraphData = {
  date: string
  organic_rank: number
  est_organic_clicks: number
  previous_median_rank?: number
}

interface KeyProps {
  value: number
  type: string
  display: string
  change: number
  pct_change: number
  comparison?: number
}

export type ProductRankingItem = {
  asin: string
  market_product_id: string
  master_product_id: string
  marketplace_id: string
  marketplace_name: string
  country_code: string
  product_title: string
  product_image_url: string
  organic_rank: { value: number; change: number }
  est_organic_clicks: { value: number; change: number }
  organic_click_share: { value: number; change: number }
  clicks_by_day: GraphData[]
  sold_by_pattern: boolean
  multiple_market_products: number
  attributed_sales: KeyProps
  cost: KeyProps
  conversion_rate: KeyProps
  acos: KeyProps
  roas: KeyProps
  cpc: KeyProps
  click_through_rate: KeyProps
  product_url?: string
}

type customSelectionCalloutProps = {
  name: string
  options?: ArrOption[]
}
type ArrOption = {
  label: string
  name: string
}

const ProductRankings = (): React.JSX.Element => {
  const {
      updateBreadcrumbs,
      customer,
      startDate,
      endDate,
      marketplaceIds,
      tags,
      secondRange_startDate,
      secondRange_endDate,
      firstRange_startDate,
      firstRange_endDate,
      timeframe,
    } = useContext(ThemeContext),
    { t } = useTranslate('content'),
    { state: rankTrackerState } = useContext(RankTrackerContext),
    aggregate_by = timeframe.aggregation,
    [search, setSearch] = useState<string>(''),
    encodedKeyword = useParams().keyword as string,
    marketProductId = useParams().marketProductId as string,
    //get marketplace_id from queryString
    { search: searchLocation, pathname } = useLocation(),
    searchParams = parseParams(searchLocation),
    marketplaceId = searchParams.marketplace_id,
    marketplaceName = searchParams.marketplace_name
      ? searchParams.marketplace_name
      : '',
    [searchURL, setSearchURL] = useState<string>(''),
    tableId = `product_rankings`,
    [sortBy, setSortBy] = useState(
      tryLocalStorageParse(`sort_by_${tableId}`) ?? {
        prop: 'est_organic_clicks',
        flip: false,
        isByChange: false,
      },
    ),
    isMounted = useIsMounted(),
    keyword = decodeURIComponent(encodeURIComponent(encodedKeyword)),
    screenIsMdMax = useMediaQuery({ type: 'max', breakpoint: 'md' }),
    user = useUser().user,
    { code, symbol } = user?.current_currency ?? {},
    userCurrency = useMemo(() => {
      return {
        currency_code: code,
        currency_symbol: symbol,
      }
    }, [code, symbol]),
    defaultColumns = [
      t('organicRank'),
      t('estimatedOrganicClicks'),
      t('organicClickShare'),
      t('adSales'),
      c('adSpend'),
      t('adConversionRate'),
    ],
    userId = useDecodedUserTokenId()

  const [selectionList, setSelectionList] = useState(defaultColumns)

  useEffect(() => {
    if (marketplaceId && marketplaceName) {
      sessionStorage.setItem(
        'productRankingsMarketplaceId',
        marketplaceId as string,
      )
      const selectedMarketplaceName =
        searchParams.country_code === 'US'
          ? `${marketplaceName} ${searchParams.country_code}`
          : `${marketplaceName}`
      sessionStorage.setItem(
        'productRankingsMarketplaceLabel',
        selectedMarketplaceName,
      )
    }
  }, [marketplaceId, marketplaceName, searchParams.country_code])

  // Secondary Filter

  const [secondaryFiltersback, setSecondaryFiltersback] =
    useState<ActiveFilterProps>({})

  const [secondaryFiltersAPI, setSecondaryFiltersAPI] =
    useState<ActiveFilterAPIProps>({})
  const [responseType, setResponseType] = useState<string>('get')

  const SecondaryFilterCallback = useCallback((filter: ActiveFilterProps) => {
    setSecondaryFiltersback((prevState) => ({
      ...prevState,
      ...filter,
    }))
  }, [])
  const CustomFilterTableAPIendpoint =
    'content:rankTracker:product_rankings:customFilterTable'
  const { endpoint: customFilterUserSettingUrl } = getUserSettingEndpoint(
      CustomFilterTableAPIendpoint,
    ),
    { status: customFilterStatus, data: customGetFilterApiResponse } = useQuery(
      {
        queryKey: [customFilterUserSettingUrl, customer?.vendor_id],
        queryFn: ({ signal }) =>
          SecureAxios.get(customFilterUserSettingUrl, { signal }),
        gcTime: trafficCacheTime,
      },
    )

  const columnSettingsData =
    customFilterStatus === 'success' && customGetFilterApiResponse?.data

  const ProspectBrandNotSupportedList: Array<string> = useMemo(() => {
    return [
      'Ad Sales',
      'Ad Spend',
      'Ad Conversion Rate',
      'ACoS',
      'ROAS',
      'CPC',
      'Click Through Rate',
    ]
  }, [])

  useEffect(() => {
    if (columnSettingsData) {
      if (!customer.vendor_id) {
        const keys = Object.keys(columnSettingsData)
        const newdata = { ...columnSettingsData }
        let isSalesColumnExist = false
        keys?.forEach((item: string) => {
          const itemPresent = ProspectBrandNotSupportedList.includes(item)
          if (itemPresent) {
            isSalesColumnExist = true
            delete newdata?.[item]
          }
        })
        if (isSalesColumnExist) {
          setResponseType('post')
          setSecondaryFiltersAPI(newdata)
        } else {
          setResponseType('get')
          setSecondaryFiltersAPI(newdata)
        }
      } else {
        setResponseType('get')
        setSecondaryFiltersAPI(columnSettingsData)
      }
    }
  }, [ProspectBrandNotSupportedList, columnSettingsData, customer.vendor_id])

  useEffect(() => {
    if (responseType === 'post') {
      CustomFilterHelper({
        api: customFilterUserSettingUrl,
        data: secondaryFiltersAPI,
        type: 'custom-filter',
      })
    }
  }, [customFilterUserSettingUrl, responseType, secondaryFiltersAPI])

  const [filtersToReset, setFiltersToReset] = useState([''])
  const applySecondaryFilter = useCallback(
    (headerTitle?: string) => {
      const headerText = headerTitle?.replace('Filter ', '')
      const applySecondaryFilter = applySecondaryFilterFunction(
        secondaryFiltersback,
        headerText,
      )
      if (applySecondaryFilter !== null) {
        setSecondaryFiltersAPI((prevState) => ({
          ...prevState,
          ...applySecondaryFilter,
        }))
        setResponseType('post')
      }
      setFiltersToReset([headerTitle ?? ''])
    },
    [secondaryFiltersback],
  )

  const [tempSort, setTempSort] = useState('')
  const handleCustomFilterClose = useCallback(
    (headerText: string, secondaryFiltersAPI: ActiveFilterProps) => {
      setSecondaryFiltersback(secondaryFiltersAPI)
      setFiltersToReset([headerText])
      setTempSort(sortBy?.prop)
    },
    [sortBy?.prop],
  )
  const resetSecondaryFilter = useCallback(
    (headerText: string) =>
      resetCustomFilter({
        headerText,
        customFilterUserSettingUrl,
        secondaryFiltersAPI,
        secondaryFiltersData: secondaryFiltersback,
        setSecondaryFiltersAPI,
        setSecondaryFiltersData: setSecondaryFiltersback,
        setDefaultSort() {
          setSortBy({
            prop: 'est_organic_clicks',
            flip: false,
            isByChange: false,
          })
        },
      }),
    [secondaryFiltersAPI, customFilterUserSettingUrl, secondaryFiltersback],
  )
  const getSecondaryFilterObject = useCallback(
    ({
      filterKey,
      tableKey,
      type = 'number',
      changeValueLabel,
    }: {
      filterKey: string
      tableKey: string
      type?: 'number' | 'currency' | 'percentage'
      changeValueLabel?: string
    }) => {
      return {
        headerText: filterKey,
        secondarySortProp: tempSort,
        closeCallout: (headerText: string) =>
          handleCustomFilterClose(headerText, secondaryFiltersAPI),
        onSortChange: (sortPropName: string) => setTempSort(sortPropName),
        callout: applySecondaryFilter,
        enableFilterByChangeValues: Boolean(changeValueLabel),
        resetCallback: changeValueLabel ? resetSecondaryFilter : undefined,
        disabled: !(
          (secondaryFiltersback?.[filterKey]?.text &&
            secondaryFiltersback?.[filterKey]?.value) ||
          (changeValueLabel &&
            secondaryFiltersback?.[changeValueLabel]?.text &&
            secondaryFiltersback?.[changeValueLabel]?.value)
        ),
        children: (
          <SecondaryFilter
            filterKey={filterKey}
            tableKey={tableKey}
            {...{ textFieldType: type }}
            prefix={type === 'currency' ? '$' : ''}
            suffix={type === 'percentage' ? '%' : ''}
            SecondaryFilterCallback={SecondaryFilterCallback}
            selectedSecondaryFilters={secondaryFiltersAPI}
            filtersToReset={filtersToReset}
            enableFilterByChangeValues={Boolean(changeValueLabel)}
          />
        ),
      }
    },
    [
      SecondaryFilterCallback,
      applySecondaryFilter,
      filtersToReset,
      handleCustomFilterClose,
      secondaryFiltersAPI,
      secondaryFiltersback,
      tempSort,
      resetSecondaryFilter,
    ],
  )
  const removeFilter = useCallback(
    (removeFilter?: string) => {
      const removedFilter = RemoveFilter(secondaryFiltersAPI, removeFilter)
      setSecondaryFiltersAPI(removedFilter)
      const removedFiltersState = RemoveFilterState(
        secondaryFiltersback,
        removeFilter,
      )
      setSecondaryFiltersback(removedFiltersState)
      setResponseType('post')
    },
    [secondaryFiltersAPI, secondaryFiltersback],
  )
  const removeFilters = useCallback(
    (removeFilter?: ActiveTableFilterObj) => {
      const removedFilter = removeFilterFromAPIState(
        secondaryFiltersAPI,
        removeFilter,
      )
      const removedFiltersState = removeFilterFromState(
        secondaryFiltersback,
        removeFilter,
      )
      setSecondaryFiltersAPI(removedFilter)
      setSecondaryFiltersback(removedFiltersState)
      setResponseType('post')
    },
    [secondaryFiltersAPI, secondaryFiltersback],
  )
  const filterChangeValueDetails: { [key: string]: string } = useMemo(
    () => ({
      'Est. Organic Clicks': t('changeInEstOrganicClicks'),
      'Organic Click Share': t('changeInOrganicClickShare'),
      'Ad Sales': t('changeInAdSales'),
      'Ad Spend': t('changeInAdSpend'),
      'Ad Conversion Rate': t('changeInAdConversionRate'),
      ACoS: t('changeInAcos'),
      ROAS: t('changeInROAS'),
      CPC: t('changeInCPC'),
      'Click Through Rate': t('changeInClickThroughRate'),
    }),
    [t],
  )
  const extraCustomFilterParams = useMemo(() => {
    const keys = Object.keys(secondaryFiltersAPI)
    const newdata = { ...secondaryFiltersAPI }
    if (!customer.vendor_id) {
      keys?.forEach((item: string) => {
        const itemPresent = ProspectBrandNotSupportedList.includes(item)
        if (itemPresent) {
          delete newdata?.[item]
        }
      })
    }

    const filterParams = customFilterParams(
      newdata,
      selectionList,
      filterChangeValueDetails,
    )
    return { filter: filterParams }
  }, [
    ProspectBrandNotSupportedList,
    customer.vendor_id,
    secondaryFiltersAPI,
    selectionList,
    filterChangeValueDetails,
  ])

  const {
    newStartDate,
    newEndDate,
    comparisonStartDate,
    comparisonEndDate,
    currentTimeframeCheck,
    previousDates,
  } = useRankTrackerDates()
  const languageParams = useLanguageParams()
  // Fetch data
  const rankTrackerApi = `${getApiUrlPrefix('rank-tracker')}/api/v3/customer/${
      searchParams?.customer_id ?? customer.id
    }`,
    rankTrackerProductsApi = `${rankTrackerApi}/products`,
    commonParams = useMemo(() => {
      return {
        keyword: keyword,
        start_date: currentTimeframeCheck ? newStartDate : startDate,
        end_date: timeframe.type === 'current' ? newEndDate : endDate,
        ...(search ? { search_for: search } : {}),
        currency_code: code,
        marketplace_ids: marketplaceIds ?? [],
        ...(customer.vendor_id ? { brand_id: customer.vendor_id } : {}),
        comparison_start: secondRange_startDate
          ? secondRange_startDate
          : currentTimeframeCheck
            ? comparisonStartDate
            : previousDates?.startDate,
        comparison_end: secondRange_endDate
          ? secondRange_endDate
          : timeframe.type === 'current'
            ? comparisonEndDate
            : previousDates?.endDate,
        sort: standardSortParams(sortBy, ['product_title']),
        ...(rankTrackerState?.productsSoldBy?.id !== 0
          ? { sold_by_pattern: true }
          : {}),
        ...(tags?.tag_ids?.length > 0
          ? {
              tag_ids: tags.tag_ids,
              match_all_tags: tags.matchAllTags,
              expires_in: 300,
            }
          : {}),
        ...extraCustomFilterParams,
        ...(aggregate_by ? { aggregate_by } : {}),
        ...languageParams,
      }
    }, [
      aggregate_by,
      code,
      comparisonEndDate,
      comparisonStartDate,
      currentTimeframeCheck,
      customer.vendor_id,
      endDate,
      extraCustomFilterParams,
      keyword,
      marketplaceIds,
      newEndDate,
      newStartDate,
      previousDates?.endDate,
      previousDates?.startDate,
      rankTrackerState?.productsSoldBy?.id,
      search,
      secondRange_endDate,
      secondRange_startDate,
      sortBy,
      startDate,
      tags.matchAllTags,
      tags.tag_ids,
      timeframe.type,
      languageParams,
    ])

  const config = useMemo(
    () => [
      {
        label: c('product'),
        name: 'product_title',
        mainColumn: true,
        cell: {
          children: (product: ProductRankingItem) => {
            return (
              <span className='flex'>
                <PrimaryTableCell
                  sortBy={sortBy}
                  title={product?.product_title}
                  titleProp='product_title'
                  productLink={`${productListingRoute}/${product?.master_product_id}/traffic/performance`}
                  routerComponent={Link}
                  routerProp='to'
                  uniqId={{
                    id: product?.asin,
                    idLabel: 'ASIN',
                    idName: 'asin',
                  }}
                  imageProps={{
                    url: product?.product_image_url,
                    alt: product?.product_title,
                  }}
                  marketplaceNames={product?.marketplace_name}
                  externalLink={product?.product_url}
                />
              </span>
            )
          },
        },
      },
      {
        name: 'organic_rank',
        label: t('organicRank'),
        noSort: true,
        cell: {
          children: (product: ProductRankingItem) => {
            const topValue = product?.organic_rank?.value,
              changeValue = product?.organic_rank?.change

            const ranks: number[] = [],
              minRank = Math.min(...ranks.filter(Boolean)),
              maxRank = Math.max(...ranks),
              median = (arr: number[]) => {
                const mid = Math.floor(arr.length / 2),
                  nums = [...arr].sort((a, b) => a - b)
                return arr.length % 2 !== 0
                  ? nums[mid]
                  : (nums[mid - 1] + nums[mid]) / 2
              },
              tableTooltipData = [
                {
                  label: t('bestRank'),
                  value: ranks.length
                    ? minRank === Infinity
                      ? null
                      : minRank
                    : null,
                },
                {
                  label: t('medianRank'),
                  value: median(ranks),
                },
                {
                  label: t('worstRank'),
                  value: ranks.length ? maxRank : null,
                },
              ],
              showTrophy = !!topValue && topValue <= 4
            return (
              <div
                className={
                  screenIsMdMax
                    ? `flex-direction-column ${styles.width100} `
                    : styles.estOrganicClicksColumn
                }
              >
                {!topValue &&
                (!minRank || minRank === Infinity) &&
                !maxRank &&
                !median(ranks) ? (
                  <Mdash />
                ) : (
                  <>
                    <Tooltip
                      tooltipContent={<TableTooltip data={tableTooltipData} />}
                      maxWidth='none'
                      position='right'
                    >
                      <ListChangeData
                        value={topValue}
                        changeValue={changeValue}
                        customClass={
                          sortBy.prop === 'organic_rank' ? 'fw-semi-bold' : ''
                        }
                        changeValueClass={
                          sortBy.prop === 'change__organic_rank'
                            ? 'fw-semi-bold'
                            : ''
                        }
                        icon={showTrophy && <Icon icon='cup' iconSize='12px' />}
                      />
                    </Tooltip>

                    <div className={screenIsMdMax ? 'pat-mt-4' : ''}>
                      <RankTrackerSparklineWrapper
                        topValue={topValue}
                        title='Most Recent Organic Rank'
                        dataKey='organic_rank'
                        showTrophy={showTrophy}
                        reversed
                        min={minRank}
                        max={maxRank}
                        isDisplayCsvDownloadOption={!!product?.product_title}
                        csvTitle={`Organic Rank - ASIN ${product?.asin} - ${product?.marketplace_name}`}
                        linkName={`${t('organicRank')} ${t('productRankings')}`}
                        sparklineText={'Median'}
                        params={commonParams}
                        brand={customer?.customer_name}
                        keyword={keyword}
                        type='product'
                        asin={product?.asin}
                        productTitle={product?.product_title}
                        customerId={Number(customer.id)}
                        marketplaceIds={product?.marketplace_id}
                        marketProductId={product?.market_product_id}
                        apiEndpoint={`api/v3/product/${product?.market_product_id}/product_ranking_sparkline`}
                      ></RankTrackerSparklineWrapper>
                    </div>
                  </>
                )}
              </div>
            )
          },
        },
        tooltip: {
          content: t('organicRankTooltip'),
        },
        options: [
          {
            name: 'organic_rank',
            label: t('organicRank'),
          },
          {
            name: 'change__organic_rank',
            label: t('changeInOrganicRank'),
          },
        ],
        filter: getSecondaryFilterObject({
          filterKey: t('organicRank'),
          tableKey: 'organic_rank',
        }),
      },
      {
        name: 'est_organic_clicks',
        label: t('organicClicks'),
        ...getContentTableHeaderTooltip('estOrganicClicks'),
        cell: {
          children: (product: ProductRankingItem) => {
            return (
              <div className={styles.estOrganicClicksColumn}>
                <ListChangeData
                  value={product?.est_organic_clicks?.value}
                  changeValue={product?.est_organic_clicks?.change}
                  customClass={
                    sortBy.prop === 'est_organic_clicks' ? 'fw-semi-bold' : ''
                  }
                  changeValueClass={
                    sortBy.prop === 'change__est_organic_clicks'
                      ? 'fw-semi-bold'
                      : ''
                  }
                  thresholdValue={0}
                />
              </div>
            )
          },
        },
        options: [
          {
            name: 'est_organic_clicks',
            label: t('organicClicks'),
          },
          {
            name: 'change__est_organic_clicks',
            label: t('changeInEstOrganicClicks'),
          },
        ],
        filter: getSecondaryFilterObject({
          filterKey: t('estOrganicClicks'),
          tableKey: 'est_organic_clicks',
          changeValueLabel: t('changeInEstOrganicClicks'),
        }),
      },
      {
        name: 'organic_click_share',
        label: t('organicClickShare'),
        ...getContentTableHeaderTooltip('organicClickShare'),
        cell: {
          children: (product: ProductRankingItem) => {
            return (
              <ListChangeData
                value={product?.organic_click_share?.value}
                changeValue={getOrganicClickShareChange(
                  product?.organic_click_share?.change,
                )}
                percentage
                decimalScale={2}
                customClass={
                  sortBy.prop === 'organic_click_share' ? 'fw-semi-bold' : ''
                }
                changeValueClass={
                  sortBy.prop === 'change__organic_click_share'
                    ? 'fw-semi-bold'
                    : ''
                }
              />
            )
          },
        },
        options: [
          {
            name: 'organic_click_share',
            label: t('organicClickShare'),
          },
          {
            name: 'change__organic_click_share',
            label: t('changeInOrganicClickShare'),
          },
        ],
        filter: getSecondaryFilterObject({
          filterKey: t('organicClickShare'),
          tableKey: 'organic_click_share',
          type: 'percentage',
          changeValueLabel: t('changeInOrganicClickShare'),
        }),
      },
    ],
    [
      t,
      getSecondaryFilterObject,
      sortBy,
      screenIsMdMax,
      commonParams,
      customer?.customer_name,
      customer.id,
      keyword,
    ],
  )

  const originalConfig = useMemo(
    () => [
      ...config,
      {
        ...commonTrafficHeaders().attributedSales,
        ...getContentTableHeaderTooltip('adSales'),
        cell: {
          children: (product: ProductRankingItem) => {
            return (
              <MdashCheck check={!!product?.attributed_sales?.value}>
                <div className='flex'>
                  <div>
                    <ListChangeData
                      value={product?.attributed_sales?.value ?? 0}
                      changeValue={product.attributed_sales?.change ?? 0}
                      tooltipContent={
                        product?.attributed_sales?.pct_change ?? 0
                      }
                      currency={userCurrency}
                      changeFormat='currency'
                      decimalScale={decimalScaleHelper(
                        product?.attributed_sales?.change
                          ? Math.abs(product.attributed_sales?.change)
                          : 0,
                      )}
                      changeDecimalScale={decimalScaleHelper(
                        product?.attributed_sales?.change
                          ? Math.abs(product.attributed_sales?.change)
                          : 0,
                      )}
                      comparison={product?.attributed_sales?.comparison}
                      customClass={
                        sortBy.prop === 'attributed_sales' ? 'fw-semi-bold' : ''
                      }
                      changeValueClass={
                        sortBy.prop === 'change__attributed_sales'
                          ? 'fw-semi-bold'
                          : ''
                      }
                    />
                  </div>
                  {product?.multiple_market_products === 1 && (
                    <div>
                      <Tooltip
                        position='right'
                        tooltipContent={t('productRankingsTooltip')}
                        maxWidth='300px'
                        children={
                          <span>
                            <Icon
                              icon='info'
                              className='pat-mr-2'
                              iconSize='12px'
                            />
                          </span>
                        }
                      />
                    </div>
                  )}
                </div>
              </MdashCheck>
            )
          },
        },
        ...(customer?.vendor_id
          ? {
              filter: getSecondaryFilterObject({
                filterKey: c('adSales'),
                tableKey: 'attributed_sales',
                type: 'currency',
                changeValueLabel: t('changeInAdSales'),
              }),
            }
          : {}),
        ...(customer?.vendor_id ? {} : { noSort: true }),
      },
      {
        ...commonTrafficHeaders().cost,
        ...getContentTableHeaderTooltip('adSpend'),
        cell: {
          children: (product: ProductRankingItem) => {
            return (
              <MdashCheck check={!!product?.cost?.value}>
                <ListChangeData
                  value={product?.cost?.value ?? 0}
                  changeValue={product.cost?.change ?? 0}
                  tooltipContent={product?.cost?.pct_change ?? 0}
                  currency={userCurrency}
                  changeFormat='currency'
                  decimalScale={decimalScaleHelper(
                    product?.cost?.change ? Math.abs(product.cost?.change) : 0,
                  )}
                  changeDecimalScale={decimalScaleHelper(
                    product?.cost?.change ? Math.abs(product.cost?.change) : 0,
                  )}
                  comparison={product?.cost?.comparison}
                  customClass={sortBy.prop === 'cost' ? 'fw-semi-bold' : ''}
                  changeValueClass={
                    sortBy.prop === 'change__cost' ? 'fw-semi-bold' : ''
                  }
                />
              </MdashCheck>
            )
          },
        },
        ...(customer?.vendor_id
          ? {
              filter: getSecondaryFilterObject({
                filterKey: c('adSpend'),
                tableKey: 'cost',
                type: 'currency',
                changeValueLabel: t('changeInAdSpend'),
              }),
            }
          : {}),
        ...(customer?.vendor_id ? {} : { noSort: true }),
      },
      {
        name: 'conversion_rate',
        label: t('adConversionRate'),
        ...getContentTableHeaderTooltip('adConversionRate'),
        cell: {
          children: (product: ProductRankingItem) => {
            return (
              <MdashCheck check={!!product?.conversion_rate?.value}>
                <ListChangeData
                  value={product?.conversion_rate?.value ?? 0}
                  changeValue={
                    product?.conversion_rate?.change
                      ? product.conversion_rate?.change * 100
                      : 0
                  }
                  changeFormat='number'
                  tooltipContent={product?.conversion_rate?.pct_change ?? 0}
                  percentage
                  decimalScale={2}
                  comparison={product?.conversion_rate?.comparison}
                  customClass={
                    sortBy.prop === 'conversion_rate' ? 'fw-semi-bold' : ''
                  }
                  changeValueClass={
                    sortBy.prop === 'change__conversion_rate'
                      ? 'fw-semi-bold'
                      : ''
                  }
                />
              </MdashCheck>
            )
          },
        },
        options: [
          {
            name: 'conversion_rate',
            label: t('adConversionRate'),
          },
          {
            name: 'change__conversion_rate',
            label: t('changeInAdConversionRate'),
          },
        ],
        ...(customer?.vendor_id
          ? {
              filter: getSecondaryFilterObject({
                filterKey: t('adConversionRate'),
                tableKey: 'conversion_rate',
                type: 'percentage',
                changeValueLabel: t('changeInAdConversionRate'),
              }),
            }
          : {}),
        ...(customer?.vendor_id ? {} : { noSort: true }),
      },
      {
        ...commonTrafficHeaders().acos,
        cell: {
          children: (product: ProductRankingItem) => {
            return (
              <MdashCheck check={!!product?.acos?.value}>
                <ListChangeData
                  value={product?.acos?.value ?? 0}
                  changeValue={
                    product?.acos?.change ? product.acos?.change * 100 : 0
                  }
                  tooltipContent={product?.acos?.pct_change ?? 0}
                  percentage
                  decimalScale={2}
                  reverse
                  customClass={sortBy.prop === 'acos' ? 'fw-semi-bold' : ''}
                  changeValueClass={
                    sortBy.prop === 'change__acos' ? 'fw-semi-bold' : ''
                  }
                />
              </MdashCheck>
            )
          },
        },
        ...(customer?.vendor_id
          ? {
              filter: getSecondaryFilterObject({
                filterKey: c('acos'),
                tableKey: 'acos',
                type: 'percentage',
                changeValueLabel: t('changeInAcos'),
              }),
            }
          : {}),
        ...(customer?.vendor_id ? {} : { noSort: true }),
      },
      {
        name: 'roas',
        label: c('roas'),
        ...getContentTableHeaderTooltip('roas'),
        cell: {
          children: (product: ProductRankingItem) => {
            return (
              <MdashCheck check={!!product?.roas?.value}>
                <ListChangeData
                  value={product?.roas?.value ?? 0}
                  changeValue={product?.roas?.change ?? 0}
                  tooltipContent={product?.roas?.pct_change ?? 0}
                  decimalScale={2}
                  customClass={sortBy.prop === 'roas' ? 'fw-semi-bold' : ''}
                  changeValueClass={
                    sortBy.prop === 'change__roas' ? 'fw-semi-bold' : ''
                  }
                />
              </MdashCheck>
            )
          },
        },
        ...(customer?.vendor_id
          ? {
              options: [
                {
                  name: 'roas',
                  label: c('roas'),
                },
                {
                  name: 'change__roas',
                  label: t('changeInROAS'),
                },
              ],
            }
          : {}),
        ...(customer?.vendor_id
          ? {
              filter: getSecondaryFilterObject({
                filterKey: c('roas'),
                tableKey: 'roas',
                changeValueLabel: t('changeInROAS'),
              }),
            }
          : {}),
        ...(customer?.vendor_id ? {} : { noSort: true }),
      },
      {
        name: 'cpc',
        label: c('cpc'),
        ...getContentTableHeaderTooltip('cpc'),
        cell: {
          children: (product: ProductRankingItem) => {
            return (
              <MdashCheck check={!!product?.cpc?.value}>
                <ListChangeData
                  value={product?.cpc?.value ?? 0}
                  changeValue={product?.cpc?.change ?? 0}
                  currency={userCurrency}
                  changeFormat='currency'
                  comparison={product?.cpc?.comparison}
                  decimalScale={2}
                  reverse
                  customClass={sortBy.prop === 'cpc' ? 'fw-semi-bold' : ''}
                  tooltipContent={product?.cpc?.pct_change ?? 0}
                  changeValueClass={
                    sortBy.prop === 'change__cpc' ? 'fw-semi-bold' : ''
                  }
                />
              </MdashCheck>
            )
          },
        },
        ...(customer?.vendor_id
          ? {
              options: [
                {
                  name: 'cpc',
                  label: c('cpc'),
                },
                {
                  name: 'change__cpc',
                  label: t('changeInCPC'),
                },
              ],
            }
          : {}),
        ...(customer?.vendor_id
          ? {
              filter: getSecondaryFilterObject({
                filterKey: c('cpc'),
                tableKey: 'cpc',
                type: 'currency',
                changeValueLabel: t('changeInCPC'),
              }),
            }
          : {}),
        ...(customer?.vendor_id ? {} : { noSort: true }),
      },
      {
        name: 'click_through_rate',
        label: t('clickThroughRate'),
        ...getContentTableHeaderTooltip('clickThroughRate'),
        cell: {
          children: (product: ProductRankingItem) => {
            return (
              <MdashCheck check={!!product?.click_through_rate?.value}>
                <ListChangeData
                  value={product?.click_through_rate?.value ?? 0}
                  changeValue={
                    product?.click_through_rate?.change
                      ? product.click_through_rate?.change * 100
                      : 0
                  }
                  comparison={product?.click_through_rate?.comparison}
                  changeFormat='number'
                  tooltipContent={product?.click_through_rate?.pct_change ?? 0}
                  percentage
                  decimalScale={2}
                  customClass={
                    sortBy.prop === 'click_through_rate' ? 'fw-semi-bold' : ''
                  }
                  changeValueClass={
                    sortBy.prop === 'change__click_through_rate'
                      ? 'fw-semi-bold'
                      : ''
                  }
                />
              </MdashCheck>
            )
          },
        },
        ...(customer?.vendor_id
          ? {
              options: [
                {
                  name: 'click_through_rate',
                  label: t('clickThroughRate'),
                },
                {
                  name: 'change__click_through_rate',
                  label: t('changeInClickThroughRate'),
                },
              ],
            }
          : {}),
        ...(customer?.vendor_id
          ? {
              filter: getSecondaryFilterObject({
                filterKey: t('clickThroughRate'),
                tableKey: t('changeInClickThroughRate'),
                type: 'percentage',
                changeValueLabel: t('changeInClickThroughRate'),
              }),
            }
          : {}),
        ...(customer?.vendor_id ? {} : { noSort: true }),
      },
    ],
    [
      config,
      customer?.vendor_id,
      getSecondaryFilterObject,
      t,
      userCurrency,
      sortBy.prop,
    ],
  )

  const totalConfigItems = originalConfig.slice(1)
  const totalList = totalConfigItems.map((item) => item.label),
    {
      status,
      data: apiResponse,
      fetchNextPage,
      hasNextPage,
    } = useInfiniteQuery({
      queryKey: [
        rankTrackerProductsApi,
        startDate,
        endDate,
        search,
        sortBy,
        marketProductId,
        marketplaceIds,
        previousDates,
        tags,
        rankTrackerState?.productsSoldBy?.id,
        secondRange_startDate,
        secondRange_endDate,
        extraCustomFilterParams,
        aggregate_by,
      ],
      queryFn: ({ pageParam = 1, signal }) => {
        const params = {
          ...commonParams,
          page: pageParam,
          per_page: 20,
        }
        return SecureAxios.get(rankTrackerProductsApi, { params, signal })
      },
      initialPageParam: 1,
      gcTime: trafficCacheTime,
      getNextPageParam: (previousResponse) => {
        return previousResponse?.data?.data?.pagination?.lastPage
          ? undefined
          : previousResponse?.data?.data?.pagination?.next_page
      },
    }),
    count = apiResponse?.pages[0]?.data?.data?.pagination?.count,
    data = apiResponse
      ? apiResponse.pages.flatMap((page) => {
          return page?.data?.data?.data
        })
      : []

  const sort: SortColumnProps['sorter'] = (sortObj) => {
    isMounted() &&
      setSortBy({
        prop: sortObj.activeColumn,
        flip: sortObj.direction,
        lowerCaseParam: sortObj.lowerCaseParam,
      })
  }

  const searchInputHandler = (value: string) => {
    setSearch(value ?? '')
  }

  const csvDownloadOptions: DownloadOptionsType = [
    {
      linkName: t('productRankings'),
      csvName: t('productRankings'),
      csvFormat: {
        api: (csvParams) =>
          SecureAxios.get(rankTrackerProductsApi, { params: csvParams }).then(
            (response) => {
              return response.data
            },
          ),
        params: {
          ...commonParams,
          async: true,
          csv_name: t('productRankings'),
        },
        callout: csvToastCallout(),
      },
    },
  ]

  const keywordSummary = (
    <a href={searchURL} target='_blank' rel='noopener noreferrer'>
      <h3>{c('keyword')}</h3>
      {capitalize(keyword)}
      <div className='pat-mt-2 fs-12 fc-purple'>{marketplaceName}</div>
    </a>
  )

  useEffect(() => {
    updateBreadcrumbs({
      name: t('productRankings') + ` - ${keyword}`,
      link: pathname,
    })
  }, [pathname, keyword, updateBreadcrumbs, t])

  useBrandChangeRedirect()

  const alertmsg = (
    <MdashCheck
      check={!!sessionStorage.getItem('productRankingsMarketplaceLabel')}
    >
      {(moment(firstRange_startDate).isAfter(moment('2022-02-28'), 'day') &&
        moment(firstRange_endDate).isBefore(moment('2022-04-01'), 'day')) ||
      moment('2022-02-28').isBetween(
        firstRange_startDate,
        firstRange_endDate,
      ) ||
      moment('2022-04-01').isBetween(
        firstRange_startDate,
        firstRange_endDate,
      ) ||
      moment('2022-03-01').isBetween(startDate, endDate) ||
      moment('2022-03-31').isBetween(startDate, endDate) ||
      (moment(secondRange_startDate).isAfter(moment('2022-02-28'), 'day') &&
        moment(secondRange_endDate).isBefore(moment('2022-04-01'), 'day')) ||
      moment('2022-03-01').isBetween(
        secondRange_startDate,
        secondRange_endDate,
      ) ||
      moment('2022-03-31').isBetween(secondRange_startDate, secondRange_endDate)
        ? t('productRankingsAlert', {
            data: sessionStorage.getItem('productRankingsMarketplaceLabel'),
          })
        : t('productRankingsAlertTwo', {
            data: sessionStorage.getItem('productRankingsMarketplaceLabel'),
          })}
    </MdashCheck>
  )
  const amazonSearchUrlCallback = (type: string) => {
    setSearchURL(type)
  }

  const apiUrl = `${getApiUrlPrefix('user-settings')}/v1/${
    userId
  }/predict/content:product-rankings`

  const { status: userSettingStatus, data: userSettingsApiResponse } = useQuery(
    {
      queryKey: [userId, encodedKeyword],
      queryFn: ({ signal }) => SecureAxios.get(apiUrl, { signal }),
      gcTime: trafficCacheTime,
    },
  )

  const userSettingsData =
    userSettingStatus === 'success' &&
    userSettingsApiResponse?.data?.selectedColumns

  useEffect(() => {
    if (userSettingsData) {
      setSelectionList(userSettingsData)
    }
  }, [userSettingsData])
  const customSelectionCallout = (
    selectedList: string[],
    setToDefault?: boolean,
  ) => {
    if (
      !selectedList.includes(
        originalConfig.filter((selectionItem) => {
          const item = selectionItem as customSelectionCalloutProps
          if (item?.options) {
            const value = item.options.find(
              (option) => option.name === sortBy.prop,
            )
            return !!value
          }
          return item.name === sortBy.prop
        })[0].label,
      )
    ) {
      setSortBy((prevState: { prop: string; flip: boolean }) => ({
        ...prevState,
        prop: originalConfig[0].name,
      }))
    }
    setSelectionList(selectedList)
    saveCustomization({
      api: apiUrl,
      selected: selectedList,
      setToDefault: setToDefault,
      type: 'table',
    })

    const keys = Object.keys(secondaryFiltersAPI)
    const originLength = Object.keys(secondaryFiltersAPI)?.length
    const newdata = { ...secondaryFiltersAPI }
    keys?.forEach((item: string) => {
      const itemNotPresent = !selectedList.includes(item)
      if (itemNotPresent) {
        delete newdata?.[item]
      }
    })
    const updatedLength = Object.keys(newdata)?.length
    if (originLength !== updatedLength) {
      setSecondaryFiltersAPI(newdata)
      setResponseType('post')
    }
  }
  const ProspectBrandNotSupportedSortList: Array<string> = useMemo(() => {
    return [
      'change__attributed_sales',
      'attributed_sales',
      'cost',
      'change__cost',
      'conversion_rate',
      'change__conversion_rate',
      'acos',
      'change__acos',
      'roas',
      'change__roas',
      'cpc',
      'change__cpc',
      'click_through_rate',
      'change__click_through_rate',
    ]
  }, [])

  useEffect(() => {
    if (!customer.vendor_id) {
      const isSortSupport = ProspectBrandNotSupportedSortList.includes(
        sortBy.prop,
      )
      if (isSortSupport) {
        setSortBy((prevState: { prop: string; flip: boolean }) => ({
          ...prevState,
          prop: originalConfig[0].name,
        }))
      }
    }
  }, [
    originalConfig,
    customer.vendor_id,
    ProspectBrandNotSupportedSortList,
    sortBy.prop,
  ])
  const customizeColumnConfig = useMemo(() => {
    return [
      originalConfig[0],
      ...getSelectedConfig(originalConfig, selectionList),
    ]
  }, [originalConfig, selectionList])

  return (
    <div>
      <Alert type='info' text={alertmsg} customClass='pat-mb-5' />
      <RankGraphData
        allowFilter
        api={rankTrackerApi}
        csvTitle={t('productRankings')}
        summaryEntity='Products'
        extraParams={{
          ...(rankTrackerState?.productsSoldBy?.id !== 0
            ? { sold_by_pattern: true }
            : {}),
          keyword: keyword,
          marketplace_id: marketplaceId,
        }}
        showNewHeaderMetric
        amazonSearchUrlCallback={amazonSearchUrlCallback}
        pageName='ProductRankings'
        keywordSummary={keywordSummary}
      />
      <StandardTable
        data={data}
        config={customizeColumnConfig}
        dataKey='keyword'
        hasData={data?.length > 0}
        loading={status === 'pending'}
        hasMore={!!(status === 'success' && hasNextPage)}
        tableId={tableId}
        twoLineLabel
        sort={sort}
        sortBy={sortBy}
        noDataFields={{
          primaryText: t('noProductsFound'),
          secondaryText: t('couldNotFindAnyProduct'),
        }}
        getData={fetchNextPage}
        successStatus={status === 'success'}
        tableHeaderProps={{
          header: {
            name: t('product', { count: count }),
            value: convertTableHeaderCount(count),
          },
          search: {
            value: search,
            onChange: (value) => searchInputHandler(value),
            placeholder: c('searchProducts'),
            debounce: 250,
          },
          download: {
            csvDownloadOptions: csvDownloadOptions,
            initialDisplay: true,
            show: true,
          },
          columnFilterProps: {
            activeFilters: secondaryFiltersAPI,
            remove: removeFilters,
          },
          customColumnProps: {
            list: totalList,
            selected: selectionList,
            callout: customSelectionCallout,
            setToDefaultCallout: () =>
              customSelectionCallout(defaultColumns, true),
          },
        }}
        activeFilters={secondaryFiltersAPI}
        removeFilters={removeFilter}
      />
    </div>
  )
}

export default ProductRankings
