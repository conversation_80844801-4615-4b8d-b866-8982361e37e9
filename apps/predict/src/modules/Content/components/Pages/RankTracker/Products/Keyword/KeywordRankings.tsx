import React, {
  useCallback,
  useContext,
  useEffect,
  useMemo,
  useState,
} from 'react'
import { NumericFormat } from 'react-number-format'
import { useLocation, useParams } from 'react-router-dom'
import { useInfiniteQuery, useQuery } from '@tanstack/react-query'
import moment from 'moment'
import {
  type ActiveTableFilterObj,
  Alert,
  getApiUrlPrefix,
  hasValue,
  Icon,
  Image,
  MdashCheck,
  ReviewStars,
  type SortColumnProps,
  standardSortParams,
  StandardTable,
  Tag,
  Tooltip,
  TrimText,
  useIsMounted,
  useMediaQuery,
} from '@patterninc/react-ui'
import { useDecodedUserTokenId } from '@predict-hooks'
import {
  c,
  csvToastCallout,
  getUserSettingEndpoint,
  saveCustomization,
  SecureAxios,
  tryLocalStorageParse,
  useTranslate,
} from '@predict-services'
import { useLanguageParams } from 'src/modules/Content/components/Common/ContentHooks'

import { Currency } from '../../../../../../../common/components'
import ListChangeData from '../../../../../../../common/components/ListChangeData/ListChangeData'
import ComparisonTable from '../../../../../../../common/components/Tables/ComparisonTable/ComparisonTable'
import {
  type ActiveFilterAPIProps,
  applySecondaryFilterFunction,
  CustomFilterHelper,
  customFilterParams,
  RemoveFilter,
  removeFilterFromAPIState,
  removeFilterFromState,
  RemoveFilterState,
  resetCustomFilter,
} from '../../../../../../../common/components/Tables/CustomFilterHelper'
import SecondaryFilter, {
  type ActiveFilterProps,
} from '../../../../../../../common/components/Tables/SecondaryFilter'
import TableTooltip from '../../../../../../../common/components/TableTooltip/TableTooltip'
import { useBrandChangeRedirect } from '../../../../../../../common/hooks/useBrandChange'
import { type DownloadOptionsType } from '../../../../../../../common/services/CsvHelpers'
import { getSelectedConfig } from '../../../../../../../common/services/TableConfigService'
import { ThemeContext } from '../../../../../../../Context'
import { useUser } from '../../../../../../../context/user-context'
import {
  decimalScaleHelper,
  trafficCacheTime,
} from '../../../../../../Traffic/services/TrafficHelperService'
import {
  commonTrafficHeaders,
  convertTableHeaderCount,
} from '../../../../../../Traffic/services/TrafficTableHeaderHelperService'
import { getContentTableHeaderTooltip } from '../../../../Common/ContentTooltipHelper'
import RankGraphData from '../../Common/RankGraphData/RankGraphData'
import {
  getOrganicClickShareChange,
  useRankTrackerDates,
} from '../../Common/RankTrackerHelperService'
import RankTrackerSparklineWrapper from '../../Common/RankTrackerSparkline/RankTrackerSparklineWrapper'
import { RankTrackerContext } from '../../RankTrackerContext'
import styles from './_keyword-rankings.module.scss'
import KeywordRankingsProductProfile from './KeywordRankingsProductProfile'

interface KeyProps {
  value: number
  type: string
  display: string
  change: number
  pct_change: number
}

export interface customSelectionCalloutProps {
  name: string
  options?: ArrOption[]
}

type ArrOption = {
  label: string
  name: string
}

export type KeywordRankingItem = {
  keyword: string
  organic_rank: { value: number; change: number }
  est_organic_clicks: { value: number; change: number }
  organic_click_share: { value: number; change: number }
  pct_of_products_organic_clicks: { value: number; change: number }
  est_daily_search_volume: { value: number }
  est_daily_clicks_for_top_position: { value: number }
  multiple_market_products: number
  ranks: GraphData[]
  search_url: string
  asin: string
  best_rank: string | null
  median_rank: string | null
  worst_rank: string | null
  attributed_sales: KeyProps
  cost: KeyProps
  conversion_rate: KeyProps
  acos: KeyProps
  roas: KeyProps
  cpc: KeyProps
  click_through_rate: KeyProps
  is_amazons_choice: boolean | number
  amazons_choice_product_title: string
  amazons_choice_product_image_url: string
  amazons_choice_product_url: string
  product_name: string
  product_url: string
  amazons_choice_product_price: number
  product_price: number
  amazons_choice_product_asin: string
  amazons_choice_product_rating_count: number
  avg_rating: number
  ratings_count: number
  product_image_url: string
  product_currency_symbol: string
  amazons_choice_product_avg_rating: number
  marketplace_id: string
  country_code: string
  keyword_status: string
}

type GraphData = {
  date: string
  organic_rank: number
  est_organic_clicks: number
  previous_median_rank?: number
}
export type rowDataType = {
  asin: string
  product_name: string
  product_url: string
  product_image_url: string
  avg_rating: number
  product_price: number
  ratings_count: number
  amazon_choice: boolean
}

const KeywordRankings = (): React.JSX.Element => {
  const {
      updateBreadcrumbs,
      startDate,
      endDate,
      marketplaceIds,
      secondRange_startDate,
      secondRange_endDate,
      customer,
      firstRange_startDate,
      firstRange_endDate,
      timeframe,
    } = useContext(ThemeContext),
    [search, setSearch] = useState<string>(''),
    { state: rankTrackerState } = useContext(RankTrackerContext),
    marketProductId = useParams().marketProductId as string,
    tableId = `keyword_rankings_${marketProductId}`,
    [sortBy, setSortBy] = useState(
      tryLocalStorageParse(`sort_by_${tableId}`) ?? {
        prop: 'est_organic_clicks',
        flip: false,
        isByChange: false,
      },
    ),
    { pathname } = useLocation(),
    isMounted = useIsMounted(),
    aggregate_by = timeframe.aggregation,
    { t } = useTranslate('content')
  const [marketplaceName, setMarketplaceName] = useState<string | null>(null)
  const [productTitle, setProductTitle] = useState<string | null>(null)
  const [productAsin, setProductAsin] = useState<string | null>(null)

  const screenIsMdMax = useMediaQuery({ type: 'max', breakpoint: 'md' })
  // custom filter data
  const [secondaryFiltersData, setSecondaryFiltersData] =
    useState<ActiveFilterProps>({})
  const [secondaryFiltersAPI, setSecondaryFiltersAPI] =
    useState<ActiveFilterAPIProps>({})
  const [responseType, setResponseType] = useState<string>('get')

  const user = useUser().user,
    { code, symbol } = user?.current_currency ?? {},
    userCurrency = useMemo(() => {
      return {
        currency_code: code,
        currency_symbol: symbol,
      }
    }, [code, symbol])

  const CustomFilterTableAPIendpoint =
      'content:rankTracker:keyword_rankings:customFilterTable',
    { endpoint: customFilterUserSettingUrl } = getUserSettingEndpoint(
      CustomFilterTableAPIendpoint,
    ),
    { status: customFilterStatus, data: customGetFilterApiResponse } = useQuery(
      {
        queryKey: [customFilterUserSettingUrl, customer?.vendor_id],
        queryFn: ({ signal }) =>
          SecureAxios.get(customFilterUserSettingUrl, { signal }),
        gcTime: trafficCacheTime,
      },
    )

  const columnSettingsData =
    customFilterStatus === 'success' && customGetFilterApiResponse?.data

  const ProspectBrandNotSupportedList: Array<string> = useMemo(() => {
    return [
      'Ad Sales',
      'Ad Spend',
      'Ad Conversion Rate',
      'ACoS',
      'ROAS',
      'CPC',
      'Click Through Rate',
    ]
  }, [])

  useEffect(() => {
    if (columnSettingsData) {
      if (!customer.vendor_id) {
        const keys = Object.keys(columnSettingsData)
        const newdata = { ...columnSettingsData }
        let isSalesColumnExist = false
        keys?.forEach((item: string) => {
          const itemPresent = ProspectBrandNotSupportedList.includes(item)
          if (itemPresent) {
            isSalesColumnExist = true
            delete newdata?.[item]
          }
        })
        if (isSalesColumnExist) {
          setResponseType('post')
          setSecondaryFiltersAPI(newdata)
        } else {
          setResponseType('get')
          setSecondaryFiltersAPI(newdata)
        }
      } else {
        setResponseType('get')
        setSecondaryFiltersAPI(columnSettingsData)
      }
    }
  }, [columnSettingsData, customer?.vendor_id, ProspectBrandNotSupportedList])

  useEffect(() => {
    if (responseType === 'post') {
      CustomFilterHelper({
        api: customFilterUserSettingUrl,
        data: secondaryFiltersAPI,
        type: 'custom-filter',
      })
    }
  }, [customFilterUserSettingUrl, responseType, secondaryFiltersAPI])

  const removeFilter = useCallback(
    (removeFilter?: string) => {
      const removedFilter = RemoveFilter(secondaryFiltersAPI, removeFilter)
      setSecondaryFiltersAPI(removedFilter)
      const removedFiltersState = RemoveFilterState(
        secondaryFiltersData,
        removeFilter,
      )
      setSecondaryFiltersData(removedFiltersState)
      setResponseType('post')
    },
    [secondaryFiltersAPI, secondaryFiltersData],
  )

  const removeFilters = useCallback(
    (removeFilter?: ActiveTableFilterObj) => {
      const removedFilter = removeFilterFromAPIState(
        secondaryFiltersAPI,
        removeFilter,
      )
      const removedFiltersState = removeFilterFromState(
        secondaryFiltersData,
        removeFilter,
      )
      setSecondaryFiltersAPI(removedFilter)
      setSecondaryFiltersData(removedFiltersState)
      setResponseType('post')
    },
    [secondaryFiltersAPI, secondaryFiltersData],
  )

  const SecondaryFilterCallback = useCallback((filter: ActiveFilterProps) => {
    setSecondaryFiltersData((prevState) => ({
      ...prevState,
      ...filter,
    }))
  }, [])
  const [filtersToReset, setFiltersToReset] = useState([''])
  const applySecondaryFilter = useCallback(
    (headerTitle?: string) => {
      const headerText = headerTitle?.replace('Filter ', '')
      const applySecondaryFilter = applySecondaryFilterFunction(
        secondaryFiltersData,
        headerText,
      )
      if (applySecondaryFilter !== null) {
        setSecondaryFiltersAPI((prevState) => ({
          ...prevState,
          ...applySecondaryFilter,
        }))
        setResponseType('post')
      }
      setFiltersToReset([headerTitle ?? ''])
    },
    [secondaryFiltersData],
  )

  const [tempSort, setTempSort] = useState('')
  const handleCustomFilterClose = useCallback(
    (headerText: string, secondaryFiltersAPI: ActiveFilterProps) => {
      setSecondaryFiltersData(secondaryFiltersAPI)
      setFiltersToReset([headerText])
      setTempSort(sortBy?.prop)
    },
    [sortBy?.prop],
  )
  const resetSecondaryFilter = useCallback(
    (headerText: string) =>
      resetCustomFilter({
        headerText,
        customFilterUserSettingUrl,
        secondaryFiltersAPI,
        secondaryFiltersData,
        setSecondaryFiltersAPI,
        setSecondaryFiltersData,
        setDefaultSort() {
          setSortBy({
            prop: 'est_organic_clicks',
            flip: false,
            isByChange: false,
          })
        },
      }),
    [secondaryFiltersAPI, secondaryFiltersData, customFilterUserSettingUrl],
  )
  const getSecondaryFilterObject = useCallback(
    ({
      filterKey,
      tableKey,
      type = 'number',
      changeValueLabel,
    }: {
      filterKey: string
      tableKey: string
      type?: 'number' | 'currency' | 'percentage'
      changeValueLabel?: string
    }) => {
      return {
        headerText: filterKey,
        secondarySortProp: tempSort,
        closeCallout: (headerText: string) =>
          handleCustomFilterClose(headerText, secondaryFiltersAPI),
        onSortChange: (sortPropName: string) => setTempSort(sortPropName),
        callout: applySecondaryFilter,
        resetCallback: changeValueLabel ? resetSecondaryFilter : undefined,
        enableFilterByChangeValues: Boolean(changeValueLabel),
        disabled: !(
          (secondaryFiltersData?.[filterKey]?.text &&
            secondaryFiltersData?.[filterKey]?.value) ||
          (changeValueLabel &&
            secondaryFiltersData?.[changeValueLabel]?.text &&
            secondaryFiltersData?.[changeValueLabel]?.value)
        ),
        children: (
          <SecondaryFilter
            filterKey={filterKey}
            tableKey={tableKey}
            {...{ textFieldType: type }}
            prefix={type === 'currency' ? '$' : ''}
            suffix={type === 'percentage' ? '%' : ''}
            SecondaryFilterCallback={SecondaryFilterCallback}
            selectedSecondaryFilters={secondaryFiltersAPI}
            filtersToReset={filtersToReset}
            enableFilterByChangeValues={Boolean(changeValueLabel)}
          />
        ),
      }
    },
    [
      tempSort,
      applySecondaryFilter,
      secondaryFiltersData,
      SecondaryFilterCallback,
      secondaryFiltersAPI,
      filtersToReset,
      handleCustomFilterClose,
      resetSecondaryFilter,
    ],
  )

  // Customize Column
  const defaultColumns = [
    t('organicRank'),
    t('estimatedOrganicClicks'),
    t('organicClickShare'),
    t('pctOfProductsOrganicClicks'),
    t('estDailySearchVolume'),
    t('estDailyClicks'),
    t('keywordsOnPageOne'),
    t('adSales'),
    c('adSpend'),
    t('adConversionRate'),
    c('source'),
  ]
  const [selectionList, setSelectionList] = useState(defaultColumns)
  const filterChangeValueDetails: { [key: string]: string } = useMemo(
    () => ({
      'Organic Rank': t('changeInOrganicRank'),
      'Est. Organic Clicks': t('changeInEstOrganicClicks'),
      'Organic Click Share': t('changeInOrganicClickShare'),
      "% of Product's Organic Clicks": t('changeInPctofProductOrganicClicks'),
      'Ad Conversion Rate': t('changeInAdConversionRate'),
      ROAS: t('changeInROAS'),
      CPC: t('changeInCPC'),
      'Click Through Rate': t('changeInClickThroughRate'),
    }),
    [t],
  )
  const extraCustomFilterParams = useMemo(() => {
    const keys = Object.keys(secondaryFiltersAPI)
    const newdata = { ...secondaryFiltersAPI }
    if (!customer.vendor_id) {
      keys?.forEach((item: string) => {
        const itemPresent = ProspectBrandNotSupportedList.includes(item)
        if (itemPresent) {
          delete newdata?.[item]
        }
      })
    }

    const filterParams = customFilterParams(
      newdata,
      selectionList,

      filterChangeValueDetails,
    )
    return { filter: filterParams }
  }, [
    ProspectBrandNotSupportedList,
    customer.vendor_id,
    secondaryFiltersAPI,
    selectionList,
    filterChangeValueDetails,
  ])
  const languageParams = useLanguageParams()
  const {
    newStartDate,
    newEndDate,
    comparisonStartDate,
    comparisonEndDate,
    currentTimeframeCheck,
    previousDates,
  } = useRankTrackerDates()
  // Fetch data
  const keywordRankingsApi = `${getApiUrlPrefix(
      'rank-tracker',
    )}/api/v3/product/${marketProductId}`,
    rankTrackerKeywordsApi = `${getApiUrlPrefix(
      'rank-tracker',
    )}/api/v3/product/${marketProductId}/keywords`,
    commonParams = useMemo(() => {
      return {
        market_product_id: marketProductId,
        start_date: currentTimeframeCheck ? newStartDate : startDate,
        end_date: timeframe.type === 'current' ? newEndDate : endDate,
        ...(search ? { search_for: search } : {}),
        currency_code: code,
        marketplace_ids: marketplaceIds ?? [],
        comparison_start: secondRange_startDate
          ? secondRange_startDate
          : currentTimeframeCheck
            ? comparisonStartDate
            : previousDates?.startDate,
        comparison_end: secondRange_endDate
          ? secondRange_endDate
          : timeframe.type === 'current'
            ? comparisonEndDate
            : previousDates?.endDate,
        sort: standardSortParams(sortBy, ['keyword']),
        ...(rankTrackerState?.amazonChoice?.id !== 0
          ? { is_amazons_choice: true }
          : {}),
        ...(rankTrackerState?.keywordSource?.id === 0
          ? { keyword_status: true }
          : {}),
        ...(rankTrackerState?.amazonChoice?.id === 2
          ? { is_amazons_choice: false }
          : {}),
        ...extraCustomFilterParams,
        ...(aggregate_by ? { aggregate_by } : {}),
        ...languageParams,
      }
    }, [
      aggregate_by,
      code,
      comparisonEndDate,
      comparisonStartDate,
      currentTimeframeCheck,
      endDate,
      extraCustomFilterParams,
      marketProductId,
      marketplaceIds,
      newEndDate,
      newStartDate,
      previousDates?.endDate,
      previousDates?.startDate,
      rankTrackerState?.amazonChoice?.id,
      search,
      secondRange_endDate,
      secondRange_startDate,
      sortBy,
      startDate,
      timeframe.type,
      rankTrackerState?.keywordSource?.id,
      languageParams,
    ])

  const originalConfig = useMemo(
    () => [
      {
        name: 'keyword',
        label: c('keywords'),
        cell: {
          children: (keyword: KeywordRankingItem) => {
            const isamazonChoice = keyword?.is_amazons_choice
            const noDataAmazonChoice = keyword?.amazons_choice_product_asin
            const mediumThumbnail = {
              width: '100px',
              height: '100px',
              margin: '0 auto',
              paddingBottom: '8px',
            }
            const smallThumbnail = {
              width: '64px',
              height: '64px',
              paddingTop: '16px',
              marginBottom: '8px',
            }
            const rowData = [
              {
                asin: keyword?.asin,
                product_name: keyword?.product_name,
                keyword: keyword?.keyword,
                product_url: keyword?.product_url,
                product_image_url: keyword?.product_image_url,
                avg_rating: keyword?.avg_rating,
                product_price: keyword?.product_price,
                ratings_count: keyword?.ratings_count,
                amazon_choice: false,
              },
              ...(!isamazonChoice && noDataAmazonChoice
                ? [
                    {
                      product_price: keyword?.amazons_choice_product_price,
                      asin: keyword?.amazons_choice_product_asin,
                      avg_rating: keyword?.amazons_choice_product_avg_rating,
                      product_url: keyword?.amazons_choice_product_url,
                      product_image_url:
                        keyword?.amazons_choice_product_image_url,
                      product_name: keyword?.amazons_choice_product_title,
                      ratings_count:
                        keyword?.amazons_choice_product_rating_count,
                      amazon_choice: true,
                    },
                  ]
                : []),
            ]
            const comparisonTableConfig = [
              {
                name: 'compare',
                label: rowData.length > 1 ? t('compare') : '',
                cell: {
                  children: (rowData: rowDataType) => {
                    return rowData.product_image_url ? (
                      <div>
                        <Image
                          url={rowData.product_image_url}
                          style={
                            (keyword?.is_amazons_choice === 0 ||
                              keyword?.is_amazons_choice === null) &&
                            noDataAmazonChoice
                              ? smallThumbnail
                              : mediumThumbnail
                          }
                        />
                        <TrimText text={rowData.product_name} limit={75} />
                        {keyword?.is_amazons_choice === 0 &&
                          rowData.amazon_choice === true && (
                            <div className='pat-mt-2'>
                              <Tag color={'blue'}>{c('amazonsChoice')}</Tag>
                            </div>
                          )}
                      </div>
                    ) : (
                      <div className='text-center'>
                        <TrimText text={rowData.product_name} limit={75} />
                      </div>
                    )
                  },
                },
              },
              {
                name: 'price',
                label: c('price'),
                cell: {
                  children: (rowData: rowDataType) => {
                    return (
                      <MdashCheck check={!!rowData.product_price}>
                        <Currency
                          value={rowData?.product_price}
                          currencySymbol={keyword?.product_currency_symbol}
                        />
                      </MdashCheck>
                    )
                  },
                },
              },
              {
                name: 'ASIN',
                label: c('asin'),
                cell: {
                  children: (rowData: rowDataType) => {
                    return (
                      <span className={`flex align-items-center fc-blue`}>
                        {rowData.asin}
                        <a
                          href={rowData?.product_url}
                          target='_blank'
                          rel='noopener noreferrer'
                        >
                          <Icon
                            icon='launch'
                            className='pat-ml-2.5'
                            color='dark-blue'
                            iconSize='16px'
                          />
                        </a>
                      </span>
                    )
                  },
                },
              },
              {
                name: 'ratings_count',
                label: t('ratingsCount'),
                cell: {
                  children: (rowData: rowDataType) => {
                    return (
                      <MdashCheck check={!!rowData.ratings_count}>
                        <NumericFormat
                          value={rowData?.ratings_count}
                          thousandSeparator={true}
                          decimalScale={0}
                          displayType='text'
                        />
                      </MdashCheck>
                    )
                  },
                },
              },
              {
                name: 'avg_rating',
                label: t('averageRating'),
                cell: {
                  children: (rowData: rowDataType) => {
                    return (
                      <MdashCheck check={!!rowData.avg_rating}>
                        <span className='flex align-items-center'>
                          <span className={`pat-mr-2 w-10`}>
                            {Number(rowData.avg_rating ?? 0).toFixed(1)}
                          </span>
                          <ReviewStars rating={rowData.avg_rating} />
                        </span>
                      </MdashCheck>
                    )
                  },
                },
              },
            ]
            return (
              <>
                <a
                  href={keyword?.search_url}
                  target='_blank'
                  rel='noopener noreferrer'
                  className='link'
                >
                  <Tooltip
                    position='right'
                    className={`no-padding ${
                      !isamazonChoice && rowData.length > 1 && 'gray-arrow'
                    }`}
                    maxWidth='none'
                    tooltipContent={
                      <div className='products-info'>
                        {!noDataAmazonChoice && (
                          <div className='pat-p-4'>
                            <Alert
                              type='info'
                              customClass='pat-p-2'
                              text={
                                <span className='fs-12'>
                                  {t('amazonsChoiceDataNotFound')}
                                </span>
                              }
                            />
                          </div>
                        )}
                        <ComparisonTable
                          data={rowData}
                          config={comparisonTableConfig}
                          dataKey='asin'
                          isSingleProduct={rowData.length === 1}
                        />
                      </div>
                    }
                  >
                    <div className='pat-mb-2'>
                      <TrimText
                        text={keyword?.keyword}
                        limit={75}
                        customClass={
                          sortBy.prop === 'keyword' ? 'fw-semi-bold' : ''
                        }
                      />
                    </div>
                  </Tooltip>
                </a>
                {keyword?.is_amazons_choice === 1 && (
                  <Tag color={'blue'}>{c('amazonsChoice')}</Tag>
                )}
              </>
            )
          },
        },
        tooltip: {
          content: t('keywordTooltip'),
        },
        style: {
          minWidth: '200px',
        },
        mainColumn: true,
      },
      {
        name: 'organic_rank',
        label: t('organicRank'),
        cell: {
          children: (keyword: KeywordRankingItem) => {
            const tableTooltipData = [
              {
                label: t('bestRank'),
                value: keyword.best_rank,
              },
              {
                label: t('medianRank'),
                value: keyword.median_rank,
              },
              {
                label: t('worstRank'),
                value: keyword.worst_rank,
              },
            ]

            return (
              <div
                className={
                  screenIsMdMax
                    ? `flex-direction-column ${styles.width100} `
                    : styles.organicRankColumn
                }
              >
                <Tooltip
                  tooltipContent={<TableTooltip data={tableTooltipData} />}
                  maxWidth='none'
                  position='right'
                >
                  <MdashCheck check={!!keyword?.organic_rank?.value}>
                    <ListChangeData
                      value={keyword?.organic_rank?.value}
                      changeValue={keyword?.organic_rank?.change}
                      customClass={
                        sortBy.prop === 'organic_rank' ? 'fw-semi-bold' : ''
                      }
                      changeValueClass={
                        sortBy.prop === 'change__organic_rank'
                          ? 'fw-semi-bold'
                          : ''
                      }
                      icon={
                        // Displaying Cup icon for organic rank if value is less than 4
                        keyword?.organic_rank?.value <= 4 && (
                          <Icon icon='cup' iconSize='12px' />
                        )
                      }
                    />
                  </MdashCheck>
                </Tooltip>
                <div className={screenIsMdMax ? 'pat-mt-4' : ''}>
                  <RankTrackerSparklineWrapper
                    topValue={keyword?.organic_rank?.value}
                    title='Organic Rank'
                    dataKey='organic_rank'
                    showTrophy={
                      !!keyword?.organic_rank?.value &&
                      keyword?.organic_rank?.value <= 4
                    }
                    reversed
                    min={Number(keyword.best_rank)}
                    max={Number(keyword.worst_rank)}
                    isDisplayCsvDownloadOption={!!productTitle}
                    csvTitle={'Keyword Rankings'}
                    linkName={t('keywordRankings')}
                    sparklineText={'Median'}
                    asin={keyword.asin.toString()}
                    productTitle={keyword.product_name}
                    params={commonParams}
                    brand={customer?.customer_name}
                    brandId={customer?.id}
                    type='rankedKeywords'
                    marketProductId={keyword.keyword}
                    countryCode={keyword?.country_code}
                    apiEndpoint={`api/v3/product/${marketProductId}/keyword_ranking_sparkline`}
                  ></RankTrackerSparklineWrapper>
                </div>
              </div>
            )
          },
        },
        options: [
          {
            name: 'organic_rank',
            label: t('organicRank'),
          },
          {
            name: 'change__organic_rank',
            label: t('changeInOrganicRank'),
          },
        ],
        tooltip: {
          content: t('organicRankTooltip'),
        },
        filter: getSecondaryFilterObject({
          filterKey: t('organicRank'),
          tableKey: 'organic_rank',
          changeValueLabel: t('changeInOrganicRank'),
        }),
      },
      {
        name: 'est_organic_clicks',
        label: t('estimatedOrganicClicks'),
        ...getContentTableHeaderTooltip('estOrganicClicks'),
        cell: {
          children: (keyword: KeywordRankingItem) => {
            return (
              <ListChangeData
                value={Math.round(keyword?.est_organic_clicks?.value)}
                changeValue={keyword?.est_organic_clicks?.change}
                customClass={
                  sortBy.prop === 'est_organic_clicks' ? 'fw-semi-bold' : ''
                }
                changeValueClass={
                  sortBy.prop === 'change__est_organic_clicks'
                    ? 'fw-semi-bold'
                    : ''
                }
              />
            )
          },
        },
        options: [
          {
            name: 'est_organic_clicks',
            label: t('estimatedOrganicClicks'),
          },
          {
            name: 'change__est_organic_clicks',
            label: t('changeInEstOrganicClicks'),
          },
        ],

        filter: getSecondaryFilterObject({
          filterKey: t('estimatedOrganicClicks'),
          tableKey: 'est_organic_clicks',
          changeValueLabel: t('changeInEstOrganicClicks'),
        }),
      },
      {
        name: 'organic_click_share',
        label: t('organicClickShare'),
        ...getContentTableHeaderTooltip('organicClickShare'),
        cell: {
          children: (keyword: KeywordRankingItem) => {
            return (
              <ListChangeData
                value={keyword?.organic_click_share?.value}
                changeValue={getOrganicClickShareChange(
                  keyword?.organic_click_share?.change,
                )}
                percentage
                decimalScale={2}
                customClass={
                  sortBy.prop === 'organic_click_share' ? 'fw-semi-bold' : ''
                }
                changeValueClass={
                  sortBy.prop === 'change__organic_click_share'
                    ? 'fw-semi-bold'
                    : ''
                }
              />
            )
          },
        },
        options: [
          {
            name: 'organic_click_share',
            label: t('organicClickShare'),
          },
          {
            name: 'change__organic_click_share',
            label: t('changeInOrganicClickShare'),
          },
        ],

        filter: getSecondaryFilterObject({
          filterKey: t('organicClickShare'),
          tableKey: 'organic_click_share',
          type: 'percentage',
          changeValueLabel: t('changeInOrganicClickShare'),
        }),
      },
      {
        name: 'pct_of_products_organic_clicks',
        label: t('pctOfProductsOrganicClicks'),
        cell: {
          children: (keyword: KeywordRankingItem) => {
            return (
              <ListChangeData
                value={keyword?.pct_of_products_organic_clicks?.value}
                changeValue={getOrganicClickShareChange(
                  keyword?.pct_of_products_organic_clicks?.change,
                )}
                percentage
                decimalScale={2}
                customClass={
                  sortBy.prop === 'pct_of_products_organic_clicks'
                    ? 'fw-semi-bold'
                    : ''
                }
                changeValueClass={
                  sortBy.prop === 'change__pct_of_products_organic_clicks'
                    ? 'fw-semi-bold'
                    : ''
                }
              />
            )
          },
        },
        tooltip: {
          content: t('pctOfProductsOrganicClicksTooltip'),
        },
        options: [
          {
            name: 'pct_of_products_organic_clicks',
            label: t('pctOfProductsOrganicClicks'),
          },
          {
            name: 'change__pct_of_products_organic_clicks',
            label: t('changeInPctofProductOrganicClicks'),
          },
        ],

        filter: getSecondaryFilterObject({
          filterKey: t('pctOfProductsOrganicClicks'),
          tableKey: 'pct_of_products_organic_clicks',
          type: 'percentage',
          changeValueLabel: t('changeInPctofProductOrganicClicks'),
        }),
      },
      {
        name: 'est_daily_search_volume',
        label: t('estDailySearchVolume'),
        cell: {
          children: (keyword: KeywordRankingItem) => {
            return (
              <NumericFormat
                value={keyword?.est_daily_search_volume.value}
                thousandSeparator={true}
                displayType='text'
                className={
                  sortBy.prop === 'est_daily_search_volume'
                    ? 'fw-semi-bold'
                    : ''
                }
              />
            )
          },
        },
        tooltip: {
          content: t('estDailySearchVolumeTooltip'),
        },

        filter: getSecondaryFilterObject({
          filterKey: t('estDailySearchVolume'),
          tableKey: 'est_daily_search_volume',
        }),
      },
      {
        name: 'est_daily_clicks_for_top_position',
        label: t('estDailyClicks'),
        ...getContentTableHeaderTooltip('estDailyClicksForTopPosition'),
        cell: {
          children: (keyword: KeywordRankingItem) => {
            return (
              <NumericFormat
                value={keyword?.est_daily_clicks_for_top_position.value}
                thousandSeparator={true}
                displayType='text'
                className={
                  sortBy.prop === 'est_daily_clicks_for_top_position'
                    ? 'fw-semi-bold'
                    : ''
                }
              />
            )
          },
        },

        filter: getSecondaryFilterObject({
          filterKey: t('estDailyClicks'),
          tableKey: 'est_daily_clicks_for_top_position',
        }),
      },
      {
        name: 'keyword_status',
        label: c('source'),
        cell: {
          children: (e: KeywordRankingItem) => {
            return (
              <MdashCheck check={hasValue(e?.keyword_status)}>
                {e?.keyword_status === 'true' ? (
                  <Tag color='blue'>{t('content:highestVolumeKeyword')}</Tag>
                ) : (
                  <Tag color='orange'>{t('content:advertised')}</Tag>
                )}
              </MdashCheck>
            )
          },
        },
        tooltip: {
          content: t('keywordStatusTooltip'),
        },
        noSort: true,
      },
      {
        ...commonTrafficHeaders().attributedSales,
        ...getContentTableHeaderTooltip('adSales'),
        cell: {
          children: (keyword: KeywordRankingItem) => {
            return (
              <MdashCheck check={!!keyword?.attributed_sales?.value}>
                <div className='flex'>
                  <div>
                    <ListChangeData
                      value={keyword?.attributed_sales?.value ?? 0}
                      changeValue={keyword.attributed_sales.change ?? 0}
                      tooltipContent={
                        keyword?.attributed_sales?.pct_change ?? 0
                      }
                      currency={userCurrency}
                      changeFormat='currency'
                      decimalScale={decimalScaleHelper(
                        keyword?.attributed_sales?.change
                          ? Math.abs(keyword.attributed_sales.change)
                          : 0,
                      )}
                      changeDecimalScale={decimalScaleHelper(
                        keyword?.attributed_sales?.change
                          ? Math.abs(keyword.attributed_sales?.change)
                          : 0,
                      )}
                      customClass={
                        sortBy.prop === 'attributed_sales' ? 'fw-semi-bold' : ''
                      }
                      changeValueClass={
                        sortBy.prop === 'change__attributed_sales'
                          ? 'fw-semi-bold'
                          : ''
                      }
                    />
                  </div>
                  {keyword?.multiple_market_products === 1 && (
                    <div>
                      <Tooltip
                        position='right'
                        tooltipContent={t('productRankingsTooltip')}
                        maxWidth='300px'
                      >
                        <span>
                          <Icon
                            icon='info'
                            className='pat-mr-2'
                            iconSize='12px'
                          />
                        </span>
                      </Tooltip>
                    </div>
                  )}
                </div>
              </MdashCheck>
            )
          },
        },
        ...(customer?.vendor_id
          ? {
              filter: getSecondaryFilterObject({
                filterKey: c('adSpend'),
                tableKey: 'attributed_sales',
                type: 'currency',
                changeValueLabel: t('changeInAdSpend'),
              }),
            }
          : {}),
        ...(customer?.vendor_id ? {} : { noSort: true }),
      },
      {
        ...commonTrafficHeaders().cost,
        ...getContentTableHeaderTooltip('adSpend'),
        cell: {
          children: (keyword: KeywordRankingItem) => {
            return (
              <MdashCheck check={!!keyword?.cost?.value}>
                <ListChangeData
                  value={keyword?.cost?.value ?? 0}
                  changeValue={keyword.cost.change ?? 0}
                  tooltipContent={keyword?.cost?.pct_change ?? 0}
                  currency={userCurrency}
                  changeFormat='currency'
                  decimalScale={decimalScaleHelper(
                    keyword?.cost?.change ? Math.abs(keyword.cost.change) : 0,
                  )}
                  changeDecimalScale={decimalScaleHelper(
                    keyword?.cost?.change ? Math.abs(keyword.cost?.change) : 0,
                  )}
                  customClass={sortBy.prop === 'cost' ? 'fw-semi-bold' : ''}
                  changeValueClass={
                    sortBy.prop === 'change__cost' ? 'fw-semi-bold' : ''
                  }
                />
              </MdashCheck>
            )
          },
        },
        ...(customer?.vendor_id
          ? {
              filter: getSecondaryFilterObject({
                filterKey: c('adSpend'),
                tableKey: 'cost',
                type: 'currency',
              }),
            }
          : {}),
        ...(customer?.vendor_id ? {} : { noSort: true }),
      },
      {
        name: 'conversion_rate',
        label: t('adConversionRate'),
        ...getContentTableHeaderTooltip('adConversionRate'),
        cell: {
          children: (keyword: KeywordRankingItem) => {
            return (
              <MdashCheck check={!!keyword?.conversion_rate?.value}>
                <ListChangeData
                  value={keyword?.conversion_rate?.value ?? 0}
                  changeValue={
                    keyword?.conversion_rate?.change
                      ? keyword.conversion_rate.change * 100
                      : 0
                  }
                  changeFormat='number'
                  tooltipContent={keyword?.conversion_rate?.pct_change ?? 0}
                  percentage
                  decimalScale={2}
                  customClass={
                    sortBy.prop === 'conversion_rate' ? 'fw-semi-bold' : ''
                  }
                  changeValueClass={
                    sortBy.prop === 'change__conversion_rate'
                      ? 'fw-semi-bold'
                      : ''
                  }
                />
              </MdashCheck>
            )
          },
        },
        options: [
          {
            name: 'conversion_rate',
            label: t('adConversionRate'),
          },
          {
            name: 'change__conversion_rate',
            label: t('changeInAdConversionRate'),
          },
        ],
        ...(customer?.vendor_id
          ? {
              filter: getSecondaryFilterObject({
                filterKey: t('adConversionRate'),
                tableKey: 'conversion_rate',
                type: 'percentage',
                changeValueLabel: t('changeInAdConversionRate'),
              }),
            }
          : {}),
        ...(customer?.vendor_id ? {} : { noSort: true }),
      },
      {
        ...commonTrafficHeaders().acos,
        cell: {
          children: (keyword: KeywordRankingItem) => {
            return (
              <MdashCheck check={!!keyword?.acos?.value}>
                <ListChangeData
                  value={keyword?.acos?.value ?? 0}
                  changeValue={
                    keyword?.acos?.change ? keyword.acos.change * 100 : 0
                  }
                  tooltipContent={keyword?.acos?.pct_change ?? 0}
                  percentage
                  decimalScale={2}
                  reverse
                  customClass={sortBy.prop === 'acos' ? 'fw-semi-bold' : ''}
                  changeValueClass={
                    sortBy.prop === 'change__acos' ? 'fw-semi-bold' : ''
                  }
                />
              </MdashCheck>
            )
          },
        },
        ...(customer?.vendor_id
          ? {
              filter: getSecondaryFilterObject({
                filterKey: 'ACoS',
                tableKey: 'acos',
                type: 'percentage',
                changeValueLabel: t('changeInAcos'),
              }),
            }
          : {}),
        ...(customer?.vendor_id ? {} : { noSort: true }),
      },
      {
        name: 'roas',
        label: c('roas'),
        ...getContentTableHeaderTooltip('roas'),
        cell: {
          children: (keyword: KeywordRankingItem) => {
            return (
              <MdashCheck check={!!keyword?.roas?.value}>
                <ListChangeData
                  value={keyword?.roas?.value ?? 0}
                  changeValue={keyword?.roas?.change ?? 0}
                  tooltipContent={keyword?.roas?.pct_change ?? 0}
                  decimalScale={2}
                  customClass={sortBy.prop === 'roas' ? 'fw-semi-bold' : ''}
                  changeValueClass={
                    sortBy.prop === 'change__roas' ? 'fw-semi-bold' : ''
                  }
                />
              </MdashCheck>
            )
          },
        },
        options: [
          {
            name: 'roas',
            label: c('roas'),
          },
          {
            name: 'change__roas',
            label: t('changeInROAS'),
          },
        ],
        ...(customer.vendor_id
          ? {
              filter: getSecondaryFilterObject({
                filterKey: c('roas'),
                tableKey: 'roas',
                changeValueLabel: t('changeInROAS'),
              }),
            }
          : {}),
        ...(customer?.vendor_id ? {} : { noSort: true }),
      },
      {
        name: 'cpc',
        label: c('cpc'),
        ...getContentTableHeaderTooltip('cpc'),
        cell: {
          children: (keyword: KeywordRankingItem) => {
            return (
              <MdashCheck check={!!keyword?.cpc?.value}>
                <ListChangeData
                  value={keyword?.cpc?.value ?? 0}
                  changeValue={keyword?.cpc?.change ?? 0}
                  currency={userCurrency}
                  changeFormat='currency'
                  decimalScale={2}
                  reverse
                  customClass={sortBy.prop === 'cpc' ? 'fw-semi-bold' : ''}
                  tooltipContent={keyword?.cpc?.pct_change ?? 0}
                  changeValueClass={
                    sortBy.prop === 'change__cpc' ? 'fw-semi-bold' : ''
                  }
                />
              </MdashCheck>
            )
          },
        },
        options: [
          {
            name: 'cpc',
            label: c('cpc'),
          },
          {
            name: 'change__cpc',
            label: t('changeInCPC'),
          },
        ],
        ...(customer.vendor_id
          ? {
              filter: getSecondaryFilterObject({
                filterKey: c('cpc'),
                tableKey: 'cpc',
                type: 'currency',
                changeValueLabel: t('changeInCPC'),
              }),
            }
          : {}),
        ...(customer?.vendor_id ? {} : { noSort: true }),
      },
      {
        name: 'click_through_rate',
        label: t('clickThroughRate'),
        ...getContentTableHeaderTooltip('clickThroughRate'),
        cell: {
          children: (keyword: KeywordRankingItem) => {
            return (
              <MdashCheck check={!!keyword?.click_through_rate?.value}>
                <ListChangeData
                  value={keyword?.click_through_rate?.value ?? 0}
                  changeValue={
                    keyword?.click_through_rate?.change
                      ? keyword.click_through_rate.change * 100
                      : 0
                  }
                  changeFormat='number'
                  tooltipContent={keyword?.click_through_rate?.pct_change ?? 0}
                  percentage
                  decimalScale={2}
                  customClass={
                    sortBy.prop === 'click_through_rate' ? 'fw-semi-bold' : ''
                  }
                  changeValueClass={
                    sortBy.prop === 'change__click_through_rate'
                      ? 'fw-semi-bold'
                      : ''
                  }
                />
              </MdashCheck>
            )
          },
        },
        options: [
          {
            name: 'click_through_rate',
            label: t('clickThroughRate'),
          },
          {
            name: 'change__click_through_rate',
            label: t('changeInClickThroughRate'),
          },
        ],
        ...(customer?.vendor_id
          ? {
              filter: getSecondaryFilterObject({
                filterKey: t('clickThroughRate'),
                tableKey: 'click_through_rate',
                type: 'percentage',
                changeValueLabel: t('changeInClickThroughRate'),
              }),
            }
          : {}),
        ...(customer?.vendor_id ? {} : { noSort: true }),
      },
    ],
    [
      t,
      getSecondaryFilterObject,
      customer.vendor_id,
      customer?.customer_name,
      customer?.id,
      sortBy.prop,
      screenIsMdMax,
      productTitle,
      commonParams,
      marketProductId,
      userCurrency,
    ],
  )

  const ProspectBrandNotSupportedSortList: Array<string> = useMemo(() => {
    return [
      'change__attributed_sales',
      'attributed_sales',
      'cost',
      'change__cost',
      'conversion_rate',
      'change__conversion_rate',
      'acos',
      'change__acos',
      'roas',
      'change__roas',
      'cpc',
      'change__cpc',
      'click_through_rate',
      'change__click_through_rate',
    ]
  }, [])

  useEffect(() => {
    if (!customer.vendor_id) {
      const isSortSupport = ProspectBrandNotSupportedSortList.includes(
        sortBy.prop,
      )
      if (isSortSupport) {
        setSortBy((prevState: { prop: string; flip: boolean }) => ({
          ...prevState,
          prop: originalConfig[1].name,
        }))
      }
    }
  }, [
    originalConfig,
    customer.vendor_id,
    ProspectBrandNotSupportedSortList,
    sortBy.prop,
  ])
  const totalConfigItems = originalConfig.slice(1)
  const totalList = totalConfigItems.map((item) => item.label)

  const {
      status,
      data: apiResponse,
      fetchNextPage,
      hasNextPage,
    } = useInfiniteQuery({
      queryKey: [
        rankTrackerKeywordsApi,
        startDate,
        endDate,
        search,
        sortBy,
        marketProductId,
        marketplaceIds,
        previousDates,
        secondRange_startDate,
        secondRange_endDate,
        extraCustomFilterParams,
        rankTrackerState.amazonChoice.id,
        rankTrackerState.keywordSource,
        aggregate_by,
      ],
      queryFn: async ({ pageParam = 1, signal }) => {
        const params = {
          ...commonParams,
          page: pageParam,
          per_page: 20,
        }
        const response = await SecureAxios.get(rankTrackerKeywordsApi, {
          params,
          signal,
        })
        return response
      },
      initialPageParam: 1,
      gcTime: trafficCacheTime,
      getNextPageParam: (previousResponse) => {
        return previousResponse?.data?.data?.pagination?.lastPage
          ? undefined
          : previousResponse?.data?.data?.pagination?.next_page
      },
    }),
    count = apiResponse?.pages[0]?.data?.data?.pagination?.count,
    data = apiResponse
      ? apiResponse.pages.flatMap((page) => {
          return page?.data?.data?.data
        })
      : []

  const sort: SortColumnProps['sorter'] = (sortObj) => {
    isMounted() &&
      setSortBy({
        prop: sortObj.activeColumn,
        flip: sortObj.direction,
        lowerCaseParam: sortObj.lowerCaseParam,
      })
  }

  const searchInputHandler = (value: string) => {
    setSearch(value ?? '')
  }

  const csvDownloadOptions: DownloadOptionsType = [
    {
      linkName: t('keywordRankings'),
      csvName: t('keywordRankings'),
      csvFormat: {
        api: (csvParams) =>
          SecureAxios.get(rankTrackerKeywordsApi, { params: csvParams }).then(
            (response) => {
              return response.data
            },
          ),
        params: {
          ...commonParams,
          async: true,
          csv_name: t('keywordRankings'),
        },
        callout: csvToastCallout(),
      },
    },
  ]

  useEffect(() => {
    if (productAsin) {
      updateBreadcrumbs({
        name: `${t('keywordRankings')} - ${productAsin}`,
        link: pathname,
      })
    }
  }, [pathname, productAsin, updateBreadcrumbs, t])

  useBrandChangeRedirect()

  const alertmsg = (
    <MdashCheck check={!!marketplaceName}>
      {(moment(firstRange_startDate).isAfter(moment('2022-02-28'), 'day') &&
        moment(firstRange_endDate).isBefore(moment('2022-04-01'), 'day')) ||
      moment('2022-02-28').isBetween(
        firstRange_startDate,
        firstRange_endDate,
      ) ||
      moment('2022-04-01').isBetween(
        firstRange_startDate,
        firstRange_endDate,
      ) ||
      moment('2022-03-01').isBetween(startDate, endDate) ||
      moment('2022-03-31').isBetween(startDate, endDate) ||
      (moment(secondRange_startDate).isAfter(moment('2022-02-28'), 'day') &&
        moment(secondRange_endDate).isBefore(moment('2022-04-01'), 'day')) ||
      moment('2022-03-01').isBetween(
        secondRange_startDate,
        secondRange_endDate,
      ) ||
      moment('2022-03-31').isBetween(secondRange_startDate, secondRange_endDate)
        ? t('keywordRankingsAlertMsg', { marketplaceName: marketplaceName })
        : t('keywordRankingsAlertMsgDefault', {
            marketplaceName: marketplaceName,
          })}
    </MdashCheck>
  )

  const apiUrl = `${getApiUrlPrefix('user-settings')}/v1`
  const userId = useDecodedUserTokenId()

  const { status: userSettingStatus, data: userSettingsApiResponse } = useQuery(
    {
      queryKey: [userId, marketProductId],
      queryFn: ({ signal }) =>
        SecureAxios.get(
          apiUrl + `/${userId}/predict/content:keyword-rankings`,
          { signal },
        ),
      gcTime: trafficCacheTime,
    },
  )

  const userSettingsData =
    userSettingStatus === 'success' &&
    userSettingsApiResponse?.data?.selectedColumns

  useEffect(() => {
    if (userSettingsData) {
      setSelectionList(userSettingsData)
    }
  }, [userSettingsData])

  const customSelectionCallout = (
    selectedList: string[],
    setToDefault?: boolean,
  ) => {
    if (
      !selectedList.includes(
        originalConfig.filter((selectionItem) => {
          const item = selectionItem as customSelectionCalloutProps
          if (item?.options) {
            const value = item.options.find(
              (option) => option.name === sortBy.prop,
            )
            return !!value
          }
          return item.name === sortBy.prop
        })[0].label,
      )
    ) {
      setSortBy((prevState: { prop: string; flip: boolean }) => ({
        ...prevState,
        prop: originalConfig[0].name,
      }))
    }
    setSelectionList(selectedList)
    saveCustomization({
      api: `${getApiUrlPrefix('user-settings')}/v1/${
        userId
      }/predict/content:keyword-rankings`,
      selected: selectedList,
      setToDefault: setToDefault,
      type: 'table',
    })

    const keys = Object.keys(secondaryFiltersAPI)
    const originLength = Object.keys(secondaryFiltersAPI)?.length
    const newdata = { ...secondaryFiltersAPI }
    keys?.forEach((item: string) => {
      const itemNotPresent = !selectedList.includes(item)
      if (itemNotPresent) {
        delete newdata?.[item]
      }
    })
    const updatedLength = Object.keys(newdata)?.length
    if (originLength !== updatedLength) {
      setSecondaryFiltersAPI(newdata)
      setResponseType('post')
    }
  }

  const customizeColumnConfig = useMemo(() => {
    return [
      originalConfig[0],
      ...getSelectedConfig(originalConfig, selectionList),
    ]
  }, [originalConfig, selectionList])

  return (
    <div>
      <Alert type='info' text={alertmsg} customClass='pat-mb-5' />
      <div className='has-profile-sidebar'>
        <KeywordRankingsProductProfile
          marketProductId={marketProductId ?? ''}
          setMarketplaceName={setMarketplaceName}
          setProductTitle={setProductTitle}
          setProductAsin={setProductAsin}
        />
        <RankGraphData
          api={keywordRankingsApi}
          extraParams={{
            market_product_id: marketProductId,
            ...(rankTrackerState?.amazonChoice?.id !== 0
              ? { is_amazons_choice: true }
              : {}),
            ...(rankTrackerState?.keywordSource?.id === 0
              ? { keyword_status: true }
              : {}),
          }}
          csvTitle={t('keywordRankings')}
          summaryEntity='Keywords'
          pageName='KeywordRankings'
          showNewHeaderMetric
          allowFilter
        />
      </div>
      <StandardTable
        data={data}
        config={customizeColumnConfig}
        dataKey='keyword'
        hasData={data?.length > 0}
        loading={status === 'pending'}
        hasMore={!!(status === 'success' && hasNextPage)}
        tableId={tableId}
        twoLineLabel
        sort={sort}
        sortBy={sortBy}
        noDataFields={{
          primaryText: c('noKeywordsFound'),
          secondaryText: t('noKeywordsMsg'),
        }}
        tableHeaderProps={{
          header: {
            name: c('keyword', { count }),
            value: convertTableHeaderCount(count),
          },
          search: {
            value: search,
            onChange: (value) => searchInputHandler(value),
            placeholder: t('searchKeywords'),
            debounce: 250,
          },
          download: {
            csvDownloadOptions: csvDownloadOptions,
            initialDisplay: true,
            show: true,
          },
          columnFilterProps: {
            activeFilters: secondaryFiltersAPI,
            remove: removeFilters,
          },
          customColumnProps: {
            list: totalList,
            selected: selectionList,
            callout: customSelectionCallout,
            setToDefaultCallout: () =>
              customSelectionCallout(defaultColumns, true),
          },
        }}
        getData={fetchNextPage}
        successStatus={status === 'success'}
        activeFilters={secondaryFiltersAPI}
        removeFilters={removeFilter}
      />
    </div>
  )
}

export default KeywordRankings
