import React, { useEffect } from 'react'
import { Link } from 'react-router-dom'
import { useQuery } from '@tanstack/react-query'
import { Button, getApiUrlPrefix, InformationPane } from '@patterninc/react-ui'
import { c, productListingRoute as productUrl } from '@predict-services'

import SecureAxios from '../../../../../../../common/services/SecureAxios'
import { trafficCacheTime } from '../../../../../../Traffic/services/TrafficHelperService'

type KeywordRankingsProductProfileProps = {
  marketProductId: string
  setMarketplaceName: (marketplaceName: string) => void
  setProductTitle: (productTitle: string) => void
  setProductAsin: (productTitle: string) => void
}

const KeywordRankingsProductProfile = ({
  marketProductId,
  setMarketplaceName,
  setProductTitle,
  setProductAsin,
}: KeywordRankingsProductProfileProps): React.JSX.Element => {
  // Fetch data
  const rankTrackerProductApi = `${getApiUrlPrefix(
      'iserve',
    )}/api/v3/market_products/${marketProductId}`,
    { status: productStatus, data: productApiResponse } = useQuery({
      queryKey: [rankTrackerProductApi],
      queryFn: ({ signal }) =>
        SecureAxios.get(rankTrackerProductApi, { signal }),
      gcTime: trafficCacheTime,
    }),
    productData = productStatus === 'success' && productApiResponse?.data

  useEffect(() => {
    if (productData) {
      sessionStorage.setItem(
        'keywordRankingMarketplaceId',
        productData.marketplace_id,
      )
      const selectedMarketplace =
        productData?.country_code === 'US'
          ? `${productData?.marketplace_name} ${productData?.country_code}`
          : `${productData?.marketplace_name}`
      setMarketplaceName(selectedMarketplace)
      setProductTitle(productData?.name)
      setProductAsin(productData?.primary_tag)
    }
  }, [productData, setProductAsin, setMarketplaceName, setProductTitle])

  return (
    <InformationPane
      header={{
        labelAndData: {
          label: c('marketPlace'),
          data: productData?.marketplace_name,
          check: !!productData?.marketplace_name,
        },
        tag: {
          color: productData?.active ? 'green' : 'light-gray',
          children: productData?.active ? c('active') : c('inactive'),
        },
      }}
    >
      <InformationPane.ImageAndName
        imgUrl={productData?.image_url}
        product={productData}
      />
      <InformationPane.CustomSection>
        <Button
          as='link'
          to={`${productUrl}/${productData?.master_product_id}/marketplace/${productData?.marketplace_id}/listing/${marketProductId}/traffic/digital-shelf`}
          routerComponent={Link}
        >
          {c('viewDigitalShelf')}
        </Button>
      </InformationPane.CustomSection>
    </InformationPane>
  )
}

export default KeywordRankingsProductProfile
