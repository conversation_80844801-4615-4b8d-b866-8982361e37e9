import React, {
  useCallback,
  useContext,
  useEffect,
  useMemo,
  useState,
} from 'react'
import { NumericFormat } from 'react-number-format'
import { Link, useLocation } from 'react-router-dom'
import { useInfiniteQuery, useQuery } from '@tanstack/react-query'
import {
  type ActiveTableFilterObj,
  ButtonGroup,
  getApiUrlPrefix,
  Icon,
  MdashCheck,
  PrimaryTableCell,
  type SortColumnProps,
  standardSortParams,
  StandardTable,
  useIsMounted,
  useMediaQuery,
} from '@patterninc/react-ui'
import { useDecodedUserTokenId } from '@predict-hooks'
import {
  c,
  csvToastCallout,
  type DownloadOptionsType,
  getUserSettingEndpoint,
  parseParams,
  productListingRoute as productUrl,
  saveCustomization,
  SecureAxios,
  tryLocalStorageParse,
  useTranslate,
} from '@predict-services'
import { convertTableHeaderCount } from 'src/modules/Traffic/services/TrafficTableHeaderHelperService'
import { useGetCustomCategoryIds } from 'src/common/components/GlobalFilter/GlobalFilterHelpers'
import { initialCtDrawerState } from 'src/common/components/ProductCategoriesAndTags/helpers'
import CategoriesAndTags from 'src/common/components/ProductCategoriesAndTags/CategoriesAndTags'
import { getFilteredIds } from 'src/modules/Traffic/hooks/useCommonAPIParams'

import ListChangeData from '../../../../../../common/components/ListChangeData/ListChangeData'
import {
  type ActiveFilterAPIProps,
  applySecondaryFilterFunction,
  CustomFilterHelper,
  customFilterParams,
  RemoveFilter,
  removeFilterFromAPIState,
  removeFilterFromState,
  RemoveFilterState,
  resetCustomFilter,
} from '../../../../../../common/components/Tables/CustomFilterHelper'
import SecondaryFilter, {
  type ActiveFilterProps,
  type numberProps,
  type SelectProps,
} from '../../../../../../common/components/Tables/SecondaryFilter'
import { getSelectedConfig } from '../../../../../../common/services/TableConfigService'
import { ThemeContext } from '../../../../../../Context'
import { useUser } from '../../../../../../context/user-context'
import {
  decimalScaleHelper,
  getOrPostAPI,
  trafficCacheTime,
} from '../../../../../Traffic/services/TrafficHelperService'
import { getContentTableHeaderTooltip } from '../../../Common/ContentTooltipHelper'
import styles from '../Common/_rank-tracker.module.scss'
import {
  getOrganicClickShareChange,
  useRankTrackerDates,
} from '../Common/RankTrackerHelperService'
import RankTrackerSparklineWrapper from '../Common/RankTrackerSparkline/RankTrackerSparklineWrapper'
import { RankTrackerContext } from '../RankTrackerContext'
import { useLanguageParams } from '../../../Common/ContentHooks'

const stickyTableConfig = {
  right: 1,
}

export type GraphData = {
  date: string
  est_organic_clicks: number
  avg_previous: number
}
interface KeyProps {
  value: number
  type: string
  display: string
  change: number
  pct_change: number
}

export type RankTrackerItem = {
  product_title: string
  product_image_url: string
  market_product_id: string
  master_product_id: string
  marketplace_id: string
  marketplace_name: string
  country_code: string
  keywords_in_top_4: { value: number; change: number }
  keywords_on_page_1: { value: number; change: number }
  est_organic_clicks: { value: number; change: number }
  organic_click_share: { value: number; change: number }
  clicks_by_day: GraphData[]
  sold_by_iserve: boolean
  sold_by_pattern: boolean
  sold_by_threepn: boolean
  asin: number
  total_sales?: KeyProps
  click_through_rate?: KeyProps
  cpc?: KeyProps
  roas?: KeyProps
  acos?: KeyProps
  advertised_keywords?: number
  conversion_rate?: KeyProps
  cost?: KeyProps
  attributed_sales?: KeyProps
  amazons_choice_keywords_count: number
  product_url?: string
  root_category_name: string
  category_count?: number
  products_count: { value: number }
  lvl_2_sub_category_name: string
  sub_category_name: string
  custom_category_name: string
  custom_category_id: number
  root_category_id: number
  tags: string | string[]
}

type StateType = {
  search: string
}
type customSelectionCalloutProps = {
  name: string
  options?: ArrOption[]
}
type ArrOption = {
  label: string
  name: string
}

const RankTracker = (): React.JSX.Element => {
  const { t } = useTranslate('content')
  const {
    updateBreadcrumbs,
    customer,
    startDate,
    endDate,
    marketplaceIds,
    tags,
    secondRange_endDate,
    secondRange_startDate,
    prevCustomer,
    timeframe,
    isVatAdjustmentTurnedOn,
    brandGroupCustomer,
    allMarketplaces,
    regions: selectedRegions,
  } = useContext(ThemeContext)
  const isBrandGroupSelected = !!brandGroupCustomer
  const isAllBrand = customer?.id === 0 || isBrandGroupSelected,
    isSingleBrandView = Boolean(!isAllBrand),
    [ctDrawerState, setCtDrawerState] = useState(initialCtDrawerState),
    { state: rankTrackerState } = useContext(RankTrackerContext),
    [state, setState] = useState<StateType>({
      search: '',
    }),
    { search } = state,
    aggregate_by = timeframe.aggregation,
    { search: searchParams, pathname } = useLocation(),
    searchQueryParams = parseParams(searchParams),
    tableId = 'product_rank_tracker',
    [sortBy, setSortBy] = useState(
      tryLocalStorageParse(`sort_by_${tableId}`) ?? {
        prop: 'est_organic_clicks',
        flip: false,
        isByChange: false,
      },
    ),
    isMounted = useIsMounted(),
    user = useUser().user,
    { code, symbol } = user?.current_currency ?? {},
    userCurrency = useMemo(() => {
      return {
        currency_code: code,
        currency_symbol: symbol,
      }
    }, [code, symbol]),
    selectedBrandName = searchQueryParams['customer_name']

  const screenIsMdMax = useMediaQuery({ type: 'max', breakpoint: 'md' })
  const [triggerProductsAPI, setTriggerProductsAPI] = useState<boolean>(false)

  const CustomFilterTableAPIendpoint =
    'content:rankTracker:product_rank_tracker:customFilterTable'
  const { endpoint: customFilterUserSettingUrl } = getUserSettingEndpoint(
      CustomFilterTableAPIendpoint,
    ),
    { status: customFilterStatus, data: customGetFilterApiResponse } = useQuery(
      {
        queryKey: [customFilterUserSettingUrl, customer?.vendor_id],
        queryFn: ({ signal }) =>
          SecureAxios.get(customFilterUserSettingUrl, { signal }),
        gcTime: trafficCacheTime,
      },
    )

  const columnSettingsData =
    customFilterStatus === 'success' && customGetFilterApiResponse?.data
  const ProspectBrandNotSupportedList: Array<string> = useMemo(() => {
    return [
      'Total Sales',
      'Ad Sales',
      'Ad Spend',
      'Ad Conversion Rate',
      'ACoS',
      'ROAS',
      'CPC',
      'Click Through Rate',
      'Advertised keywords',
    ]
  }, [])
  const customCategories = useGetCustomCategoryIds()
  const doCustomCategoriesExist = customCategories?.length > 0
  // Secondary Filter
  const [secondaryFiltersback, setSecondaryFiltersback] =
    useState<ActiveFilterProps>({})

  const [secondaryFiltersAPI, setSecondaryFiltersAPI] =
    useState<ActiveFilterAPIProps>({})
  const [responseType, setResponseType] = useState<string>('get')

  const SecondaryFilterCallback = useCallback((filter: ActiveFilterProps) => {
    setSecondaryFiltersback((prevState) => ({
      ...prevState,
      ...filter,
    }))
  }, [])
  useEffect(() => {
    if (columnSettingsData) {
      if (!customer.vendor_id) {
        const keys = Object.keys(columnSettingsData)
        const newdata = { ...columnSettingsData }
        let isSalesColumnExist = false
        keys?.forEach((item: string) => {
          const itemPresent = ProspectBrandNotSupportedList.includes(item)
          if (itemPresent) {
            isSalesColumnExist = true
            delete newdata?.[item]
          }
        })
        if (isSalesColumnExist) {
          setResponseType('post')
          setSecondaryFiltersAPI(newdata)
        } else {
          setResponseType('get')
          setSecondaryFiltersAPI(newdata)
        }
      } else {
        setResponseType('get')
        setSecondaryFiltersAPI(columnSettingsData)
      }
    }
  }, [ProspectBrandNotSupportedList, columnSettingsData, customer.vendor_id])

  useEffect(() => {
    if (responseType === 'post') {
      CustomFilterHelper({
        api: customFilterUserSettingUrl,
        data: secondaryFiltersAPI,
        type: 'custom-filter',
      })
    }
  }, [customFilterUserSettingUrl, responseType, secondaryFiltersAPI])
  const [filtersToReset, setFiltersToReset] = useState([''])
  const applySecondaryFilter = useCallback(
    (headerTitle?: string) => {
      const headerText = headerTitle?.replace('Filter ', '')
      const applySecondaryFilter = applySecondaryFilterFunction(
        secondaryFiltersback,
        headerText,
      )
      if (applySecondaryFilter !== null) {
        setSecondaryFiltersAPI((prevState) => ({
          ...prevState,
          ...applySecondaryFilter,
        }))
        setResponseType('post')
      }
      setFiltersToReset([headerTitle ?? ''])
    },
    [secondaryFiltersback],
  )

  const [tempSort, setTempSort] = useState('')
  const handleCustomFilterClose = useCallback(
    (headerText: string, secondaryFiltersAPI: ActiveFilterProps) => {
      setSecondaryFiltersback(secondaryFiltersAPI)
      setFiltersToReset([headerText])
      setTempSort(sortBy?.prop)
    },
    [sortBy?.prop],
  )
  const resetSecondaryFilter = useCallback(
    (headerText: string) =>
      resetCustomFilter({
        headerText,
        customFilterUserSettingUrl,
        secondaryFiltersAPI,
        secondaryFiltersData: secondaryFiltersback,
        setSecondaryFiltersAPI,
        setSecondaryFiltersData: setSecondaryFiltersback,
        setDefaultSort() {
          setSortBy({
            prop: 'est_organic_clicks',
            flip: false,
            isByChange: false,
          })
        },
      }),
    [secondaryFiltersAPI, customFilterUserSettingUrl, secondaryFiltersback],
  )
  const getSecondaryFilterObject = useCallback(
    ({
      filterKey,
      tableKey,
      type = 'number',
      typeField,
      selectProps,
      numberProps,
      changeValueLabel,
    }: {
      filterKey: string
      tableKey: string
      type?: 'number' | 'currency' | 'percentage'
      typeField?: string | undefined
      selectProps?: SelectProps
      numberProps?: numberProps
      changeValueLabel?: string
    }) => {
      return {
        headerText: filterKey,
        secondarySortProp: tempSort,
        closeCallout: (headerText: string) =>
          handleCustomFilterClose(headerText, secondaryFiltersAPI),
        onSortChange: (sortPropName: string) => setTempSort(sortPropName),
        callout: applySecondaryFilter,
        enableFilterByChangeValues: Boolean(changeValueLabel),
        resetCallback: changeValueLabel ? resetSecondaryFilter : undefined,
        disabled: !(
          (secondaryFiltersback?.[filterKey]?.text &&
            secondaryFiltersback?.[filterKey]?.value) ||
          (changeValueLabel &&
            secondaryFiltersback?.[changeValueLabel]?.text &&
            secondaryFiltersback?.[changeValueLabel]?.value)
        ),
        children: (
          <SecondaryFilter
            filterKey={filterKey}
            tableKey={tableKey}
            {...{ textFieldType: type }}
            prefix={type === 'currency' ? '$' : ''}
            suffix={type === 'percentage' ? '%' : ''}
            SecondaryFilterCallback={SecondaryFilterCallback}
            selectedSecondaryFilters={secondaryFiltersAPI}
            typeField={typeField}
            selectProps={selectProps}
            numberProps={numberProps}
            filtersToReset={filtersToReset}
            enableFilterByChangeValues={Boolean(changeValueLabel)}
          />
        ),
      }
    },
    [
      SecondaryFilterCallback,
      applySecondaryFilter,
      filtersToReset,
      handleCustomFilterClose,
      secondaryFiltersAPI,
      secondaryFiltersback,
      tempSort,
      resetSecondaryFilter,
    ],
  )
  const removeFilter = useCallback(
    (removeFilter?: string) => {
      const removedFilter = RemoveFilter(secondaryFiltersAPI, removeFilter)
      setSecondaryFiltersAPI(removedFilter)
      const removedFiltersState = RemoveFilterState(
        secondaryFiltersback,
        removeFilter,
      )
      setSecondaryFiltersback(removedFiltersState)
      setResponseType('post')
    },
    [secondaryFiltersAPI, secondaryFiltersback],
  )

  const removeFilters = useCallback(
    (removeFilter?: ActiveTableFilterObj) => {
      const removedFilter = removeFilterFromAPIState(
        secondaryFiltersAPI,
        removeFilter,
      )
      const removedFiltersState = removeFilterFromState(
        secondaryFiltersback,
        removeFilter,
      )
      setSecondaryFiltersAPI(removedFilter)
      setSecondaryFiltersback(removedFiltersState)
      setResponseType('post')
    },
    [secondaryFiltersAPI, secondaryFiltersback],
  )

  // Customize Column

  const defaultColumns = [
    c('totalSales'),
    t('organicClicks'),
    t('keywordsInTopFour'),
    t('keywordsOnPageOne'),
    t('organicClickShare'),
    t('adSales'),
    c('adSpend'),
    t('adConversionRate'),
    c('category'),
    c('tags'),
  ]

  const [selectionList, setSelectionList] = useState(defaultColumns)
  const filterChangeValueDetails: { [key: string]: string } = useMemo(
    () => ({
      'Total Sales': c('changeInTotalSales'),
      'Est. Organic Clicks': t('changeInEstOrganicClicks'),
      'Keywords in Top 4': t('changeInKeywordInTopFour'),
      'Keywords on Page 1': t('changeInKeywordsOnPageOne'),
      'Organic Click Share': t('changeInOrganicClickShare'),
      'Ad Sales': t('changeInAdSales'),
      'Ad Spend': t('changeInAdSpend'),
      'Ad Conversion Rate': t('changeInAdConversionRate'),
      ACoS: t('changeInAcos'),
      ROAS: t('changeInROAS'),
      CPC: t('changeInCPC'),
      'Click Through Rate': t('changeInClickThroughRate'),
    }),
    [t],
  )
  const extraCustomFilterParams = useMemo(() => {
    const keys = Object.keys(secondaryFiltersAPI)
    const newdata = { ...secondaryFiltersAPI }
    if (!customer.vendor_id) {
      keys?.forEach((item: string) => {
        const itemPresent = ProspectBrandNotSupportedList.includes(item)
        if (itemPresent) {
          delete newdata?.[item]
        }
      })
    }

    const filterParams = customFilterParams(
      newdata,
      selectionList,
      filterChangeValueDetails,
    )
    return { filter: filterParams }
  }, [
    ProspectBrandNotSupportedList,
    customer.vendor_id,
    secondaryFiltersAPI,
    selectionList,
    filterChangeValueDetails,
  ])
  const {
    newStartDate,
    newEndDate,
    comparisonStartDate,
    comparisonEndDate,
    currentTimeframeCheck,
    previousDates,
  } = useRankTrackerDates()

  useEffect(() => {
    if (columnSettingsData || customFilterStatus === 'success') {
      setTriggerProductsAPI(true)
    }
  }, [columnSettingsData, customFilterStatus])

  useEffect(() => {
    if (Object.keys(extraCustomFilterParams['filter']).length) {
      setTriggerProductsAPI(true)
    }
  }, [extraCustomFilterParams])
  // Fetch data
  const languageParams = useLanguageParams()
  const rankTrackerApi = `${getApiUrlPrefix('rank-tracker')}/api/v3/customer/${
      searchQueryParams?.customer_id
        ? searchQueryParams?.customer_id
        : customer.id
    }`,
    rankTrackerProductsApi = `${rankTrackerApi}/products`,
    commonParams = useMemo(() => {
      return {
        start_date: currentTimeframeCheck ? newStartDate : startDate,
        end_date: timeframe.type === 'current' ? newEndDate : endDate,
        ...(search ? { search_for: search } : {}),
        ...(customer.vendor_id ? { brand_id: customer.vendor_id } : {}),
        ...(searchQueryParams?.brand_id &&
        searchQueryParams?.brand_id !== 'null'
          ? { brand_id: searchQueryParams?.brand_id }
          : {}),
        currency_code: userCurrency?.currency_code,
        marketplace_ids:
          getFilteredIds(
            true,
            false,
            marketplaceIds ?? [],
            selectedRegions,
            allMarketplaces,
          ) ?? [],
        comparison_start: secondRange_startDate
          ? secondRange_startDate
          : currentTimeframeCheck
            ? comparisonStartDate
            : previousDates?.startDate,
        comparison_end: secondRange_endDate
          ? secondRange_endDate
          : timeframe.type === 'current'
            ? comparisonEndDate
            : previousDates?.endDate,
        ...(aggregate_by ? { aggregate_by } : {}),
        sort: standardSortParams(sortBy, ['product_title']),
        ...(rankTrackerState.productsSoldBy.id !== 0
          ? { sold_by_pattern: true }
          : {}),
        ...(tags?.tag_ids?.length > 0
          ? {
              tag_ids: tags.tag_ids,
              match_all_tags: tags.matchAllTags,
              expires_in: 300,
            }
          : {}),
        ...(rankTrackerState?.keywordSource?.id === 0
          ? { keyword_status: true }
          : {}),
        ...extraCustomFilterParams,
        ...(isVatAdjustmentTurnedOn
          ? { vat_adjusted: isVatAdjustmentTurnedOn }
          : {}),
        ...(doCustomCategoriesExist
          ? { custom_category_ids: customCategories }
          : {}),
        ...languageParams,
      }
    }, [
      currentTimeframeCheck,
      newStartDate,
      startDate,
      timeframe.type,
      newEndDate,
      endDate,
      search,
      customer.vendor_id,
      searchQueryParams?.brand_id,
      userCurrency?.currency_code,
      marketplaceIds,
      secondRange_startDate,
      comparisonStartDate,
      previousDates?.startDate,
      previousDates?.endDate,
      secondRange_endDate,
      comparisonEndDate,
      aggregate_by,
      sortBy,
      rankTrackerState.productsSoldBy.id,
      tags.tag_ids,
      tags.matchAllTags,
      extraCustomFilterParams,
      isVatAdjustmentTurnedOn,
      rankTrackerState?.keywordSource?.id,
      doCustomCategoriesExist,
      customCategories,
      languageParams,
      selectedRegions,
      allMarketplaces,
    ]),
    {
      status,
      data: apiResponse,
      fetchNextPage,
      hasNextPage,
    } = useInfiniteQuery({
      queryKey: [
        rankTrackerProductsApi,
        startDate,
        endDate,
        search,
        sortBy?.prop,
        sortBy?.flip,
        marketplaceIds,
        previousDates,
        commonParams,
        secondRange_endDate,
        secondRange_startDate,
        aggregate_by,
        customCategories,
      ],
      queryFn: ({ pageParam = 1, signal }) => {
        const params = {
          ...commonParams,
          page: pageParam,
          per_page: 20,
        }
        return getOrPostAPI(
          !doCustomCategoriesExist,
          rankTrackerProductsApi,
          params,
          signal,
        )
      },
      initialPageParam: 1,
      enabled: triggerProductsAPI,
      gcTime: trafficCacheTime,
      getNextPageParam: (previousResponse) => {
        return previousResponse?.data?.data?.pagination?.lastPage
          ? undefined
          : previousResponse?.data?.data?.pagination?.next_page
      },
    }),
    count = apiResponse?.pages[0]?.data?.data?.pagination?.count,
    data = apiResponse
      ? apiResponse.pages
          .flatMap((page) => {
            return page?.data?.data?.data
          })
          .map((item: RankTrackerItem) => {
            return {
              ...item,
              tags:
                item.tags &&
                item.tags.includes(',') &&
                typeof item.tags === 'string'
                  ? item.tags?.split(',')
                  : item.tags,
            }
          })
      : []

  const searchInputHandler = (value: string) => {
    setState((prevState) => ({
      ...prevState,
      search: value ?? '',
    }))
  }

  const sort: SortColumnProps['sorter'] = (sortObj) => {
    isMounted() &&
      setSortBy({
        prop: sortObj.activeColumn,
        flip: sortObj.direction,
        lowerCaseParam: sortObj.lowerCaseParam,
      })
  }

  const csvDownloadOptions: DownloadOptionsType = [
    {
      linkName: c('rankTracker'),
      csvName: c('rankTracker'),
      csvFormat: {
        api: (csvParams) =>
          getOrPostAPI(
            !doCustomCategoriesExist,
            rankTrackerProductsApi,
            csvParams,
          ).then((response) => {
            return response.data
          }),
        params: {
          ...commonParams,
          async: true,
          csv_name: c('rankTracker'),
        },
        callout: csvToastCallout(),
      },
    },
  ]

  useEffect(() => {
    updateBreadcrumbs({
      name:
        searchQueryParams?.customer_id && selectedBrandName
          ? selectedBrandName
          : c('conversion'),
      link: `${pathname}${searchParams}`,
      changeType: !searchQueryParams?.customer_id ? 'rootLevel' : 'tab',
    })
  }, [
    pathname,
    searchQueryParams?.customer_id,
    selectedBrandName,
    updateBreadcrumbs,
    searchParams,
  ])

  const originalConfig = useMemo(
    () => [
      {
        name: 'product_title',
        label: c('product'),
        cell: {
          children: (product: RankTrackerItem) => {
            const link = `/content/rank-tracker/products/${
              product?.market_product_id
            }/ranked-keywords${
              searchQueryParams?.customer_id
                ? '?customer_id=' + searchQueryParams?.customer_id
                : ''
            }`
            return (
              <span className='flex'>
                <PrimaryTableCell
                  sortBy={sortBy}
                  title={product?.product_title}
                  titleProp='product_title'
                  uniqId={{
                    id: product?.asin,
                    idLabel: 'ASIN',
                    idName: 'asin',
                  }}
                  imageProps={{
                    url: product?.product_image_url,
                    alt: product?.product_title,
                  }}
                  marketplaceNames={product?.marketplace_name}
                  productLink={link}
                  routerComponent={Link}
                  routerProp='to'
                  externalLink={product?.product_url}
                />
              </span>
            )
          },
        },
        style: {
          minWidth: '300px',
        },
        mainColumn: true,
      },
      {
        name: 'total_sales',
        label: c('totalSales'),
        ...getContentTableHeaderTooltip('totalSales'),
        cell: {
          children: (product: RankTrackerItem) => {
            return (
              <MdashCheck check={!!product?.total_sales?.value}>
                <ListChangeData
                  value={product?.total_sales?.value ?? 0}
                  changeValue={product?.total_sales?.change ?? 0}
                  currency={userCurrency}
                  changeFormat='currency'
                  tooltipContent={product?.total_sales?.pct_change ?? 0}
                  decimalScale={decimalScaleHelper(
                    product?.total_sales?.value ?? 0,
                  )}
                  changeDecimalScale={decimalScaleHelper(
                    product?.total_sales?.change ?? 0,
                  )}
                  customClass={
                    sortBy.prop === 'total_sales' ? 'fw-semi-bold' : ''
                  }
                  changeValueClass={
                    sortBy.prop === 'change__total_sales' ? 'fw-semi-bold' : ''
                  }
                />
              </MdashCheck>
            )
          },
        },
        ...(customer?.vendor_id
          ? {
              options: [
                {
                  name: 'total_sales',
                  label: c('totalSales'),
                },
                {
                  name: 'change__total_sales',
                  label: c('changeInTotalSales'),
                },
              ],
            }
          : {}),
        ...(customer?.vendor_id
          ? {
              filter: getSecondaryFilterObject({
                filterKey: c('totalSales'),
                tableKey: 'total_sales',
                type: 'currency',
                changeValueLabel: c('changeInTotalSales'),
              }),
            }
          : {}),
        ...(customer?.vendor_id ? {} : { noSort: true }),
      },

      {
        name: 'est_organic_clicks',
        label: t('organicClicks'),
        ...getContentTableHeaderTooltip('estOrganicClicks'),
        cell: {
          children: (product: RankTrackerItem) => {
            return (
              <div
                className={
                  screenIsMdMax
                    ? `flex-direction-column  ${styles.width100}`
                    : styles.estOrganicClicksColumn
                }
              >
                <ListChangeData
                  value={product?.est_organic_clicks?.value}
                  changeValue={product?.est_organic_clicks?.change}
                  customClass={
                    sortBy.prop === 'est_organic_clicks' ? 'fw-semi-bold' : ''
                  }
                  changeValueClass={
                    sortBy.prop === 'change__est_organic_clicks'
                      ? 'fw-semi-bold'
                      : ''
                  }
                  thresholdValue={0}
                />
                <div className={screenIsMdMax ? 'pat-mt-4' : ''}>
                  <MdashCheck check={!!product?.est_organic_clicks?.value}>
                    <RankTrackerSparklineWrapper
                      topValue={product?.est_organic_clicks?.value}
                      title='Est. Organic Clicks'
                      dataKey='est_organic_clicks'
                      brand={customer.customer_name}
                      asin={product?.asin.toString()}
                      productTitle={product?.product_title}
                      marketplace={product?.marketplace_name}
                      isDisplayCsvDownloadOption
                      params={commonParams}
                      marketplaceIds={product?.marketplace_id}
                      marketProductId={product?.market_product_id}
                      apiEndpoint={`api/v3/customer/${
                        searchQueryParams?.customer_id
                          ? Number(searchQueryParams?.customer_id)
                          : Number(customer.id)
                      }/product_sparkline`}
                    ></RankTrackerSparklineWrapper>
                  </MdashCheck>
                </div>
              </div>
            )
          },
        },
        options: [
          {
            name: 'est_organic_clicks',
            label: t('organicClicks'),
          },
          {
            name: 'change__est_organic_clicks',
            label: t('changeInEstOrganicClicks'),
          },
        ],
        filter: getSecondaryFilterObject({
          filterKey: t('organicClicks'),
          tableKey: 'est_organic_clicks',
          type: 'number',
          typeField: 'number',
          numberProps: {
            onlyWholeNumbers: true,
          },
          changeValueLabel: t('changeInEstOrganicClicks'),
        }),
      },
      {
        name: 'keywords_in_top_4',
        label: t('keywordsInTopFour'),
        ...getContentTableHeaderTooltip('keywordsInTop4'),
        cell: {
          children: (product: RankTrackerItem) => {
            return (
              <ListChangeData
                value={product?.keywords_in_top_4?.value}
                changeValue={product?.keywords_in_top_4?.change}
                icon={<Icon icon='cup' iconSize='12px' />}
                customClass={
                  sortBy.prop === 'keywords_in_top_4' ? 'fw-semi-bold' : ''
                }
                changeValueClass={
                  sortBy.prop === 'change__keywords_in_top_4'
                    ? 'fw-semi-bold'
                    : ''
                }
              />
            )
          },
        },
        options: [
          {
            name: 'keywords_in_top_4',
            label: t('keywordsInTopFour'),
          },
          {
            name: 'change__keywords_in_top_4',
            label: t('changeInKeywordInTopFour'),
          },
        ],
        filter: getSecondaryFilterObject({
          filterKey: t('keywordsInTopFour'),
          tableKey: 'keywords_in_top_4',
          type: 'number',
          typeField: 'number',
          numberProps: {
            onlyWholeNumbers: true,
          },
          changeValueLabel: t('changeInKeywordInTopFour'),
        }),
      },
      {
        name: 'keywords_on_page_1',
        label: t('keywordsOnPageOne'),
        ...getContentTableHeaderTooltip('keywordsOnPage1'),
        cell: {
          children: (product: RankTrackerItem) => {
            return (
              <ListChangeData
                value={product?.keywords_on_page_1?.value}
                changeValue={product?.keywords_on_page_1?.change}
                customClass={
                  sortBy.prop === 'keywords_on_page_1' ? 'fw-semi-bold' : ''
                }
                changeValueClass={
                  sortBy.prop === 'change__keywords_on_page_1'
                    ? 'fw-semi-bold'
                    : ''
                }
              />
            )
          },
        },
        options: [
          {
            name: 'keywords_on_page_1',
            label: t('keywordsOnPageOne'),
          },
          {
            name: 'change__keywords_on_page_1',
            label: t('changeInKeywordsOnPageOne'),
          },
        ],
        filter: getSecondaryFilterObject({
          filterKey: t('keywordsOnPageOne'),
          tableKey: 'keywords_on_page_1',
          type: 'number',
          typeField: 'number',
          numberProps: {
            onlyWholeNumbers: true,
          },
          changeValueLabel: t('changeInKeywordsOnPageOne'),
        }),
      },
      {
        name: 'amazons_choice_keywords_count',
        label: t('keywordsWithAmazonsChoice'),
        cell: {
          children: (product: RankTrackerItem) => {
            return (
              <MdashCheck check={!!product?.amazons_choice_keywords_count}>
                <NumericFormat
                  value={product?.amazons_choice_keywords_count}
                  thousandSeparator={true}
                  displayType='text'
                  className={
                    sortBy.prop === 'amazons_choice_keywords_count'
                      ? 'fw-semi-bold'
                      : ''
                  }
                />
              </MdashCheck>
            )
          },
        },
        tooltip: {
          content: t('keywordTooltip'),
        },
        ...(getSecondaryFilterObject && {
          filter: getSecondaryFilterObject({
            filterKey: t('keywordsWithAmazonsChoice'),
            tableKey: 'amazons_choice_keywords_count',
            type: 'number',
            typeField: 'number',
            numberProps: {
              onlyWholeNumbers: true,
            },
          }),
        }),
      },
      {
        name: 'organic_click_share',
        label: t('organicClickShare'),
        ...getContentTableHeaderTooltip('organicClickShare'),
        cell: {
          children: (product: RankTrackerItem) => {
            return (
              <ListChangeData
                value={product?.organic_click_share?.value}
                changeValue={getOrganicClickShareChange(
                  product?.organic_click_share?.change,
                )}
                percentage
                decimalScale={2}
                customClass={
                  sortBy.prop === 'organic_click_share' ? 'fw-semi-bold' : ''
                }
                changeValueClass={
                  sortBy.prop === 'change__organic_click_share'
                    ? 'fw-semi-bold'
                    : ''
                }
              />
            )
          },
        },
        options: [
          {
            name: 'organic_click_share',
            label: t('organicClickShare'),
          },
          {
            name: 'change__organic_click_share',
            label: t('changeInOrganicClickShare'),
          },
        ],
        filter: getSecondaryFilterObject({
          filterKey: t('organicClickShare'),
          tableKey: 'organic_click_share',
          type: 'percentage',
          changeValueLabel: t('changeInOrganicClickShare'),
        }),
      },
      {
        name: 'attributed_sales',
        label: t('adSales'),
        ...getContentTableHeaderTooltip('adSales'),
        cell: {
          children: (product: RankTrackerItem) => {
            return (
              <MdashCheck check={!!product?.attributed_sales?.value}>
                <ListChangeData
                  value={product?.attributed_sales?.value ?? 0}
                  changeValue={product?.attributed_sales?.change ?? 0}
                  tooltipContent={product?.attributed_sales?.pct_change ?? 0}
                  currency={userCurrency}
                  changeFormat='currency'
                  decimalScale={decimalScaleHelper(
                    product?.attributed_sales?.change
                      ? Math.abs(product.attributed_sales.change)
                      : 0,
                  )}
                  changeDecimalScale={decimalScaleHelper(
                    product?.attributed_sales?.change
                      ? Math.abs(product.attributed_sales?.change)
                      : 0,
                  )}
                  customClass={
                    sortBy.prop === 'attributed_sales' ? 'fw-semi-bold' : ''
                  }
                  changeValueClass={
                    sortBy.prop === 'change__attributed_sales'
                      ? 'fw-semi-bold'
                      : ''
                  }
                />
              </MdashCheck>
            )
          },
        },
        ...(customer?.vendor_id
          ? {
              options: [
                {
                  name: 'attributed_sales',
                  label: t('adSales'),
                },
                {
                  name: 'change__attributed_sales',
                  label: t('changeInAdSales'),
                },
              ],
            }
          : {}),
        ...(customer?.vendor_id
          ? {
              filter: getSecondaryFilterObject({
                filterKey: t('adSales'),
                tableKey: 'attributed_sales',
                type: 'currency',
                changeValueLabel: 'Change in Ad Sales',
              }),
            }
          : {}),
        ...(customer?.vendor_id ? {} : { noSort: true }),
      },
      {
        name: 'cost',
        label: c('adSpend'),
        ...getContentTableHeaderTooltip('adSpend'),
        cell: {
          children: (product: RankTrackerItem) => {
            return (
              <MdashCheck check={!!product?.cost?.value}>
                <ListChangeData
                  value={product?.cost?.value ?? 0}
                  changeValue={product?.cost?.change ?? 0}
                  tooltipContent={product?.cost?.pct_change ?? 0}
                  currency={userCurrency}
                  changeFormat='currency'
                  decimalScale={decimalScaleHelper(
                    product?.cost?.change ? Math.abs(product.cost.change) : 0,
                  )}
                  changeDecimalScale={decimalScaleHelper(
                    product?.cost?.change ? Math.abs(product.cost?.change) : 0,
                  )}
                  customClass={sortBy.prop === 'cost' ? 'fw-semi-bold' : ''}
                  changeValueClass={
                    sortBy.prop === 'change__cost' ? 'fw-semi-bold' : ''
                  }
                />
              </MdashCheck>
            )
          },
        },
        ...(customer?.vendor_id
          ? {
              options: [
                {
                  name: 'cost',
                  label: c('adSpend'),
                },
                {
                  name: 'change__cost',
                  label: t('changeInAdSpend'),
                },
              ],
            }
          : {}),
        ...(customer?.vendor_id
          ? {
              filter: getSecondaryFilterObject({
                filterKey: c('adSpend'),
                tableKey: 'cost',
                type: 'currency',
                changeValueLabel: 'Change in Ad Spend',
              }),
            }
          : {}),
        ...(customer?.vendor_id ? {} : { noSort: true }),
      },
      {
        name: 'conversion_rate',
        label: t('adConversionRate'),
        ...getContentTableHeaderTooltip('adConversionRate'),
        cell: {
          children: (product: RankTrackerItem) => {
            return (
              <MdashCheck check={!!product?.conversion_rate?.value}>
                <ListChangeData
                  value={product?.conversion_rate?.value ?? 0}
                  changeValue={
                    product?.conversion_rate?.change
                      ? product.conversion_rate.change * 100
                      : 0
                  }
                  changeFormat='number'
                  tooltipContent={product?.conversion_rate?.pct_change ?? 0}
                  percentage
                  decimalScale={2}
                  customClass={
                    sortBy.prop === 'conversion_rate' ? 'fw-semi-bold' : ''
                  }
                  changeValueClass={
                    sortBy.prop === 'change__conversion_rate'
                      ? 'fw-semi-bold'
                      : ''
                  }
                />
              </MdashCheck>
            )
          },
        },
        ...(customer?.vendor_id
          ? {
              options: [
                {
                  name: 'conversion_rate',
                  label: t('adConversionRate'),
                },
                {
                  name: 'change__conversion_rate',
                  label: t('changeInAdConversionRate'),
                },
              ],
            }
          : {}),
        ...(customer?.vendor_id
          ? {
              filter: getSecondaryFilterObject({
                filterKey: t('adConversionRate'),
                tableKey: 'conversion_rate',
                type: 'percentage',
                changeValueLabel: t('changeInAdConversionRate'),
              }),
            }
          : {}),
        ...(customer?.vendor_id ? {} : { noSort: true }),
      },
      {
        name: 'advertised_keywords',
        label: t('advertisedkeywords'),
        ...getContentTableHeaderTooltip('advertisedKeywords'),
        cell: {
          children: (product: RankTrackerItem) => {
            return (
              <MdashCheck check={!!product?.advertised_keywords}>
                <NumericFormat
                  value={product?.advertised_keywords}
                  thousandSeparator
                  displayType='text'
                />
              </MdashCheck>
            )
          },
        },
        ...(customer?.vendor_id
          ? {
              filter: getSecondaryFilterObject({
                filterKey: t('advertisedkeywords'),
                tableKey: 'advertised_keywords',
                type: 'number',
                typeField: 'number',
                numberProps: {
                  onlyWholeNumbers: true,
                },
              }),
            }
          : {}),
        ...(customer?.vendor_id ? {} : { noSort: true }),
      },
      {
        name: 'acos',
        label: c('acos'),
        ...getContentTableHeaderTooltip('acos'),
        cell: {
          children: (product: RankTrackerItem) => {
            return (
              <MdashCheck check={!!product?.acos?.value}>
                <ListChangeData
                  value={product?.acos?.value ?? 0}
                  changeValue={
                    product?.acos?.change ? product.acos.change * 100 : 0
                  }
                  tooltipContent={product?.acos?.pct_change ?? 0}
                  percentage
                  decimalScale={2}
                  reverse
                  customClass={sortBy.prop === 'acos' ? 'fw-semi-bold' : ''}
                  changeValueClass={
                    sortBy.prop === 'change__acos' ? 'fw-semi-bold' : ''
                  }
                />
              </MdashCheck>
            )
          },
        },
        ...(customer?.vendor_id
          ? {
              options: [
                {
                  name: 'acos',
                  label: c('acos'),
                },
                {
                  name: 'change__acos',
                  label: t('changeInAcos'),
                },
              ],
            }
          : {}),
        ...(customer?.vendor_id
          ? {
              filter: getSecondaryFilterObject({
                filterKey: c('acos'),
                tableKey: 'acos',
                type: 'percentage',
                changeValueLabel: t('changeInAcos'),
              }),
            }
          : {}),
        ...(customer?.vendor_id ? {} : { noSort: true }),
      },
      {
        name: 'roas',
        label: c('roas'),
        ...getContentTableHeaderTooltip('roas'),
        cell: {
          children: (product: RankTrackerItem) => {
            return (
              <MdashCheck check={!!product?.roas?.value}>
                <ListChangeData
                  value={product?.roas?.value ?? 0}
                  changeValue={product?.roas?.change ?? 0}
                  tooltipContent={product?.roas?.pct_change ?? 0}
                  decimalScale={2}
                  customClass={sortBy.prop === 'roas' ? 'fw-semi-bold' : ''}
                  changeValueClass={
                    sortBy.prop === 'change__roas' ? 'fw-semi-bold' : ''
                  }
                />
              </MdashCheck>
            )
          },
        },
        ...(customer?.vendor_id
          ? {
              options: [
                {
                  name: 'roas',
                  label: c('roas'),
                },
                {
                  name: 'change__roas',
                  label: t('changeInROAS'),
                },
              ],
            }
          : {}),
        ...(customer?.vendor_id
          ? {
              filter: getSecondaryFilterObject({
                filterKey: c('roas'),
                tableKey: 'roas',
                changeValueLabel: t('changeInROAS'),
              }),
            }
          : {}),
        ...(customer?.vendor_id ? {} : { noSort: true }),
      },
      {
        name: 'cpc',
        label: c('cpc'),
        ...getContentTableHeaderTooltip('cpc'),
        cell: {
          children: (product: RankTrackerItem) => {
            return (
              <MdashCheck check={!!product?.cpc?.value}>
                <ListChangeData
                  value={product?.cpc?.value ?? 0}
                  changeValue={product?.cpc?.change ?? 0}
                  currency={userCurrency}
                  changeFormat='currency'
                  decimalScale={2}
                  reverse
                  customClass={sortBy.prop === 'cpc' ? 'fw-semi-bold' : ''}
                  changeValueClass={
                    sortBy.prop === 'change__cpc' ? 'fw-semi-bold' : ''
                  }
                  tooltipContent={product?.cpc?.pct_change ?? 0}
                />
              </MdashCheck>
            )
          },
        },
        ...(customer?.vendor_id
          ? {
              options: [
                {
                  name: 'cpc',
                  label: c('cpc'),
                },
                {
                  name: 'change__cpc',
                  label: t('changeInCPC'),
                },
              ],
            }
          : {}),
        ...(customer?.vendor_id
          ? {
              filter: getSecondaryFilterObject({
                filterKey: c('cpc'),
                tableKey: 'cpc',
                type: 'currency',
                changeValueLabel: t('changeInCPC'),
              }),
            }
          : {}),
        ...(customer?.vendor_id ? {} : { noSort: true }),
      },
      {
        name: 'click_through_rate',
        label: t('clickThroughRate'),
        ...getContentTableHeaderTooltip('clickThroughRate'),
        cell: {
          children: (product: RankTrackerItem) => {
            return (
              <MdashCheck check={!!product?.click_through_rate?.value}>
                <ListChangeData
                  value={product?.click_through_rate?.value ?? 0}
                  changeValue={
                    product?.click_through_rate?.change
                      ? product.click_through_rate.change * 100
                      : 0
                  }
                  changeFormat='number'
                  tooltipContent={product?.click_through_rate?.pct_change ?? 0}
                  percentage
                  decimalScale={2}
                  customClass={
                    sortBy.prop === 'click_through_rate' ? 'fw-semi-bold' : ''
                  }
                  changeValueClass={
                    sortBy.prop === 'change__click_through_rate'
                      ? 'fw-semi-bold'
                      : ''
                  }
                />
              </MdashCheck>
            )
          },
        },
        ...(customer?.vendor_id
          ? {
              options: [
                {
                  name: 'click_through_rate',
                  label: t('clickThroughRate'),
                },
                {
                  name: 'change__click_through_rate',
                  label: t('changeInClickThroughRate'),
                },
              ],
            }
          : {}),
        ...(customer?.vendor_id
          ? {
              filter: getSecondaryFilterObject({
                filterKey: t('clickThroughRate'),
                tableKey: 'click_through_rate',
                type: 'percentage',
                changeValueLabel: t('changeInClickThroughRate'),
              }),
            }
          : {}),
        ...(customer?.vendor_id ? {} : { noSort: true }),
      },
      ...(isSingleBrandView
        ? CategoriesAndTags({
            ctDrawerState,
            setCtDrawerState,
          }).config<RankTrackerItem>({
            categoryNameKey: 'custom_category_name',
            tagDetailsKey: 'tags',
            rootCategoryIdKey: 'root_category_id',
            leafCategoryIdKey: 'custom_category_id',
            sortTagsList: true,
            isCategorySortable: true,
            isTagsSortable: true,
          })
        : []),
      {
        name: '',
        label: '',
        cell: {
          children: (product: RankTrackerItem) => {
            const link = `/content/rank-tracker/products/${
              product?.market_product_id
            }/ranked-keywords${
              searchQueryParams?.customer_id
                ? '?customer_id=' + searchQueryParams?.customer_id
                : ''
            }`
            return (
              <ButtonGroup
                buttons={[
                  {
                    actions: [
                      {
                        callout: () => null,
                        icon: 'graph',
                        link: `${productUrl}/${product?.master_product_id}/marketplace/${product?.marketplace_id}/listing/${product?.market_product_id}/traffic/digital-shelf`,
                        routerComponent: Link,
                        routerProp: 'to',
                        text: c('viewDigitalShelf'),
                      },
                    ],
                  },
                  {
                    children: t('viewKeywords'),
                    as: 'link',
                    to: link,
                    routerComponent: Link,
                  },
                ]}
              />
            )
          },
        },
        isButton: true,
        style: {
          minWidth: '170px',
        },
        noSort: true,
      },
    ],
    [
      customer?.vendor_id,
      customer.customer_name,
      customer.id,
      getSecondaryFilterObject,
      t,
      searchQueryParams?.customer_id,
      sortBy,
      userCurrency,
      screenIsMdMax,
      commonParams,
      isSingleBrandView,
      ctDrawerState,
    ],
  )
  const userId = useDecodedUserTokenId()
  const apiUrl = `${getApiUrlPrefix('user-settings')}/v1/${
    userId
  }/predict/content:rank_tracker:product_rank_tracker`
  const customFilterTableQueryKey = 'products'
  const { status: userSettingStatus, data: userSettingsApiResponse } = useQuery(
    {
      queryKey: [userId, customFilterTableQueryKey],
      queryFn: ({ signal }) => SecureAxios.get(apiUrl, { signal }),
      gcTime: trafficCacheTime,
    },
  )

  const userSettingsData =
    userSettingStatus === 'success' &&
    userSettingsApiResponse?.data?.selectedColumns

  useEffect(() => {
    if (userSettingsData) {
      setSelectionList(userSettingsData)
    }
  }, [userSettingsData])

  const totalConfigItems = originalConfig.slice(1, -1)
  const totalList = totalConfigItems.map((item) => item.label)
  const customizeColumnConfig = useMemo(() => {
    return [
      originalConfig[0],
      ...getSelectedConfig(originalConfig, selectionList, 'selected'),
      originalConfig[originalConfig.length - 1],
    ]
  }, [originalConfig, selectionList])
  useEffect(() => {
    // Some filters are not available for prospects brand
    if (!customer.vendor_id) {
      setSortBy((prevState: { prop: string; flip: boolean }) => ({
        ...prevState,
        prop: 'est_organic_clicks',
      }))
    }
  }, [customer.vendor_id])
  const customSelectionCallout = (
    selectedList: string[],
    setToDefault?: boolean,
  ) => {
    if (
      !selectedList.includes(
        originalConfig.filter((selectionItem) => {
          const item = selectionItem as customSelectionCalloutProps
          if (item?.options) {
            const value = item.options.find(
              (option) => option.name === sortBy.prop,
            )
            return !!value
          }
          return item.name === sortBy.prop
        })[0].label,
      )
    ) {
      setSortBy((prevState: { prop: string; flip: boolean }) => ({
        ...prevState,
        prop: originalConfig[0].name,
      }))
    }
    setSelectionList(selectedList)
    saveCustomization({
      api: apiUrl,
      selected: selectedList,
      setToDefault: setToDefault,
      type: 'table',
    })

    const keys = Object.keys(secondaryFiltersAPI)
    const originLength = Object.keys(secondaryFiltersAPI)?.length
    const newdata = { ...secondaryFiltersAPI }
    keys?.forEach((item: string) => {
      const itemNotPresent = !selectedList.includes(item)
      if (itemNotPresent) {
        delete newdata?.[item]
      }
    })
    const updatedLength = Object.keys(newdata)?.length
    if (originLength !== updatedLength) {
      setSecondaryFiltersAPI(newdata)
      setResponseType('post')
    }
  }
  const ProspectBrandNotSupportedSortList: Array<string> = useMemo(() => {
    return [
      'total_sales',
      'change__total_sales',
      'change__attributed_sales',
      'attributed_sales',
      'cost',
      'change__cost',
      'conversion_rate',
      'change__conversion_rate',
      'acos',
      'change__acos',
      'roas',
      'change__roas',
      'cpc',
      'change__cpc',
      'click_through_rate',
      'change__click_through_rate',
      'advertised_keywords',
    ]
  }, [])

  useEffect(() => {
    if (!customer.vendor_id) {
      const isSortSupport = ProspectBrandNotSupportedSortList.includes(
        sortBy.prop,
      )
      if (isSortSupport) {
        setSortBy((prevState: { prop: string; flip: boolean }) => ({
          ...prevState,
          prop: originalConfig[0].name,
        }))
      }
    }
  }, [
    originalConfig,
    customer.vendor_id,
    ProspectBrandNotSupportedSortList,
    sortBy.prop,
  ])
  useEffect(() => {
    if (prevCustomer !== customer) {
      setState((prevState) => ({
        ...prevState,
        search: '',
      }))
    }
  }, [customer, prevCustomer])

  return (
    <div>
      <div>
        <StandardTable
          data={data}
          config={customizeColumnConfig}
          dataKey='product_title'
          hasData={data?.length > 0}
          loading={status === 'pending' || customFilterStatus === 'pending'}
          hasMore={!!(status === 'success' && hasNextPage)}
          tableId={tableId}
          twoLineLabel
          sort={sort}
          sortBy={sortBy}
          noDataFields={{
            primaryText: t('noProductsFound'),
            secondaryText: t('couldNotFindAnyProduct'),
          }}
          tableHeaderProps={{
            header: {
              name: c(count !== 1 ? 'products' : 'product'),
              value: convertTableHeaderCount(count),
            },
            search: {
              value: search,
              onChange: (value) => searchInputHandler(value),
              placeholder: c('searchProducts'),
              debounce: 250,
            },
            download: {
              csvDownloadOptions: csvDownloadOptions,
              initialDisplay: true,
              show: true,
            },
            columnFilterProps: {
              activeFilters: secondaryFiltersAPI,
              remove: removeFilters,
            },
            customColumnProps: {
              list: totalList,
              selected: selectionList,
              callout: customSelectionCallout,
              setToDefaultCallout: () =>
                customSelectionCallout(defaultColumns, true),
              isColumnsReorderable: true,
            },
          }}
          getData={fetchNextPage}
          successStatus={status === 'success'}
          stickyTableConfig={stickyTableConfig}
          activeFilters={secondaryFiltersAPI}
          removeFilters={removeFilter}
        />
      </div>
      {isSingleBrandView &&
        CategoriesAndTags({ ctDrawerState, setCtDrawerState }).sideDrawer()}
    </div>
  )
}

export default RankTracker
