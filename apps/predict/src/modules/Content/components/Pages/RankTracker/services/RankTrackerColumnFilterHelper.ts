import { c, t } from '@predict-services'

export const RankTrackerFilterDetails = {
  total_sales: {
    filterKey: c('totalSales'),
    tableKey: 'total_sales',
    type: 'currency',
  },
  est_organic_clicks: {
    filterKey: t('content:organicClicks'),
    tableKey: 'est_organic_clicks',
    type: 'number',
    numberProps: {
      onlyWholeNumbers: true,
    },
  },
  keywords_in_top_4: {
    filterKey: t('content:keywordsInTopFour'),
    tableKey: 'keywords_in_top_4',
    type: 'number',
    numberProps: {
      onlyWholeNumbers: true,
    },
  },
  keywords_on_page_1: {
    filterKey: t('content:keywordsOnPageOne'),
    tableKey: 'keywords_on_page_1',
    type: 'number',
    numberProps: {
      onlyWholeNumbers: true,
    },
  },
  amazons_choice_keywords_count: {
    filterKey: t('content:keywordsWithAmazonsChoice'),
    tableKey: 'amazons_choice_keywords_count',
    type: 'number',
    numberProps: {
      onlyWholeNumbers: true,
    },
  },
  organic_click_share: {
    filterKey: t('content:organicClickShare'),
    tableKey: 'organic_click_share',
    type: 'percentage',
  },
  attributed_sales: {
    filterKey: t('content:adSales'),
    tableKey: 'attributed_sales',
    type: 'currency',
  },
  cost: {
    filterKey: c('adSpend'),
    tableKey: 'cost',
    type: 'currency',
  },
  conversion_rate: {
    filterKey: t('content:adConversionRate'),
    tableKey: 'conversion_rate',
    type: 'percentage',
  },
  advertised_keywords: {
    filterKey: t('content:advertisedkeywords'),
    tableKey: 'advertised_keywords',
    type: 'number',
  },
  roas: {
    filterKey: c('roas'),
    tableKey: 'roas',
    type: 'percentage',
  },
  acos: {
    filterKey: c('acos'),
    tableKey: 'acos',
    type: 'percentage',
  },
  cpc: {
    filterKey: c('cpc'),
    tableKey: 'cpc',
    type: 'currency',
  },
  click_through_rate: {
    filterKey: t('content:clickThroughRate'),
    tableKey: 'click_through_rate',
    type: 'percentage',
  },
}

export const RankTrackerFilterChangeValues: Record<
  string,
  { label: string }[]
> = {
  total_sales: [
    {
      label: c('totalSales'),
    },
    {
      label: c('changeInTotalSales'),
    },
  ],
  est_organic_clicks: [
    {
      label: t('content:organicClicks'),
    },
    {
      label: t('content:changeInEstOrganicClicks'),
    },
  ],
  keywords_in_top_4: [
    {
      label: t('content:keywordsInTopFour'),
    },
    {
      label: t('content:changeInKeywordInTopFour'),
    },
  ],
  keywords_on_page_1: [
    {
      label: t('content:keywordsOnPageOne'),
    },
    {
      label: t('content:changeInKeywordsOnPageOne'),
    },
  ],
  organic_click_share: [
    {
      label: t('content:organicClickShare'),
    },
    {
      label: t('content:changeInOrganicClickShare'),
    },
  ],
  attributed_sales: [
    {
      label: t('content:adSales'),
    },
    {
      label: t('content:changeInAdSales'),
    },
  ],
  cost: [
    {
      label: c('adSpend'),
    },
    {
      label: t('traffic:changeInAdSpend'),
    },
  ],
  conversion_rate: [
    {
      label: t('traffic:adConversionRate'),
    },
    {
      label: t('traffic:changeInAdConversionRate'),
    },
  ],
  acos: [
    {
      label: c('acos'),
    },
    {
      label: t('content:changeInAcos'),
    },
  ],
  roas: [
    {
      label: c('roas'),
    },
    {
      label: t('content:changeInROAS'),
    },
  ],
  cpc: [
    {
      label: c('cpc'),
    },
    {
      label: t('content:changeInCPC'),
    },
  ],
  click_through_rate: [
    {
      label: t('content:clickThroughRate'),
    },
    {
      label: t('content:changeInClickThroughRate'),
    },
  ],
}
