// Master index file for all retail readiness headers
import { productListingHeaders } from './ProductListingHeaders'
import { contentMetricsHeaders } from './ContentMetricsHeaders'
import { salesRevenueHeaders } from './SalesRevenueHeaders'
import { reviewsRatingsHeaders } from './ReviewsRatingsHeaders'
import { trafficViewsHeaders } from './TrafficViewsHeaders'
import { advertisingMetricsHeaders } from './AdvertisingMetricsHeaders'
import { brandSummaryHeaders } from './BrandSummaryHeaders'
import { missingContentHeaders } from './MissingContentHeaders'
import { percentageRateHeaders } from './PercentageRateHeaders'

// Re-export all header groups
export {
  productListingHeaders,
  contentMetricsHeaders,
  salesRevenueHeaders,
  reviewsRatingsHeaders,
  trafficViewsHeaders,
  advertisingMetricsHeaders,
  brandSummaryHeaders,
  missingContentHeaders,
  percentageRateHeaders,
}

// Combined headers object that reconstructs the original commonRetailReadinessHeaders
export const allRetailReadinessHeaders = {
  ...productListingHeaders,
  ...contentMetricsHeaders,
  ...salesRevenueHeaders,
  ...reviewsRatingsHeaders,
  ...trafficViewsHeaders,
  ...advertisingMetricsHeaders,
  ...brandSummaryHeaders,
  ...missingContentHeaders,
  ...percentageRateHeaders,
}

// Export individual headers for convenience
export const {
  // Product/Listing details
  productTitle,
  brandTitle,
  action,
  actionBrand,

  // Content metrics
  overallReadiness,
  titleLength,
  titleLengthNew,
  bullets,
  bulletsNew,
  images,
  videos,
  videosNew,
  aPlusContent,
  aPlusContentNew,

  // Sales/Revenue data
  totalSales30D,
  totalSales30DBrand,
  totalSales,
  creativeOpportunity,
  buyBox30d,
  buyBox,
  weeksOnHand,
  weeksOnHandNew,

  // Reviews/Ratings
  avgRating,
  reviewValue,
  reviewValueNew,

  // Traffic/Views data
  totalPageViews,
  patternPageViews,
  organicPatternPageViews,
  conversion,
  conversion_rate_page_views,
  adConversionRate,

  // Advertising metrics
  adSales30D,
  adSales,
  cost30D,
  cost,
  conversion30D,
  advtStrategy,

  // Brand/Summary views
  retailReadinessNew,
  retailReadiness,
  revenueWeightedRetailReadiness,
  revenueWeightedRetailReadinessNew,
  noProducts,
  noProductsNew,
  listings100PercentReady,
  listings100PercentReadyNew,

  // Missing content tracking
  missingAvgRating,
  missingReviews,
  missingTitleLength,
  missingBullets,
  missingImages,
  missingVideo,
  missingAPlusContent,
  missingRatingsReviews,
  missingWrittenContent,
  missingVisualContent,

  // Percentage/Rate calculations
  pctOfListingMissingAPlusContent,
  pctOfListingMissingAvgRating,
  pctOfListingMissingBullets,
  pctOfListingMissingImages,
  pctOfListingMissingReviews,
  pctOfListingMissingTitleLength,
  pctOfListingMissingVideo,
  pctOfListings100PercentReady,
  readinessCriteria,
} = allRetailReadinessHeaders
