//@ts-check
const path = require('path')

const { composePlugins, withNx } = require('@nx/next')
const createNextIntlPlugin = require('next-intl/plugin')
const withNextIntl = createNextIntlPlugin(
  './src/modules/static-shared/src/translations/request.ts',
)
const loaderUtils = require('loader-utils')
// @ts-expect-error disable linting for regexEqual
const regexEqual = (x, y) => {
  return (
    x instanceof RegExp &&
    y instanceof RegExp &&
    x.source === y.source &&
    x.global === y.global &&
    x.ignoreCase === y.ignoreCase &&
    x.multiline === y.multiline
  )
}
// @ts-expect-error disable linting for localIdent
const localIdent = (loaderContext, localIdentName, localName, options) => {
  return (
    loaderUtils
      .interpolateName(loaderContext, `[folder]_[name]__${localName}`, options)
      // Webpack name interpolation returns `about_about.module__root` for
      // `.root {}` inside a file named `about/about.module.css`. Let's simplify
      // this.
      .replace(/\.module_/, '_')
      // Replace invalid symbols with underscores instead of escaping
      // https://mathiasbynens.be/notes/css-escapes#identifiers-strings
      .replace(/[^a-zA-Z0-9-_]/g, '_')
      // "they cannot start with a digit, two hyphens, or a hyphen followed by a digit [sic]"
      // https://www.w3.org/TR/CSS21/syndata.html#characters
      .replace(/^(\d|--|-\d)/, '__$1')
  )
}

// Overrides for css-loader plugin
// @ts-expect-error disable linting for cssLoaderOptions
function cssLoaderOptions(modules) {
  const { getLocalIdent, ...others } = modules
  return {
    ...others,
    getLocalIdent: localIdent,
    exportLocalsConvention: 'camelCaseOnly',
    mode: 'local',
  }
}

/**
 * @type {import('@nx/next/plugins/with-nx').WithNxOptions}
 **/
const nextConfig = {
  nx: {
    // Set this to true if you would like to use SVGR
    // See: https://github.com/gregberge/svgr
    svgr: false,
  },
  env: {
    ROUTE_PREFIX_V3: '/v3',
    CLIENT_CDN_ENDPOINT: process.env.CLIENT_CDN_ENDPOINT,
    NEXT_PUBLIC_AUTH0_DOMAIN: process.env.AUTH0_ISSUER_BASE_URL,
    NEXT_PUBLIC_AUTH0_CLIENT_ID: process.env.AUTH0_CLIENT_ID,
    AUTH_CACHE_URL: process.env.AUTH_CACHE_URL,
    NEXT_PUBLIC_CLIENT_UPLOAD_API: process.env.CLIENT_UPLOAD_API,
    NEXT_PUBLIC_CLIENT_PDF_UPLOAD_API: process.env.CLIENT_PDF_UPLOAD_API,
    NEXT_PUBLIC_CLIENT_ADMIN_PORTAL_URL: process.env.CLIENT_ADMIN_PORTAL_URL,
    NEXT_PUBLIC_ONE_SCHEMA_IMPORT_WEBHOOK_KEY:
      process.env.CLIENT_ONE_SCHEMA_IMPORT_WEBHOOK_KEY,
    NEXT_PUBLIC_DEFAULT_ONE_SCHEMA_TEMPLATE_KEY:
      process.env.CLIENT_DEFAULT_ONE_SCHEMA_TEMPLATE_KEY,
  },
  experimental: {
    serverActions: {
      bodySizeLimit: '200mb',
      allowedOrigins: [
        '*.localhost:8080',
        '*.pxm.localhost:8080',
        '*.staging.amplifi.io',
        '*.amplifi.io',
      ],
    },
    serverComponentsExternalPackages: ['pino', 'pino-pretty'],
  },
  images: {
    remotePatterns: [
      {
        protocol: 'https',
        hostname: 'cdn.*.pattern.com',
        port: '',
      },
    ],
  },
  // rewriting the path to the toggle-api to avoid CORS issues in order to fetch data from the API
  rewrites: async () => {
    return [
      {
        source: '/toggle/:path*',
        destination: 'https://toggle-api.usepredict.com/:path*',
      },
    ]
  },

  webpack: (config) => {
    config.module.rules.push({
      test: [
        // Original rule for npm package
        /node_modules[\\/]@patterninc[\\/]react-ui[\\/.].*\.js$/,
        // Add this pattern to match your local react-ui components
        /[\\/]react-ui[\\/]src[\\/].*\.(js|jsx|ts|tsx)$/,
      ],
      loader: require.resolve('./use-client-loader'),
    })
    config.resolve.alias = {
      ...config.resolve.alias,
      '@patterninc/react-ui': path.resolve(__dirname, '../../lib/react-ui/src'),
    }
    // @ts-expect-error disable linting for file-loader
    // Exclude SVGs from default file-loader processing
    config.module.rules.forEach((rule) => {
      if (rule.test && rule.test instanceof RegExp && rule.test.test('.svg')) {
        rule.exclude = /\.svg$/
      }
    })

    // Add custom SVGR loader for importing SVGs as React components
    config.module.rules.push({
      test: /\.svg$/,
      use: [
        {
          loader: '@svgr/webpack',
          options: {
            svgoConfig: {
              plugins: [
                {
                  name: 'preset-default',
                  params: { overrides: { removeViewBox: false } },
                },
              ],
            },
            titleProp: true,
          },
        },
      ],
    })

    const oneOf = config.module.rules.find(
      // @ts-expect-error disable linting for oneOf
      (rule) => typeof rule.oneOf === 'object',
    )

    if (oneOf) {
      // Find the module which targets
      // @ts-expect-error disable linting for moduleSassRule
      const moduleSassRule = oneOf.oneOf.find((rule) =>
        regexEqual(rule.test, /\.module\.(scss|sass)$/),
      )

      if (moduleSassRule) {
        // @ts-expect-error disable linting for cssLoader
        // Get the config object for css-loader plugin
        const cssLoader = moduleSassRule.use.find(({ loader }) =>
          loader.includes('/css-loader'),
        )
        if (cssLoader) {
          cssLoader.options = {
            ...cssLoader.options,
            modules: cssLoaderOptions(cssLoader.options.modules),
          }
        }
      }
    }

    // Recursively update all css-loader options to set exportLocalsConvention: 'camelCase'
    // @ts-expect-error disable linting for updateCssLoaderExportLocalsConvention
    function updateCssLoaderExportLocalsConvention(rules) {
      // @ts-expect-error disable linting for rules
      rules.forEach((rule) => {
        if (rule.oneOf && Array.isArray(rule.oneOf)) {
          updateCssLoaderExportLocalsConvention(rule.oneOf)
        }
        if (rule.use && Array.isArray(rule.use)) {
          // @ts-expect-error disable linting for rule.use
          rule.use.forEach((u) => {
            if (
              u.loader &&
              u.loader.includes('css-loader') &&
              u.options &&
              typeof u.options === 'object'
            ) {
              if (u.options.modules) {
                u.options.modules = {
                  ...u.options.modules,
                  exportLocalsConvention: 'camelCase',
                }
              }
            }
          })
        }
      })
    }
    updateCssLoaderExportLocalsConvention(config.module.rules)

    return config
  },
}

const plugins = [
  // Add more Next.js plugins to this list if needed.
  withNx,
  withNextIntl,
]
module.exports = {
  watchOptions: {
    ignored: ['**/node_modules', '**/dist', '**/build', '**/.next'],
  },
  async headers() {
    return [
      {
        source: '/api/sse',
        headers: [
          { key: 'Content-Type', value: 'text/event-stream' },
          { key: 'Cache-Control', value: 'no-cache, no-store, no-transform' },
          { key: 'Connection', value: 'keep-alive' },
          { key: 'Content-Encoding', value: 'none' },
          { key: 'Pragma', value: 'no-cache' },
          { key: 'Expires', value: '0' },
        ],
      },
    ]
  },
}

module.exports = composePlugins(...plugins)(nextConfig)
