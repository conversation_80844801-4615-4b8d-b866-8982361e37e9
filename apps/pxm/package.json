{"name": "pxm", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"setup:env": "bash scripts/env.sh", "setup:npmrc": "bash scripts/npmrc.sh"}, "keywords": [], "author": "Pattern Inc.", "private": true, "license": "UNLICENSED", "dependencies": {"@patterninc/laravel_js_validator": "^1.0.18", "@patterninc/react-ui": "workspace:*", "@rjsf/core": "^5.24.8", "@rjsf/utils": "^5.24.8", "@rjsf/validator-ajv8": "^5.24.8", "@sheet/edit": "^2.20250109.2", "dd-trace": "^5.36.0", "diff": "^7.0.0", "file-selector": "^2.1.2", "fine-uploader-wrappers": "^1.0.1", "idb": "^8.0.2", "json-schema-to-ts": "^3.1.1", "is-url": "^1.2.4", "pino": "9.6.0", "pino-pretty": "^13.0.0", "react": "19.1.0", "react-dom": "19.1.0", "react-fine-uploader": "^1.1.1"}, "devDependencies": {"@eslint/eslintrc": "^3.3.0", "@types/diff": "^7.0.1", "@types/is-url": "^1.2.32", "msw": "^2.7.3", "msw-storybook-addon": "^2.0.4"}, "msw": {"workerDirectory": ["./public"]}}