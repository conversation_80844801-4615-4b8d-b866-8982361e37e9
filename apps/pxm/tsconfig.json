{"compilerOptions": {"jsx": "preserve", "allowJs": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "strict": true, "forceConsistentCasingInFileNames": true, "noEmit": true, "resolveJsonModule": true, "isolatedModules": true, "incremental": true, "plugins": [{"name": "next"}], "types": ["vitest/globals", "node"], "paths": {"@amplifi-workspace/agents": ["apps/pxm/src/modules/agents/src/index.ts"], "@amplifi-workspace/agents/server": ["apps/pxm/src/modules/agents/src/server.ts"], "@amplifi-workspace/home": ["apps/pxm/src/modules/home/<USER>/index.ts"], "@amplifi-workspace/home/<USER>": ["apps/pxm/src/modules/home/<USER>/server.ts"], "@amplifi-workspace/match": ["apps/pxm/src/modules/match/src/index.ts"], "@amplifi-workspace/match/server": ["apps/pxm/src/modules/match/src/server.ts"], "@amplifi-workspace/optimize": ["apps/pxm/src/modules/optimize/src/index.ts"], "@amplifi-workspace/optimize/server": ["apps/pxm/src/modules/optimize/src/server.ts"], "@amplifi-workspace/services/redis": ["services/redisClient.ts"], "@amplifi-workspace/settings": ["apps/pxm/src/modules/settings/src/index.ts"], "@amplifi-workspace/static-shared": ["apps/pxm/src/modules/static-shared/src/index.ts"], "@amplifi-workspace/static-shared/server": ["apps/pxm/src/modules/static-shared/src/server.ts"], "@amplifi-workspace/store": ["apps/pxm/src/modules/store/src/index.ts"], "@amplifi-workspace/store/server": ["apps/pxm/src/modules/store/src/server.ts"], "@amplifi-workspace/syndication": ["apps/pxm/src/modules/syndicate/src/index.ts"], "@amplifi-workspace/syndication/server": ["apps/pxm/src/modules/syndicate/src/server.ts"], "@amplifi-workspace/web-shared": ["apps/pxm/src/modules/shared/src/index.ts"], "@amplifi-workspace/web-shared/server": ["apps/pxm/src/modules/shared/src/server.ts"], "@patterninc/react-ui": ["lib/react-ui/src/module.ts"], "@patterninc/react-ui/dist/variables": ["lib/react-ui/src/scss/base/_variables.scss"]}}, "exclude": ["node_modules", "jest.config.ts", "**/*.stories.ts", "**/*.stories.js", "mcp-server", ".next"], "extends": "../../tsconfig.base.json", "include": ["**/*.ts", "**/*.tsx", "**/*.js", "**/*.jsx", "../../apps/pxm/.next/types/**/*.ts", "../../dist/apps/pxm/.next/types/**/*.ts", "next-env.d.ts", ".next/types/**/*.ts"]}